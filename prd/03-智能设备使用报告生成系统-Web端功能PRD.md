# 📄 智能设备使用报告生成系统 - Web端功能PRD
**版本**：V2.0  
**编写人**：产品经理  
**发布日期**：2025-07-29
**文档类型**：Web端功能设计  

---

## 一、Web端功能概述

### 1.1 核心功能
Web端作为用户交互界面，负责：
- **报告配置**：支持数据源选择、模块配置、Prompt设置
- **实时生成**：支持SSE流式报告生成和进度展示
- **混合渲染**：支持Markdown和Web组件混合展示
- **交互功能**：支持图表点击、数据钻取、对话查询
- **响应式设计**：支持多设备适配和良好用户体验

### 1.2 技术架构
- **框架**：React 18 + TypeScript
- **构建工具**：Vite
- **状态管理**：React Query + Zustand
- **图表库**：ECharts + React-ECharts
- **UI组件**：Ant Design + Tailwind CSS
- **样式**：CSS Modules + Tailwind CSS

---

## 二、页面结构设计

### 2.1 主要页面

#### 2.1.1 报告配置页面
- **路径**：`/reports/configure`
- **功能**：报告参数配置、数据源选择、模块设置
- **组件**：配置表单、数据源选择器、模块配置器

#### 2.1.2 报告生成页面
- **路径**：`/reports/generate`
- **功能**：实时报告生成、进度展示、结果预览
- **组件**：进度条、实时日志、结果预览

#### 2.1.3 报告展示页面
- **路径**：`/reports/view/:reportId`
- **功能**：混合渲染展示、交互功能、数据钻取
- **组件**：渲染引擎、交互组件、导航面板

### 2.2 页面路由设计

```typescript
// 路由配置
const routes = [
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        path: 'reports',
        children: [
          {
            path: 'configure',
            element: <ReportConfigurePage />
          },
          {
            path: 'generate',
            element: <ReportGeneratePage />
          },
          {
            path: 'view/:reportId',
            element: <ReportViewPage />
          }
        ]
      },
      {
        path: 'dashboard',
        element: <DashboardPage />
      }
    ]
  }
];
```

---

## 三、核心组件设计

### 3.1 渲染引擎组件

#### 3.1.1 混合渲染引擎
```typescript
interface RenderEngineProps {
  modules: ReportModule[];
  onInteraction: (interaction: InteractionEvent) => void;
}

const RenderEngine: React.FC<RenderEngineProps> = ({ modules, onInteraction }) => {
  const renderModule = (module: ReportModule) => {
    switch (module.display_logic) {
      case 'server_query_llm_text':
        return <MarkdownRenderer content={module.content} />;
      
      case 'server_query_llm_web':
        return (
          <div>
            <ChartRenderer 
              config={module.chartConfig}
              queryParams={module.queryParams}
              onInteraction={onInteraction}
            />
            <MarkdownRenderer content={module.analysisText} />
          </div>
        );
      
      case 'query_llm_interactive':
        return (
          <InteractiveRenderer 
            content={module.content}
            config={module.config}
            onInteraction={onInteraction}
          />
        );
      
      default:
        return <div>不支持的渲染类型</div>;
    }
  };

  return (
    <div className="render-engine">
      {modules.map(module => (
        <div key={module.id} className="module-container">
          {renderModule(module)}
        </div>
      ))}
    </div>
  );
};
```

#### 3.1.2 Markdown渲染组件
```typescript
interface MarkdownRendererProps {
  content: string;
  className?: string;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, className }) => {
  return (
    <div className={`markdown-renderer ${className || ''}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight]}
        components={{
          code: ({ node, inline, className, children, ...props }) => {
            const match = /language-(\w+)/.exec(className || '');
            return !inline && match ? (
              <SyntaxHighlighter
                style={github}
                language={match[1]}
                PreTag="div"
                {...props}
              >
                {String(children).replace(/\n$/, '')}
              </SyntaxHighlighter>
            ) : (
              <code className={className} {...props}>
                {children}
              </code>
            );
          }
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};
```

### 3.2 图表组件库

#### 3.2.1 基础图表组件
```typescript
interface ChartRendererProps {
  config: ChartConfig;
  queryParams: QueryParams;
  onInteraction: (interaction: ChartInteraction) => void;
}

const ChartRenderer: React.FC<ChartRendererProps> = ({ 
  config, 
  queryParams, 
  onInteraction 
}) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['chart', queryParams],
    queryFn: () => fetchChartData(queryParams),
    enabled: !!queryParams
  });

  const chartOption = useMemo(() => {
    return buildChartOption(config, data);
  }, [config, data]);

  const handleChartClick = (params: any) => {
    onInteraction({
      type: 'chart_click',
      chartId: config.chartId,
      data: params,
      action: 'drill_down'
    });
  };

  if (isLoading) return <ChartSkeleton />;
  if (error) return <ChartError error={error} />;

  return (
    <div className="chart-container">
      <ECharts
        option={chartOption}
        onEvents={{
          click: handleChartClick
        }}
        style={{ height: '400px' }}
      />
    </div>
  );
};
```

#### 3.2.2 趋势图组件
```typescript
const TrendChart: React.FC<TrendChartProps> = ({ data, config }) => {
  const option = useMemo(() => ({
    title: {
      text: config.title,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: config.series.map(s => s.name),
      top: 30
    },
    xAxis: {
      type: 'category',
      data: data.map(d => d.time)
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '平均值',
        position: 'right'
      }
    ],
    series: config.series.map(series => ({
      name: series.name,
      type: series.type || 'line',
      data: data.map(d => d[series.field]),
      yAxisIndex: series.yAxisIndex || 0
    }))
  }), [data, config]);

  return (
    <div className="trend-chart">
      <ECharts option={option} style={{ height: '400px' }} />
    </div>
  );
};
```

#### 3.2.3 分布图组件
```typescript
const DistributionChart: React.FC<DistributionChartProps> = ({ data, config }) => {
  const option = useMemo(() => ({
    title: {
      text: config.title,
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: data.map(d => d.name)
    },
    series: [
      {
        name: config.seriesName,
        type: 'pie',
        radius: '50%',
        data: data.map(d => ({
          name: d.name,
          value: d.value
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }), [data, config]);

  return (
    <div className="distribution-chart">
      <ECharts option={option} style={{ height: '400px' }} />
    </div>
  );
};
```

### 3.3 交互式组件

#### 3.3.1 事件列表组件
```typescript
interface EventListProps {
  events: EventItem[];
  config: EventListConfig;
  onEventClick: (event: EventItem) => void;
}

const EventList: React.FC<EventListProps> = ({ events, config, onEventClick }) => {
  return (
    <div className="event-list">
      <div className="event-list-header">
        <h3>{config.title}</h3>
        <span className="event-count">共 {events.length} 个事件</span>
      </div>
      <div className="event-list-content">
        {events.map(event => (
          <EventCard
            key={event.id}
            event={event}
            onClick={() => onEventClick(event)}
          />
        ))}
      </div>
    </div>
  );
};

const EventCard: React.FC<EventCardProps> = ({ event, onClick }) => {
  return (
    <div className="event-card" onClick={onClick}>
      <div className="event-header">
        <span className="event-type">{event.type}</span>
        <span className="event-time">{formatTime(event.time)}</span>
      </div>
      <div className="event-title">{event.title}</div>
      <div className="event-description">{event.description}</div>
      {event.clickAction && (
        <div className="event-action">
          <Button type="link" size="small">
            查看详情
          </Button>
        </div>
      )}
    </div>
  );
};
```

#### 3.3.2 对话详情组件
```typescript
interface ConversationDetailProps {
  sessionId: string;
  onClose: () => void;
}

const ConversationDetail: React.FC<ConversationDetailProps> = ({ 
  sessionId, 
  onClose 
}) => {
  const { data: conversation, isLoading } = useQuery({
    queryKey: ['conversation', sessionId],
    queryFn: () => fetchConversationDetails(sessionId)
  });

  return (
    <Modal
      title="对话详情"
      open={true}
      onCancel={onClose}
      width={800}
      footer={null}
    >
      <div className="conversation-detail">
        {isLoading ? (
          <Spin size="large" />
        ) : (
          <div className="conversation-messages">
            {conversation?.messages.map(message => (
              <MessageItem key={message.id} message={message} />
            ))}
          </div>
        )}
      </div>
    </Modal>
  );
};

const MessageItem: React.FC<MessageItemProps> = ({ message }) => {
  const isUser = message.role === 'user';
  
  return (
    <div className={`message-item ${isUser ? 'user' : 'assistant'}`}>
      <div className="message-avatar">
        {isUser ? <UserOutlined /> : <RobotOutlined />}
      </div>
      <div className="message-content">
        <div className="message-text">{message.content}</div>
        <div className="message-time">{formatTime(message.timestamp)}</div>
      </div>
    </div>
  );
};
```

---

## 四、状态管理设计

### 4.1 全局状态管理

#### 4.1.1 Zustand Store
```typescript
interface AppState {
  // 报告配置状态
  reportConfig: ReportConfig;
  // 报告生成状态
  reportGeneration: ReportGenerationState;
  // 用户偏好
  userPreferences: UserPreferences;
  // 交互状态
  interactions: InteractionState;
  
  // Actions
  setReportConfig: (config: ReportConfig) => void;
  updateReportGeneration: (state: Partial<ReportGenerationState>) => void;
  setUserPreferences: (preferences: UserPreferences) => void;
  addInteraction: (interaction: InteractionEvent) => void;
}

const useAppStore = create<AppState>((set, get) => ({
  // 初始状态
  reportConfig: {
    enterpriseId: '',
    deviceId: '',
    dateRange: {
      startDate: '',
      endDate: ''
    },
    modules: [],
    llmConfig: {
      modelName: 'gemini-2.5-pro',
      promptVersion: 'UNIFIED_PROMPT_V2'
    }
  },
  
  reportGeneration: {
    status: 'idle',
    progress: 0,
    currentModule: null,
    generatedModules: [],
    error: null
  },
  
  userPreferences: {
    theme: 'light',
    language: 'zh-CN',
    chartTheme: 'default'
  },
  
  interactions: {
    history: [],
    currentInteraction: null
  },
  
  // Actions
  setReportConfig: (config) => set({ reportConfig: config }),
  
  updateReportGeneration: (state) => set((prev) => ({
    reportGeneration: { ...prev.reportGeneration, ...state }
  })),
  
  setUserPreferences: (preferences) => set({ userPreferences: preferences }),
  
  addInteraction: (interaction) => set((prev) => ({
    interactions: {
      ...prev.interactions,
      history: [...prev.interactions.history, interaction],
      currentInteraction: interaction
    }
  }))
}));
```

#### 4.1.2 React Query配置
```typescript
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
    },
  },
});

const App: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <AppRoutes />
      </Router>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
};
```

### 4.2 组件状态管理

#### 4.2.1 自定义Hooks
```typescript
// 报告生成Hook
const useReportGeneration = () => {
  const { reportConfig, updateReportGeneration } = useAppStore();
  
  const generateReport = useMutation({
    mutationFn: (config: ReportConfig) => 
      fetch('/api/v1/reports/stream-generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config)
      }),
    onMutate: () => {
      updateReportGeneration({ status: 'generating', progress: 0 });
    },
    onSuccess: (data) => {
      // 处理SSE流式数据
      handleSSEStream(data);
    },
    onError: (error) => {
      updateReportGeneration({ 
        status: 'error', 
        error: error.message 
      });
    }
  });
  
  return { generateReport };
};

// 图表数据Hook
const useChartData = (queryParams: QueryParams) => {
  return useQuery({
    queryKey: ['chart', queryParams],
    queryFn: () => fetchChartData(queryParams),
    enabled: !!queryParams,
    staleTime: 2 * 60 * 1000, // 2分钟
  });
};

// 交互事件Hook
const useInteraction = () => {
  const { addInteraction } = useAppStore();
  
  const handleChartClick = useCallback((chartId: string, data: any) => {
    addInteraction({
      type: 'chart_click',
      chartId,
      data,
      timestamp: new Date().toISOString()
    });
  }, [addInteraction]);
  
  const handleEventClick = useCallback((event: EventItem) => {
    addInteraction({
      type: 'event_click',
      eventId: event.id,
      data: event,
      timestamp: new Date().toISOString()
    });
  }, [addInteraction]);
  
  return { handleChartClick, handleEventClick };
};
```

---

## 五、API集成设计

### 5.1 API客户端

#### 5.1.1 基础API客户端
```typescript
class ApiClient {
  private baseURL: string;
  private headers: Record<string, string>;
  
  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.headers = {
      'Content-Type': 'application/json',
    };
  }
  
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const config: RequestInit = {
      headers: this.headers,
      ...options,
    };
    
    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }
  
  // 报告相关API
  async createReport(config: ReportConfig): Promise<{ reportId: string }> {
    return this.request('/api/v1/reports/generate', {
      method: 'POST',
      body: JSON.stringify(config),
    });
  }
  
  async getReportStatus(reportId: string): Promise<ReportStatus> {
    return this.request(`/api/v1/reports/${reportId}/status`);
  }
  
  // 统计数据API
  async getStatistics(params: StatisticsParams): Promise<StatisticsData> {
    const queryString = new URLSearchParams(params).toString();
    return this.request(`/api/v1/stats/summary?${queryString}`);
  }
  
  // 对话数据API
  async getConversationDetails(sessionId: string): Promise<ConversationData> {
    return this.request(`/api/v1/queries/conversation-details?session_id=${sessionId}`);
  }
  
  // 交互事件API
  async sendInteraction(interaction: InteractionEvent): Promise<void> {
    return this.request('/api/v1/interactions/chart-click', {
      method: 'POST',
      body: JSON.stringify(interaction),
    });
  }
}

// 创建API客户端实例
export const apiClient = new ApiClient(process.env.REACT_APP_API_BASE_URL || '');
```

#### 5.1.2 SSE流式处理
```typescript
class SSEHandler {
  private eventSource: EventSource | null = null;
  private listeners: Map<string, (data: any) => void> = new Map();
  
  connect(url: string): void {
    this.eventSource = new EventSource(url);
    
    this.eventSource.onopen = () => {
      console.log('SSE连接已建立');
    };
    
    this.eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handleEvent(data);
      } catch (error) {
        console.error('SSE数据解析失败:', error);
      }
    };
    
    this.eventSource.onerror = (error) => {
      console.error('SSE连接错误:', error);
      this.disconnect();
    };
  }
  
  private handleEvent(data: any): void {
    const { event, ...eventData } = data;
    
    const listener = this.listeners.get(event);
    if (listener) {
      listener(eventData);
    }
  }
  
  on(event: string, callback: (data: any) => void): void {
    this.listeners.set(event, callback);
  }
  
  off(event: string): void {
    this.listeners.delete(event);
  }
  
  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }
}

// 使用示例
const useSSEConnection = (reportId: string) => {
  const { updateReportGeneration } = useAppStore();
  const sseHandler = useRef<SSEHandler | null>(null);
  
  useEffect(() => {
    if (reportId) {
      sseHandler.current = new SSEHandler();
      sseHandler.current.connect(`/api/v1/reports/${reportId}/stream`);
      
      sseHandler.current.on('module_start', (data) => {
        updateReportGeneration({
          currentModule: data.module_id,
          progress: data.progress
        });
      });
      
      sseHandler.current.on('module_content', (data) => {
        // 处理模块内容
        updateReportGeneration({
          generatedModules: (prev) => [...prev, data]
        });
      });
      
      sseHandler.current.on('web_query_instruction', (data) => {
        // 处理Web查询指令
        handleWebQueryInstruction(data);
      });
      
      sseHandler.current.on('interactive_content', (data) => {
        // 处理交互式内容
        handleInteractiveContent(data);
      });
      
      sseHandler.current.on('generation_complete', (data) => {
        updateReportGeneration({
          status: 'completed',
          progress: 100
        });
      });
    }
    
    return () => {
      sseHandler.current?.disconnect();
    };
  }, [reportId]);
};
```

---

## 六、用户界面设计

### 6.1 布局设计

#### 6.1.1 主布局组件
```typescript
const Layout: React.FC = () => {
  return (
    <div className="app-layout">
      <Header />
      <div className="main-content">
        <Sidebar />
        <div className="content-area">
          <Outlet />
        </div>
      </div>
      <Footer />
    </div>
  );
};

const Header: React.FC = () => {
  const { userPreferences, setUserPreferences } = useAppStore();
  
  return (
    <header className="app-header">
      <div className="header-left">
        <Logo />
        <Navigation />
      </div>
      <div className="header-right">
        <ThemeToggle 
          theme={userPreferences.theme}
          onChange={(theme) => setUserPreferences({ ...userPreferences, theme })}
        />
        <UserMenu />
      </div>
    </header>
  );
};

const Sidebar: React.FC = () => {
  return (
    <aside className="app-sidebar">
      <div className="sidebar-content">
        <QuickActions />
        <RecentReports />
        <SystemStatus />
      </div>
    </aside>
  );
};
```

#### 6.1.2 响应式设计
```css
/* 基础布局 */
.app-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.content-area {
  flex: 1;
  overflow: auto;
  padding: 20px;
}

/* 响应式断点 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }
  
  .app-sidebar {
    width: 100%;
    height: auto;
  }
  
  .content-area {
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: 10px;
  }
  
  .content-area {
    padding: 5px;
  }
}
```

### 6.2 组件样式

#### 6.2.1 图表组件样式
```css
.chart-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

.chart-skeleton {
  height: 400px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
```

#### 6.2.2 交互组件样式
```css
.event-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.event-list-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.event-list-content {
  max-height: 600px;
  overflow-y: auto;
}

.event-card {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.event-card:hover {
  background-color: #f8f9fa;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.event-type {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.event-type.highlight {
  background-color: #e6f7ff;
  color: #1890ff;
}

.event-type.warning {
  background-color: #fff7e6;
  color: #fa8c16;
}

.event-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.event-description {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}
```

---

## 七、交互功能设计

### 7.1 图表交互

#### 7.1.1 点击事件处理
```typescript
const handleChartClick = useCallback((params: any) => {
  const { dataIndex, seriesName, value, name } = params;
  
  // 根据图表类型处理不同的点击事件
  switch (chartType) {
    case 'pie':
      handlePieChartClick({ name, value, dataIndex });
      break;
    case 'line':
      handleLineChartClick({ seriesName, value, dataIndex });
      break;
    case 'bar':
      handleBarChartClick({ name, value, dataIndex });
      break;
    default:
      console.log('未支持的图表类型:', chartType);
  }
}, [chartType]);

const handlePieChartClick = ({ name, value, dataIndex }: any) => {
  // 显示详细信息弹窗
  setDetailModal({
    visible: true,
    title: `${name} 详情`,
    data: { name, value, dataIndex }
  });
};

const handleLineChartClick = ({ seriesName, value, dataIndex }: any) => {
  // 钻取到时间点详情
  const timePoint = chartData[dataIndex]?.time;
  if (timePoint) {
    navigate(`/reports/drill-down?time=${timePoint}&series=${seriesName}`);
  }
};
```

#### 7.1.2 悬停事件处理
```typescript
const handleChartHover = useCallback((params: any) => {
  const { dataIndex, seriesName, value, name } = params;
  
  // 显示悬停提示
  setTooltip({
    visible: true,
    content: {
      title: name || seriesName,
      value: formatValue(value),
      time: chartData[dataIndex]?.time
    },
    position: {
      x: params.event.offsetX,
      y: params.event.offsetY
    }
  });
}, [chartData]);

const handleChartMouseOut = useCallback(() => {
  setTooltip({ visible: false });
}, []);
```

### 7.2 数据钻取

#### 7.2.1 钻取导航
```typescript
const DrillDownNavigation: React.FC = () => {
  const [breadcrumb, setBreadcrumb] = useState<BreadcrumbItem[]>([]);
  const navigate = useNavigate();
  
  const handleDrillDown = (item: DrillDownItem) => {
    // 添加新的钻取层级
    const newBreadcrumb = [...breadcrumb, {
      id: item.id,
      name: item.name,
      level: breadcrumb.length + 1
    }];
    setBreadcrumb(newBreadcrumb);
    
    // 加载钻取数据
    loadDrillDownData(item);
  };
  
  const handleBreadcrumbClick = (item: BreadcrumbItem) => {
    // 回到指定层级
    const newBreadcrumb = breadcrumb.slice(0, item.level);
    setBreadcrumb(newBreadcrumb);
    
    // 重新加载数据
    loadDataForLevel(item.level);
  };
  
  return (
    <div className="drill-down-navigation">
      <Breadcrumb>
        {breadcrumb.map(item => (
          <Breadcrumb.Item 
            key={item.id}
            onClick={() => handleBreadcrumbClick(item)}
          >
            {item.name}
          </Breadcrumb.Item>
        ))}
      </Breadcrumb>
    </div>
  );
};
```

#### 7.2.2 钻取数据加载
```typescript
const useDrillDownData = (drillDownParams: DrillDownParams) => {
  return useQuery({
    queryKey: ['drill-down', drillDownParams],
    queryFn: () => fetchDrillDownData(drillDownParams),
    enabled: !!drillDownParams,
    staleTime: 5 * 60 * 1000, // 5分钟
  });
};

const fetchDrillDownData = async (params: DrillDownParams) => {
  const response = await apiClient.request('/api/v1/data/drill-down', {
    method: 'POST',
    body: JSON.stringify(params)
  });
  
  return response.data;
};
```

### 7.3 对话查询

#### 7.3.1 对话搜索
```typescript
const ConversationSearch: React.FC = () => {
  const [searchParams, setSearchParams] = useState<SearchParams>({
    keywords: '',
    timeRange: {
      start: '',
      end: ''
    },
    sessionId: ''
  });
  
  const { data: searchResults, isLoading } = useQuery({
    queryKey: ['conversation-search', searchParams],
    queryFn: () => searchConversations(searchParams),
    enabled: !!searchParams.keywords || !!searchParams.sessionId
  });
  
  const handleSearch = (params: SearchParams) => {
    setSearchParams(params);
  };
  
  return (
    <div className="conversation-search">
      <SearchForm onSearch={handleSearch} />
      {isLoading ? (
        <Spin size="large" />
      ) : (
        <SearchResults results={searchResults} />
      )}
    </div>
  );
};
```

#### 7.3.2 对话详情展示
```typescript
const ConversationDetail: React.FC<ConversationDetailProps> = ({ 
  sessionId 
}) => {
  const { data: conversation, isLoading } = useQuery({
    queryKey: ['conversation', sessionId],
    queryFn: () => fetchConversationDetails(sessionId)
  });
  
  const [selectedMessage, setSelectedMessage] = useState<string | null>(null);
  
  const handleMessageClick = (messageId: string) => {
    setSelectedMessage(messageId);
  };
  
  return (
    <div className="conversation-detail">
      <div className="conversation-header">
        <h3>对话详情</h3>
        <span className="session-id">会话ID: {sessionId}</span>
      </div>
      
      {isLoading ? (
        <Spin size="large" />
      ) : (
        <div className="conversation-messages">
          {conversation?.messages.map(message => (
            <MessageItem
              key={message.id}
              message={message}
              isSelected={selectedMessage === message.id}
              onClick={() => handleMessageClick(message.id)}
            />
          ))}
        </div>
      )}
      
      {selectedMessage && (
        <MessageDetail messageId={selectedMessage} />
      )}
    </div>
  );
};
```

---

## 八、性能优化

### 8.1 组件优化

#### 8.1.1 组件懒加载
```typescript
// 路由级别的懒加载
const ReportConfigurePage = lazy(() => import('./pages/ReportConfigurePage'));
const ReportGeneratePage = lazy(() => import('./pages/ReportGeneratePage'));
const ReportViewPage = lazy(() => import('./pages/ReportViewPage'));

// 组件级别的懒加载
const ChartRenderer = lazy(() => import('./components/ChartRenderer'));
const InteractiveRenderer = lazy(() => import('./components/InteractiveRenderer'));

// 使用Suspense包装
const AppRoutes: React.FC = () => {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <Routes>
        <Route path="/reports/configure" element={<ReportConfigurePage />} />
        <Route path="/reports/generate" element={<ReportGeneratePage />} />
        <Route path="/reports/view/:reportId" element={<ReportViewPage />} />
      </Routes>
    </Suspense>
  );
};
```

#### 8.1.2 组件记忆化
```typescript
// 使用React.memo优化渲染性能
const ChartRenderer = React.memo<ChartRendererProps>(({ 
  config, 
  queryParams, 
  onInteraction 
}) => {
  // 组件实现
});

// 使用useMemo优化计算
const chartOption = useMemo(() => {
  return buildChartOption(config, data);
}, [config, data]);

// 使用useCallback优化函数引用
const handleChartClick = useCallback((params: any) => {
  onInteraction({
    type: 'chart_click',
    chartId: config.chartId,
    data: params
  });
}, [onInteraction, config.chartId]);
```

### 8.2 数据优化

#### 8.2.1 虚拟滚动
```typescript
import { FixedSizeList as List } from 'react-window';

const VirtualizedEventList: React.FC<VirtualizedEventListProps> = ({ 
  events 
}) => {
  const Row = ({ index, style }: { index: number; style: CSSProperties }) => (
    <div style={style}>
      <EventCard event={events[index]} />
    </div>
  );
  
  return (
    <List
      height={600}
      itemCount={events.length}
      itemSize={120}
      width="100%"
    >
      {Row}
    </List>
  );
};
```

#### 8.2.2 数据分页
```typescript
const usePaginatedData = (queryKey: string[], fetchFn: Function) => {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  
  const { data, isLoading, error } = useQuery({
    queryKey: [...queryKey, page, pageSize],
    queryFn: () => fetchFn({ page, pageSize }),
    keepPreviousData: true
  });
  
  return {
    data,
    isLoading,
    error,
    page,
    setPage,
    pageSize,
    setPageSize
  };
};
```

### 8.3 缓存优化

#### 8.3.1 浏览器缓存
```typescript
// 使用Service Worker缓存静态资源
const cacheStaticAssets = async () => {
  const cache = await caches.open('static-v1');
  await cache.addAll([
    '/static/js/main.js',
    '/static/css/main.css',
    '/static/media/logo.png'
  ]);
};

// 使用localStorage缓存用户配置
const useLocalStorage = <T>(key: string, initialValue: T) => {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(error);
      return initialValue;
    }
  });
  
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(error);
    }
  };
  
  return [storedValue, setValue] as const;
};
```

---

## 九、错误处理

### 9.1 错误边界

#### 9.1.1 全局错误边界
```typescript
class ErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  
  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    
    // 上报错误信息
    reportError({
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack
    });
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>页面出现错误</h2>
          <p>抱歉，页面加载时出现了问题。请刷新页面重试。</p>
          <Button onClick={() => window.location.reload()}>
            刷新页面
          </Button>
        </div>
      );
    }
    
    return this.props.children;
  }
}
```

#### 9.1.2 组件错误处理
```typescript
const ChartRenderer: React.FC<ChartRendererProps> = ({ 
  config, 
  queryParams, 
  onInteraction 
}) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['chart', queryParams],
    queryFn: () => fetchChartData(queryParams),
    enabled: !!queryParams
  });
  
  if (error) {
    return (
      <div className="chart-error">
        <ExclamationCircleOutlined />
        <p>图表加载失败</p>
        <Button size="small" onClick={() => window.location.reload()}>
          重试
        </Button>
      </div>
    );
  }
  
  if (isLoading) {
    return <ChartSkeleton />;
  }
  
  return (
    <div className="chart-container">
      <ECharts option={buildChartOption(config, data)} />
    </div>
  );
};
```

### 9.2 网络错误处理

#### 9.2.1 网络状态监控
```typescript
const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  return isOnline;
};

const NetworkStatusIndicator: React.FC = () => {
  const isOnline = useNetworkStatus();
  
  if (!isOnline) {
    return (
      <div className="network-status offline">
        <WifiOutlined />
        <span>网络连接已断开</span>
      </div>
    );
  }
  
  return null;
};
```

#### 9.2.2 请求重试
```typescript
const useRetryRequest = (queryFn: Function, retryCount = 3) => {
  const [retryAttempts, setRetryAttempts] = useState(0);
  
  const { data, error, isLoading, refetch } = useQuery({
    queryFn,
    retry: retryCount,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    onError: () => {
      setRetryAttempts(prev => prev + 1);
    }
  });
  
  const handleRetry = () => {
    setRetryAttempts(0);
    refetch();
  };
  
  return {
    data,
    error,
    isLoading,
    retryAttempts,
    handleRetry
  };
};
```

---

## 十、测试策略

### 10.1 单元测试

#### 10.1.1 组件测试
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('ChartRenderer', () => {
  it('should render chart correctly', () => {
    const mockConfig = {
      chartId: 'test-chart',
      type: 'line',
      title: '测试图表'
    };
    
    const mockData = [
      { time: '2025-01-01', value: 100 },
      { time: '2025-01-02', value: 200 }
    ];
    
    render(
      <TestWrapper>
        <ChartRenderer 
          config={mockConfig}
          queryParams={{ dataType: 'test' }}
          onInteraction={jest.fn()}
        />
      </TestWrapper>
    );
    
    expect(screen.getByText('测试图表')).toBeInTheDocument();
  });
  
  it('should handle chart click events', () => {
    const mockOnInteraction = jest.fn();
    
    render(
      <TestWrapper>
        <ChartRenderer 
          config={mockConfig}
          queryParams={{ dataType: 'test' }}
          onInteraction={mockOnInteraction}
        />
      </TestWrapper>
    );
    
    // 模拟图表点击事件
    fireEvent.click(screen.getByRole('img'));
    
    expect(mockOnInteraction).toHaveBeenCalledWith({
      type: 'chart_click',
      chartId: 'test-chart',
      data: expect.any(Object)
    });
  });
});
```

#### 10.1.2 Hook测试
```typescript
import { renderHook, act } from '@testing-library/react-hooks';
import { useReportGeneration } from '../hooks/useReportGeneration';

describe('useReportGeneration', () => {
  it('should generate report successfully', async () => {
    const { result } = renderHook(() => useReportGeneration());
    
    const mockConfig = {
      enterpriseId: 'test',
      deviceId: 'test',
      modules: []
    };
    
    await act(async () => {
      await result.current.generateReport.mutateAsync(mockConfig);
    });
    
    expect(result.current.generateReport.isSuccess).toBe(true);
  });
  
  it('should handle generation errors', async () => {
    const { result } = renderHook(() => useReportGeneration());
    
    // 模拟API错误
    jest.spyOn(global, 'fetch').mockRejectedValueOnce(new Error('API Error'));
    
    await act(async () => {
      await result.current.generateReport.mutateAsync({});
    });
    
    expect(result.current.generateReport.isError).toBe(true);
    expect(result.current.generateReport.error?.message).toBe('API Error');
  });
});
```

### 10.2 集成测试

#### 10.2.1 页面集成测试
```typescript
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      <TestWrapper>
        {component}
      </TestWrapper>
    </BrowserRouter>
  );
};

describe('ReportConfigurePage', () => {
  it('should configure report successfully', async () => {
    const user = userEvent.setup();
    
    renderWithRouter(<ReportConfigurePage />);
    
    // 填写表单
    await user.type(screen.getByLabelText('企业ID'), 'test-enterprise');
    await user.type(screen.getByLabelText('设备ID'), 'test-device');
    await user.click(screen.getByText('选择日期'));
    await user.click(screen.getByText('今天'));
    
    // 选择模块
    await user.click(screen.getByText('使用情况概览'));
    await user.click(screen.getByText('消息行为趋势'));
    
    // 提交表单
    await user.click(screen.getByText('生成报告'));
    
    await waitFor(() => {
      expect(screen.getByText('报告生成中...')).toBeInTheDocument();
    });
  });
});
```

#### 10.2.2 API集成测试
```typescript
import { rest } from 'msw';
import { setupServer } from 'msw/node';

const server = setupServer(
  rest.post('/api/v1/reports/generate', (req, res, ctx) => {
    return res(
      ctx.json({
        reportId: 'test-report-id',
        status: 'generating'
      })
    );
  }),
  
  rest.get('/api/v1/stats/summary', (req, res, ctx) => {
    return res(
      ctx.json({
        data: {
          session_behavior: {
            total_sessions: 100,
            avg_duration: 300
          }
        }
      })
    );
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

describe('API Integration', () => {
  it('should fetch statistics data', async () => {
    const { result } = renderHook(() => 
      useQuery({
        queryKey: ['stats'],
        queryFn: () => fetch('/api/v1/stats/summary').then(res => res.json())
      })
    );
    
    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });
    
    expect(result.current.data.data.session_behavior.total_sessions).toBe(100);
  });
});
```

### 10.3 E2E测试

#### 10.3.1 完整流程测试
```typescript
import { test, expect } from '@playwright/test';

test('complete report generation flow', async ({ page }) => {
  // 访问配置页面
  await page.goto('/reports/configure');
  
  // 填写配置信息
  await page.fill('[data-testid="enterprise-id"]', 'test-enterprise');
  await page.fill('[data-testid="device-id"]', 'test-device');
  await page.click('[data-testid="date-picker"]');
  await page.click('[data-testid="today"]');
  
  // 选择模块
  await page.click('[data-testid="module-usage-overview"]');
  await page.click('[data-testid="module-message-trend"]');
  
  // 生成报告
  await page.click('[data-testid="generate-report"]');
  
  // 等待生成完成
  await page.waitForSelector('[data-testid="report-complete"]');
  
  // 验证结果
  await expect(page.locator('[data-testid="report-content"]')).toBeVisible();
  await expect(page.locator('[data-testid="chart-container"]')).toHaveCount(2);
});
```

---

## 十一、部署和运维

### 11.1 构建配置

#### 11.1.1 Vite配置
```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@pages': resolve(__dirname, 'src/pages'),
      '@hooks': resolve(__dirname, 'src/hooks'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@types': resolve(__dirname, 'src/types')
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          charts: ['echarts', 'react-echarts'],
          ui: ['antd', '@ant-design/icons']
        }
      }
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  }
});
```

#### 11.1.2 环境配置
```typescript
// .env.development
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_TITLE=设备使用报告系统(开发环境)
VITE_ENABLE_DEVTOOLS=true

// .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_APP_TITLE=设备使用报告系统
VITE_ENABLE_DEVTOOLS=false
```

### 11.2 Docker部署

#### 11.2.1 Dockerfile
```dockerfile
# 多阶段构建
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
```

#### 11.2.2 Nginx配置
```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # API代理
        location /api/ {
            proxy_pass http://backend:8000/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 前端路由
        location / {
            try_files $uri $uri/ /index.html;
        }
    }
}
```

### 11.3 监控和日志

#### 11.3.1 性能监控
```typescript
// 性能监控工具
class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();
  
  // 监控页面加载性能
  monitorPageLoad() {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      this.recordMetric('page_load_time', navigation.loadEventEnd - navigation.loadEventStart);
      this.recordMetric('dom_content_loaded', navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart);
      this.recordMetric('first_paint', performance.getEntriesByName('first-paint')[0]?.startTime || 0);
      this.recordMetric('first_contentful_paint', performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0);
    });
  }
  
  // 监控API请求性能
  monitorApiRequests() {
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const startTime = performance.now();
      try {
        const response = await originalFetch(...args);
        const endTime = performance.now();
        this.recordMetric('api_request_time', endTime - startTime);
        return response;
      } catch (error) {
        const endTime = performance.now();
        this.recordMetric('api_request_error_time', endTime - startTime);
        throw error;
      }
    };
  }
  
  // 记录指标
  private recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(value);
    
    // 上报到监控系统
    this.reportMetric(name, value);
  }
  
  // 上报指标
  private reportMetric(name: string, value: number) {
    fetch('/api/v1/metrics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name,
        value,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href
      })
    }).catch(console.error);
  }
}

// 初始化监控
const performanceMonitor = new PerformanceMonitor();
performanceMonitor.monitorPageLoad();
performanceMonitor.monitorApiRequests();
```

#### 11.3.2 错误监控
```typescript
// 错误监控工具
class ErrorMonitor {
  constructor() {
    this.setupGlobalErrorHandlers();
  }
  
  private setupGlobalErrorHandlers() {
    // 捕获JavaScript错误
    window.addEventListener('error', (event) => {
      this.reportError({
        type: 'javascript_error',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error?.stack
      });
    });
    
    // 捕获Promise错误
    window.addEventListener('unhandledrejection', (event) => {
      this.reportError({
        type: 'promise_error',
        message: event.reason?.message || 'Promise rejected',
        error: event.reason?.stack
      });
    });
    
    // 捕获React错误
    if (process.env.NODE_ENV === 'production') {
      const originalConsoleError = console.error;
      console.error = (...args) => {
        this.reportError({
          type: 'react_error',
          message: args.join(' '),
          stack: new Error().stack
        });
        originalConsoleError.apply(console, args);
      };
    }
  }
  
  private reportError(error: ErrorReport) {
    fetch('/api/v1/errors', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...error,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: this.getUserId()
      })
    }).catch(console.error);
  }
  
  private getUserId(): string {
    // 从localStorage或cookie获取用户ID
    return localStorage.getItem('userId') || 'anonymous';
  }
}

// 初始化错误监控
const errorMonitor = new ErrorMonitor();
```

### 11.4 自动化部署

#### 11.4.1 GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Build application
        run: npm run build
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-files
          path: dist/

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-files
          path: dist/
      
      - name: Deploy to server
        run: |
          # 部署脚本
          echo "Deploying to production..."
          # 这里可以添加具体的部署命令
```

---

## 十二、安全设计

### 12.1 前端安全

#### 12.1.1 XSS防护
```typescript
// XSS防护工具
class XSSProtection {
  // 清理用户输入
  static sanitizeInput(input: string): string {
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }
  
  // 安全的HTML渲染
  static safeHtml(html: string): string {
    const div = document.createElement('div');
    div.textContent = html;
    return div.innerHTML;
  }
  
  // 验证URL
  static validateUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return ['http:', 'https:'].includes(urlObj.protocol);
    } catch {
      return false;
    }
  }
}

// 在组件中使用
const SafeInput: React.FC<SafeInputProps> = ({ value, onChange }) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const sanitizedValue = XSSProtection.sanitizeInput(e.target.value);
    onChange(sanitizedValue);
  };
  
  return (
    <input
      value={value}
      onChange={handleChange}
      className="safe-input"
    />
  );
};
```

#### 12.1.2 CSRF防护
```typescript
// CSRF Token管理
class CSRFProtection {
  private static token: string | null = null;
  
  // 获取CSRF Token
  static async getToken(): Promise<string> {
    if (!this.token) {
      const response = await fetch('/api/v1/csrf-token');
      const data = await response.json();
      this.token = data.token;
    }
    return this.token;
  }
  
  // 添加CSRF Token到请求头
  static async addTokenToHeaders(headers: Headers): Promise<void> {
    const token = await this.getToken();
    headers.set('X-CSRF-Token', token);
  }
}

// 安全的API客户端
class SecureApiClient extends ApiClient {
  async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const headers = new Headers(options.headers);
    
    // 添加CSRF Token
    if (options.method && options.method !== 'GET') {
      await CSRFProtection.addTokenToHeaders(headers);
    }
    
    const secureOptions = {
      ...options,
      headers
    };
    
    return super.request(endpoint, secureOptions);
  }
}
```

### 12.2 数据安全

#### 12.2.1 敏感数据保护
```typescript
// 敏感数据脱敏
class DataMasking {
  // 脱敏企业ID
  static maskEnterpriseId(enterpriseId: string): string {
    if (enterpriseId.length <= 4) return enterpriseId;
    return enterpriseId.substring(0, 2) + '*'.repeat(enterpriseId.length - 4) + enterpriseId.substring(enterpriseId.length - 2);
  }
  
  // 脱敏设备ID
  static maskDeviceId(deviceId: string): string {
    if (deviceId.length <= 6) return deviceId;
    return deviceId.substring(0, 3) + '*'.repeat(deviceId.length - 6) + deviceId.substring(deviceId.length - 3);
  }
  
  // 脱敏会话ID
  static maskSessionId(sessionId: string): string {
    if (sessionId.length <= 8) return sessionId;
    return sessionId.substring(0, 4) + '*'.repeat(sessionId.length - 8) + sessionId.substring(sessionId.length - 4);
  }
}

// 在组件中使用脱敏
const MaskedData: React.FC<MaskedDataProps> = ({ data, type }) => {
  const getMaskedValue = () => {
    switch (type) {
      case 'enterprise_id':
        return DataMasking.maskEnterpriseId(data);
      case 'device_id':
        return DataMasking.maskDeviceId(data);
      case 'session_id':
        return DataMasking.maskSessionId(data);
      default:
        return data;
    }
  };
  
  return (
    <span className="masked-data">
      {getMaskedValue()}
    </span>
  );
};
```

#### 12.2.2 本地存储安全
```typescript
// 安全的本地存储
class SecureStorage {
  private static readonly ENCRYPTION_KEY = 'your-encryption-key';
  
  // 加密数据
  private static encrypt(data: string): string {
    // 这里应该使用更安全的加密算法
    return btoa(data);
  }
  
  // 解密数据
  private static decrypt(encryptedData: string): string {
    return atob(encryptedData);
  }
  
  // 安全存储
  static setItem(key: string, value: any): void {
    try {
      const encryptedValue = this.encrypt(JSON.stringify(value));
      localStorage.setItem(key, encryptedValue);
    } catch (error) {
      console.error('Failed to store data securely:', error);
    }
  }
  
  // 安全获取
  static getItem<T>(key: string): T | null {
    try {
      const encryptedValue = localStorage.getItem(key);
      if (!encryptedValue) return null;
      
      const decryptedValue = this.decrypt(encryptedValue);
      return JSON.parse(decryptedValue);
    } catch (error) {
      console.error('Failed to retrieve data securely:', error);
      return null;
    }
  }
  
  // 安全删除
  static removeItem(key: string): void {
    localStorage.removeItem(key);
  }
  
  // 清除所有数据
  static clear(): void {
    localStorage.clear();
  }
}

// 使用安全存储
const useSecureStorage = <T>(key: string, initialValue: T) => {
  const [storedValue, setStoredValue] = useState<T>(() => {
    return SecureStorage.getItem<T>(key) || initialValue;
  });
  
  const setValue = (value: T | ((val: T) => T)) => {
    const valueToStore = value instanceof Function ? value(storedValue) : value;
    setStoredValue(valueToStore);
    SecureStorage.setItem(key, valueToStore);
  };
  
  return [storedValue, setValue] as const;
};
```

---

## 十三、国际化支持

### 13.1 多语言配置

#### 13.1.1 语言包配置
```typescript
// locales/zh-CN.ts
export default {
  common: {
    loading: '加载中...',
    error: '错误',
    success: '成功',
    cancel: '取消',
    confirm: '确认',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    view: '查看'
  },
  report: {
    title: '设备使用报告',
    generate: '生成报告',
    configure: '配置报告',
    view: '查看报告',
    status: {
      generating: '生成中',
      completed: '已完成',
      failed: '失败'
    }
  },
  chart: {
    title: '图表',
    loading: '图表加载中...',
    error: '图表加载失败',
    noData: '暂无数据'
  }
};

// locales/en-US.ts
export default {
  common: {
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    cancel: 'Cancel',
    confirm: 'Confirm',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    view: 'View'
  },
  report: {
    title: 'Device Usage Report',
    generate: 'Generate Report',
    configure: 'Configure Report',
    view: 'View Report',
    status: {
      generating: 'Generating',
      completed: 'Completed',
      failed: 'Failed'
    }
  },
  chart: {
    title: 'Chart',
    loading: 'Loading chart...',
    error: 'Failed to load chart',
    noData: 'No data available'
  }
};
```

#### 13.1.2 国际化Hook
```typescript
import { createIntl, IntlShape } from '@formatjs/intl';

class I18nManager {
  private intl: IntlShape;
  private locale: string;
  
  constructor(locale: string = 'zh-CN') {
    this.locale = locale;
    this.intl = createIntl({
      locale,
      messages: this.loadMessages(locale)
    });
  }
  
  private loadMessages(locale: string) {
    switch (locale) {
      case 'zh-CN':
        return require('./locales/zh-CN').default;
      case 'en-US':
        return require('./locales/en-US').default;
      default:
        return require('./locales/zh-CN').default;
    }
  }
  
  t(key: string, values?: Record<string, any>): string {
    return this.intl.formatMessage({ id: key }, values);
  }
  
  setLocale(locale: string): void {
    this.locale = locale;
    this.intl = createIntl({
      locale,
      messages: this.loadMessages(locale)
    });
  }
  
  getLocale(): string {
    return this.locale;
  }
}

// 创建全局实例
const i18n = new I18nManager();

// 国际化Hook
const useI18n = () => {
  const [locale, setLocale] = useState(i18n.getLocale());
  
  const t = useCallback((key: string, values?: Record<string, any>) => {
    return i18n.t(key, values);
  }, []);
  
  const changeLocale = useCallback((newLocale: string) => {
    i18n.setLocale(newLocale);
    setLocale(newLocale);
  }, []);
  
  return { t, locale, changeLocale };
};
```

### 13.2 组件国际化

#### 13.2.1 国际化组件
```typescript
// 使用国际化的组件示例
const ReportConfigurePage: React.FC = () => {
  const { t, locale, changeLocale } = useI18n();
  
  return (
    <div className="report-configure-page">
      <div className="page-header">
        <h1>{t('report.title')}</h1>
        <div className="locale-selector">
          <Select value={locale} onChange={changeLocale}>
            <Select.Option value="zh-CN">中文</Select.Option>
            <Select.Option value="en-US">English</Select.Option>
          </Select>
        </div>
      </div>
      
      <Form layout="vertical">
        <Form.Item label={t('report.enterpriseId')}>
          <Input placeholder={t('report.enterpriseIdPlaceholder')} />
        </Form.Item>
        
        <Form.Item label={t('report.deviceId')}>
          <Input placeholder={t('report.deviceIdPlaceholder')} />
        </Form.Item>
        
        <Form.Item>
          <Button type="primary">
            {t('report.generate')}
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};
```

---

## 十四、总结

### 14.1 技术亮点

1. **混合渲染架构**：支持Markdown和Web组件的混合渲染，提供灵活的展示方式
2. **实时交互**：基于SSE的实时报告生成和交互式数据探索
3. **模块化设计**：组件化架构，支持按需加载和复用
4. **性能优化**：虚拟滚动、懒加载、缓存策略等多重优化
5. **安全防护**：XSS、CSRF防护，敏感数据脱敏
6. **国际化支持**：完整的多语言支持体系
7. **监控体系**：性能监控、错误监控、用户行为分析

### 14.2 扩展性

1. **新图表类型**：可轻松添加新的图表组件
2. **新交互方式**：支持扩展新的交互模式
3. **新数据源**：可集成新的数据源和API
4. **新渲染引擎**：支持添加新的渲染引擎
5. **新语言支持**：可快速添加新的语言包

### 14.3 维护性

1. **类型安全**：完整的TypeScript类型定义
2. **测试覆盖**：单元测试、集成测试、E2E测试
3. **文档完善**：详细的组件文档和API文档
4. **代码规范**：ESLint、Prettier代码规范
5. **版本管理**：语义化版本控制和变更日志

### 14.4 部署运维

1. **容器化部署**：Docker + Nginx部署方案
2. **自动化CI/CD**：GitHub Actions自动化部署
3. **监控告警**：完整的监控和告警体系
4. **日志管理**：结构化日志和错误追踪
5. **性能优化**：构建优化和运行时优化

这个Web端功能PRD提供了完整的前端架构设计，涵盖了从组件设计到部署运维的各个方面，为工程师提供了详细的开发指导。