
# 机器人设备报告生成与对话系统 - 产品需求文档 (PRD)

## 1. 项目背景与目标

本系统旨在为机器人设备生成每日报告，报告内容将通过大语言模型（LLM）进行生成，并实时推送给用户。LLM会根据阶段性 **prompt** 生成内容，并结合 **SSE（Server-Sent Events）** 协议，实时推送报告生成的每个阶段到前端展示。

## 2. 目标功能

1. **报告生成**：根据设备使用情况，分阶段生成报告内容。
2. **实时推送**：通过 **SSE** 协议将报告内容逐步推送到前端。
3. **LLM 记忆**：记录和回溯每次对话及生成内容的历史，以便后续对话中调用。

## 3. 任务阶段及每阶段的 Prompt

为了生成完整的设备报告，我们将报告分为 **9个阶段**，每个阶段使用不同的 **prompt** 来调用 LLM 生成相应的内容。具体的 **prompt** 已在文档中列出。

### 阶段 1：基础使用数据概览 (Basic Usage Overview)

**英文 Prompt**:
```text
"Generate a basic usage overview for the robot device. Provide information on device activity, usage statistics, and any anomalies. Include the total usage time, number of active users, and system performance."
```

**中文 Prompt**:
```text
"生成机器人设备的基本使用概览。提供设备活动、使用统计和任何异常信息。包括总使用时间、活跃用户数和系统性能等信息。"
```

### 阶段 2：数据趋势分析 (Data Trend Analysis)

**英文 Prompt**:
```text
"Analyze the usage trends of the robot device over the past week. Identify any significant patterns in activity and usage, such as peak usage times and average interaction duration. Provide insights into the data collected during this period."
```

**中文 Prompt**:
```text
"分析机器人设备在过去一周的使用趋势。识别活动和使用中的重要模式，如高峰使用时间和平均交互时长。提供这一期间收集到的数据洞察。"
```

### 阶段 3：今日亮点事件 (Today's Highlight Events)

**英文 Prompt**:
```text
"Summarize today's highlight events related to the robot device. Include key performance highlights, such as successful tasks performed, any detected issues, and user feedback. Emphasize the most notable events or issues that occurred today."
```

**中文 Prompt**:
```text
"总结今天与机器人设备相关的亮点事件。包括关键性能亮点，如完成的任务、检测到的问题以及用户反馈。强调今天发生的最重要的事件或问题。"
```

### 阶段 4：预警事件 (Warning Events)

**英文 Prompt**:
```text
"Provide details on any warning events for the robot device today. Include critical information such as battery status, system errors, or other issues that need immediate attention. If there were any failures or system malfunctions, describe them in detail."
```

**中文 Prompt**:
```text
"提供今天机器人设备的任何预警事件的详细信息。包括重要信息，如电池状态、系统错误或其他需要立即处理的问题。如果有任何故障或系统故障，请详细描述。"
```

### 阶段 5：满意服务案例 (Satisfied Service Cases)

**英文 Prompt**:
```text
"List the most satisfied service cases reported by users for the robot device today. Describe the tasks performed and the user feedback. Include any specific comments on system performance, reliability, or improvements."
```

**中文 Prompt**:
```text
"列出今天用户反馈的最满意服务案例。描述完成的任务和用户反馈。包括对系统性能、可靠性或改进的具体评论。"
```

### 阶段 6：服务改进案例 (Service Improvement Cases)

**英文 Prompt**:
```text
"List the service improvement cases for the robot device today. Highlight areas where users reported dissatisfaction or where the system failed to meet expectations. Provide suggestions for improvements based on user feedback."
```

**中文 Prompt**:
```text
"列出今天机器人设备的服务改进案例。突出用户反馈不满意的地方或系统未能达到预期的地方。根据用户反馈提供改进建议。"
```

### 阶段 7：新视角观察 (New Insights)

**英文 Prompt**:
```text
"Offer new insights or observations based on today's usage data and user feedback. Suggest areas for future development or enhancement. Identify patterns or behaviors that could help improve the robot's performance."
```

**中文 Prompt**:
```text
"根据今天的使用数据和用户反馈提供新的见解或观察。建议未来发展的方向或提升点。识别可以帮助提高机器人性能的模式或行为。"
```

### 阶段 8：经营改进建议 (Operational Improvement Suggestions)

**英文 Prompt**:
```text
"Provide recommendations for improving the operational efficiency of the robot device. Based on the collected data, suggest actions to optimize performance, reduce failures, or enhance user experience."
```

**中文 Prompt**:
```text
"提供改进机器人设备运营效率的建议。根据收集到的数据，建议优化性能、减少故障或提升用户体验的行动。"
```

### 阶段 9：明日重点工作 (Key Tasks for Tomorrow)

**英文 Prompt**:
```text
"Outline the key tasks and goals for the robot device for tomorrow. Include any important actions that need to be taken to address identified issues, improve performance, or enhance service offerings."
```

**中文 Prompt**:
```text
"概述明天机器人设备的重点工作和目标。包括需要采取的重要措施，以解决已识别的问题、提升性能或增强服务。"
```

## 4. SSE 推送机制

### 4.1 SSE 协议概述

**SSE (Server-Sent Events)** 是一种从服务器向客户端推送数据的单向通信协议。与传统的 HTTP 请求-响应模式不同，SSE 在连接建立后，服务器可以持续向客户端推送数据，直到连接断开。SSE 可以很好地用于报告生成系统的场景，因为我们需要逐步生成报告并实时传输内容。

### 4.2 推送机制设计

为了保证报告内容按步骤逐步生成并顺利推送到前端，我们需要设计一个 **SSE 推送机制**，该机制需要满足以下几个要求：

1. 逐步推送报告内容：每个报告的生成阶段内容将在完成时推送给前端，保证阶段性数据的推送。
2. 不同类型的数据：不同的数据类型（例如报告内容、进度更新、错误信息等）需要通过不同的机制进行推送。
3. 数据顺序性：确保数据推送的顺序与报告生成的顺序一致，保证用户端能够按顺序接收到数据。
4. 错误与重试机制：处理生成过程中可能发生的错误，提供合理的重试机制，避免连接中断或数据丢失。
5. 推送完成通知：每次生成完一个阶段的报告，服务端将发送完成通知，告知前端该阶段报告已生成并完成。

### 4.3 推送机制流程

1. **建立连接**：
   - 前端通过 **EventSource** 与服务端建立连接。
   - 一旦连接建立，前端开始接收服务端发送的每个阶段内容。

2. **阶段性报告推送**：
   - 服务端会根据不同的 **prompt** 调用 **LLM** 生成报告的各个部分。
   - 每生成一个阶段的内容，服务端通过 **SSE** 协议将生成的报告内容发送到前端。
   - 每次推送都包含以下数据：报告类型、生成的阶段、内容、时间戳等。

3. **错误与进度更新推送**：
   - 如果在生成过程中发生错误，服务端会使用 **错误消息格式** 推送错误信息到前端，告知用户报告生成失败的具体原因。
   - 在报告生成过程中，服务端可以周期性地推送 **进度更新**，通知前端当前报告生成的进度（如完成度、剩余时间等）。

4. **报告完成通知**：
   - 一旦报告的所有阶段都完成，服务端会发送 **完成通知** 到前端，告知报告已生成完毕，用户可以查看完整报告。

## 5. 数据类型与格式

| 数据类型               | 示例字段                                      | 描述                                           |
|------------------------|-----------------------------------------------|------------------------------------------------|
| **Report Content**      | `type`, `stage`, `content`, `timestamp`        | 传输生成的报告内容                           |
| **Progress Update**     | `type`, `progress`, `stage`, `message`, `timestamp` | 传输报告生成进度                             |
| **Error Message**       | `type`, `error_code`, `error_message`, `timestamp` | 错误信息及详细描述                           |
| **Completion Notification** | `type`, `status`, `message`, `timestamp`   | 通知报告生成完成或失败                       |

## 6. 系统架构与功能流程

### 6.1 系统架构

- **前端**：通过 **SSE** 与服务端连接，逐步接收报告内容并实时展示给用户。
- **后端**：使用 **Flask** 实现，处理请求、调用 **LLM** 生成报告并通过 **SSE** 协议实时推送数据。
- **数据库**（可选）：可扩展为将生成的报告、对话记录及设备记忆存储到数据库，以便后续查询和智能对话。

### 6.2 功能流程

1. **用户请求**：用户访问报告生成页面，前端发起请求。
2. **后端生成报告**：后端依次调用LLM，根据各阶段的 **prompt** 生成报告内容。
3. **SSE推送**：每生成一个阶段的内容后，后端通过 **SSE** 协议将内容推送到前端。
4. **前端展示**：前端实时显示每个阶段生成的报告内容。
5. **记忆功能**：系统会记录每次与LLM的对话，包括 **prompt** 和 **response**，以及设备的上下文记忆。

## 7. 总结

本 **PRD** 文档详细定义了 **机器人设备报告生成系统** 的核心功能，包括各个阶段的 **prompt**、系统架构设计、功能流程、前后端交互、以及 **SSE 数据格式规范**。开发团队可以依据该文档全面了解需求，并高效完成系统的开发和集成。
