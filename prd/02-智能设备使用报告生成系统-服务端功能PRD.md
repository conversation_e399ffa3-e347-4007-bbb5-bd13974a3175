# 📄 智能设备使用报告生成系统 - 服务端功能PRD
**版本**：V2.0  
**编写人**：产品经理  
**发布日期**：2025-07-29 
**文档类型**：服务端功能设计  

---

## 一、服务端功能概述

### 1.1 核心功能
服务端作为整个系统的核心，负责：
- **数据源管理**：统一管理统计数据、Query数据、人脸识别数据
- **LLM集成**：集成多种大语言模型，提供智能分析能力
- **报告生成**：支持三种展示逻辑的报告生成
- **API服务**：提供RESTful API和SSE流式接口
- **任务调度**：管理异步任务和分步执行

### 1.2 技术架构
- **框架**：FastAPI + SQLAlchemy + Celery
- **数据库**：MySQL + Redis
- **LLM集成**：OpenAI API + Google Gemini API
- **缓存**：Redis缓存
- **监控**：Prometheus + Grafana

---

## 二、数据源管理服务

### 2.1 统计数据源服务

#### 2.1.1 服务接口
```python
class StatisticsService:
    """统计数据源服务"""
    
    async def get_summary(
        self,
        enterprise_id: str,
        device_id: str,
        start_time: datetime,
        end_time: datetime,
        data_types: List[str]
    ) -> Dict[str, Any]:
        """获取统计数据汇总"""
        pass
    
    async def get_message_behavior(
        self,
        enterprise_id: str,
        device_id: str,
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """获取消息行为统计"""
        pass
    
    async def get_session_behavior(
        self,
        enterprise_id: str,
        device_id: str,
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """获取会话行为统计"""
        pass
```

#### 2.1.2 数据表映射
| 数据表 | 服务方法 | 主要指标 |
|--------|----------|----------|
| `aos_stat_session_behavior_hourly` | `get_session_behavior` | 会话总数、有效会话数、平均对话轮数 |
| `aos_stat_message_behavior_hourly` | `get_message_behavior` | 用户消息数、机器人消息数、Action触发数 |
| `aos_stat_action_behavior_hourly` | `get_action_behavior` | Action类型频次、功能使用分布 |
| `aos_stat_user_question_summary_hourly` | `get_user_questions` | 高频问题、关键词词云 |

### 2.2 Query数据源服务

#### 2.2.1 服务接口
```python
class QueryDataService:
    """Query数据源服务"""
    
    async def get_conversations(
        self,
        enterprise_id: str,
        device_id: str,
        start_time: datetime,
        end_time: datetime
    ) -> List[Dict[str, Any]]:
        """获取对话数据"""
        pass
    
    async def get_conversation_details(
        self,
        session_id: str
    ) -> Dict[str, Any]:
        """获取对话详情"""
        pass
    
    async def search_conversations(
        self,
        enterprise_id: str,
        device_id: str,
        keywords: List[str],
        start_time: datetime,
        end_time: datetime
    ) -> List[Dict[str, Any]]:
        """搜索对话内容"""
        pass
```

#### 2.2.2 数据文件管理
- **文件路径**：`app/data/{enterprise_id}/{device_id}/`
- **文件格式**：`*_chats数据_YYYY-MM-DD.txt`
- **编码格式**：UTF-8
- **文件大小限制**：< 100MB

### 2.3 人脸识别数据源服务

#### 2.3.1 服务接口
```python
class FaceRecognitionService:
    """人脸识别数据源服务"""
    
    async def get_visitor_profile(
        self,
        enterprise_id: str,
        device_id: str,
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """获取访客画像"""
        pass
    
    async def get_recognition_performance(
        self,
        enterprise_id: str,
        device_id: str,
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """获取识别性能统计"""
        pass
```

---

## 三、LLM集成服务

### 3.1 LLM服务接口

#### 3.1.1 基础LLM服务
```python
class LLMService:
    """LLM集成服务"""
    
    def __init__(self, model_name: str = "gemini-2.5-pro"):
        self.model_name = model_name
        self.client = self._init_client()
    
    async def analyze_data(
        self,
        data: Dict[str, Any],
        analysis_type: str,
        prompt_template: str
    ) -> Dict[str, Any]:
        """分析数据"""
        pass
    
    async def generate_chart_config(
        self,
        data: Dict[str, Any],
        chart_type: str
    ) -> Dict[str, Any]:
        """生成图表配置"""
        pass
    
    async def generate_interactive_content(
        self,
        query_data: List[Dict[str, Any]],
        content_type: str
    ) -> Dict[str, Any]:
        """生成交互式内容"""
        pass
```

#### 3.1.2 支持的模型
| 模型名称 | 提供商 | 用途 | 配置 |
|----------|--------|------|------|
| `gemini-2.5-pro` | Google | 主要分析模型 | 默认模型 |
| `gpt-4` | OpenAI | 备用分析模型 | 备用模型 |
| `gpt-3.5-turbo` | OpenAI | 快速分析模型 | 快速响应 |

### 3.2 提示词管理

#### 3.2.1 提示词模板
```python
class PromptTemplates:
    """提示词模板管理"""
    
    @staticmethod
    def get_basic_stats_prompt(data: Dict[str, Any]) -> str:
        """基础统计提示词"""
        return f"""
        请分析以下设备使用统计数据，生成一份简洁的概览报告：
        
        数据内容：
        {json.dumps(data, ensure_ascii=False, indent=2)}
        
        请从以下角度进行分析：
        1. 核心指标概览
        2. 使用趋势分析
        3. 关键发现
        4. 简要建议
        
        输出格式：Markdown格式
        """
    
    @staticmethod
    def get_highlight_events_prompt(query_data: List[Dict[str, Any]]) -> str:
        """亮点事件提示词"""
        return f"""
        请分析以下对话数据，识别当日的亮点事件和预警事件：
        
        对话数据：
        {json.dumps(query_data, ensure_ascii=False, indent=2)}
        
        请识别：
        1. 亮点事件（3-5个）
        2. 预警事件（2-3个）
        3. 满意服务案例（2-3个）
        4. 不满意案例（1-2个）
        5. 新视角观察（用户行为、时间模式等）
        6. 经营建议（具体可执行的建议）
        
        输出格式：结构化的交互式内容
        """
```

---

## 四、报告生成服务

### 4.1 智能报告服务

#### 4.1.1 核心服务类
```python
class SmartReportService:
    """智能报告生成服务"""
    
    def __init__(self):
        self.llm_service = LLMService()
        self.stats_service = StatisticsService()
        self.query_service = QueryDataService()
        self.face_service = FaceRecognitionService()
    
    async def generate_report(
        self,
        request: ReportRequest
    ) -> AsyncGenerator[ReportEvent, None]:
        """生成智能报告"""
        for module in request.modules:
            if module.display_logic == "server_query_llm_text":
                yield await self._generate_text_module(module, request)
            elif module.display_logic == "server_query_llm_web":
                yield await self._generate_web_module(module, request)
            elif module.display_logic == "query_llm_interactive":
                yield await self._generate_interactive_module(module, request)
```

#### 4.1.2 模块生成方法

**文本模块生成**
```python
async def _generate_text_module(
    self,
    module: ModuleConfig,
    request: ReportRequest
) -> ReportEvent:
    """生成文本模块"""
    # 查询统计数据
    stats_data = await self.stats_service.get_summary(
        enterprise_id=request.enterprise_id,
        device_id=request.device_id,
        start_time=request.date_range.start_date,
        end_time=request.date_range.end_date,
        data_types=module.data_sources
    )
    
    # LLM分析
    analysis_prompt = PromptTemplates.get_basic_stats_prompt(stats_data)
    analysis_result = await self.llm_service.analyze_data(
        data=stats_data,
        analysis_type="basic_stats",
        prompt_template=analysis_prompt
    )
    
    return ReportEvent(
        event_type="module_content",
        module_id=module.module_id,
        display_logic="server_query_llm_text",
        content=analysis_result.text,
        render_type="markdown"
    )
```

**Web模块生成**
```python
async def _generate_web_module(
    self,
    module: ModuleConfig,
    request: ReportRequest
) -> ReportEvent:
    """生成Web渲染模块"""
    # 查询统计数据
    stats_data = await self.stats_service.get_summary(
        enterprise_id=request.enterprise_id,
        device_id=request.device_id,
        start_time=request.date_range.start_date,
        end_time=request.date_range.end_date,
        data_types=module.data_sources
    )
    
    # LLM分析并生成图表配置
    chart_result = await self.llm_service.generate_chart_config(
        data=stats_data,
        chart_type=module.chart_types[0]
    )
    
    return ReportEvent(
        event_type="web_query_instruction",
        module_id=module.module_id,
        display_logic="server_query_llm_web",
        query_params={
            "enterprise_id": request.enterprise_id,
            "device_id": request.device_id,
            "start_time": request.date_range.start_date,
            "end_time": request.date_range.end_date,
            "data_type": module.query_params.data_type,
            "chart_type": module.chart_types[0],
            "render_type": "web"
        },
        analysis_text=chart_result.analysis,
        chart_config=chart_result.config
    )
```

**交互式模块生成**
```python
async def _generate_interactive_module(
    self,
    module: ModuleConfig,
    request: ReportRequest
) -> ReportEvent:
    """生成交互式模块"""
    # 获取Query数据
    query_data = await self.query_service.get_conversations(
        enterprise_id=request.enterprise_id,
        device_id=request.device_id,
        start_time=request.date_range.start_date,
        end_time=request.date_range.end_date
    )
    
    # LLM分析并生成交互式内容
    interactive_result = await self.llm_service.generate_interactive_content(
        query_data=query_data,
        content_type="highlight_events"
    )
    
    return ReportEvent(
        event_type="interactive_content",
        module_id=module.module_id,
        display_logic="query_llm_interactive",
        content=interactive_result.content,
        interactive_features=module.interactive_features
    )
```

---

## 五、API接口设计

### 5.1 报告生成接口

#### 5.1.1 创建报告任务
```http
POST /api/v1/reports/generate
```

**请求体**：
```json
{
  "enterprise_id": "string",
  "device_id": "string",
  "date_range": {
    "start_date": "2025-01-01",
    "end_date": "2025-01-07"
  },
  "modules": [
    {
      "module_id": "basic_stats",
      "display_logic": "server_query_llm_text",
      "data_sources": ["statistics"],
      "chart_types": ["summary_cards"]
    },
    {
      "module_id": "message_trends",
      "display_logic": "server_query_llm_web",
      "data_sources": ["statistics"],
      "chart_types": ["line_chart", "bar_chart"],
      "query_params": {
        "data_type": "message_behavior_summary",
        "render_type": "web"
      }
    },
    {
      "module_id": "highlight_events",
      "display_logic": "query_llm_interactive",
      "data_sources": ["query_data"],
      "interactive_features": ["click_to_query", "case_details"]
    }
  ],
  "llm_config": {
    "model_name": "gemini-2.5-pro",
    "prompt_version": "UNIFIED_PROMPT_V2"
  }
}
```

#### 5.1.2 流式报告生成
```http
POST /api/v1/reports/stream-generate
```

**响应格式**：
```json
{
  "event": "module_start",
  "data": {
    "module_id": "basic_stats",
    "display_logic": "server_query_llm_text",
    "content": "# 基础统计概览\n\n..."
  }
}
```

### 5.2 数据查询接口

#### 5.2.1 统计数据查询
```http
GET /api/v1/stats/summary
```

**查询参数**：
- `enterprise_id`: 企业ID
- `device_id`: 设备ID
- `start_time`: 开始时间
- `end_time`: 结束时间
- `data_type`: 数据类型（message_behavior_summary, active_hours_summary等）
- `group_by`: 聚合方式（hour, day, total）

#### 5.2.2 对话详情查询
```http
GET /api/v1/queries/conversation-details
```

**查询参数**：
- `session_id`: 会话ID
- `start_time`: 开始时间
- `end_time`: 结束时间
- `enterprise_id`: 企业ID
- `device_id`: 设备ID

### 5.3 交互事件接口

#### 5.3.1 图表点击事件
```http
POST /api/v1/interactions/chart-click
```

**请求体**：
```json
{
  "chart_id": "message_trends_chart",
  "click_data": {
    "category": "用户消息",
    "value": 150,
    "time": "2025-01-01T10:00:00Z"
  },
  "action": "drill_down"
}
```

#### 5.3.2 对话查询事件
```http
POST /api/v1/interactions/query-conversation
```

**请求体**：
```json
{
  "session_id": "session_123",
  "time_range": {
    "start": "08:30:00",
    "end": "08:35:00"
  },
  "enterprise_id": "string",
  "device_id": "string"
}
```

---

## 六、任务调度服务

### 6.1 Celery任务配置

#### 6.1.1 任务定义
```python
@shared_task(name="reports.generate_smart_report")
def generate_smart_report_task(
    enterprise_id: str,
    device_id: str,
    report_config: Dict[str, Any]
) -> str:
    """生成智能报告任务"""
    try:
        # 创建报告服务实例
        report_service = SmartReportService()
        
        # 生成报告
        report_id = report_service.generate_report_sync(
            enterprise_id=enterprise_id,
            device_id=device_id,
            config=report_config
        )
        
        return report_id
    except Exception as e:
        logger.error(f"生成报告失败: {str(e)}")
        raise
```

#### 6.1.2 任务队列配置
```python
# celery配置
CELERY_CONFIG = {
    'broker_url': 'redis://localhost:6379/0',
    'result_backend': 'redis://localhost:6379/0',
    'task_serializer': 'json',
    'result_serializer': 'json',
    'accept_content': ['json'],
    'timezone': 'Asia/Shanghai',
    'enable_utc': True,
    'task_routes': {
        'reports.*': {'queue': 'reports'},
        'llm.*': {'queue': 'llm'},
        'data.*': {'queue': 'data'}
    }
}
```

### 6.2 异步任务管理

#### 6.2.1 任务状态管理
```python
class TaskStatus(enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.redis_client = redis.Redis()
    
    async def create_task(
        self,
        task_type: str,
        params: Dict[str, Any]
    ) -> str:
        """创建任务"""
        task_id = str(uuid.uuid4())
        task_data = {
            'id': task_id,
            'type': task_type,
            'params': params,
            'status': TaskStatus.PENDING.value,
            'created_at': datetime.utcnow().isoformat()
        }
        
        await self.redis_client.hset(
            f"task:{task_id}",
            mapping=task_data
        )
        
        return task_id
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        task_data = await self.redis_client.hgetall(f"task:{task_id}")
        return task_data
```

---

## 七、缓存服务

### 7.1 缓存策略

#### 7.1.1 缓存层级
- **L1缓存**：本地内存缓存（LRU）
- **L2缓存**：Redis缓存
- **L3缓存**：数据库

#### 7.1.2 缓存配置
```python
CACHE_CONFIG = {
    'default': {
        'backend': 'redis',
        'location': 'redis://localhost:6379/1',
        'timeout': 300,
        'key_prefix': 'smart_report:'
    },
    'llm_results': {
        'backend': 'redis',
        'location': 'redis://localhost:6379/2',
        'timeout': 3600,
        'key_prefix': 'llm:'
    },
    'statistics': {
        'backend': 'redis',
        'location': 'redis://localhost:6379/3',
        'timeout': 600,
        'key_prefix': 'stats:'
    }
}
```

### 7.2 缓存服务实现

#### 7.2.1 缓存服务类
```python
class CacheService:
    """缓存服务"""
    
    def __init__(self):
        self.redis_client = redis.Redis()
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            data = await self.redis_client.get(key)
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"获取缓存失败: {str(e)}")
            return None
    
    async def set(
        self,
        key: str,
        value: Any,
        timeout: int = 300
    ) -> bool:
        """设置缓存"""
        try:
            data = json.dumps(value, ensure_ascii=False)
            await self.redis_client.setex(key, timeout, data)
            return True
        except Exception as e:
            logger.error(f"设置缓存失败: {str(e)}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            await self.redis_client.delete(key)
            return True
        except Exception as e:
            logger.error(f"删除缓存失败: {str(e)}")
            return False
```

---

## 八、监控和日志

### 8.1 监控指标

#### 8.1.1 业务指标监控
```python
class BusinessMetrics:
    """业务指标监控"""
    
    def __init__(self):
        self.prometheus_client = PrometheusClient()
    
    def record_report_generation(self, report_type: str, duration: float):
        """记录报告生成指标"""
        self.prometheus_client.histogram(
            'report_generation_duration_seconds',
            duration,
            labels={'report_type': report_type}
        )
    
    def record_llm_api_call(self, model: str, duration: float, success: bool):
        """记录LLM API调用指标"""
        self.prometheus_client.histogram(
            'llm_api_duration_seconds',
            duration,
            labels={'model': model, 'success': str(success)}
        )
    
    def record_data_query(self, data_source: str, duration: float):
        """记录数据查询指标"""
        self.prometheus_client.histogram(
            'data_query_duration_seconds',
            duration,
            labels={'data_source': data_source}
        )
```

#### 8.1.2 系统指标监控
- **CPU使用率**：< 80%
- **内存使用率**：< 80%
- **磁盘使用率**：< 80%
- **网络延迟**：< 100ms
- **数据库连接数**：< 80%

### 8.2 日志管理

#### 8.2.1 日志配置
```python
import logging
import structlog

# 结构化日志配置
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

# 日志级别配置
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'json': {
            '()': structlog.stdlib.ProcessorFormatter,
            'processor': structlog.processors.JSONRenderer(),
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'json',
        },
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/smart_report.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'json',
        },
    },
    'loggers': {
        'smart_report': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
        },
    },
}
```

#### 8.2.2 日志记录
```python
class LoggerService:
    """日志服务"""
    
    def __init__(self, logger_name: str = "smart_report"):
        self.logger = structlog.get_logger(logger_name)
    
    def log_report_generation(
        self,
        enterprise_id: str,
        device_id: str,
        report_type: str,
        duration: float,
        success: bool
    ):
        """记录报告生成日志"""
        self.logger.info(
            "report_generation",
            enterprise_id=enterprise_id,
            device_id=device_id,
            report_type=report_type,
            duration=duration,
            success=success
        )
    
    def log_llm_api_call(
        self,
        model: str,
        prompt_length: int,
        response_length: int,
        duration: float,
        success: bool,
        error: str = None
    ):
        """记录LLM API调用日志"""
        self.logger.info(
            "llm_api_call",
            model=model,
            prompt_length=prompt_length,
            response_length=response_length,
            duration=duration,
            success=success,
            error=error
        )
    
    def log_data_query(
        self,
        data_source: str,
        query_params: Dict[str, Any],
        result_count: int,
        duration: float,
        success: bool
    ):
        """记录数据查询日志"""
        self.logger.info(
            "data_query",
            data_source=data_source,
            query_params=query_params,
            result_count=result_count,
            duration=duration,
            success=success
        )
```

---

## 九、错误处理和异常管理

### 9.1 异常分类

#### 9.1.1 业务异常
```python
class BusinessException(Exception):
    """业务异常基类"""
    
    def __init__(self, message: str, error_code: str, details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)

class DataNotFoundException(BusinessException):
    """数据未找到异常"""
    pass

class LLMServiceException(BusinessException):
    """LLM服务异常"""
    pass

class ReportGenerationException(BusinessException):
    """报告生成异常"""
    pass
```

#### 9.1.2 系统异常
```python
class SystemException(Exception):
    """系统异常基类"""
    pass

class DatabaseException(SystemException):
    """数据库异常"""
    pass

class CacheException(SystemException):
    """缓存异常"""
    pass

class NetworkException(SystemException):
    """网络异常"""
    pass
```

### 9.2 异常处理策略

#### 9.2.1 全局异常处理
```python
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse

@app.exception_handler(BusinessException)
async def business_exception_handler(request: Request, exc: BusinessException):
    """业务异常处理"""
    return JSONResponse(
        status_code=400,
        content={
            "error": {
                "code": exc.error_code,
                "message": exc.message,
                "details": exc.details
            }
        }
    )

@app.exception_handler(SystemException)
async def system_exception_handler(request: Request, exc: SystemException):
    """系统异常处理"""
    return JSONResponse(
        status_code=500,
        content={
            "error": {
                "code": "SYSTEM_ERROR",
                "message": "系统内部错误",
                "details": {"original_error": str(exc)}
            }
        }
    )
```

#### 9.2.2 重试机制
```python
import asyncio
from functools import wraps

def async_retry(max_retries: int = 3, delay: float = 1.0):
    """异步重试装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        await asyncio.sleep(delay * (2 ** attempt))  # 指数退避
                    else:
                        raise last_exception
            return None
        return wrapper
    return decorator

# 使用示例
@async_retry(max_retries=3, delay=1.0)
async def call_llm_api(prompt: str) -> str:
    """调用LLM API（带重试）"""
    # LLM API调用逻辑
    pass
```

---

## 十、配置管理

### 10.1 配置结构

#### 10.1.1 应用配置
```python
from pydantic import BaseSettings

class Settings(BaseSettings):
    """应用配置"""
    
    # 数据库配置
    DATABASE_URL: str = "mysql://user:pass@localhost/speech_ai_robot"
    REDIS_URL: str = "redis://localhost:6379"
    
    # LLM配置
    OPENAI_API_KEY: str = ""
    GEMINI_API_KEY: str = ""
    DEFAULT_LLM_MODEL: str = "gemini-2.5-pro"
    
    # 缓存配置
    CACHE_TTL: int = 600
    LLM_CACHE_TTL: int = 3600
    
    # 任务配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/0"
    
    # 监控配置
    PROMETHEUS_PORT: int = 9090
    LOG_LEVEL: str = "INFO"
    
    class Config:
        env_file = ".env"
```

#### 10.1.2 环境配置
```bash
# .env文件
DATABASE_URL=mysql://user:pass@localhost/speech_ai_robot
REDIS_URL=redis://localhost:6379
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key
DEFAULT_LLM_MODEL=gemini-2.5-pro
CACHE_TTL=600
LLM_CACHE_TTL=3600
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
PROMETHEUS_PORT=9090
LOG_LEVEL=INFO
```

### 10.2 配置验证

#### 10.2.1 配置验证器
```python
class ConfigValidator:
    """配置验证器"""
    
    @staticmethod
    def validate_database_config(settings: Settings) -> bool:
        """验证数据库配置"""
        try:
            # 测试数据库连接
            engine = create_engine(settings.DATABASE_URL)
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            return True
        except Exception as e:
            logger.error(f"数据库配置验证失败: {str(e)}")
            return False
    
    @staticmethod
    def validate_redis_config(settings: Settings) -> bool:
        """验证Redis配置"""
        try:
            # 测试Redis连接
            redis_client = redis.from_url(settings.REDIS_URL)
            redis_client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis配置验证失败: {str(e)}")
            return False
    
    @staticmethod
    def validate_llm_config(settings: Settings) -> bool:
        """验证LLM配置"""
        if not settings.OPENAI_API_KEY and not settings.GEMINI_API_KEY:
            logger.error("LLM API密钥未配置")
            return False
        return True
```

---

## 十一、数据库设计

### 11.1 核心表结构

#### 11.1.1 报告任务表
```sql
CREATE TABLE aos_smart_reports (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    enterprise_id VARCHAR(100) NOT NULL,
    device_id VARCHAR(100) NOT NULL,
    report_type ENUM('single_day', 'multi_day', 'custom') NOT NULL,
    status ENUM('pending', 'generating', 'completed', 'failed') NOT NULL,
    modules_config JSON NOT NULL,
    llm_config JSON NOT NULL,
    generated_content JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_enterprise_device (enterprise_id, device_id),
    INDEX idx_status (status)
);
```

#### 11.1.2 执行步骤表
```sql
CREATE TABLE aos_report_steps (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    report_id BIGINT NOT NULL,
    step_id VARCHAR(50) NOT NULL,
    step_name VARCHAR(100) NOT NULL,
    status ENUM('pending', 'executing', 'completed', 'failed') NOT NULL,
    data_sources JSON NOT NULL,
    prompt_config JSON NOT NULL,
    template_config JSON NOT NULL,
    result_content LONGTEXT,
    result_charts JSON,
    error_message TEXT,
    execution_time INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (report_id) REFERENCES aos_smart_reports(id),
    INDEX idx_report_step (report_id, step_id)
);
```

#### 11.1.3 交互事件表
```sql
CREATE TABLE aos_report_interactions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    report_id BIGINT NOT NULL,
    module_id VARCHAR(50) NOT NULL,
    interaction_type ENUM('chart_click', 'conversation_query', 'drill_down') NOT NULL,
    interaction_data JSON NOT NULL,
    session_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (report_id) REFERENCES aos_smart_reports(id),
    INDEX idx_report_module (report_id, module_id)
);
```

### 11.2 数据模型

#### 11.2.1 SQLAlchemy模型
```python
from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class SmartReport(Base):
    """智能报告表"""
    __tablename__ = "aos_smart_reports"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    enterprise_id = Column(String(100), nullable=False)
    device_id = Column(String(100), nullable=False)
    report_type = Column(String(20), nullable=False)
    status = Column(String(20), nullable=False)
    modules_config = Column(JSON, nullable=False)
    llm_config = Column(JSON, nullable=False)
    generated_content = Column(JSON)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    steps = relationship("ReportStep", back_populates="report")
    interactions = relationship("ReportInteraction", back_populates="report")

class ReportStep(Base):
    """报告步骤表"""
    __tablename__ = "aos_report_steps"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    report_id = Column(Integer, ForeignKey("aos_smart_reports.id"), nullable=False)
    step_id = Column(String(50), nullable=False)
    step_name = Column(String(100), nullable=False)
    status = Column(String(20), nullable=False)
    data_sources = Column(JSON, nullable=False)
    prompt_config = Column(JSON, nullable=False)
    template_config = Column(JSON, nullable=False)
    result_content = Column(Text)
    result_charts = Column(JSON)
    error_message = Column(Text)
    execution_time = Column(Integer)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    report = relationship("SmartReport", back_populates="steps")

class ReportInteraction(Base):
    """报告交互表"""
    __tablename__ = "aos_report_interactions"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    report_id = Column(Integer, ForeignKey("aos_smart_reports.id"), nullable=False)
    module_id = Column(String(50), nullable=False)
    interaction_type = Column(String(20), nullable=False)
    interaction_data = Column(JSON, nullable=False)
    session_id = Column(String(100))
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关联关系
    report = relationship("SmartReport", back_populates="interactions")
```

---

## 十二、测试策略

### 12.1 单元测试

#### 12.1.1 服务层测试
```python
import pytest
from unittest.mock import Mock, patch
from app.services.smart_report_service import SmartReportService

class TestSmartReportService:
    """智能报告服务测试"""
    
    @pytest.fixture
    def report_service(self):
        """创建报告服务实例"""
        return SmartReportService()
    
    @pytest.mark.asyncio
    async def test_generate_text_module(self, report_service):
        """测试文本模块生成"""
        # 准备测试数据
        module_config = {
            "module_id": "basic_stats",
            "display_logic": "server_query_llm_text",
            "data_sources": ["statistics"]
        }
        request = {
            "enterprise_id": "test_enterprise",
            "device_id": "test_device",
            "date_range": {
                "start_date": "2025-01-01",
                "end_date": "2025-01-07"
            }
        }
        
        # Mock依赖服务
        with patch.object(report_service.stats_service, 'get_summary') as mock_stats:
            with patch.object(report_service.llm_service, 'analyze_data') as mock_llm:
                mock_stats.return_value = {"total_sessions": 100}
                mock_llm.return_value = {"text": "# 测试报告\n\n这是测试内容"}
                
                # 执行测试
                result = await report_service._generate_text_module(module_config, request)
                
                # 验证结果
                assert result.event_type == "module_content"
                assert result.module_id == "basic_stats"
                assert result.render_type == "markdown"
    
    @pytest.mark.asyncio
    async def test_generate_web_module(self, report_service):
        """测试Web模块生成"""
        # 测试Web渲染模块生成逻辑
        pass
    
    @pytest.mark.asyncio
    async def test_generate_interactive_module(self, report_service):
        """测试交互式模块生成"""
        # 测试交互式模块生成逻辑
        pass
```

#### 12.1.2 API层测试
```python
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

class TestReportAPI:
    """报告API测试"""
    
    def test_create_report(self):
        """测试创建报告"""
        response = client.post(
            "/api/v1/reports/generate",
            json={
                "enterprise_id": "test_enterprise",
                "device_id": "test_device",
                "date_range": {
                    "start_date": "2025-01-01",
                    "end_date": "2025-01-07"
                },
                "modules": [
                    {
                        "module_id": "basic_stats",
                        "display_logic": "server_query_llm_text",
                        "data_sources": ["statistics"]
                    }
                ]
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "report_id" in data
    
    def test_get_statistics(self):
        """测试获取统计数据"""
        response = client.get(
            "/api/v1/stats/summary",
            params={
                "enterprise_id": "test_enterprise",
                "device_id": "test_device",
                "start_time": "2025-01-01T00:00:00Z",
                "end_time": "2025-01-07T23:59:59Z",
                "data_type": "message_behavior_summary"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
```

### 12.2 集成测试

#### 12.2.1 端到端测试
```python
class TestEndToEnd:
    """端到端测试"""
    
    @pytest.mark.asyncio
    async def test_full_report_generation(self):
        """测试完整报告生成流程"""
        # 1. 创建报告请求
        # 2. 执行报告生成
        # 3. 验证生成结果
        # 4. 测试交互功能
        pass
    
    @pytest.mark.asyncio
    async def test_data_integration(self):
        """测试数据集成"""
        # 1. 测试统计数据查询
        # 2. 测试Query数据查询
        # 3. 测试数据融合
        pass
```

### 12.3 性能测试

#### 12.3.1 负载测试
```python
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor

class PerformanceTest:
    """性能测试"""
    
    @staticmethod
    async def test_concurrent_report_generation(num_requests: int = 100):
        """测试并发报告生成"""
        start_time = time.time()
        
        # 创建并发任务
        tasks = []
        for i in range(num_requests):
            task = asyncio.create_task(
                generate_test_report(f"enterprise_{i}", f"device_{i}")
            )
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 统计结果
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        error_count = len(results) - success_count
        
        print(f"并发测试结果:")
        print(f"总请求数: {num_requests}")
        print(f"成功数: {success_count}")
        print(f"失败数: {error_count}")
        print(f"总耗时: {duration:.2f}秒")
        print(f"平均响应时间: {duration/num_requests:.2f}秒")
        print(f"QPS: {num_requests/duration:.2f}")
```

---

## 十三、部署和运维

### 13.1 Docker配置

#### 13.1.1 Dockerfile
```dockerfile
# 使用Python 3.11作为基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建日志目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 13.1.2 Docker Compose
```yaml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql://user:pass@mysql:3306/speech_ai_robot
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    depends_on:
      - mysql
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  celery-worker:
    build: .
    command: celery -A app.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=mysql://user:pass@mysql:3306/speech_ai_robot
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mysql
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  celery-beat:
    build: .
    command: celery -A app.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=mysql://user:pass@mysql:3306/speech_ai_robot
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mysql
      - redis
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=speech_ai_robot
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana

volumes:
  mysql_data:
  redis_data:
  prometheus_data:
  grafana_data:
```

### 13.2 监控配置

#### 13.2.1 Prometheus配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'smart-report-api'
    static_configs:
      - targets: ['api:8000']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'celery-workers'
    static_configs:
      - targets: ['celery-worker:8000']
    metrics_path: '/metrics'

  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql:3306']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
```

#### 13.2.2 Grafana仪表板
```json
{
  "dashboard": {
    "title": "智能报告系统监控",
    "panels": [
      {
        "title": "API响应时间",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "报告生成成功率",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(report_generation_total{status=\"success\"}[5m]) / rate(report_generation_total[5m]) * 100",
            "legendFormat": "成功率"
          }
        ]
      },
      {
        "title": "LLM API调用统计",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(llm_api_calls_total[5m])",
            "legendFormat": "调用频率"
          }
        ]
      }
    ]
  }
}
```

---

## 十四、安全考虑

### 14.1 数据安全

#### 14.1.1 数据加密
- **传输加密**：使用HTTPS/TLS加密传输
- **存储加密**：敏感数据加密存储
- **API密钥管理**：使用环境变量或密钥管理服务

#### 14.1.2 数据脱敏
```python
class DataMaskingService:
    """数据脱敏服务"""
    
    @staticmethod
    def mask_personal_info(data: Dict[str, Any]) -> Dict[str, Any]:
        """脱敏个人信息"""
        masked_data = data.copy()
        
        # 脱敏手机号
        if 'phone' in masked_data:
            masked_data['phone'] = masked_data['phone'][:3] + '****' + masked_data['phone'][-4:]
        
        # 脱敏邮箱
        if 'email' in masked_data:
            email_parts = masked_data['email'].split('@')
            if len(email_parts) == 2:
                username = email_parts[0]
                domain = email_parts[1]
                masked_username = username[:2] + '***' + username[-1] if len(username) > 3 else '***'
                masked_data['email'] = f"{masked_username}@{domain}"
        
        return masked_data
```

### 14.2 接口安全

#### 14.2.1 认证授权
```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import jwt

security = HTTPBearer()

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证JWT Token"""
    try:
        payload = jwt.decode(
            credentials.credentials,
            SECRET_KEY,
            algorithms=["HS256"]
        )
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token已过期"
        )
    except jwt.JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的Token"
        )

@app.get("/api/v1/reports")
async def get_reports(token: dict = Depends(verify_token)):
    """获取报告列表（需要认证）"""
    # 实现获取报告列表逻辑
    pass
```

#### 14.2.2 接口限流
```python
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

@app.post("/api/v1/reports/generate")
@limiter.limit("10/minute")  # 每分钟最多10次请求
async def create_report(request: Request, report_request: ReportRequest):
    """创建报告（带限流）"""
    # 实现创建报告逻辑
    pass
```

---

## 十五、总结

### 15.1 核心功能总结

服务端作为智能设备使用报告生成系统的核心，提供了以下关键功能：

1. **多数据源管理**：统一管理统计数据、Query数据、人脸识别数据
2. **LLM智能分析**：集成多种大语言模型，提供智能分析能力
3. **三种展示逻辑**：支持文本下发、Web渲染、交互式展示
4. **模块化设计**：支持按模块分步执行和配置
5. **高性能架构**：异步处理、缓存优化、负载均衡
6. **完善监控**：业务指标、系统指标、日志管理
7. **安全防护**：数据加密、接口认证、限流控制

### 15.2 技术亮点

- **异步架构**：基于FastAPI和Celery的异步处理架构
- **智能分析**：集成多种LLM模型，提供智能数据分析
- **缓存优化**：多级缓存策略，提升系统性能
- **监控完善**：Prometheus + Grafana监控体系
- **容器化部署**：Docker + Docker Compose部署方案
- **安全可靠**：完善的安全防护和异常处理机制

### 15.3 扩展性

- **模块化设计**：支持新模块的快速添加
- **插件化架构**：支持新的数据源和LLM模型集成
- **配置化**：支持运行时配置更新
- **水平扩展**：支持多实例部署和负载均衡

---

**文档结束**