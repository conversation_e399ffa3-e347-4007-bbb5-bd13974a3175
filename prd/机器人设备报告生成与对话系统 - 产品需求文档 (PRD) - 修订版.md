# 机器人设备报告生成与对话系统 - 产品需求文档 (PRD) - 修订版

## 1. 项目背景与目标

本系统基于现有的 **FastAPI** 架构，为机器人设备生成每日报告。报告内容将通过大语言模型（LLM）进行生成，并通过 **SSE（Server-Sent Events）** 协议实时推送给前端。系统将扩展现有的CEMonitor API，增加智能报告生成功能。

## 2. 技术栈确认

- **后端框架**: FastAPI (现有)
- **数据库**: MySQL (现有)
- **任务队列**: Celery (现有)
- **LLM服务**: OpenAI API (现有依赖)
- **实时通信**: SSE (StreamingResponse)
- **缓存**: Redis (现有)

## 3. 目标功能

1. **报告生成**：根据设备使用情况，分阶段生成报告内容
2. **实时推送**：通过 FastAPI 的 **StreamingResponse** 实现SSE推送
3. **LLM 记忆**：记录和回溯每次对话及生成内容的历史
4. **接口安全**：基于现有架构的认证机制
5. **性能优化**：并发控制和缓存机制

## 4. 系统架构设计

### 4.1 目录结构扩展
```
apps/api/app/
├── routers/
│   ├── reports.py              # 新增：报告生成路由
│   └── report_sse.py           # 新增：SSE推送路由
├── services/
│   ├── llm_service.py          # 新增：LLM调用服务
│   ├── report_service.py       # 新增：报告生成服务
│   └── sse_service.py          # 新增：SSE推送服务
├── models/
│   ├── report_models.py        # 新增：报告相关数据模型
│   └── llm_models.py           # 新增：LLM相关数据模型
├── tasks/
│   └── report_tasks.py         # 新增：报告生成异步任务
└── config/
    └── llm_config.py           # 新增：LLM配置管理
```

### 4.2 数据库设计
```sql
-- 报告记录表
CREATE TABLE robot_reports (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(100) NOT NULL,
    report_date DATE NOT NULL,
    report_type ENUM('daily', 'weekly', 'monthly') DEFAULT 'daily',
    status ENUM('generating', 'completed', 'failed') DEFAULT 'generating',
    total_stages INT DEFAULT 9,
    completed_stages INT DEFAULT 0,
    content JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_device_date (device_id, report_date),
    INDEX idx_status (status)
);

-- LLM对话记录表
CREATE TABLE llm_conversations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    report_id BIGINT,
    stage_number INT,
    prompt TEXT NOT NULL,
    response TEXT,
    model_name VARCHAR(50),
    tokens_used INT,
    response_time_ms INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (report_id) REFERENCES robot_reports(id)
);

-- 报告生成会话表
CREATE TABLE report_sessions (
    id VARCHAR(36) PRIMARY KEY,
    device_id VARCHAR(100) NOT NULL,
    user_id VARCHAR(100),
    status ENUM('active', 'completed', 'expired') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    INDEX idx_device_id (device_id),
    INDEX idx_status_expires (status, expires_at)
);
```

## 5. 报告生成阶段与Prompt设计

### 5.1 阶段配置
```python
REPORT_STAGES = {
    1: {
        "name": "基础使用数据概览",
        "prompt_template": "基于设备 {device_id} 在 {report_date} 的使用数据：{usage_data}，生成基础使用概览报告。包括总使用时间、活跃用户数和系统性能等信息。",
        "timeout": 30
    },
    2: {
        "name": "数据趋势分析", 
        "prompt_template": "分析设备 {device_id} 在过去一周的使用趋势：{trend_data}。识别活动模式，如高峰使用时间和平均交互时长。",
        "timeout": 45
    },
    # ... 其他7个阶段
}
```

## 6. FastAPI SSE实现方案

### 6.1 SSE路由设计
```python
from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse

@router.get("/reports/{device_id}/stream")
async def stream_report_generation(device_id: str):
    async def event_generator():
        # 报告生成逻辑
        for stage in range(1, 10):
            # 调用LLM生成内容
            content = await generate_stage_content(device_id, stage)
            
            # 推送数据
            yield {
                "event": "stage_completed",
                "data": json.dumps({
                    "stage": stage,
                    "content": content,
                    "timestamp": datetime.now().isoformat()
                })
            }
            
        yield {
            "event": "report_completed", 
            "data": json.dumps({"status": "success"})
        }
    
    return EventSourceResponse(event_generator())
```

### 6.2 SSE数据格式规范
```typescript
// 阶段完成事件
interface StageCompletedEvent {
    event: "stage_completed";
    data: {
        stage: number;
        stageName: string;
        content: string;
        timestamp: string;
        progress: number; // 0-100
    }
}

// 进度更新事件
interface ProgressUpdateEvent {
    event: "progress_update";
    data: {
        stage: number;
        progress: number;
        message: string;
        timestamp: string;
    }
}

// 错误事件
interface ErrorEvent {
    event: "error";
    data: {
        errorCode: string;
        errorMessage: string;
        stage?: number;
        timestamp: string;
    }
}

// 完成事件
interface CompletionEvent {
    event: "report_completed";
    data: {
        status: "success" | "failed";
        reportId: string;
        timestamp: string;
    }
}
```

## 7. LLM服务集成

### 7.1 配置管理
```python
class LLMConfig:
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY")
    MODEL_NAME: str = "gpt-3.5-turbo"
    MAX_TOKENS: int = 1000
    TEMPERATURE: float = 0.7
    TIMEOUT: int = 30
    MAX_RETRIES: int = 3
    RATE_LIMIT_PER_MINUTE: int = 60
```

### 7.2 错误处理与重试机制
```python
class LLMService:
    async def generate_content(self, prompt: str, stage: int) -> str:
        for attempt in range(self.max_retries):
            try:
                response = await self.client.chat.completions.create(
                    model=self.model_name,
                    messages=[{"role": "user", "content": prompt}],
                    timeout=self.timeout
                )
                return response.choices[0].message.content
            except OpenAIError as e:
                if attempt == self.max_retries - 1:
                    raise ReportGenerationError(f"LLM调用失败: {str(e)}")
                await asyncio.sleep(2 ** attempt)  # 指数退避
```

## 8. 性能与安全考虑

### 8.1 并发控制
- 使用信号量限制同时进行的报告生成数量
- Redis实现分布式锁防止重复生成
- 使用连接池管理数据库连接

### 8.2 安全机制
- 基于现有认证系统的用户验证
- API接口限流（每用户每分钟最多5个报告生成请求）
- 输入参数验证和SQL注入防护

### 8.3 缓存策略
- Redis缓存LLM响应（24小时）
- 数据库查询结果缓存
- 静态配置缓存

## 9. 监控与日志

### 9.1 关键指标监控
- 报告生成成功率
- LLM调用延迟和成功率
- SSE连接数和断开率
- 各阶段生成耗时

### 9.2 日志设计
```python
# 结构化日志格式
{
    "timestamp": "2024-01-01T10:00:00Z",
    "level": "INFO",
    "event": "stage_completed",
    "device_id": "M03SCN2A170253075474",
    "stage": 1,
    "duration_ms": 2500,
    "tokens_used": 150
}
```

## 10. API接口设计

### 10.1 核心接口
```python
# 启动报告生成
POST /api/v1/reports/generate
{
    "device_id": "M03SCN2A170253075474",
    "report_date": "2024-01-01",
    "language": "zh"
}

# SSE流式获取
GET /api/v1/reports/{device_id}/stream?session_id={session_id}

# 获取历史报告
GET /api/v1/reports/{device_id}?date=2024-01-01

# 获取报告状态
GET /api/v1/reports/status/{session_id}
```

## 11. 部署与配置

### 11.1 环境变量
```bash
# LLM配置
OPENAI_API_KEY=sk-xxx
LLM_MODEL_NAME=gpt-3.5-turbo
LLM_MAX_TOKENS=1000

# Redis配置（现有）
REDIS_URL=redis://localhost:6379

# 数据库配置（现有）
DATABASE_URL=mysql://user:pass@localhost/cemonitor
```

### 11.2 容器化配置
```dockerfile
# 在现有Dockerfile基础上添加
RUN pip install sse-starlette openai
```

## 12. 测试策略

### 12.1 单元测试
- LLM服务调用测试
- SSE推送机制测试  
- 数据库操作测试

### 12.2 集成测试
- 端到端报告生成流程测试
- 并发场景测试
- 错误恢复测试

## 13. 实施计划

### Phase 1: 基础架构 (1周)
- 数据库表创建
- LLM服务集成
- 基础API接口

### Phase 2: SSE实现 (1周)  
- SSE推送机制
- 前端连接管理
- 错误处理

### Phase 3: 优化与测试 (1周)
- 性能优化
- 安全加固
- 全面测试

## 14. 风险评估

| 风险项 | 影响 | 概率 | 缓解措施 |
|--------|------|------|----------|
| LLM服务不稳定 | 高 | 中 | 重试机制、降级方案 |
| SSE连接中断 | 中 | 中 | 断线重连、状态恢复 |
| 并发性能瓶颈 | 高 | 低 | 负载测试、容量规划 |
| 数据安全风险 | 高 | 低 | 数据加密、访问控制 |

## 15. 总结

本修订版PRD基于现有FastAPI架构，提供了完整的技术实现方案。相比原版本，主要改进包括：

1. **技术栈对齐**：基于现有FastAPI架构
2. **详细实现方案**：具体的代码结构和数据库设计
3. **安全性考虑**：认证、限流、错误处理
4. **性能优化**：并发控制、缓存策略
5. **可运维性**：监控、日志、部署方案

该PRD为开发团队提供了清晰的实施路径和技术规范。 