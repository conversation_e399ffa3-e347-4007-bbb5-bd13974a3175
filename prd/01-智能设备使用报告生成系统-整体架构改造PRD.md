# 📄 智能设备使用报告生成系统 - 整体架构改造PRD
**版本**：V2.0  
**编写人**：产品经理  
**发布日期**：2025-07-29  
**文档类型**：整体架构设计  

---

## 一、项目概述

### 1.1 项目背景
当前"设备使用报告生成"系统存在以下核心问题：

1. **数据来源单一**：仅使用机器人query对话数据，未充分利用平台统计类指标
2. **展示方式有限**：仅支持Markdown格式，图表展示受限
3. **缺少交互能力**：无法支持图表点击查看关联的query数据详情
4. **执行方式不灵活**：不支持按模块/指标分步执行任务
5. **数据整合不足**：统计数据和query数据分离，无法协同分析

### 1.2 改造目标
构建一个**智能化、模块化、交互式**的设备使用报告生成系统，实现：

- **多数据源整合**：统一整合统计数据和query对话数据
- **分步执行能力**：支持按模块/指标分步生成报告
- **丰富展示方式**：结合Markdown和Web图表展示
- **交互式体验**：支持图表点击查看详情
- **灵活配置**：支持自定义数据源、Prompt和模板

### 1.3 核心价值
- **提升报告质量**：通过多数据源整合和LLM分析，提供更全面、深入的洞察
- **增强用户体验**：交互式图表和分步执行，提供更好的用户交互体验
- **提高开发效率**：模块化设计，支持快速迭代和功能扩展
- **降低维护成本**：统一的数据源管理和渲染引擎，减少代码重复

---

## 二、系统架构设计

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        A[Web前端] --> B[组件库]
        B --> C[渲染引擎]
    end
    
    subgraph "API网关层"
        D[API网关] --> E[认证授权]
        D --> F[请求路由]
        D --> G[限流控制]
    end
    
    subgraph "业务服务层"
        H[报告生成服务] --> I[数据源管理]
        H --> J[LLM服务]
        H --> K[渲染引擎]
        H --> L[任务调度]
    end
    
    subgraph "数据源层"
        I --> M[统计数据源]
        I --> N[Query数据源]
        I --> O[人脸识别数据源]
    end
    
    subgraph "数据存储层"
        M --> P[统计数据库]
        N --> Q[聊天记录文件]
        O --> R[飞书多维表格]
    end
    
    subgraph "基础设施层"
        S[Redis缓存] --> H
        T[消息队列] --> L
        U[监控告警] --> H
    end
    
    A --> D
    D --> H
```

### 2.2 核心模块设计

#### 2.2.1 数据展示逻辑分类

系统支持三种数据展示逻辑：

**逻辑一：服务端查询 + LLM分析 + 文本下发**
- **流程**：服务端查询数据 → LLM分析 → 生成文本 → Web展示
- **适用场景**：需要AI深度分析的数据，如趋势分析、异常检测、建议生成
- **输出格式**：纯文本Markdown格式

**逻辑二：服务端查询 + LLM分析 + Web渲染**
- **流程**：服务端查询数据 → LLM分析 → 生成图表配置 → Web渲染
- **适用场景**：需要图表展示的统计数据，如趋势图、分布图、对比图
- **输出格式**：图表配置 + 分析文本

**逻辑三：Query数据 + LLM分析 + 交互式展示**
- **流程**：Query数据 → LLM分析 → 生成交互式内容 → Web展示
- **适用场景**：需要查看详细对话的案例，如亮点事件、服务案例
- **输出格式**：交互式组件 + 可点击查询

#### 2.2.2 数据流设计

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API网关
    participant R as 报告服务
    participant D as 数据服务
    participant L as LLM服务
    participant C as 缓存服务
    
    U->>F: 创建报告请求
    F->>A: 发送请求
    A->>R: 路由到报告服务
    R->>C: 检查缓存
    alt 缓存命中
        C-->>R: 返回缓存数据
    else 缓存未命中
        R->>D: 查询数据源
        D-->>R: 返回数据
        R->>L: 发送给LLM分析
        L-->>R: 返回分析结果
        R->>C: 缓存结果
    end
    R-->>A: 返回结果
    A-->>F: 响应请求
    F-->>U: 展示结果
```

### 2.3 技术架构

#### 2.3.1 后端技术栈
- **框架**：FastAPI + SQLAlchemy + Celery
- **数据库**：MySQL + Redis
- **消息队列**：Celery + Redis
- **LLM集成**：OpenAI API + Google Gemini API
- **监控**：Prometheus + Grafana

#### 2.3.2 前端技术栈
- **框架**：React 18 + TypeScript
- **构建工具**：Vite
- **状态管理**：React Query + Zustand
- **图表库**：ECharts + React-ECharts
- **UI组件**：Ant Design + Tailwind CSS

#### 2.3.3 部署架构
- **容器化**：Docker + Docker Compose
- **编排**：Kubernetes（生产环境）
- **网关**：Nginx
- **负载均衡**：HAProxy

---

## 三、数据架构设计

### 3.1 数据源整合

#### 3.1.1 统计数据源
**来源**：`speech_ai_robot`数据库中的统计表

| 数据表 | 主要指标 | 用途 | 更新频率 |
|--------|----------|------|----------|
| `aos_stat_session_behavior_hourly` | 会话总数、有效会话数、平均对话轮数 | 会话行为分析 | 每小时 |
| `aos_stat_message_behavior_hourly` | 用户消息数、机器人消息数、Action触发数 | 消息行为分析 | 每小时 |
| `aos_stat_action_behavior_hourly` | Action类型频次、功能使用分布 | 功能使用分析 | 每小时 |
| `aos_stat_user_question_summary_hourly` | 高频问题、关键词词云 | 用户问题分析 | 每小时 |
| `aos_stat_session_duration_distribution_hourly` | 会话时长分布 | 使用时长分析 | 每小时 |

#### 3.1.2 Query数据源
**来源**：聊天记录文件 + 实时对话数据

| 数据类型 | 内容 | 用途 | 存储方式 |
|----------|------|------|----------|
| 对话记录 | 用户-机器人完整对话 | 详细交互分析 | 文件系统 |
| 时间戳 | 精确到秒的交互时间 | 时间序列分析 | 数据库 |
| 用户意图 | 用户问题分类 | 意图识别分析 | 数据库 |
| 机器人响应 | 响应内容、Action执行 | 服务质量分析 | 数据库 |

#### 3.1.3 人脸识别数据源
**来源**：飞书多维表格

| 数据类型 | 内容 | 用途 | 更新频率 |
|----------|------|------|----------|
| 访客画像 | 性别、年龄分布 | 用户画像分析 | 实时 |
| 识别成功率 | 识别成功/失败统计 | 技术性能分析 | 实时 |
| 高频访客 | 重复访问用户 | 用户粘性分析 | 实时 |

### 3.2 数据融合策略

#### 3.2.1 数据标准化
- **时间统一**：所有数据源使用UTC时间，前端显示时转换为本地时间
- **ID映射**：建立企业ID、设备ID的统一映射关系
- **格式统一**：统一数据格式，支持JSON Schema验证

#### 3.2.2 数据缓存策略
- **热点数据缓存**：统计数据的查询结果缓存10分钟
- **LLM结果缓存**：分析结果缓存1小时
- **用户会话缓存**：用户配置和偏好缓存30分钟

#### 3.2.3 数据质量保证
- **数据验证**：输入数据格式和范围验证
- **异常检测**：数据异常值检测和处理
- **数据补全**：缺失数据的智能补全

---

## 四、模块化设计

### 4.1 功能模块划分

#### 4.1.1 基础数据展示模块
- **模块ID**：`basic_stats`
- **功能描述**：通过获取当前统计数据来展示内容
- **子功能**：
  - 文本输出：支持指定数据的文本输出
  - 事件驱动Web查询：服务端生成查询指令，Web端主动查询数据
  - 混合渲染模式：支持Web渲染和Markdown渲染交叉出现

#### 4.1.2 LLM智能分析模块
- **模块ID**：`llm_analysis`
- **功能描述**：通过大模型获取数据并进行分析
- **分析内容**：
  - 当日亮点事件和预警事件
  - 当日满意服务案例和不满意案例
  - 新视角观察（最积极用户、高峰时段等）
  - 经营建议（具体可执行的建议）

#### 4.1.3 交互式查询模块
- **模块ID**：`interactive_query`
- **功能描述**：实现点击即可查询聊天功能
- **核心功能**：
  - Query数据与机器人对话绑定
  - 点击事件触发详细对话查询
  - 支持多维度数据钻取
  - 实时对话内容展示

### 4.2 模块依赖关系

```mermaid
graph TD
    A[基础数据展示模块] --> B[LLM智能分析模块]
    B --> C[交互式查询模块]
    A --> D[图表渲染模块]
    B --> D
    C --> E[对话详情模块]
    D --> F[交互事件模块]
    E --> F
```

### 4.3 模块配置管理

#### 4.3.1 模块配置结构
```json
{
  "module_id": "basic_stats",
  "name": "基础统计概览",
  "description": "展示设备使用的基础统计数据",
  "display_logic": "server_query_llm_text",
  "data_sources": ["statistics"],
  "dependencies": [],
  "config": {
    "render_type": "markdown",
    "cache_ttl": 600,
    "llm_model": "gemini-2.5-pro"
  }
}
```

#### 4.3.2 动态模块加载
- **模块注册**：支持动态注册新模块
- **配置热更新**：支持运行时更新模块配置
- **版本管理**：支持模块版本管理和回滚

---

## 五、渲染引擎设计

### 5.1 渲染类型分类

#### 5.1.1 Markdown渲染
- **适用场景**：文本内容、分析报告、建议说明
- **特点**：支持数学公式、代码高亮、表格等
- **配置项**：主题、字体、间距等

#### 5.1.2 Web组件渲染
- **适用场景**：图表、交互式组件、数据表格
- **特点**：支持交互、动画、响应式布局
- **组件类型**：图表组件、列表组件、卡片组件等

#### 5.1.3 混合渲染
- **适用场景**：复杂报告页面
- **特点**：Markdown和Web组件混合使用
- **布局方式**：流式布局、网格布局、弹性布局

### 5.2 渲染引擎架构

```mermaid
graph TD
    A[渲染请求] --> B[渲染引擎]
    B --> C[内容解析器]
    C --> D[Markdown解析器]
    C --> E[组件解析器]
    D --> F[Markdown渲染器]
    E --> G[组件渲染器]
    F --> H[样式应用]
    G --> H
    H --> I[输出结果]
```

### 5.3 渲染优化策略

#### 5.3.1 性能优化
- **懒加载**：组件按需加载
- **虚拟滚动**：大数据列表虚拟滚动
- **缓存策略**：渲染结果缓存

#### 5.3.2 用户体验优化
- **骨架屏**：加载时显示骨架屏
- **渐进式加载**：内容渐进式显示
- **错误边界**：组件错误隔离

---

## 六、交互设计

### 6.1 交互类型

#### 6.1.1 图表交互
- **点击事件**：点击图表元素查看详情
- **悬停事件**：悬停显示详细信息
- **缩放事件**：图表缩放和平移
- **选择事件**：多选和单选操作

#### 6.1.2 数据钻取
- **层级钻取**：从汇总数据钻取到明细数据
- **时间钻取**：从日数据钻取到小时数据
- **维度钻取**：从多维度数据钻取到单维度数据

#### 6.1.3 对话查询
- **会话查询**：点击查看完整对话
- **时间范围查询**：查询特定时间范围的对话
- **关键词查询**：按关键词搜索对话内容

### 6.2 交互流程设计

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as 图表组件
    participant E as 事件处理器
    participant A as API服务
    participant D as 数据服务
    
    U->>C: 点击图表元素
    C->>E: 触发点击事件
    E->>A: 发送查询请求
    A->>D: 查询详细数据
    D-->>A: 返回数据
    A-->>E: 返回查询结果
    E->>C: 更新图表显示
    C-->>U: 显示详细信息
```

---

## 七、性能设计

### 7.1 性能指标

#### 7.1.1 响应时间
- **页面加载时间**：< 3秒
- **API响应时间**：< 1秒
- **图表渲染时间**：< 500ms
- **交互响应时间**：< 200ms

#### 7.1.2 并发能力
- **单机并发**：> 1000 QPS
- **集群并发**：> 10000 QPS
- **用户并发**：> 1000 同时在线

#### 7.1.3 数据处理能力
- **单次查询数据量**：< 100MB
- **实时数据处理**：< 1秒延迟
- **批量数据处理**：< 10分钟

### 7.2 性能优化策略

#### 7.2.1 前端优化
- **代码分割**：按路由和组件分割代码
- **资源压缩**：CSS、JS、图片压缩
- **CDN加速**：静态资源CDN分发
- **缓存策略**：浏览器缓存和Service Worker

#### 7.2.2 后端优化
- **数据库优化**：索引优化、查询优化
- **缓存策略**：Redis缓存、本地缓存
- **异步处理**：Celery异步任务
- **连接池**：数据库连接池、HTTP连接池

#### 7.2.3 架构优化
- **负载均衡**：Nginx负载均衡
- **服务拆分**：微服务架构
- **数据分片**：数据库分片
- **CDN加速**：全球CDN分发

---

## 八、安全设计

### 8.1 安全威胁分析

#### 8.1.1 数据安全
- **数据泄露**：敏感数据泄露风险
- **数据篡改**：数据被恶意篡改风险
- **数据丢失**：数据意外丢失风险

#### 8.1.2 系统安全
- **SQL注入**：数据库注入攻击
- **XSS攻击**：跨站脚本攻击
- **CSRF攻击**：跨站请求伪造攻击

#### 8.1.3 接口安全
- **接口滥用**：API接口被恶意调用
- **权限绕过**：绕过权限验证
- **数据越权**：访问非授权数据

### 8.2 安全防护措施

#### 8.2.1 数据安全
- **数据加密**：敏感数据加密存储
- **数据脱敏**：展示数据脱敏处理
- **数据备份**：定期数据备份

#### 8.2.2 系统安全
- **输入验证**：严格输入验证
- **输出编码**：输出内容编码
- **安全配置**：安全配置管理

#### 8.2.3 接口安全
- **认证授权**：JWT认证和RBAC授权
- **接口限流**：API接口限流
- **审计日志**：操作审计日志

---

## 九、监控告警

### 9.1 监控指标

#### 9.1.1 业务指标
- **报告生成成功率**：> 99%
- **用户满意度**：> 90%
- **功能使用率**：各功能模块使用情况

#### 9.1.2 技术指标
- **系统可用性**：> 99.5%
- **响应时间**：API平均响应时间
- **错误率**：系统错误率 < 1%

#### 9.1.3 资源指标
- **CPU使用率**：< 80%
- **内存使用率**：< 80%
- **磁盘使用率**：< 80%

### 9.2 告警策略

#### 9.2.1 告警级别
- **P0**：系统不可用，立即处理
- **P1**：功能异常，1小时内处理
- **P2**：性能下降，4小时内处理
- **P3**：信息提醒，24小时内处理

#### 9.2.2 告警方式
- **即时告警**：电话、短信
- **延迟告警**：邮件、钉钉
- **汇总告警**：日报、周报

---

## 十、部署方案

### 10.1 环境规划

#### 10.1.1 开发环境
- **用途**：功能开发和测试
- **配置**：单机部署
- **数据**：测试数据

#### 10.1.2 测试环境
- **用途**：集成测试和性能测试
- **配置**：小规模集群
- **数据**：生产数据副本

#### 10.1.3 生产环境
- **用途**：正式服务
- **配置**：大规模集群
- **数据**：生产数据

### 10.2 部署架构

```mermaid
graph TD
    subgraph "负载均衡层"
        A[Nginx] --> B[HAProxy]
    end
    
    subgraph "应用服务层"
        B --> C[API服务1]
        B --> D[API服务2]
        B --> E[API服务N]
    end
    
    subgraph "数据服务层"
        C --> F[MySQL主]
        D --> F
        E --> F
        C --> G[Redis集群]
        D --> G
        E --> G
    end
    
    subgraph "监控层"
        H[Prometheus] --> I[Grafana]
        H --> J[AlertManager]
    end
```

### 10.3 部署流程

#### 10.3.1 自动化部署
- **代码构建**：GitLab CI/CD
- **镜像构建**：Docker镜像构建
- **服务部署**：Kubernetes部署
- **健康检查**：服务健康检查

#### 10.3.2 回滚策略
- **快速回滚**：一键回滚到上一版本
- **数据回滚**：数据库版本回滚
- **配置回滚**：配置文件回滚

---

## 十一、项目计划

### 11.1 开发阶段

| 阶段 | 时间 | 主要任务 | 交付物 |
|------|------|----------|--------|
| 需求分析 | 1周 | 详细需求分析 | 需求文档 |
| 架构设计 | 1周 | 系统架构设计 | 架构文档 |
| 数据库设计 | 1周 | 数据库表设计 | 数据库脚本 |
| 后端开发 | 3周 | API开发、服务开发 | 后端代码 |
| 前端开发 | 3周 | 界面开发、交互开发 | 前端代码 |
| 集成测试 | 1周 | 系统集成测试 | 测试报告 |
| 部署上线 | 1周 | 生产环境部署 | 上线文档 |

### 11.2 里程碑

- **M1**：基础架构完成（第2周）
- **M2**：核心功能开发完成（第6周）
- **M3**：系统集成完成（第8周）
- **M4**：生产环境上线（第10周）

### 11.3 资源需求

#### 11.3.1 人力资源
- **产品经理**：1人，负责需求管理和项目协调
- **架构师**：1人，负责系统架构设计
- **后端开发**：2人，负责API和服务开发
- **前端开发**：2人，负责界面和交互开发
- **测试工程师**：1人，负责测试和质量保证
- **运维工程师**：1人，负责部署和运维

#### 11.3.2 硬件资源
- **开发环境**：2台服务器
- **测试环境**：4台服务器
- **生产环境**：8台服务器
- **数据库**：MySQL主从 + Redis集群

---

## 十二、风险评估

### 12.1 技术风险

#### 12.1.1 LLM API稳定性
- **风险描述**：大模型服务可能不稳定
- **影响程度**：高
- **应对措施**：
  - 建立备用LLM服务
  - 实现重试机制
  - 本地模型备用方案

#### 12.1.2 数据源复杂性
- **风险描述**：多数据源整合复杂度高
- **影响程度**：中
- **应对措施**：
  - 分阶段实施
  - 建立数据质量检查
  - 异常处理机制

#### 12.1.3 性能瓶颈
- **风险描述**：大量数据处理可能影响性能
- **影响程度**：中
- **应对措施**：
  - 性能测试和优化
  - 缓存策略
  - 异步处理

### 12.2 业务风险

#### 12.2.1 需求变更
- **风险描述**：业务需求可能频繁变更
- **影响程度**：中
- **应对措施**：
  - 敏捷开发模式
  - 模块化设计
  - 快速迭代

#### 12.2.2 数据质量
- **风险描述**：数据源质量可能影响报告质量
- **影响程度**：中
- **应对措施**：
  - 数据质量检查
  - 异常数据处理
  - 用户反馈机制

#### 12.2.3 用户接受度
- **风险描述**：新系统用户接受度未知
- **影响程度**：低
- **应对措施**：
  - 用户培训
  - 渐进式推广
  - 用户反馈收集

### 12.3 风险应对策略

#### 12.3.1 风险监控
- **定期评估**：每周评估风险状态
- **预警机制**：建立风险预警机制
- **应急预案**：制定应急预案

#### 12.3.2 风险缓解
- **技术储备**：建立技术储备
- **人员培训**：加强人员培训
- **流程优化**：优化开发流程

---

## 十三、成功标准

### 13.1 功能标准
- ✅ 支持三种数据展示逻辑
- ✅ 实现Web和Markdown混合渲染
- ✅ 提供完整的交互式功能
- ✅ 支持LLM智能分析
- ✅ 实现模块化设计

### 13.2 性能标准
- ✅ 报告生成时间 < 30秒
- ✅ 图表交互响应时间 < 1秒
- ✅ 系统并发支持 > 1000用户
- ✅ 系统可用性 > 99.5%

### 13.3 质量标准
- ✅ 数据准确性 > 99%
- ✅ 用户满意度 > 90%
- ✅ 代码覆盖率 > 80%
- ✅ 文档完整性 > 95%

### 13.4 业务标准
- ✅ 提升报告质量30%
- ✅ 减少报告生成时间50%
- ✅ 提高用户使用频率40%
- ✅ 降低系统维护成本20%

---

## 十四、附录

### 14.1 术语表

| 术语 | 定义 |
|------|------|
| LLM | Large Language Model，大语言模型 |
| Query数据 | 机器人对话的原始数据 |
| 统计数据 | 经过统计处理的指标数据 |
| 渲染引擎 | 负责内容展示的组件系统 |
| 数据钻取 | 从汇总数据查看明细数据的过程 |

### 14.2 参考文档

- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [React官方文档](https://react.dev/)
- [ECharts官方文档](https://echarts.apache.org/)
- [OpenAI API文档](https://platform.openai.com/docs)

### 14.3 变更记录

| 版本 | 日期 | 变更内容 | 变更人 |
|------|------|----------|--------|
| V1.0 | 2025-01-15 | 初始版本 | 产品经理 |
| V2.0 | 2025-01-20 | 完整架构设计 | 产品经理 |

---

**文档结束** 