# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.pytest_cache
#build/
#develop-eggs/
#dist/
#downloads/
#eggs/
#.eggs/
#lib/
#lib64/
#parts/
#sdist/
#var/
#wheels/
#*.egg-info/
#.installed.cfg
#*.egg

# Virtual Environment
venv/
env/
ENV/
# .env
# .venv
# env.bak/
# venv.bak/

# IDE
#.idea/
#.vscode/
*.swp
*.swo
.DS_Store

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Build
#dist/
#build/
#*.tsbuildinfo

# Testing
#coverage/
#.nyc_output/

# Logs
logs/
*.log 