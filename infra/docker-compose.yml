version: '3.8'

services:
  # MySQL 国内库
  mysql_cn:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ce_monitor_cn
    volumes:
      - mysql_cn_data:/var/lib/mysql
    ports:
      - "3306:3306"

  # MySQL 海外库
  mysql_os:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ce_monitor_os
    volumes:
      - mysql_os_data:/var/lib/mysql
    ports:
      - "3307:3306"

  # Redis
  redis:
    image: redis:7.0
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # API 服务
  api:
    build:
      context: ..
      dockerfile: infra/Dockerfile.api
    environment:
      - MYSQL_CN_URI=mysql+pymysql://root:${MYSQL_ROOT_PASSWORD}@mysql_cn:3306/ce_monitor_cn
      - MYSQL_OS_URI=mysql+pymysql://root:${MYSQL_ROOT_PASSWORD}@mysql_os:3306/ce_monitor_os
      - REDIS_URL=redis://redis:6379/0
      - FEISHU_APP_ID=${FEISHU_APP_ID}
      - FEISHU_APP_SECRET=${FEISHU_APP_SECRET}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
    ports:
      - "8000:8000"
    depends_on:
      - mysql_cn
      - mysql_os
      - redis

  # Celery Worker
  celery_worker:
    build:
      context: ..
      dockerfile: infra/Dockerfile.api
    command: celery -A celery_app worker --loglevel=info
    environment:
      - MYSQL_CN_URI=mysql+pymysql://root:${MYSQL_ROOT_PASSWORD}@mysql_cn:3306/ce_monitor_cn
      - MYSQL_OS_URI=mysql+pymysql://root:${MYSQL_ROOT_PASSWORD}@mysql_os:3306/ce_monitor_os
      - REDIS_URL=redis://redis:6379/0
      - FEISHU_APP_ID=${FEISHU_APP_ID}
      - FEISHU_APP_SECRET=${FEISHU_APP_SECRET}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - redis
      - mysql_cn
      - mysql_os

  # Celery Beat
  celery_beat:
    build:
      context: ..
      dockerfile: infra/Dockerfile.api
    command: celery -A celery_app beat --loglevel=info
    environment:
      - MYSQL_CN_URI=mysql+pymysql://root:${MYSQL_ROOT_PASSWORD}@mysql_cn:3306/ce_monitor_cn
      - MYSQL_OS_URI=mysql+pymysql://root:${MYSQL_ROOT_PASSWORD}@mysql_os:3306/ce_monitor_os
      - REDIS_URL=redis://redis:6379/0
      - FEISHU_APP_ID=${FEISHU_APP_ID}
      - FEISHU_APP_SECRET=${FEISHU_APP_SECRET}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - redis
      - mysql_cn
      - mysql_os

  # 前端服务
  web:
    build:
      context: ..
      dockerfile: infra/Dockerfile.web
    ports:
      - "5173:5173"
    environment:
      - VITE_API_URL=http://localhost:8000
    depends_on:
      - api

volumes:
  mysql_cn_data:
  mysql_os_data:
  redis_data: 