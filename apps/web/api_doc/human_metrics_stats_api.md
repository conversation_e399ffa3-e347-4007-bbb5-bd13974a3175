# API 文档: 机器人服务效能深度统计 (`/stats/human-metrics`) V3.0

## 1. 接口概述

本接口是机器人服务效能的核心数据中枢。它基于对飞书多维表格中 **所有功能模块** 的深度解析，提供一个三位一体的分析视图：**访客价值分析**、**机器人效能与负载** 以及 **用户互动与体验**。

该接口旨在取代零散的数据统计，为管理层提供一个全面、精准、可指导业务决策的数据仪表盘。

- **Endpoint**: `GET /stats/human-metrics`
- **Method**: `GET`
- **缓存机制**: 采用基于Redis的10分钟TTL全量刷新策略，兼顾性能与数据准确性。

---

## 2. 请求参数

| 参数名 | 类型 | 是否必须 | 描述 |
| :--- | :--- | :--- | :--- |
| `enterprise_id` | `string` | **是** | 企业ID，用于数据隔离。 |
| `device_id` | `string` | 否 | 设备ID。如果提供，则只统计该设备的数据。 |
| `start_time` | `string` | 否 | 查询开始时间 (格式: `YYYY-MM-DDTHH:MM:SS`)。将被作为**北京时间**处理。 |
| `end_time` | `string` | 否 | 查询结束时间 (格式: `YYYY-MM-DDTHH:MM:SS`)。将被作为**北京时间**处理。 |
| `group_by` | `string` | 否 | 数据聚合方式。可选值为: `total`, `day`, `hour`。默认为 `total`。 |

---

## 3. 响应结构

响应体为一个 JSON 对象，包含两个顶级字段：`query_summary` 和 `data`。

```json
{
  "query_summary": { ... },
  "data": [ ... ]
}
```

---

## 4. 核心数据对象 (`data` 数组元素) 深度解析

`data` 数组中的每个元素都是一个完整的数据分析对象，其内部结构按三大板块组织。

### **板块一: 访客价值分析 (Visitor Value Analytics)**

> **核心问题**: “谁来了？他们来干什么？我们服务得怎么样？”

#### 4.1. `visitor_demographics` (访客画像)
- **`gender_distribution_metrics`**: 性别分布 (男/女/未知 数量)。
- **`age_distribution_metrics`**: 年龄分布 (平均/最小/最大年龄，及年龄段分布)。

#### 4.2. `visitor_purpose_metrics` (访客意图洞察)
- **业务价值**: 量化机器人在核心接待场景中的作用。
- **计算来源**:
  - **面试者**: 基于 `面试/访客登记` 模块。
  - **参观者**: 基于 `参阅讲解` 模块或导航目的地。
  - **外部访客**: 综合 `访客登记` 模块和 `人脸识别` 模块的陌生人记录。

| 字段名 | 含义 |
| :--- | :--- |
| `total_external_visitors`| 总外部访客数 |
| `interview_applicants_count` | 面试者数量 |
| `tour_visitors_count` | 参观者数量 |
| `other_visitors_count` | 其他访客数量 |

#### 4.3. `detailed_visitor_log` (访客360°档案)
- **业务价值**: **本接口最核心的“聚焦到人”模块**。将每个独立访客的所有行为串联成一个完整的旅程档案。
- **数据结构**: 一个对象数组，每个对象代表一位访客的完整档案，包含姓名、是否登记、联系方式、被访人、来访目的、性别、年龄、互动总次数、首次/末次出现时间等。详见响应示例。

---

### **板块二: 机器人效能与负载 (Robot Performance & Workload)**

> **核心问题**: “机器人每天都在忙什么？哪些功能最受欢迎？哪些功能无人问津？”

#### 4.4. `robot_performance_metrics` (机器人性能指标)
- **`recognition_performance_metrics`**: 人脸识别性能 (识别成功数/失败数/成功率)。基于`人脸识别`模块的`标签信息`计算。

#### 4.5. `robot_workload_metrics` (机器人工作负载)
- **业务价值**: 全面洞察机器人工作内容和繁忙程度，为功能迭代提供数据支持。

| 字段名 | 含义 |
| :--- | :--- |
| **`module_usage_distribution`** | **核心模块使用分布**。一个数组，精确统计每个`功能模块`的使用总次数，并下钻分析其内部的`标签信息`分布。 |
| **`specific_service_counts`** | **专项服务量化统计**。一个对象，包含各关键服务的总调用次数。 |
| **`navigation_summary`** | **热门导航目的地**。一个对象，展示Top N的热门导航点及其次数。 |

---

### **板块三: 用户互动与体验 (User Engagement & Experience)**

> **核心问题**: “用户喜欢和机器人互动吗？他们对服务满意吗？”

#### 4.6. `user_engagement_metrics` (用户互动指标)
- **`feedback_summary`**: 用户反馈摘要 (例如：点赞总数)。
- **`records_by_hour_of_day`**: 小时活跃度曲线，展示一天中各时段的总互动量。

---

## 5. 响应示例 (`group_by=total`)

```json
{
  "query_summary": {
    "enterprise_id": "orion.ovs.entprise.4498860269",
    "device_id": null,
    "start_time": "2025-07-09T00:00:00+08:00",
    "end_time": "2025-07-09T23:59:59+08:00",
    "group_by": "total"
  },
  "data": [
    {
      "time_bucket": null,
      "overall_summary": {
        "total_records": 1024,
        "unique_devices": 3
      },
      "visitor_demographics": {
        "gender_distribution_metrics": { "male_count": 450, "female_count": 350, "unknown_gender_count": 224 },
        "age_distribution_metrics": { "average_age": 34.8, "minimum_age": 22, "maximum_age": 65, "age_group_distribution": { "19-30": 300, "31-45": 400, "46-60": 90, "60+": 10 } }
      },
      "visitor_purpose_metrics": {
        "total_external_visitors": 85,
        "interview_applicants_count": 20,
        "tour_visitors_count": 15,
        "other_visitors_count": 50
      },
      "detailed_visitor_log": [
        {
          "display_name": "何佳欢",
          "is_registered": true,
          "phone_number": "135****0094",
          "company": "XX科技有限公司",
          "host_person": "李笑颖",
          "team_size": "1人",
          "stated_purpose": "其他",
          "inferred_purpose": "面试",
          "gender": "female",
          "age": 29,
          "interaction_count": 5,
          "first_seen": "2025-07-09T09:30:10+08:00",
          "last_seen": "2025-07-09T10:15:25+08:00",
          "activity_summary": [ "访客登记", "带路引领", "智能问答" ]
        }
      ],
      "robot_performance_metrics": {
        "recognition_performance_metrics": { "recognized_records_count": 600, "unrecognized_records_count": 120, "recognition_success_rate_percent": 83.33 }
      },
      "robot_workload_metrics": {
        "module_usage_distribution": [
          { "module_name": "人脸识别", "total_count": 720, "tags_breakdown": { "系统操作": 720, "有效人脸": 600 } },
          { "module_name": "带路引领", "total_count": 150, "tags_breakdown": { "点击": 150 } },
          { "module_name": "智能问答", "total_count": 90, "tags_breakdown": { "点击": 80, "语音": 10 } },
          { "module_name": "面试/访客登记", "total_count": 85, "tags_breakdown": { "点击": 85 } },
          { "module_name": "翻译助手", "total_count": 25, "tags_breakdown": { "语音": 25 } }
        ],
        "specific_service_counts": {
          "navigation_count": 150,
          "qna_count": 90,
          "registration_count": 85,
          "translation_count": 25,
          "message_count": 10
        },
        "navigation_summary": {
          "会议室A01": 40,
          "面试间B02": 25,
          "前台": 15
        }
      },
      "user_engagement_metrics": {
        "feedback_summary": { "likes_count": 18 },
        "records_by_hour_of_day": [ { "hour_of_day": "09:00", "records_count": 120 }, { "hour_of_day": "10:00", "records_count": 250 } ]
      }
    }
  ]
}
```

---

## 6. 历史决策附录

本章节记录了API设计过程中的重要技术选型与权衡，以便于后续维护。

### 6.1. 全量刷新 vs. 增量更新

我们最终选择 **全量刷新** 策略，用可控的周期性性能开销，换取了 **数据的绝对准确性** 和 **系统逻辑的简洁性**，这是为了确保提供给管理层的数据100%可靠。增量更新方案因无法处理数据修改和删除，已被否决。 