# 设备监控系统

## 环境变量配置

在项目根目录创建 `.env` 文件，并配置以下环境变量：

```bash
# 高德地图配置
VITE_AMAP_KEY=your_amap_key_here
VITE_AMAP_SECURITY_CODE=your_security_code_here

# Google Maps配置
VITE_GOOGLE_MAPS_KEY=your_google_maps_key_here
```

### 获取密钥

1. 高德地图密钥：
   - 访问[高德开放平台](https://lbs.amap.com/)
   - 创建应用并获取API Key
   - 在应用设置中获取安全密钥（Security Key）

2. Google Maps密钥：
   - 访问[Google Cloud Console](https://console.cloud.google.com/)
   - 创建项目并启用Maps JavaScript API
   - 创建API密钥

## 开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 启动开发服务器
npm run dev:test
```

## 构建

```bash
npm run build
```

# 启动服务

## 1. 安装依赖
cd apps/web

# 使用 nvm 安装新版本 Node.js
-- curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
bash nvm_install.sh
source ~/.nvm/nvm.sh
nvm install 18
nvm use 18

# 安装依赖
npm install -g npm@11.4.2
npm install --legacy-peer-deps

## 2. 启动开发服务器
npm run dev

## 3. 访问服务
http://localhost:5173

