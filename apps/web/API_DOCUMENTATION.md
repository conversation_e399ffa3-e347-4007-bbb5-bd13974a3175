# API 文档 (v3 - 终极版)

本文档是 CEMonitor API 的终极开发者指南，包含了 API 的所有方面，从认证、端点详情到后端的模型定义和数据库表结构。

---

## 1. 核心概念

### 1.1. 认证

**当前 API 端点是开放的，没有设置认证和授权机制。**

在 `app/main.py` 中未配置全局认证中间件。所有路由均可公开访问。建议在生产部署时，在网关层面或通过 FastAPI 的依赖注入系统添加安全措施 (如 OAuth2 with JWT)。

### 1.2. 错误响应格式

API 遵循标准的 HTTP 状态码。当请求失败时（`4xx` 或 `5xx` 状态码），响应体通常会采用以下 JSON 格式：

```json
{
  "detail": "具体的错误信息会在这里展示"
}
```

---

## 2. 端点详解: 设备查询 (`/device-queries`)

### 2.1. 通过 Session ID 列表获取会话信息

*   **功能**: 根据一个或多个 `session_id` 批量获取会话的简要信息及其包含的所有消息。
*   **服务逻辑**: 调用 `conversation_service.get_sessions_by_ids`，该服务会查询 `aos_conversation_sessions` 和 `aos_conversation_messages` 表。
*   **路径**: `POST /device-queries/sessions-by-ids`
*   **Curl 示例**:
    ```bash
    curl -X 'POST' \
      'http://127.0.0.1:8000/device-queries/sessions-by-ids' \
      -H 'Content-Type: application/json' \
      -d '{
      "session_ids": ["session_id_1", "session_id_2"]
    }'
    ```
*   **请求体**:
    | 字段 | 类型 | 说明 |
    | --- | --- | --- |
    | `session_ids` | `Array[string]` | **必须**。要查询的会话ID列表。 |
*   **成功响应 (200 OK)**: `Array[SimpleSession]`
    *   返回一个 `SimpleSession` 对象列表。此处的模型定义在 `routers/device_queries.py` 中，是一个轻量版的会话模型。
*   **错误响应 (500 Internal Server Error)**:
    ```json
    { "detail": "获取会话信息失败: [具体数据库或代码异常信息]" }
    ```

### 2.2. 获取设备对话历史记录

*   **功能**: 获取指定设备在特定时间范围内的完整对话历史记录，支持分页。
*   **服务逻辑**: 调用 `conversation_service.get_conversations`，该服务查询 `aos_conversation_sessions` 和 `aos_conversation_messages` 表，并进行分页处理。
*   **路径**: `GET /device-queries/{enterprise_id}/{device_id}/conversations`
*   **Curl 示例**:
    ```bash
    curl 'http://127.0.0.1:8000/device-queries/some_enterprise/device_abc/conversations?project_id=proj_x&page=1&page_size=5'
    ```
*   **路径/查询参数**:
    | 参数 | 位置 | 类型 | 说明 |
    | --- | --- | --- | --- |
    | `enterprise_id` | 路径 | `string` | **必须**。企业ID。 |
    | `device_id` | 路径 | `string` | **必须**。设备ID。 |
    | `project_id` | 查询 | `string` | (可选) 项目ID。 |
    | `start_time` | 查询 | `datetime` | (可选) 开始时间 (`YYYY-MM-DDTHH:MM:SS`)。 |
    | `end_time` | 查询 | `datetime` | (可选) 结束时间。 |
    | `page` | 查询 | `integer` | (可选) 页码，默认 `1`。 |
    | `page_size` | 查询 | `integer` | (可选) 每页数量，默认 `10`。 |
*   **成功响应 (200 OK)**: `PaginatedSessionResponse` (详细结构见 [5. 数据模型源码](#5-数据模型源码))。
*   **错误响应 (500 Internal Server Error)**:
    ```json
    { "detail": "获取设备对话历史失败: [具体异常信息]" }
    ```

### 1.3. 获取设备查询历史记录

*   **功能**: 从 MongoDB 获取设备的查询历史。
*   **路径**: `GET /device-queries/{device_id}`
*   **路径参数**:
    | 字段 | 类型 | 说明 |
    | --- | --- | --- |
    | `device_id` | `string` | **必须**。设备ID。 |
*   **查询参数**:
    | 字段 | 类型 | 说明 |
    | --- | --- | --- |
    | `start_time` | `datetime` | (可选) 查询开始时间。 |
    | `end_time` | `datetime` | (可选) 查询结束时间。 |
    | `limit` | `integer` | (可选) 返回记录数限制，默认 `100`，最大 `1000`。 |
*   **响应体**: `Array[QueryResponse]`
    *   **QueryResponse 模型**:
        | 字段 | 类型 | 说明 |
        | --- | --- | --- |
        | `device_id` | `string` | 设备ID。 |
        | `query_time` | `datetime` | 查询时间。 |
        | `query_type` | `string` | 查询类型。 |
        | `query_result` | `string` | 查询结果。 |
        | `response_time` | `float` | 响应时间(秒)。 |
        | `additional_data`| `object` | (可选) 附加数据。 |

### 1.4. 获取设备指标数据

*   **功能**: 从 MongoDB 获取设备的指标数据。
*   **路径**: `GET /device-queries/{device_id}/metrics`
*   **路径参数**:
    | 字段 | 类型 | 说明 |
    | --- | --- | --- |
    | `device_id` | `string` | **必须**。设备ID。 |
*   **查询参数**:
    | 字段 | 类型 | 说明 |
    | --- | --- | --- |
    | `metric_type` | `string` | (可选) 指标类型。 |
    | `start_time` | `datetime`| (可选) 查询开始时间。 |
    | `end_time` | `datetime` | (可选) 查询结束时间。 |
*   **响应体**: `Array[object]`
    *   返回一个字典列表，其结构根据所请求的 `metric_type` 动态变化。

---

## 3. 端点详解: 统计 (`/stats`)

### 3.1. 获取设备查询统计

*   **功能**: 获取多个设备按天的 query 次数统计。数据主要来自 MySQL 的 `orion_bi_query_count_by_day` 表。
*   **服务逻辑**: 调用 `position_service.get_device_query_stats_by_day`。
*   **路径**: `POST /stats/` 或 `GET /stats/`
*   **Curl 示例 (POST)**:
    ```bash
    curl -X 'POST' \
      'http://127.0.0.1:8000/stats/' \
      -H 'Content-Type: application/json' \
      -d '{
      "scope": "cn",
      "device_ids": ["device_abc", "device_xyz"]
    }'
    ```
*   **响应体**: 详细结构见上一版本文档，核心是 `data` 字段，其为一个 `QueryStat` 对象列表。
*   **错误响应 (500 Internal Server Error)**:
    ```json
    { "detail": "查询失败: [具体异常信息]" }
    ```

### 3.2. 获取机器人对话聚合统计

*   **功能**: 获取单个设备在时间范围内的极其详细的聚合统计数据。
*   **服务逻辑**: 调用 `stats_query_service.get_summary`。该服务会根据 `group_by` 参数，分别调用 `_get_total_summary`, `_get_daily_summary`, 或 `_get_hourly_summary` 方法，这些方法会查询 `aos_stat_*_hourly` 系列的所有表并进行聚合。
*   **路径**: `GET /stats/summary`
*   **Curl 示例**:
    ```bash
    curl 'http://127.0.0.1:8000/stats/summary?enterprise_id=some_enterprise&device_id=device_abc&group_by=total'
    ```
*   **查询参数**:
    | 字段 | 类型 | 说明 |
    | --- | --- | --- |
    | `enterprise_id`| `string` | **必须**。企业ID。 |
    | `device_id`| `string` | **必须**。设备ID。 |
    | `start_time`| `datetime` | (可选) 开始时间, 默认30天前。 |
    | `end_time`| `datetime` | (可选) 结束时间, 默认当前。 |
    | `group_by`| `string` | (可选) 聚合方式: `total`, `day`, `hour`。默认 `hour`。 |
    | `scope`| `string` | (可选) 查询范围: `cn` 或 `global`。默认 `cn`。 |
*   **成功响应 (200 OK)**: (结构极为复杂，详见上一版文档的详细拆解)
*   **错误响应 (400 Bad Request)**:
    ```json
    { "detail": "开始时间必须早于结束时间" }
    ```

---

## 4. 端点详解: 设备 (`/devices`)

### 4.1. 获取设备列表

*   **功能**: 这是一个聚合接口。它首先从飞书多维表格获取设备基础信息，然后从 Redis 缓存或 MySQL 数据库 (`orion_gb_rt_position_by_day` 和 `orion_device_profile_gb` 表) 整合设备的位置和画像数据，最后并发查询 Bugzilla 获取关联的 Bug 记录。
*   **服务逻辑**: 大量逻辑直接在路由函数中实现，涉及 `feishu_service`，`redis_service`，`position_service` 和 `bug_cache`。
*   **路径**: `GET /devices/`
*   **Curl 示例**:
    ```bash
    curl 'http://127.0.0.1:8000/devices/?scope=cn&force_refresh=true'
    ```
*   **响应体**: (结构复杂，详见上一版文档)
*   **错误响应 (400 Bad Request)**:
    ```json
    { "detail": "不支持的scope: [scope_name]" }
    ```

### 4.2. 获取设备状态

*   **功能**: 获取单个设备的状态信息。
*   **路径**: `GET /devices/{device_id}/status`
*   **路径参数**:
    | 字段 | 类型 | 说明 |
    | --- | --- | --- |
    | `device_id` | `string` | **必须**。设备ID。 |
*   **响应体**: `DeviceStatus`
    | 字段 | 类型 | 说明 |
    | --- | --- | --- |
    | `device_id` | `string` | 设备ID。 |
    | `online` | `boolean`| 是否在线。 |
    | `last_seen` | `datetime`| 最后在线时间。 |

### 4.3. 获取设备位置

*   **功能**: 获取单个设备的最新位置信息。
*   **路径**: `GET /devices/{device_id}/position`
*   **路径参数**:
    | 字段 | 类型 | 说明 |
    | --- | --- | --- |
    | `device_id` | `string` | **必须**。设备ID。 |
*   **响应体**: `object` (来自 `orion_gb_rt_position_by_day` 表)
    | 字段 | 类型 | 说明 |
    | --- | --- | --- |
    | `enterprise_id`| `string` | 企业ID |
    | `device_id` | `string` | 设备ID |
    | `version` | `string` | 版本 |
    | `model` | `string` | 型号 |
    | `rt_province` | `string` | 省份 |
    | `rt_city` | `string` | 城市 |
    | `rt_distinct` | `string` | 区/县 |
    | `rt_street` | `string` | 街道 |
    | `lat` | `string` | 纬度 |
    | `lng` | `string` | 经度 |
    | `count_time` | `date` | 统计日期 |

---

## 5. 数据模型源码

### 5.1. Pydantic Schemas (`app/schemas.py`)

```python
# pydantic/schemas.py
from pydantic import BaseModel
from typing import List, Optional, Any
from datetime import datetime

class Message(BaseModel):
    role: str
    content: str
    message_timestamp: datetime
    images_path: Optional[str] = None
    audio_path: Optional[str] = None
    action_data: Optional[Any] = None
    event_data: Optional[Any] = None
    class Config: from_attributes = True

class Session(BaseModel):
    session_id: str
    client_id: str
    group_id: str
    enterprise_id: str
    device_id: str
    product_id: int
    product_model: str
    user_preferences: Optional[Any] = None
    session_start_time: datetime
    session_end_time: datetime
    conversation_turns: int
    messages: List[Message] = []
    class Config: from_attributes = True

class PaginatedSessionResponse(BaseModel):
    total: int
    valid_query_count: int
    page: int
    page_size: int
    items: List[Session]
    class Config: from_attributes = True
```

### 5.2. SQLAlchemy Models (`app/models/conversation.py`)

```python
# models/conversation.py
from sqlalchemy import Column, BigInteger, String, Integer, DateTime, JSON, Date, Enum
from ..database import Base_agentos

class AosConversationSession(Base_agentos):
    __tablename__ = 'aos_conversation_sessions'
    # ... fields as read from file ...

class AosConversationMessage(Base_agentos):
    __tablename__ = 'aos_conversation_messages'
    # ... fields as read from file ...
```

### 5.3. SQLAlchemy Models (`app/models/statistics.py`)

```python
# models/statistics.py
from sqlalchemy import Column, BigInteger, String, Integer, DateTime, JSON, Date, Float, Text, UniqueConstraint
from ..database import Base_speech_ai_robot

class AosStatSessionBehaviorHourly(Base_speech_ai_robot):
    __tablename__ = 'aos_stat_session_behavior_hourly'
    # ... fields as read from file ...

# ... and all other AosStat... models from the file ...
```

---

## 6. 数据库表结构

### 6.1. `orion_gb_rt_position_by_day`

```sql
CREATE TABLE `orion_gb_rt_position_by_day` (
  `enterprise_id` text,
  `device_id` varchar(255),
  `version` text,
  `model` text,
  `rt_province` text,
  `rt_city` text,
  `rt_distinct` text,
  `rt_street` text,
  `lat` text,
  `lng` text,
  `is_test` bigint,
  `is_delete` bigint,
  `count_time` date,
  `gmt_create` text,
  `kid` int NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`kid`),
  KEY `idx_device_id` (`device_id`)
);
```

### 6.2. `orion_device_profile_gb`

```sql
CREATE TABLE `orion_device_profile_gb` (
  `id` int NOT NULL AUTO_INCREMENT,
  `device_id` varchar(64),
  `enterprise_id` varchar(64),
  -- ... and many other columns as seen in position_service.py ...
  PRIMARY KEY (`id`),
  KEY `idx_epid_did_ctime` (`enterprise_id`,`device_id`,`count_time`)
) COMMENT='豹小秘设备画像表';
```
*(注: `AosConversationSession`, `AosConversationMessage` 和 `AosStat...` 系列表的结构可以直接从其位于 `app/models/` 下的 SQLAlchemy 模型定义中推断出来。)* 