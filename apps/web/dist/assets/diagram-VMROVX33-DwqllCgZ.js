import{s as we}from"./chunk-SKB7J2MH-DcUs-12S.js";import{_ as A,D as pe,E as me,H as Ce,e as Me,l as ie,a_ as I,d as Q,b as Te,a as Le,p as $e,q as ke,g as Ae,s as Fe,F as Ne,a$ as ze,y as Pe}from"./index-D3E3rxcK.js";import{p as _e}from"./chunk-353BL4L5-DSeTQI2p.js";import{p as De}from"./treemap-75Q7IDZK-sXfIlz-Y.js";import{o as re}from"./ordinal-Cboi1Yqb.js";import"./_baseUniq-BreRhdPu.js";import"./_basePickBy-C8BujCyK.js";import"./clone-C-6ZQwlJ.js";import"./init-Gi6I4Gst.js";function Ve(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function te(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,a=e.slice(0,r);return[a.length>1?a[0]+a.slice(2):a,+e.slice(r+1)]}function Ee(e){return e=te(Math.abs(e)),e?e[1]:NaN}function Be(e,t){return function(r,a){for(var o=r.length,i=[],c=0,h=e[0],S=0;o>0&&h>0&&(S+h+1>a&&(h=Math.max(1,a-S)),i.push(r.substring(o-=h,o+h)),!((S+=h+1)>a));)h=e[c=(c+1)%e.length];return i.reverse().join(t)}}function Re(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var We=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function oe(e){if(!(t=We.exec(e)))throw new Error("invalid format: "+e);var t;return new se({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}oe.prototype=se.prototype;function se(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}se.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function Ie(e){e:for(var t=e.length,r=1,a=-1,o;r<t;++r)switch(e[r]){case".":a=o=r;break;case"0":a===0&&(a=r),o=r;break;default:if(!+e[r])break e;a>0&&(a=0);break}return a>0?e.slice(0,a)+e.slice(o+1):e}var ge;function He(e,t){var r=te(e,t);if(!r)return e+"";var a=r[0],o=r[1],i=o-(ge=Math.max(-8,Math.min(8,Math.floor(o/3)))*3)+1,c=a.length;return i===c?a:i>c?a+new Array(i-c+1).join("0"):i>0?a.slice(0,i)+"."+a.slice(i):"0."+new Array(1-i).join("0")+te(e,Math.max(0,t+i-1))[0]}function ce(e,t){var r=te(e,t);if(!r)return e+"";var a=r[0],o=r[1];return o<0?"0."+new Array(-o).join("0")+a:a.length>o+1?a.slice(0,o+1)+"."+a.slice(o+1):a+new Array(o-a.length+2).join("0")}const de={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:Ve,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>ce(e*100,t),r:ce,s:He,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function he(e){return e}var ue=Array.prototype.map,fe=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function je(e){var t=e.grouping===void 0||e.thousands===void 0?he:Be(ue.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",a=e.currency===void 0?"":e.currency[1]+"",o=e.decimal===void 0?".":e.decimal+"",i=e.numerals===void 0?he:Re(ue.call(e.numerals,String)),c=e.percent===void 0?"%":e.percent+"",h=e.minus===void 0?"−":e.minus+"",S=e.nan===void 0?"NaN":e.nan+"";function d(p){p=oe(p);var s=p.fill,v=p.align,y=p.sign,b=p.symbol,u=p.zero,x=p.width,F=p.comma,T=p.precision,P=p.trim,M=p.type;M==="n"?(F=!0,M="g"):de[M]||(T===void 0&&(T=12),P=!0,M="g"),(u||s==="0"&&v==="=")&&(u=!0,s="0",v="=");var _=b==="$"?r:b==="#"&&/[boxX]/.test(M)?"0"+M.toLowerCase():"",Z=b==="$"?a:/[%p]/.test(M)?c:"",J=de[M],E=/[defgprs%]/.test(M);T=T===void 0?6:/[gprs]/.test(M)?Math.max(1,Math.min(21,T)):Math.max(0,Math.min(20,T));function K(f){var V=_,$=Z,n,l,g;if(M==="c")$=J(f)+$,f="";else{f=+f;var L=f<0||1/f<0;if(f=isNaN(f)?S:J(Math.abs(f),T),P&&(f=Ie(f)),L&&+f==0&&y!=="+"&&(L=!1),V=(L?y==="("?y:h:y==="-"||y==="("?"":y)+V,$=(M==="s"?fe[8+ge/3]:"")+$+(L&&y==="("?")":""),E){for(n=-1,l=f.length;++n<l;)if(g=f.charCodeAt(n),48>g||g>57){$=(g===46?o+f.slice(n+1):f.slice(n))+$,f=f.slice(0,n);break}}}F&&!u&&(f=t(f,1/0));var k=V.length+f.length+$.length,C=k<x?new Array(x-k+1).join(s):"";switch(F&&u&&(f=t(C+f,C.length?x-$.length:1/0),C=""),v){case"<":f=V+f+$+C;break;case"=":f=V+C+f+$;break;case"^":f=C.slice(0,k=C.length>>1)+V+f+$+C.slice(k);break;default:f=C+V+f+$;break}return i(f)}return K.toString=function(){return p+""},K}function m(p,s){var v=d((p=oe(p),p.type="f",p)),y=Math.max(-8,Math.min(8,Math.floor(Ee(s)/3)))*3,b=Math.pow(10,-y),u=fe[8+y/3];return function(x){return v(b*x)+u}}return{format:d,formatPrefix:m}}var ee,j;Oe({thousands:",",grouping:[3],currency:["$",""]});function Oe(e){return ee=je(e),j=ee.format,ee.formatPrefix,ee}function Ge(e){var t=0,r=e.children,a=r&&r.length;if(!a)t=1;else for(;--a>=0;)t+=r[a].value;e.value=t}function Xe(){return this.eachAfter(Ge)}function qe(e,t){let r=-1;for(const a of this)e.call(t,a,++r,this);return this}function Ye(e,t){for(var r=this,a=[r],o,i,c=-1;r=a.pop();)if(e.call(t,r,++c,this),o=r.children)for(i=o.length-1;i>=0;--i)a.push(o[i]);return this}function Ue(e,t){for(var r=this,a=[r],o=[],i,c,h,S=-1;r=a.pop();)if(o.push(r),i=r.children)for(c=0,h=i.length;c<h;++c)a.push(i[c]);for(;r=o.pop();)e.call(t,r,++S,this);return this}function Ze(e,t){let r=-1;for(const a of this)if(e.call(t,a,++r,this))return a}function Je(e){return this.eachAfter(function(t){for(var r=+e(t.data)||0,a=t.children,o=a&&a.length;--o>=0;)r+=a[o].value;t.value=r})}function Ke(e){return this.eachBefore(function(t){t.children&&t.children.sort(e)})}function Qe(e){for(var t=this,r=et(t,e),a=[t];t!==r;)t=t.parent,a.push(t);for(var o=a.length;e!==r;)a.splice(o,0,e),e=e.parent;return a}function et(e,t){if(e===t)return e;var r=e.ancestors(),a=t.ancestors(),o=null;for(e=r.pop(),t=a.pop();e===t;)o=e,e=r.pop(),t=a.pop();return o}function tt(){for(var e=this,t=[e];e=e.parent;)t.push(e);return t}function nt(){return Array.from(this)}function at(){var e=[];return this.eachBefore(function(t){t.children||e.push(t)}),e}function rt(){var e=this,t=[];return e.each(function(r){r!==e&&t.push({source:r.parent,target:r})}),t}function*it(){var e=this,t,r=[e],a,o,i;do for(t=r.reverse(),r=[];e=t.pop();)if(yield e,a=e.children)for(o=0,i=a.length;o<i;++o)r.push(a[o]);while(r.length)}function le(e,t){e instanceof Map?(e=[void 0,e],t===void 0&&(t=lt)):t===void 0&&(t=st);for(var r=new ne(e),a,o=[r],i,c,h,S;a=o.pop();)if((c=t(a.data))&&(S=(c=Array.from(c)).length))for(a.children=c,h=S-1;h>=0;--h)o.push(i=c[h]=new ne(c[h])),i.parent=a,i.depth=a.depth+1;return r.eachBefore(dt)}function ot(){return le(this).eachBefore(ct)}function st(e){return e.children}function lt(e){return Array.isArray(e)?e[1]:null}function ct(e){e.data.value!==void 0&&(e.value=e.data.value),e.data=e.data.data}function dt(e){var t=0;do e.height=t;while((e=e.parent)&&e.height<++t)}function ne(e){this.data=e,this.depth=this.height=0,this.parent=null}ne.prototype=le.prototype={constructor:ne,count:Xe,each:qe,eachAfter:Ue,eachBefore:Ye,find:Ze,sum:Je,sort:Ke,path:Qe,ancestors:tt,descendants:nt,leaves:at,links:rt,copy:ot,[Symbol.iterator]:it};function ht(e){if(typeof e!="function")throw new Error;return e}function q(){return 0}function Y(e){return function(){return e}}function ut(e){e.x0=Math.round(e.x0),e.y0=Math.round(e.y0),e.x1=Math.round(e.x1),e.y1=Math.round(e.y1)}function ft(e,t,r,a,o){for(var i=e.children,c,h=-1,S=i.length,d=e.value&&(a-t)/e.value;++h<S;)c=i[h],c.y0=r,c.y1=o,c.x0=t,c.x1=t+=c.value*d}function pt(e,t,r,a,o){for(var i=e.children,c,h=-1,S=i.length,d=e.value&&(o-r)/e.value;++h<S;)c=i[h],c.x0=t,c.x1=a,c.y0=r,c.y1=r+=c.value*d}var mt=(1+Math.sqrt(5))/2;function gt(e,t,r,a,o,i){for(var c=[],h=t.children,S,d,m=0,p=0,s=h.length,v,y,b=t.value,u,x,F,T,P,M,_;m<s;){v=o-r,y=i-a;do u=h[p++].value;while(!u&&p<s);for(x=F=u,M=Math.max(y/v,v/y)/(b*e),_=u*u*M,P=Math.max(F/_,_/x);p<s;++p){if(u+=d=h[p].value,d<x&&(x=d),d>F&&(F=d),_=u*u*M,T=Math.max(F/_,_/x),T>P){u-=d;break}P=T}c.push(S={value:u,dice:v<y,children:h.slice(m,p)}),S.dice?ft(S,r,a,o,b?a+=y*u/b:i):pt(S,r,a,b?r+=v*u/b:o,i),b-=u,m=p}return c}const yt=function e(t){function r(a,o,i,c,h){gt(t,a,o,i,c,h)}return r.ratio=function(a){return e((a=+a)>1?a:1)},r}(mt);function St(){var e=yt,t=!1,r=1,a=1,o=[0],i=q,c=q,h=q,S=q,d=q;function m(s){return s.x0=s.y0=0,s.x1=r,s.y1=a,s.eachBefore(p),o=[0],t&&s.eachBefore(ut),s}function p(s){var v=o[s.depth],y=s.x0+v,b=s.y0+v,u=s.x1-v,x=s.y1-v;u<y&&(y=u=(y+u)/2),x<b&&(b=x=(b+x)/2),s.x0=y,s.y0=b,s.x1=u,s.y1=x,s.children&&(v=o[s.depth+1]=i(s)/2,y+=d(s)-v,b+=c(s)-v,u-=h(s)-v,x-=S(s)-v,u<y&&(y=u=(y+u)/2),x<b&&(b=x=(b+x)/2),e(s,y,b,u,x))}return m.round=function(s){return arguments.length?(t=!!s,m):t},m.size=function(s){return arguments.length?(r=+s[0],a=+s[1],m):[r,a]},m.tile=function(s){return arguments.length?(e=ht(s),m):e},m.padding=function(s){return arguments.length?m.paddingInner(s).paddingOuter(s):m.paddingInner()},m.paddingInner=function(s){return arguments.length?(i=typeof s=="function"?s:Y(+s),m):i},m.paddingOuter=function(s){return arguments.length?m.paddingTop(s).paddingRight(s).paddingBottom(s).paddingLeft(s):m.paddingTop()},m.paddingTop=function(s){return arguments.length?(c=typeof s=="function"?s:Y(+s),m):c},m.paddingRight=function(s){return arguments.length?(h=typeof s=="function"?s:Y(+s),m):h},m.paddingBottom=function(s){return arguments.length?(S=typeof s=="function"?s:Y(+s),m):S},m.paddingLeft=function(s){return arguments.length?(d=typeof s=="function"?s:Y(+s),m):d},m}var O,ye=(O=class{constructor(){this.nodes=[],this.levels=new Map,this.outerNodes=[],this.classes=new Map,this.setAccTitle=Te,this.getAccTitle=Le,this.setDiagramTitle=$e,this.getDiagramTitle=ke,this.getAccDescription=Ae,this.setAccDescription=Fe}getNodes(){return this.nodes}getConfig(){const t=Ne,r=me();return pe({...t.treemap,...r.treemap??{}})}addNode(t,r){this.nodes.push(t),this.levels.set(t,r),r===0&&(this.outerNodes.push(t),this.root??(this.root=t))}getRoot(){return{name:"",children:this.outerNodes}}addClass(t,r){const a=this.classes.get(t)??{id:t,styles:[],textStyles:[]},o=r.replace(/\\,/g,"§§§").replace(/,/g,";").replace(/§§§/g,",").split(";");o&&o.forEach(i=>{ze(i)&&(a!=null&&a.textStyles?a.textStyles.push(i):a.textStyles=[i]),a!=null&&a.styles?a.styles.push(i):a.styles=[i]}),this.classes.set(t,a)}getClasses(){return this.classes}getStylesForClass(t){var r;return((r=this.classes.get(t))==null?void 0:r.styles)??[]}clear(){Pe(),this.nodes=[],this.levels=new Map,this.outerNodes=[],this.classes=new Map,this.root=void 0}},A(O,"TreeMapDB"),O);function Se(e){if(!e.length)return[];const t=[],r=[];return e.forEach(a=>{const o={name:a.name,children:a.type==="Leaf"?void 0:[]};for(o.classSelector=a==null?void 0:a.classSelector,a!=null&&a.cssCompiledStyles&&(o.cssCompiledStyles=[a.cssCompiledStyles]),a.type==="Leaf"&&a.value!==void 0&&(o.value=a.value);r.length>0&&r[r.length-1].level>=a.level;)r.pop();if(r.length===0)t.push(o);else{const i=r[r.length-1].node;i.children?i.children.push(o):i.children=[o]}a.type!=="Leaf"&&r.push({node:o,level:a.level})}),t}A(Se,"buildHierarchy");var xt=A((e,t)=>{_e(e,t);const r=[];for(const i of e.TreemapRows??[])i.$type==="ClassDefStatement"&&t.addClass(i.className??"",i.styleText??"");for(const i of e.TreemapRows??[]){const c=i.item;if(!c)continue;const h=i.indent?parseInt(i.indent):0,S=bt(c),d=c.classSelector?t.getStylesForClass(c.classSelector):[],m=d.length>0?d.join(";"):void 0,p={level:h,name:S,type:c.$type,value:c.value,classSelector:c.classSelector,cssCompiledStyles:m};r.push(p)}const a=Se(r),o=A((i,c)=>{for(const h of i)t.addNode(h,c),h.children&&h.children.length>0&&o(h.children,c+1)},"addNodesRecursively");o(a,0)},"populate"),bt=A(e=>e.name?String(e.name):"","getItemName"),xe={parser:{yy:void 0},parse:A(async e=>{var t;try{const a=await De("treemap",e);ie.debug("Treemap AST:",a);const o=(t=xe.parser)==null?void 0:t.yy;if(!(o instanceof ye))throw new Error("parser.parser?.yy was not a TreemapDB. This is due to a bug within Mermaid, please report this issue at https://github.com/mermaid-js/mermaid/issues.");xt(a,o)}catch(r){throw ie.error("Error parsing treemap:",r),r}},"parse")},vt=10,H=10,U=25,wt=A((e,t,r,a)=>{const o=a.db,i=o.getConfig(),c=i.padding??vt,h=o.getDiagramTitle(),S=o.getRoot(),{themeVariables:d}=me();if(!S)return;const m=h?30:0,p=Ce(t),s=i.nodeWidth?i.nodeWidth*H:960,v=i.nodeHeight?i.nodeHeight*H:500,y=s,b=v+m;p.attr("viewBox",`0 0 ${y} ${b}`),Me(p,b,y,i.useMaxWidth);let u;try{const n=i.valueFormat||",";if(n==="$0,0")u=A(l=>"$"+j(",")(l),"valueFormat");else if(n.startsWith("$")&&n.includes(",")){const l=/\.\d+/.exec(n),g=l?l[0]:"";u=A(L=>"$"+j(","+g)(L),"valueFormat")}else if(n.startsWith("$")){const l=n.substring(1);u=A(g=>"$"+j(l||"")(g),"valueFormat")}else u=j(n)}catch(n){ie.error("Error creating format function:",n),u=j(",")}const x=re().range(["transparent",d.cScale0,d.cScale1,d.cScale2,d.cScale3,d.cScale4,d.cScale5,d.cScale6,d.cScale7,d.cScale8,d.cScale9,d.cScale10,d.cScale11]),F=re().range(["transparent",d.cScalePeer0,d.cScalePeer1,d.cScalePeer2,d.cScalePeer3,d.cScalePeer4,d.cScalePeer5,d.cScalePeer6,d.cScalePeer7,d.cScalePeer8,d.cScalePeer9,d.cScalePeer10,d.cScalePeer11]),T=re().range([d.cScaleLabel0,d.cScaleLabel1,d.cScaleLabel2,d.cScaleLabel3,d.cScaleLabel4,d.cScaleLabel5,d.cScaleLabel6,d.cScaleLabel7,d.cScaleLabel8,d.cScaleLabel9,d.cScaleLabel10,d.cScaleLabel11]);h&&p.append("text").attr("x",y/2).attr("y",m/2).attr("class","treemapTitle").attr("text-anchor","middle").attr("dominant-baseline","middle").text(h);const P=p.append("g").attr("transform",`translate(0, ${m})`).attr("class","treemapContainer"),M=le(S).sum(n=>n.value??0).sort((n,l)=>(l.value??0)-(n.value??0)),Z=St().size([s,v]).paddingTop(n=>n.children&&n.children.length>0?U+H:0).paddingInner(c).paddingLeft(n=>n.children&&n.children.length>0?H:0).paddingRight(n=>n.children&&n.children.length>0?H:0).paddingBottom(n=>n.children&&n.children.length>0?H:0).round(!0)(M),J=Z.descendants().filter(n=>n.children&&n.children.length>0),E=P.selectAll(".treemapSection").data(J).enter().append("g").attr("class","treemapSection").attr("transform",n=>`translate(${n.x0},${n.y0})`);E.append("rect").attr("width",n=>n.x1-n.x0).attr("height",U).attr("class","treemapSectionHeader").attr("fill","none").attr("fill-opacity",.6).attr("stroke-width",.6).attr("style",n=>n.depth===0?"display: none;":""),E.append("clipPath").attr("id",(n,l)=>`clip-section-${t}-${l}`).append("rect").attr("width",n=>Math.max(0,n.x1-n.x0-12)).attr("height",U),E.append("rect").attr("width",n=>n.x1-n.x0).attr("height",n=>n.y1-n.y0).attr("class",(n,l)=>`treemapSection section${l}`).attr("fill",n=>x(n.data.name)).attr("fill-opacity",.6).attr("stroke",n=>F(n.data.name)).attr("stroke-width",2).attr("stroke-opacity",.4).attr("style",n=>{if(n.depth===0)return"display: none;";const l=I({cssCompiledStyles:n.data.cssCompiledStyles});return l.nodeStyles+";"+l.borderStyles.join(";")}),E.append("text").attr("class","treemapSectionLabel").attr("x",6).attr("y",U/2).attr("dominant-baseline","middle").text(n=>n.depth===0?"":n.data.name).attr("font-weight","bold").attr("style",n=>{if(n.depth===0)return"display: none;";const l="dominant-baseline: middle; font-size: 12px; fill:"+T(n.data.name)+"; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;",g=I({cssCompiledStyles:n.data.cssCompiledStyles});return l+g.labelStyles.replace("color:","fill:")}).each(function(n){if(n.depth===0)return;const l=Q(this),g=n.data.name;l.text(g);const L=n.x1-n.x0,k=6;let C;i.showValues!==!1&&n.value?C=L-10-30-10-k:C=L-k-6;const D=Math.max(15,C),w=l.node();if(w.getComputedTextLength()>D){const N="...";let z=g;for(;z.length>0;){if(z=g.substring(0,z.length-1),z.length===0){l.text(N),w.getComputedTextLength()>D&&l.text("");break}if(l.text(z+N),w.getComputedTextLength()<=D)break}}}),i.showValues!==!1&&E.append("text").attr("class","treemapSectionValue").attr("x",n=>n.x1-n.x0-10).attr("y",U/2).attr("text-anchor","end").attr("dominant-baseline","middle").text(n=>n.value?u(n.value):"").attr("font-style","italic").attr("style",n=>{if(n.depth===0)return"display: none;";const l="text-anchor: end; dominant-baseline: middle; font-size: 10px; fill:"+T(n.data.name)+"; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;",g=I({cssCompiledStyles:n.data.cssCompiledStyles});return l+g.labelStyles.replace("color:","fill:")});const K=Z.leaves(),f=P.selectAll(".treemapLeafGroup").data(K).enter().append("g").attr("class",(n,l)=>`treemapNode treemapLeafGroup leaf${l}${n.data.classSelector?` ${n.data.classSelector}`:""}x`).attr("transform",n=>`translate(${n.x0},${n.y0})`);f.append("rect").attr("width",n=>n.x1-n.x0).attr("height",n=>n.y1-n.y0).attr("class","treemapLeaf").attr("fill",n=>n.parent?x(n.parent.data.name):x(n.data.name)).attr("style",n=>I({cssCompiledStyles:n.data.cssCompiledStyles}).nodeStyles).attr("fill-opacity",.3).attr("stroke",n=>n.parent?x(n.parent.data.name):x(n.data.name)).attr("stroke-width",3),f.append("clipPath").attr("id",(n,l)=>`clip-${t}-${l}`).append("rect").attr("width",n=>Math.max(0,n.x1-n.x0-4)).attr("height",n=>Math.max(0,n.y1-n.y0-4)),f.append("text").attr("class","treemapLabel").attr("x",n=>(n.x1-n.x0)/2).attr("y",n=>(n.y1-n.y0)/2).attr("style",n=>{const l="text-anchor: middle; dominant-baseline: middle; font-size: 38px;fill:"+T(n.data.name)+";",g=I({cssCompiledStyles:n.data.cssCompiledStyles});return l+g.labelStyles.replace("color:","fill:")}).attr("clip-path",(n,l)=>`url(#clip-${t}-${l})`).text(n=>n.data.name).each(function(n){const l=Q(this),g=n.x1-n.x0,L=n.y1-n.y0,k=l.node(),C=4,W=g-2*C,D=L-2*C;if(W<10||D<10){l.style("display","none");return}let w=parseInt(l.style("font-size"),10);const B=8,N=28,z=.6,R=6,G=2;for(;k.getComputedTextLength()>W&&w>B;)w--,l.style("font-size",`${w}px`);let X=Math.max(R,Math.min(N,Math.round(w*z))),ae=w+G+X;for(;ae>D&&w>B&&(w--,X=Math.max(R,Math.min(N,Math.round(w*z))),!(X<R&&w===B));)l.style("font-size",`${w}px`),ae=w+G+X;l.style("font-size",`${w}px`),(k.getComputedTextLength()>W||w<B||D<w)&&l.style("display","none")}),i.showValues!==!1&&f.append("text").attr("class","treemapValue").attr("x",l=>(l.x1-l.x0)/2).attr("y",function(l){return(l.y1-l.y0)/2}).attr("style",l=>{const g="text-anchor: middle; dominant-baseline: hanging; font-size: 28px;fill:"+T(l.data.name)+";",L=I({cssCompiledStyles:l.data.cssCompiledStyles});return g+L.labelStyles.replace("color:","fill:")}).attr("clip-path",(l,g)=>`url(#clip-${t}-${g})`).text(l=>l.value?u(l.value):"").each(function(l){const g=Q(this),L=this.parentNode;if(!L){g.style("display","none");return}const k=Q(L).select(".treemapLabel");if(k.empty()||k.style("display")==="none"){g.style("display","none");return}const C=parseFloat(k.style("font-size")),W=28,D=.6,w=6,B=2,N=Math.max(w,Math.min(W,Math.round(C*D)));g.style("font-size",`${N}px`);const R=(l.y1-l.y0)/2+C/2+B;g.attr("y",R);const G=l.x1-l.x0,be=l.y1-l.y0-4,ve=G-2*4;g.node().getComputedTextLength()>ve||R+N>be||N<w?g.style("display","none"):g.style("display",null)});const $=i.diagramPadding??8;we(p,$,"flowchart",(i==null?void 0:i.useMaxWidth)||!1)},"draw"),Ct=A(function(e,t){return t.db.getClasses()},"getClasses"),Mt={draw:wt,getClasses:Ct},Tt={sectionStrokeColor:"black",sectionStrokeWidth:"1",sectionFillColor:"#efefef",leafStrokeColor:"black",leafStrokeWidth:"1",leafFillColor:"#efefef",labelColor:"black",labelFontSize:"12px",valueFontSize:"10px",valueColor:"black",titleColor:"black",titleFontSize:"14px"},Lt=A(({treemap:e}={})=>{const t=pe(Tt,e);return`
  .treemapNode.section {
    stroke: ${t.sectionStrokeColor};
    stroke-width: ${t.sectionStrokeWidth};
    fill: ${t.sectionFillColor};
  }
  .treemapNode.leaf {
    stroke: ${t.leafStrokeColor};
    stroke-width: ${t.leafStrokeWidth};
    fill: ${t.leafFillColor};
  }
  .treemapLabel {
    fill: ${t.labelColor};
    font-size: ${t.labelFontSize};
  }
  .treemapValue {
    fill: ${t.valueColor};
    font-size: ${t.valueFontSize};
  }
  .treemapTitle {
    fill: ${t.titleColor};
    font-size: ${t.titleFontSize};
  }
  `},"getStyles"),$t=Lt,Et={parser:xe,get db(){return new ye},renderer:Mt,styles:$t};export{Et as diagram};
