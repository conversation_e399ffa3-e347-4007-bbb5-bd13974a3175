import{_ as s,c as xt,l as E,d as q,V as kt,W as vt,X as _t,Y as bt,B as wt,$ as St,y as Et}from"./index-D3E3rxcK.js";import{d as nt}from"./arc-COJi3xuc.js";var X=function(){var n=s(function(f,r,a,h){for(a=a||{},h=f.length;h--;a[f[h]]=r);return a},"o"),t=[6,8,10,11,12,14,16,17,20,21],e=[1,9],l=[1,10],i=[1,11],d=[1,12],c=[1,13],g=[1,16],m=[1,17],p={trace:s(function(){},"trace"),yy:{},symbols_:{error:2,start:3,timeline:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NEWLINE:10,title:11,acc_title:12,acc_title_value:13,acc_descr:14,acc_descr_value:15,acc_descr_multiline_value:16,section:17,period_statement:18,event_statement:19,period:20,event:21,$accept:0,$end:1},terminals_:{2:"error",4:"timeline",6:"EOF",8:"SPACE",10:"NEWLINE",11:"title",12:"acc_title",13:"acc_title_value",14:"acc_descr",15:"acc_descr_value",16:"acc_descr_multiline_value",17:"section",20:"period",21:"event"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,1],[18,1],[19,1]],performAction:s(function(r,a,h,u,y,o,S){var k=o.length-1;switch(y){case 1:return o[k-1];case 2:this.$=[];break;case 3:o[k-1].push(o[k]),this.$=o[k-1];break;case 4:case 5:this.$=o[k];break;case 6:case 7:this.$=[];break;case 8:u.getCommonDb().setDiagramTitle(o[k].substr(6)),this.$=o[k].substr(6);break;case 9:this.$=o[k].trim(),u.getCommonDb().setAccTitle(this.$);break;case 10:case 11:this.$=o[k].trim(),u.getCommonDb().setAccDescription(this.$);break;case 12:u.addSection(o[k].substr(8)),this.$=o[k].substr(8);break;case 15:u.addTask(o[k],0,""),this.$=o[k];break;case 16:u.addEvent(o[k].substr(2)),this.$=o[k];break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},n(t,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:e,12:l,14:i,16:d,17:c,18:14,19:15,20:g,21:m},n(t,[2,7],{1:[2,1]}),n(t,[2,3]),{9:18,11:e,12:l,14:i,16:d,17:c,18:14,19:15,20:g,21:m},n(t,[2,5]),n(t,[2,6]),n(t,[2,8]),{13:[1,19]},{15:[1,20]},n(t,[2,11]),n(t,[2,12]),n(t,[2,13]),n(t,[2,14]),n(t,[2,15]),n(t,[2,16]),n(t,[2,4]),n(t,[2,9]),n(t,[2,10])],defaultActions:{},parseError:s(function(r,a){if(a.recoverable)this.trace(r);else{var h=new Error(r);throw h.hash=a,h}},"parseError"),parse:s(function(r){var a=this,h=[0],u=[],y=[null],o=[],S=this.table,k="",$=0,C=0,B=2,J=1,O=o.slice.call(arguments,1),v=Object.create(this.lexer),N={yy:{}};for(var L in this.yy)Object.prototype.hasOwnProperty.call(this.yy,L)&&(N.yy[L]=this.yy[L]);v.setInput(r,N.yy),N.yy.lexer=v,N.yy.parser=this,typeof v.yylloc>"u"&&(v.yylloc={});var b=v.yylloc;o.push(b);var M=v.options&&v.options.ranges;typeof N.yy.parseError=="function"?this.parseError=N.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function R(T){h.length=h.length-2*T,y.length=y.length-T,o.length=o.length-T}s(R,"popStack");function A(){var T;return T=u.pop()||v.lex()||J,typeof T!="number"&&(T instanceof Array&&(u=T,T=u.pop()),T=a.symbols_[T]||T),T}s(A,"lex");for(var w,H,I,K,V={},j,P,et,G;;){if(H=h[h.length-1],this.defaultActions[H]?I=this.defaultActions[H]:((w===null||typeof w>"u")&&(w=A()),I=S[H]&&S[H][w]),typeof I>"u"||!I.length||!I[0]){var Q="";G=[];for(j in S[H])this.terminals_[j]&&j>B&&G.push("'"+this.terminals_[j]+"'");v.showPosition?Q="Parse error on line "+($+1)+`:
`+v.showPosition()+`
Expecting `+G.join(", ")+", got '"+(this.terminals_[w]||w)+"'":Q="Parse error on line "+($+1)+": Unexpected "+(w==J?"end of input":"'"+(this.terminals_[w]||w)+"'"),this.parseError(Q,{text:v.match,token:this.terminals_[w]||w,line:v.yylineno,loc:b,expected:G})}if(I[0]instanceof Array&&I.length>1)throw new Error("Parse Error: multiple actions possible at state: "+H+", token: "+w);switch(I[0]){case 1:h.push(w),y.push(v.yytext),o.push(v.yylloc),h.push(I[1]),w=null,C=v.yyleng,k=v.yytext,$=v.yylineno,b=v.yylloc;break;case 2:if(P=this.productions_[I[1]][1],V.$=y[y.length-P],V._$={first_line:o[o.length-(P||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-(P||1)].first_column,last_column:o[o.length-1].last_column},M&&(V._$.range=[o[o.length-(P||1)].range[0],o[o.length-1].range[1]]),K=this.performAction.apply(V,[k,C,$,N.yy,I[1],y,o].concat(O)),typeof K<"u")return K;P&&(h=h.slice(0,-1*P*2),y=y.slice(0,-1*P),o=o.slice(0,-1*P)),h.push(this.productions_[I[1]][0]),y.push(V.$),o.push(V._$),et=S[h[h.length-2]][h[h.length-1]],h.push(et);break;case 3:return!0}}return!0},"parse")},x=function(){var f={EOF:1,parseError:s(function(a,h){if(this.yy.parser)this.yy.parser.parseError(a,h);else throw new Error(a)},"parseError"),setInput:s(function(r,a){return this.yy=a||this.yy||{},this._input=r,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:s(function(){var r=this._input[0];this.yytext+=r,this.yyleng++,this.offset++,this.match+=r,this.matched+=r;var a=r.match(/(?:\r\n?|\n).*/g);return a?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),r},"input"),unput:s(function(r){var a=r.length,h=r.split(/(?:\r\n?|\n)/g);this._input=r+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-a),this.offset-=a;var u=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),h.length-1&&(this.yylineno-=h.length-1);var y=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:h?(h.length===u.length?this.yylloc.first_column:0)+u[u.length-h.length].length-h[0].length:this.yylloc.first_column-a},this.options.ranges&&(this.yylloc.range=[y[0],y[0]+this.yyleng-a]),this.yyleng=this.yytext.length,this},"unput"),more:s(function(){return this._more=!0,this},"more"),reject:s(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:s(function(r){this.unput(this.match.slice(r))},"less"),pastInput:s(function(){var r=this.matched.substr(0,this.matched.length-this.match.length);return(r.length>20?"...":"")+r.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:s(function(){var r=this.match;return r.length<20&&(r+=this._input.substr(0,20-r.length)),(r.substr(0,20)+(r.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:s(function(){var r=this.pastInput(),a=new Array(r.length+1).join("-");return r+this.upcomingInput()+`
`+a+"^"},"showPosition"),test_match:s(function(r,a){var h,u,y;if(this.options.backtrack_lexer&&(y={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(y.yylloc.range=this.yylloc.range.slice(0))),u=r[0].match(/(?:\r\n?|\n).*/g),u&&(this.yylineno+=u.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:u?u[u.length-1].length-u[u.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+r[0].length},this.yytext+=r[0],this.match+=r[0],this.matches=r,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(r[0].length),this.matched+=r[0],h=this.performAction.call(this,this.yy,this,a,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),h)return h;if(this._backtrack){for(var o in y)this[o]=y[o];return!1}return!1},"test_match"),next:s(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var r,a,h,u;this._more||(this.yytext="",this.match="");for(var y=this._currentRules(),o=0;o<y.length;o++)if(h=this._input.match(this.rules[y[o]]),h&&(!a||h[0].length>a[0].length)){if(a=h,u=o,this.options.backtrack_lexer){if(r=this.test_match(h,y[o]),r!==!1)return r;if(this._backtrack){a=!1;continue}else return!1}else if(!this.options.flex)break}return a?(r=this.test_match(a,y[u]),r!==!1?r:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:s(function(){var a=this.next();return a||this.lex()},"lex"),begin:s(function(a){this.conditionStack.push(a)},"begin"),popState:s(function(){var a=this.conditionStack.length-1;return a>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:s(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:s(function(a){return a=this.conditionStack.length-1-Math.abs(a||0),a>=0?this.conditionStack[a]:"INITIAL"},"topState"),pushState:s(function(a){this.begin(a)},"pushState"),stateStackSize:s(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:s(function(a,h,u,y){switch(u){case 0:break;case 1:break;case 2:return 10;case 3:break;case 4:break;case 5:return 4;case 6:return 11;case 7:return this.begin("acc_title"),12;case 8:return this.popState(),"acc_title_value";case 9:return this.begin("acc_descr"),14;case 10:return this.popState(),"acc_descr_value";case 11:this.begin("acc_descr_multiline");break;case 12:this.popState();break;case 13:return"acc_descr_multiline_value";case 14:return 17;case 15:return 21;case 16:return 20;case 17:return 6;case 18:return"INVALID"}},"anonymous"),rules:[/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:#[^\n]*)/i,/^(?:timeline\b)/i,/^(?:title\s[^\n]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:section\s[^:\n]+)/i,/^(?::\s(?:[^:\n]|:(?!\s))+)/i,/^(?:[^#:\n]+)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[12,13],inclusive:!1},acc_descr:{rules:[10],inclusive:!1},acc_title:{rules:[8],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,9,11,14,15,16,17,18],inclusive:!0}}};return f}();p.lexer=x;function _(){this.yy={}}return s(_,"Parser"),_.prototype=p,p.Parser=_,new _}();X.parser=X;var Tt=X,at={};wt(at,{addEvent:()=>yt,addSection:()=>ht,addTask:()=>pt,addTaskOrg:()=>gt,clear:()=>ct,default:()=>It,getCommonDb:()=>ot,getSections:()=>dt,getTasks:()=>ut});var W="",lt=0,Y=[],U=[],z=[],ot=s(()=>St,"getCommonDb"),ct=s(function(){Y.length=0,U.length=0,W="",z.length=0,Et()},"clear"),ht=s(function(n){W=n,Y.push(n)},"addSection"),dt=s(function(){return Y},"getSections"),ut=s(function(){let n=it();const t=100;let e=0;for(;!n&&e<t;)n=it(),e++;return U.push(...z),U},"getTasks"),pt=s(function(n,t,e){const l={id:lt++,section:W,type:W,task:n,score:t||0,events:e?[e]:[]};z.push(l)},"addTask"),yt=s(function(n){z.find(e=>e.id===lt-1).events.push(n)},"addEvent"),gt=s(function(n){const t={section:W,type:W,description:n,task:n,classes:[]};U.push(t)},"addTaskOrg"),it=s(function(){const n=s(function(e){return z[e].processed},"compileTask");let t=!0;for(const[e,l]of z.entries())n(e),t=t&&l.processed;return t},"compileTasks"),It={clear:ct,getCommonDb:ot,addSection:ht,getSections:dt,getTasks:ut,addTask:pt,addTaskOrg:gt,addEvent:yt},Nt=12,Z=s(function(n,t){const e=n.append("rect");return e.attr("x",t.x),e.attr("y",t.y),e.attr("fill",t.fill),e.attr("stroke",t.stroke),e.attr("width",t.width),e.attr("height",t.height),e.attr("rx",t.rx),e.attr("ry",t.ry),t.class!==void 0&&e.attr("class",t.class),e},"drawRect"),Lt=s(function(n,t){const l=n.append("circle").attr("cx",t.cx).attr("cy",t.cy).attr("class","face").attr("r",15).attr("stroke-width",2).attr("overflow","visible"),i=n.append("g");i.append("circle").attr("cx",t.cx-15/3).attr("cy",t.cy-15/3).attr("r",1.5).attr("stroke-width",2).attr("fill","#666").attr("stroke","#666"),i.append("circle").attr("cx",t.cx+15/3).attr("cy",t.cy-15/3).attr("r",1.5).attr("stroke-width",2).attr("fill","#666").attr("stroke","#666");function d(m){const p=nt().startAngle(Math.PI/2).endAngle(3*(Math.PI/2)).innerRadius(7.5).outerRadius(6.8181818181818175);m.append("path").attr("class","mouth").attr("d",p).attr("transform","translate("+t.cx+","+(t.cy+2)+")")}s(d,"smile");function c(m){const p=nt().startAngle(3*Math.PI/2).endAngle(5*(Math.PI/2)).innerRadius(7.5).outerRadius(6.8181818181818175);m.append("path").attr("class","mouth").attr("d",p).attr("transform","translate("+t.cx+","+(t.cy+7)+")")}s(c,"sad");function g(m){m.append("line").attr("class","mouth").attr("stroke",2).attr("x1",t.cx-5).attr("y1",t.cy+7).attr("x2",t.cx+5).attr("y2",t.cy+7).attr("class","mouth").attr("stroke-width","1px").attr("stroke","#666")}return s(g,"ambivalent"),t.score>3?d(i):t.score<3?c(i):g(i),l},"drawFace"),$t=s(function(n,t){const e=n.append("circle");return e.attr("cx",t.cx),e.attr("cy",t.cy),e.attr("class","actor-"+t.pos),e.attr("fill",t.fill),e.attr("stroke",t.stroke),e.attr("r",t.r),e.class!==void 0&&e.attr("class",e.class),t.title!==void 0&&e.append("title").text(t.title),e},"drawCircle"),ft=s(function(n,t){const e=t.text.replace(/<br\s*\/?>/gi," "),l=n.append("text");l.attr("x",t.x),l.attr("y",t.y),l.attr("class","legend"),l.style("text-anchor",t.anchor),t.class!==void 0&&l.attr("class",t.class);const i=l.append("tspan");return i.attr("x",t.x+t.textMargin*2),i.text(e),l},"drawText"),Mt=s(function(n,t){function e(i,d,c,g,m){return i+","+d+" "+(i+c)+","+d+" "+(i+c)+","+(d+g-m)+" "+(i+c-m*1.2)+","+(d+g)+" "+i+","+(d+g)}s(e,"genPoints");const l=n.append("polygon");l.attr("points",e(t.x,t.y,50,20,7)),l.attr("class","labelBox"),t.y=t.y+t.labelMargin,t.x=t.x+.5*t.labelMargin,ft(n,t)},"drawLabel"),Ht=s(function(n,t,e){const l=n.append("g"),i=D();i.x=t.x,i.y=t.y,i.fill=t.fill,i.width=e.width,i.height=e.height,i.class="journey-section section-type-"+t.num,i.rx=3,i.ry=3,Z(l,i),mt(e)(t.text,l,i.x,i.y,i.width,i.height,{class:"journey-section section-type-"+t.num},e,t.colour)},"drawSection"),rt=-1,Pt=s(function(n,t,e){const l=t.x+e.width/2,i=n.append("g");rt++;const d=300+5*30;i.append("line").attr("id","task"+rt).attr("x1",l).attr("y1",t.y).attr("x2",l).attr("y2",d).attr("class","task-line").attr("stroke-width","1px").attr("stroke-dasharray","4 2").attr("stroke","#666"),Lt(i,{cx:l,cy:300+(5-t.score)*30,score:t.score});const c=D();c.x=t.x,c.y=t.y,c.fill=t.fill,c.width=e.width,c.height=e.height,c.class="task task-type-"+t.num,c.rx=3,c.ry=3,Z(i,c),mt(e)(t.task,i,c.x,c.y,c.width,c.height,{class:"task"},e,t.colour)},"drawTask"),At=s(function(n,t){Z(n,{x:t.startx,y:t.starty,width:t.stopx-t.startx,height:t.stopy-t.starty,fill:t.fill,class:"rect"}).lower()},"drawBackgroundRect"),Ct=s(function(){return{x:0,y:0,fill:void 0,"text-anchor":"start",width:100,height:100,textMargin:0,rx:0,ry:0}},"getTextObj"),D=s(function(){return{x:0,y:0,width:100,anchor:"start",height:100,rx:0,ry:0}},"getNoteRect"),mt=function(){function n(i,d,c,g,m,p,x,_){const f=d.append("text").attr("x",c+m/2).attr("y",g+p/2+5).style("font-color",_).style("text-anchor","middle").text(i);l(f,x)}s(n,"byText");function t(i,d,c,g,m,p,x,_,f){const{taskFontSize:r,taskFontFamily:a}=_,h=i.split(/<br\s*\/?>/gi);for(let u=0;u<h.length;u++){const y=u*r-r*(h.length-1)/2,o=d.append("text").attr("x",c+m/2).attr("y",g).attr("fill",f).style("text-anchor","middle").style("font-size",r).style("font-family",a);o.append("tspan").attr("x",c+m/2).attr("dy",y).text(h[u]),o.attr("y",g+p/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),l(o,x)}}s(t,"byTspan");function e(i,d,c,g,m,p,x,_){const f=d.append("switch"),a=f.append("foreignObject").attr("x",c).attr("y",g).attr("width",m).attr("height",p).attr("position","fixed").append("xhtml:div").style("display","table").style("height","100%").style("width","100%");a.append("div").attr("class","label").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(i),t(i,f,c,g,m,p,x,_),l(a,x)}s(e,"byFo");function l(i,d){for(const c in d)c in d&&i.attr(c,d[c])}return s(l,"_setTextAttrs"),function(i){return i.textPlacement==="fo"?e:i.textPlacement==="old"?n:t}}(),Rt=s(function(n){n.append("defs").append("marker").attr("id","arrowhead").attr("refX",5).attr("refY",2).attr("markerWidth",6).attr("markerHeight",4).attr("orient","auto").append("path").attr("d","M 0,0 V 4 L6,2 Z")},"initGraphics");function tt(n,t){n.each(function(){var e=q(this),l=e.text().split(/(\s+|<br>)/).reverse(),i,d=[],c=1.1,g=e.attr("y"),m=parseFloat(e.attr("dy")),p=e.text(null).append("tspan").attr("x",0).attr("y",g).attr("dy",m+"em");for(let x=0;x<l.length;x++)i=l[l.length-1-x],d.push(i),p.text(d.join(" ").trim()),(p.node().getComputedTextLength()>t||i==="<br>")&&(d.pop(),p.text(d.join(" ").trim()),i==="<br>"?d=[""]:d=[i],p=e.append("tspan").attr("x",0).attr("y",g).attr("dy",c+"em").text(i))})}s(tt,"wrap");var Ft=s(function(n,t,e,l){var _;const i=e%Nt-1,d=n.append("g");t.section=i,d.attr("class",(t.class?t.class+" ":"")+"timeline-node "+("section-"+i));const c=d.append("g"),g=d.append("g"),p=g.append("text").text(t.descr).attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle").call(tt,t.width).node().getBBox(),x=(_=l.fontSize)!=null&&_.replace?l.fontSize.replace("px",""):l.fontSize;return t.height=p.height+x*1.1*.5+t.padding,t.height=Math.max(t.height,t.maxHeight),t.width=t.width+2*t.padding,g.attr("transform","translate("+t.width/2+", "+t.padding/2+")"),Wt(c,t,i,l),t},"drawNode"),Vt=s(function(n,t,e){var g;const l=n.append("g"),d=l.append("text").text(t.descr).attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle").call(tt,t.width).node().getBBox(),c=(g=e.fontSize)!=null&&g.replace?e.fontSize.replace("px",""):e.fontSize;return l.remove(),d.height+c*1.1*.5+t.padding},"getVirtualNodeHeight"),Wt=s(function(n,t,e){n.append("path").attr("id","node-"+t.id).attr("class","node-bkg node-"+t.type).attr("d",`M0 ${t.height-5} v${-t.height+2*5} q0,-5 5,-5 h${t.width-2*5} q5,0 5,5 v${t.height-5} H0 Z`),n.append("line").attr("class","node-line-"+e).attr("x1",0).attr("y1",t.height).attr("x2",t.width).attr("y2",t.height)},"defaultBkg"),F={drawRect:Z,drawCircle:$t,drawSection:Ht,drawText:ft,drawLabel:Mt,drawTask:Pt,drawBackgroundRect:At,getTextObj:Ct,getNoteRect:D,initGraphics:Rt,drawNode:Ft,getVirtualNodeHeight:Vt},zt=s(function(n,t,e,l){var O,v,N;const i=xt(),d=((O=i.timeline)==null?void 0:O.leftMargin)??50;E.debug("timeline",l.db);const c=i.securityLevel;let g;c==="sandbox"&&(g=q("#i"+t));const p=(c==="sandbox"?q(g.nodes()[0].contentDocument.body):q("body")).select("#"+t);p.append("g");const x=l.db.getTasks(),_=l.db.getCommonDb().getDiagramTitle();E.debug("task",x),F.initGraphics(p);const f=l.db.getSections();E.debug("sections",f);let r=0,a=0,h=0,u=0,y=50+d,o=50;u=50;let S=0,k=!0;f.forEach(function(L){const b={number:S,descr:L,section:S,width:150,padding:20,maxHeight:r},M=F.getVirtualNodeHeight(p,b,i);E.debug("sectionHeight before draw",M),r=Math.max(r,M+20)});let $=0,C=0;E.debug("tasks.length",x.length);for(const[L,b]of x.entries()){const M={number:L,descr:b,section:b.section,width:150,padding:20,maxHeight:a},R=F.getVirtualNodeHeight(p,M,i);E.debug("taskHeight before draw",R),a=Math.max(a,R+20),$=Math.max($,b.events.length);let A=0;for(const w of b.events){const H={descr:w,section:b.section,number:b.section,width:150,padding:20,maxHeight:50};A+=F.getVirtualNodeHeight(p,H,i)}b.events.length>0&&(A+=(b.events.length-1)*10),C=Math.max(C,A)}E.debug("maxSectionHeight before draw",r),E.debug("maxTaskHeight before draw",a),f&&f.length>0?f.forEach(L=>{const b=x.filter(w=>w.section===L),M={number:S,descr:L,section:S,width:200*Math.max(b.length,1)-50,padding:20,maxHeight:r};E.debug("sectionNode",M);const R=p.append("g"),A=F.drawNode(R,M,S,i);E.debug("sectionNode output",A),R.attr("transform",`translate(${y}, ${u})`),o+=r+50,b.length>0&&st(p,b,S,y,o,a,i,$,C,r,!1),y+=200*Math.max(b.length,1),o=u,S++}):(k=!1,st(p,x,S,y,o,a,i,$,C,r,!0));const B=p.node().getBBox();E.debug("bounds",B),_&&p.append("text").text(_).attr("x",B.width/2-d).attr("font-size","4ex").attr("font-weight","bold").attr("y",20),h=k?r+a+150:a+100,p.append("g").attr("class","lineWrapper").append("line").attr("x1",d).attr("y1",h).attr("x2",B.width+3*d).attr("y2",h).attr("stroke-width",4).attr("stroke","black").attr("marker-end","url(#arrowhead)"),kt(void 0,p,((v=i.timeline)==null?void 0:v.padding)??50,((N=i.timeline)==null?void 0:N.useMaxWidth)??!1)},"draw"),st=s(function(n,t,e,l,i,d,c,g,m,p,x){var _;for(const f of t){const r={descr:f.task,section:e,number:e,width:150,padding:20,maxHeight:d};E.debug("taskNode",r);const a=n.append("g").attr("class","taskWrapper"),u=F.drawNode(a,r,e,c).height;if(E.debug("taskHeight after draw",u),a.attr("transform",`translate(${l}, ${i})`),d=Math.max(d,u),f.events){const y=n.append("g").attr("class","lineWrapper");let o=d;i+=100,o=o+Bt(n,f.events,e,l,i,c),i-=100,y.append("line").attr("x1",l+190/2).attr("y1",i+d).attr("x2",l+190/2).attr("y2",i+d+100+m+100).attr("stroke-width",2).attr("stroke","black").attr("marker-end","url(#arrowhead)").attr("stroke-dasharray","5,5")}l=l+200,x&&!((_=c.timeline)!=null&&_.disableMulticolor)&&e++}i=i-10},"drawTasks"),Bt=s(function(n,t,e,l,i,d){let c=0;const g=i;i=i+100;for(const m of t){const p={descr:m,section:e,number:e,width:150,padding:20,maxHeight:50};E.debug("eventNode",p);const x=n.append("g").attr("class","eventWrapper"),f=F.drawNode(x,p,e,d).height;c=c+f,x.attr("transform",`translate(${l}, ${i})`),i=i+10+f}return i=g,c},"drawEvents"),Ot={setConf:s(()=>{},"setConf"),draw:zt},jt=s(n=>{let t="";for(let e=0;e<n.THEME_COLOR_LIMIT;e++)n["lineColor"+e]=n["lineColor"+e]||n["cScaleInv"+e],vt(n["lineColor"+e])?n["lineColor"+e]=_t(n["lineColor"+e],20):n["lineColor"+e]=bt(n["lineColor"+e],20);for(let e=0;e<n.THEME_COLOR_LIMIT;e++){const l=""+(17-3*e);t+=`
    .section-${e-1} rect, .section-${e-1} path, .section-${e-1} circle, .section-${e-1} path  {
      fill: ${n["cScale"+e]};
    }
    .section-${e-1} text {
     fill: ${n["cScaleLabel"+e]};
    }
    .node-icon-${e-1} {
      font-size: 40px;
      color: ${n["cScaleLabel"+e]};
    }
    .section-edge-${e-1}{
      stroke: ${n["cScale"+e]};
    }
    .edge-depth-${e-1}{
      stroke-width: ${l};
    }
    .section-${e-1} line {
      stroke: ${n["cScaleInv"+e]} ;
      stroke-width: 3;
    }

    .lineWrapper line{
      stroke: ${n["cScaleLabel"+e]} ;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }
    `}return t},"genSections"),Gt=s(n=>`
  .edge {
    stroke-width: 3;
  }
  ${jt(n)}
  .section-root rect, .section-root path, .section-root circle  {
    fill: ${n.git0};
  }
  .section-root text {
    fill: ${n.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .eventWrapper  {
   filter: brightness(120%);
  }
`,"getStyles"),qt=Gt,Jt={db:at,renderer:Ot,parser:Tt,styles:qt};export{Jt as diagram};
