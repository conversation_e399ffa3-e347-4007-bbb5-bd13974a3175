import{aV as an,aW as I,aX as Y,aY as fn,aZ as Z}from"./index-D3E3rxcK.js";import{i as cn}from"./init-Gi6I4Gst.js";function hn(n,t){t||(t=[]);var e=n?Math.min(t.length,n.length):0,r=t.slice(),i;return function(f){for(i=0;i<e;++i)r[i]=n[i]*(1-f)+t[i]*f;return r}}function ln(n){return ArrayBuffer.isView(n)&&!(n instanceof DataView)}function mn(n,t){var e=t?t.length:0,r=n?Math.min(e,n.length):0,i=new Array(r),f=new Array(e),u;for(u=0;u<r;++u)i[u]=C(n[u],t[u]);for(;u<e;++u)f[u]=t[u];return function(a){for(u=0;u<r;++u)f[u]=i[u](a);return f}}function sn(n,t){var e=new Date;return n=+n,t=+t,function(r){return e.setTime(n*(1-r)+t*r),e}}function dn(n,t){var e={},r={},i;(n===null||typeof n!="object")&&(n={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in n?e[i]=C(n[i],t[i]):r[i]=t[i];return function(f){for(i in e)r[i]=e[i](f);return r}}function C(n,t){var e=typeof t,r;return t==null||e==="boolean"?an(t):(e==="number"?I:e==="string"?(r=Z(t))?(t=r,Y):fn:t instanceof Z?Y:t instanceof Date?sn:ln(t)?hn:Array.isArray(t)?mn:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?dn:I)(n,t)}function gn(n,t){return n=+n,t=+t,function(e){return Math.round(n*(1-e)+t*e)}}function F(n,t){return n==null||t==null?NaN:n<t?-1:n>t?1:n>=t?0:NaN}function yn(n,t){return n==null||t==null?NaN:t<n?-1:t>n?1:t>=n?0:NaN}function _(n){let t,e,r;n.length!==2?(t=F,e=(a,c)=>F(n(a),c),r=(a,c)=>n(a)-c):(t=n===F||n===yn?n:Mn,e=n,r=n);function i(a,c,o=0,s=a.length){if(o<s){if(t(c,c)!==0)return s;do{const h=o+s>>>1;e(a[h],c)<0?o=h+1:s=h}while(o<s)}return o}function f(a,c,o=0,s=a.length){if(o<s){if(t(c,c)!==0)return s;do{const h=o+s>>>1;e(a[h],c)<=0?o=h+1:s=h}while(o<s)}return o}function u(a,c,o=0,s=a.length){const h=i(a,c,o,s-1);return h>o&&r(a[h-1],c)>-r(a[h],c)?h-1:h}return{left:i,center:u,right:f}}function Mn(){return 0}function pn(n){return n===null?NaN:+n}const wn=_(F),Nn=wn.right;_(pn).center;const kn=Math.sqrt(50),xn=Math.sqrt(10),An=Math.sqrt(2);function R(n,t,e){const r=(t-n)/Math.max(0,e),i=Math.floor(Math.log10(r)),f=r/Math.pow(10,i),u=f>=kn?10:f>=xn?5:f>=An?2:1;let a,c,o;return i<0?(o=Math.pow(10,-i)/u,a=Math.round(n*o),c=Math.round(t*o),a/o<n&&++a,c/o>t&&--c,o=-o):(o=Math.pow(10,i)*u,a=Math.round(n/o),c=Math.round(t/o),a*o<n&&++a,c*o>t&&--c),c<a&&.5<=e&&e<2?R(n,t,e*2):[a,c,o]}function vn(n,t,e){if(t=+t,n=+n,e=+e,!(e>0))return[];if(n===t)return[n];const r=t<n,[i,f,u]=r?R(t,n,e):R(n,t,e);if(!(f>=i))return[];const a=f-i+1,c=new Array(a);if(r)if(u<0)for(let o=0;o<a;++o)c[o]=(f-o)/-u;else for(let o=0;o<a;++o)c[o]=(f-o)*u;else if(u<0)for(let o=0;o<a;++o)c[o]=(i+o)/-u;else for(let o=0;o<a;++o)c[o]=(i+o)*u;return c}function L(n,t,e){return t=+t,n=+n,e=+e,R(n,t,e)[2]}function Sn(n,t,e){t=+t,n=+n,e=+e;const r=t<n,i=r?L(t,n,e):L(n,t,e);return(r?-1:1)*(i<0?1/-i:i)}function bn(n){return function(){return n}}function jn(n){return+n}var U=[0,1];function A(n){return n}function q(n,t){return(t-=n=+n)?function(e){return(e-n)/t}:bn(isNaN(t)?NaN:.5)}function Pn(n,t){var e;return n>t&&(e=n,n=t,t=e),function(r){return Math.max(n,Math.min(t,r))}}function zn(n,t,e){var r=n[0],i=n[1],f=t[0],u=t[1];return i<r?(r=q(i,r),f=e(u,f)):(r=q(r,i),f=e(f,u)),function(a){return f(r(a))}}function $n(n,t,e){var r=Math.min(n.length,t.length)-1,i=new Array(r),f=new Array(r),u=-1;for(n[r]<n[0]&&(n=n.slice().reverse(),t=t.slice().reverse());++u<r;)i[u]=q(n[u],n[u+1]),f[u]=e(t[u],t[u+1]);return function(a){var c=Nn(n,a,1,r)-1;return f[c](i[c](a))}}function Fn(n,t){return t.domain(n.domain()).range(n.range()).interpolate(n.interpolate()).clamp(n.clamp()).unknown(n.unknown())}function Rn(){var n=U,t=U,e=C,r,i,f,u=A,a,c,o;function s(){var m=Math.min(n.length,t.length);return u!==A&&(u=Pn(n[0],n[m-1])),a=m>2?$n:zn,c=o=null,h}function h(m){return m==null||isNaN(m=+m)?f:(c||(c=a(n.map(r),t,e)))(r(u(m)))}return h.invert=function(m){return u(i((o||(o=a(t,n.map(r),I)))(m)))},h.domain=function(m){return arguments.length?(n=Array.from(m,jn),s()):n.slice()},h.range=function(m){return arguments.length?(t=Array.from(m),s()):t.slice()},h.rangeRound=function(m){return t=Array.from(m),e=gn,s()},h.clamp=function(m){return arguments.length?(u=m?!0:A,s()):u!==A},h.interpolate=function(m){return arguments.length?(e=m,s()):e},h.unknown=function(m){return arguments.length?(f=m,h):f},function(m,p){return r=m,i=p,s()}}function En(){return Rn()(A,A)}function Dn(n){return Math.abs(n=Math.round(n))>=1e21?n.toLocaleString("en").replace(/,/g,""):n.toString(10)}function E(n,t){if((e=(n=t?n.toExponential(t-1):n.toExponential()).indexOf("e"))<0)return null;var e,r=n.slice(0,e);return[r.length>1?r[0]+r.slice(2):r,+n.slice(e+1)]}function v(n){return n=E(Math.abs(n)),n?n[1]:NaN}function Tn(n,t){return function(e,r){for(var i=e.length,f=[],u=0,a=n[0],c=0;i>0&&a>0&&(c+a+1>r&&(a=Math.max(1,r-c)),f.push(e.substring(i-=a,i+a)),!((c+=a+1)>r));)a=n[u=(u+1)%n.length];return f.reverse().join(t)}}function In(n){return function(t){return t.replace(/[0-9]/g,function(e){return n[+e]})}}var Ln=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function D(n){if(!(t=Ln.exec(n)))throw new Error("invalid format: "+n);var t;return new V({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}D.prototype=V.prototype;function V(n){this.fill=n.fill===void 0?" ":n.fill+"",this.align=n.align===void 0?">":n.align+"",this.sign=n.sign===void 0?"-":n.sign+"",this.symbol=n.symbol===void 0?"":n.symbol+"",this.zero=!!n.zero,this.width=n.width===void 0?void 0:+n.width,this.comma=!!n.comma,this.precision=n.precision===void 0?void 0:+n.precision,this.trim=!!n.trim,this.type=n.type===void 0?"":n.type+""}V.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function qn(n){n:for(var t=n.length,e=1,r=-1,i;e<t;++e)switch(n[e]){case".":r=i=e;break;case"0":r===0&&(r=e),i=e;break;default:if(!+n[e])break n;r>0&&(r=0);break}return r>0?n.slice(0,r)+n.slice(i+1):n}var nn;function Cn(n,t){var e=E(n,t);if(!e)return n+"";var r=e[0],i=e[1],f=i-(nn=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,u=r.length;return f===u?r:f>u?r+new Array(f-u+1).join("0"):f>0?r.slice(0,f)+"."+r.slice(f):"0."+new Array(1-f).join("0")+E(n,Math.max(0,t+f-1))[0]}function W(n,t){var e=E(n,t);if(!e)return n+"";var r=e[0],i=e[1];return i<0?"0."+new Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+new Array(i-r.length+2).join("0")}const H={"%":function(n,t){return(n*100).toFixed(t)},b:function(n){return Math.round(n).toString(2)},c:function(n){return n+""},d:Dn,e:function(n,t){return n.toExponential(t)},f:function(n,t){return n.toFixed(t)},g:function(n,t){return n.toPrecision(t)},o:function(n){return Math.round(n).toString(8)},p:function(n,t){return W(n*100,t)},r:W,s:Cn,X:function(n){return Math.round(n).toString(16).toUpperCase()},x:function(n){return Math.round(n).toString(16)}};function J(n){return n}var K=Array.prototype.map,Q=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Vn(n){var t=n.grouping===void 0||n.thousands===void 0?J:Tn(K.call(n.grouping,Number),n.thousands+""),e=n.currency===void 0?"":n.currency[0]+"",r=n.currency===void 0?"":n.currency[1]+"",i=n.decimal+"",f=n.numerals===void 0?J:In(K.call(n.numerals,String)),u=n.percent===void 0?"%":n.percent+"",a=n.minus+"",c=n.nan===void 0?"NaN":n.nan+"";function o(h){h=D(h);var m=h.fill,p=h.align,y=h.sign,S=h.symbol,k=h.zero,b=h.width,T=h.comma,w=h.precision,X=h.trim,d=h.type;d==="n"?(T=!0,d="g"):H[d]||(w===void 0&&(w=12),X=!0,d="g"),(k||m==="0"&&p==="=")&&(k=!0,m="0",p="=");var en=S==="$"?e:S==="#"&&/[boxX]/.test(d)?"0"+d.toLowerCase():"",on=S==="$"?r:/[%p]/.test(d)?u:"",B=H[d],un=/[defgprs%]/.test(d);w=w===void 0?6:/[gprs]/.test(d)?Math.max(1,Math.min(21,w)):Math.max(0,Math.min(20,w));function G(l){var N=en,g=on,x,O,j;if(d==="c")g=B(l)+g,l="";else{l=+l;var P=l<0||1/l<0;if(l=isNaN(l)?c:B(Math.abs(l),w),X&&(l=qn(l)),P&&+l==0&&y!=="+"&&(P=!1),N=(P?y==="("?y:a:y==="-"||y==="("?"":y)+N,g=(d==="s"?Q[8+nn/3]:"")+g+(P&&y==="("?")":""),un){for(x=-1,O=l.length;++x<O;)if(j=l.charCodeAt(x),48>j||j>57){g=(j===46?i+l.slice(x+1):l.slice(x))+g,l=l.slice(0,x);break}}}T&&!k&&(l=t(l,1/0));var z=N.length+l.length+g.length,M=z<b?new Array(b-z+1).join(m):"";switch(T&&k&&(l=t(M+l,M.length?b-g.length:1/0),M=""),p){case"<":l=N+l+g+M;break;case"=":l=N+M+l+g;break;case"^":l=M.slice(0,z=M.length>>1)+N+l+g+M.slice(z);break;default:l=M+N+l+g;break}return f(l)}return G.toString=function(){return h+""},G}function s(h,m){var p=o((h=D(h),h.type="f",h)),y=Math.max(-8,Math.min(8,Math.floor(v(m)/3)))*3,S=Math.pow(10,-y),k=Q[8+y/3];return function(b){return p(S*b)+k}}return{format:o,formatPrefix:s}}var $,tn,rn;Xn({decimal:".",thousands:",",grouping:[3],currency:["$",""],minus:"-"});function Xn(n){return $=Vn(n),tn=$.format,rn=$.formatPrefix,$}function Bn(n){return Math.max(0,-v(Math.abs(n)))}function Gn(n,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(v(t)/3)))*3-v(Math.abs(n)))}function On(n,t){return n=Math.abs(n),t=Math.abs(t)-n,Math.max(0,v(t)-v(n))+1}function Yn(n,t,e,r){var i=Sn(n,t,e),f;switch(r=D(r??",f"),r.type){case"s":{var u=Math.max(Math.abs(n),Math.abs(t));return r.precision==null&&!isNaN(f=Gn(i,u))&&(r.precision=f),rn(r,u)}case"":case"e":case"g":case"p":case"r":{r.precision==null&&!isNaN(f=On(i,Math.max(Math.abs(n),Math.abs(t))))&&(r.precision=f-(r.type==="e"));break}case"f":case"%":{r.precision==null&&!isNaN(f=Bn(i))&&(r.precision=f-(r.type==="%")*2);break}}return tn(r)}function Zn(n){var t=n.domain;return n.ticks=function(e){var r=t();return vn(r[0],r[r.length-1],e??10)},n.tickFormat=function(e,r){var i=t();return Yn(i[0],i[i.length-1],e??10,r)},n.nice=function(e){e==null&&(e=10);var r=t(),i=0,f=r.length-1,u=r[i],a=r[f],c,o,s=10;for(a<u&&(o=u,u=a,a=o,o=i,i=f,f=o);s-- >0;){if(o=L(u,a,e),o===c)return r[i]=u,r[f]=a,t(r);if(o>0)u=Math.floor(u/o)*o,a=Math.ceil(a/o)*o;else if(o<0)u=Math.ceil(u*o)/o,a=Math.floor(a*o)/o;else break;c=o}return n},n}function Un(){var n=En();return n.copy=function(){return Fn(n,Un())},cn.apply(n,arguments),Zn(n)}export{Fn as a,_ as b,En as c,Un as l,Sn as t};
