import{b0 as qn,b1 as Er,b2 as Bn,b3 as Xn,b4 as Qn,b5 as xt,b6 as Hr,aH as Gt,aI as jt,_ as d,g as Or,s as Nr,q as Vr,p as zr,a as Pr,b as Rr,c as He,d as ot,e as Zr,b7 as ce,l as ht,k as qr,j as Br,y as Xr,u as Qr}from"./index-D3E3rxcK.js";import{b as Gr,t as pn,c as jr,a as Jr,l as Kr}from"./linear-IZ0Mthlb.js";import{i as ea}from"./init-Gi6I4Gst.js";function ta(e,t){let n;if(t===void 0)for(const r of e)r!=null&&(n<r||n===void 0&&r>=r)&&(n=r);else{let r=-1;for(let i of e)(i=t(i,++r,e))!=null&&(n<i||n===void 0&&i>=i)&&(n=i)}return n}function na(e,t){let n;if(t===void 0)for(const r of e)r!=null&&(n>r||n===void 0&&r>=r)&&(n=r);else{let r=-1;for(let i of e)(i=t(i,++r,e))!=null&&(n>i||n===void 0&&i>=i)&&(n=i)}return n}function ra(e){return e}var ct=1,Ct=2,Ot=3,ut=4,bn=1e-6;function aa(e){return"translate("+e+",0)"}function ia(e){return"translate(0,"+e+")"}function sa(e){return t=>+e(t)}function oa(e,t){return t=Math.max(0,e.bandwidth()-t*2)/2,e.round()&&(t=Math.round(t)),n=>+e(n)+t}function ua(){return!this.__axis}function Gn(e,t){var n=[],r=null,i=null,a=6,s=6,f=3,D=typeof window<"u"&&window.devicePixelRatio>1?0:.5,p=e===ct||e===ut?-1:1,k=e===ut||e===Ct?"x":"y",A=e===ct||e===Ot?aa:ia;function S(U){var G=r??(t.ticks?t.ticks.apply(t,n):t.domain()),O=i??(t.tickFormat?t.tickFormat.apply(t,n):ra),F=Math.max(a,0)+f,E=t.range(),z=+E[0]+D,W=+E[E.length-1]+D,R=(t.bandwidth?oa:sa)(t.copy(),D),J=U.selection?U.selection():U,w=J.selectAll(".domain").data([null]),N=J.selectAll(".tick").data(G,t).order(),M=N.exit(),$=N.enter().append("g").attr("class","tick"),_=N.select("line"),Y=N.select("text");w=w.merge(w.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),N=N.merge($),_=_.merge($.append("line").attr("stroke","currentColor").attr(k+"2",p*a)),Y=Y.merge($.append("text").attr("fill","currentColor").attr(k,p*F).attr("dy",e===ct?"0em":e===Ot?"0.71em":"0.32em")),U!==J&&(w=w.transition(U),N=N.transition(U),_=_.transition(U),Y=Y.transition(U),M=M.transition(U).attr("opacity",bn).attr("transform",function(b){return isFinite(b=R(b))?A(b+D):this.getAttribute("transform")}),$.attr("opacity",bn).attr("transform",function(b){var L=this.parentNode.__axis;return A((L&&isFinite(L=L(b))?L:R(b))+D)})),M.remove(),w.attr("d",e===ut||e===Ct?s?"M"+p*s+","+z+"H"+D+"V"+W+"H"+p*s:"M"+D+","+z+"V"+W:s?"M"+z+","+p*s+"V"+D+"H"+W+"V"+p*s:"M"+z+","+D+"H"+W),N.attr("opacity",1).attr("transform",function(b){return A(R(b)+D)}),_.attr(k+"2",p*a),Y.attr(k,p*F).text(O),J.filter(ua).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",e===Ct?"start":e===ut?"end":"middle"),J.each(function(){this.__axis=R})}return S.scale=function(U){return arguments.length?(t=U,S):t},S.ticks=function(){return n=Array.from(arguments),S},S.tickArguments=function(U){return arguments.length?(n=U==null?[]:Array.from(U),S):n.slice()},S.tickValues=function(U){return arguments.length?(r=U==null?null:Array.from(U),S):r&&r.slice()},S.tickFormat=function(U){return arguments.length?(i=U,S):i},S.tickSize=function(U){return arguments.length?(a=s=+U,S):a},S.tickSizeInner=function(U){return arguments.length?(a=+U,S):a},S.tickSizeOuter=function(U){return arguments.length?(s=+U,S):s},S.tickPadding=function(U){return arguments.length?(f=+U,S):f},S.offset=function(U){return arguments.length?(D=+U,S):D},S}function ca(e){return Gn(ct,e)}function la(e){return Gn(Ot,e)}const fa=Math.PI/180,ha=180/Math.PI,gt=18,jn=.96422,Jn=1,Kn=.82521,er=4/29,Oe=6/29,tr=3*Oe*Oe,ga=Oe*Oe*Oe;function nr(e){if(e instanceof me)return new me(e.l,e.a,e.b,e.opacity);if(e instanceof ye)return rr(e);e instanceof qn||(e=Er(e));var t=Ut(e.r),n=Ut(e.g),r=Ut(e.b),i=wt((.2225045*t+.7168786*n+.0606169*r)/Jn),a,s;return t===n&&n===r?a=s=i:(a=wt((.4360747*t+.3850649*n+.1430804*r)/jn),s=wt((.0139322*t+.0971045*n+.7141733*r)/Kn)),new me(116*i-16,500*(a-i),200*(i-s),e.opacity)}function ma(e,t,n,r){return arguments.length===1?nr(e):new me(e,t,n,r??1)}function me(e,t,n,r){this.l=+e,this.a=+t,this.b=+n,this.opacity=+r}Bn(me,ma,Xn(Qn,{brighter(e){return new me(this.l+gt*(e??1),this.a,this.b,this.opacity)},darker(e){return new me(this.l-gt*(e??1),this.a,this.b,this.opacity)},rgb(){var e=(this.l+16)/116,t=isNaN(this.a)?e:e+this.a/500,n=isNaN(this.b)?e:e-this.b/200;return t=jn*Mt(t),e=Jn*Mt(e),n=Kn*Mt(n),new qn(Dt(3.1338561*t-1.6168667*e-.4906146*n),Dt(-.9787684*t+1.9161415*e+.033454*n),Dt(.0719453*t-.2289914*e+1.4052427*n),this.opacity)}}));function wt(e){return e>ga?Math.pow(e,1/3):e/tr+er}function Mt(e){return e>Oe?e*e*e:tr*(e-er)}function Dt(e){return 255*(e<=.0031308?12.92*e:1.055*Math.pow(e,1/2.4)-.055)}function Ut(e){return(e/=255)<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4)}function da(e){if(e instanceof ye)return new ye(e.h,e.c,e.l,e.opacity);if(e instanceof me||(e=nr(e)),e.a===0&&e.b===0)return new ye(NaN,0<e.l&&e.l<100?0:NaN,e.l,e.opacity);var t=Math.atan2(e.b,e.a)*ha;return new ye(t<0?t+360:t,Math.sqrt(e.a*e.a+e.b*e.b),e.l,e.opacity)}function Nt(e,t,n,r){return arguments.length===1?da(e):new ye(e,t,n,r??1)}function ye(e,t,n,r){this.h=+e,this.c=+t,this.l=+n,this.opacity=+r}function rr(e){if(isNaN(e.h))return new me(e.l,0,0,e.opacity);var t=e.h*fa;return new me(e.l,Math.cos(t)*e.c,Math.sin(t)*e.c,e.opacity)}Bn(ye,Nt,Xn(Qn,{brighter(e){return new ye(this.h,this.c,this.l+gt*(e??1),this.opacity)},darker(e){return new ye(this.h,this.c,this.l-gt*(e??1),this.opacity)},rgb(){return rr(this).rgb()}}));function ya(e){return function(t,n){var r=e((t=Nt(t)).h,(n=Nt(n)).h),i=xt(t.c,n.c),a=xt(t.l,n.l),s=xt(t.opacity,n.opacity);return function(f){return t.h=r(f),t.c=i(f),t.l=a(f),t.opacity=s(f),t+""}}}const Ta=ya(Hr);function ka(e,t){e=e.slice();var n=0,r=e.length-1,i=e[n],a=e[r],s;return a<i&&(s=n,n=r,r=s,s=i,i=a,a=s),e[n]=t.floor(i),e[r]=t.ceil(a),e}const St=new Date,Ft=new Date;function re(e,t,n,r){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const s=i(a),f=i.ceil(a);return a-s<f-a?s:f},i.offset=(a,s)=>(t(a=new Date(+a),s==null?1:Math.floor(s)),a),i.range=(a,s,f)=>{const D=[];if(a=i.ceil(a),f=f==null?1:Math.floor(f),!(a<s)||!(f>0))return D;let p;do D.push(p=new Date(+a)),t(a,f),e(a);while(p<a&&a<s);return D},i.filter=a=>re(s=>{if(s>=s)for(;e(s),!a(s);)s.setTime(s-1)},(s,f)=>{if(s>=s)if(f<0)for(;++f<=0;)for(;t(s,-1),!a(s););else for(;--f>=0;)for(;t(s,1),!a(s););}),n&&(i.count=(a,s)=>(St.setTime(+a),Ft.setTime(+s),e(St),e(Ft),Math.floor(n(St,Ft))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(r?s=>r(s)%a===0:s=>i.count(0,s)%a===0):i)),i}const mt=re(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);mt.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?re(t=>{t.setTime(Math.floor(t/e)*e)},(t,n)=>{t.setTime(+t+n*e)},(t,n)=>(n-t)/e):mt);mt.range;const Te=1e3,fe=Te*60,ke=fe*60,ve=ke*24,Jt=ve*7,xn=ve*30,Yt=ve*365,Ee=re(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*Te)},(e,t)=>(t-e)/Te,e=>e.getUTCSeconds());Ee.range;const Kt=re(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*Te)},(e,t)=>{e.setTime(+e+t*fe)},(e,t)=>(t-e)/fe,e=>e.getMinutes());Kt.range;const va=re(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*fe)},(e,t)=>(t-e)/fe,e=>e.getUTCMinutes());va.range;const en=re(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*Te-e.getMinutes()*fe)},(e,t)=>{e.setTime(+e+t*ke)},(e,t)=>(t-e)/ke,e=>e.getHours());en.range;const pa=re(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*ke)},(e,t)=>(t-e)/ke,e=>e.getUTCHours());pa.range;const tn=re(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*fe)/ve,e=>e.getDate()-1);tn.range;const ba=re(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/ve,e=>e.getUTCDate()-1);ba.range;const xa=re(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/ve,e=>Math.floor(e/ve));xa.range;function Se(e){return re(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,n)=>{t.setDate(t.getDate()+n*7)},(t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*fe)/Jt)}const nn=Se(0),Ca=Se(1),wa=Se(2),Ma=Se(3),Da=Se(4),Ua=Se(5),Sa=Se(6);nn.range;Ca.range;wa.range;Ma.range;Da.range;Ua.range;Sa.range;function Fe(e){return re(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n*7)},(t,n)=>(n-t)/Jt)}const Fa=Fe(0),Ya=Fe(1),_a=Fe(2),$a=Fe(3),Wa=Fe(4),La=Fe(5),Aa=Fe(6);Fa.range;Ya.range;_a.range;$a.range;Wa.range;La.range;Aa.range;const rn=re(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());rn.range;const Ia=re(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());Ia.range;const pt=re(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());pt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:re(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,n)=>{t.setFullYear(t.getFullYear()+n*e)});pt.range;const ar=re(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());ar.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:re(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n*e)});ar.range;function Ea(e,t,n,r,i,a){const s=[[Ee,1,Te],[Ee,5,5*Te],[Ee,15,15*Te],[Ee,30,30*Te],[a,1,fe],[a,5,5*fe],[a,15,15*fe],[a,30,30*fe],[i,1,ke],[i,3,3*ke],[i,6,6*ke],[i,12,12*ke],[r,1,ve],[r,2,2*ve],[n,1,Jt],[t,1,xn],[t,3,3*xn],[e,1,Yt]];function f(p,k,A){const S=k<p;S&&([p,k]=[k,p]);const U=A&&typeof A.range=="function"?A:D(p,k,A),G=U?U.range(p,+k+1):[];return S?G.reverse():G}function D(p,k,A){const S=Math.abs(k-p)/A,U=Gr(([,,F])=>F).right(s,S);if(U===s.length)return e.every(pn(p/Yt,k/Yt,A));if(U===0)return mt.every(Math.max(pn(p,k,A),1));const[G,O]=s[S/s[U-1][2]<s[U][2]/S?U-1:U];return G.every(O)}return[f,D]}const[Ha,Oa]=Ea(pt,rn,nn,tn,en,Kt);var _t=new Date,$t=new Date;function pe(e,t,n,r){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=function(a){return e(a=new Date(+a)),a},i.ceil=function(a){return e(a=new Date(a-1)),t(a,1),e(a),a},i.round=function(a){var s=i(a),f=i.ceil(a);return a-s<f-a?s:f},i.offset=function(a,s){return t(a=new Date(+a),s==null?1:Math.floor(s)),a},i.range=function(a,s,f){var D=[],p;if(a=i.ceil(a),f=f==null?1:Math.floor(f),!(a<s)||!(f>0))return D;do D.push(p=new Date(+a)),t(a,f),e(a);while(p<a&&a<s);return D},i.filter=function(a){return pe(function(s){if(s>=s)for(;e(s),!a(s);)s.setTime(s-1)},function(s,f){if(s>=s)if(f<0)for(;++f<=0;)for(;t(s,-1),!a(s););else for(;--f>=0;)for(;t(s,1),!a(s););})},n&&(i.count=function(a,s){return _t.setTime(+a),$t.setTime(+s),e(_t),e($t),Math.floor(n(_t,$t))},i.every=function(a){return a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(r?function(s){return r(s)%a===0}:function(s){return i.count(0,s)%a===0}):i}),i}var ir=6e4,sr=864e5,or=6048e5,an=pe(function(e){e.setHours(0,0,0,0)},function(e,t){e.setDate(e.getDate()+t)},function(e,t){return(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*ir)/sr},function(e){return e.getDate()-1});an.range;function Ye(e){return pe(function(t){t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},function(t,n){t.setDate(t.getDate()+n*7)},function(t,n){return(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*ir)/or})}var ur=Ye(0),dt=Ye(1),Na=Ye(2),Va=Ye(3),Ve=Ye(4),za=Ye(5),Pa=Ye(6);ur.range;dt.range;Na.range;Va.range;Ve.range;za.range;Pa.range;var Ce=pe(function(e){e.setMonth(0,1),e.setHours(0,0,0,0)},function(e,t){e.setFullYear(e.getFullYear()+t)},function(e,t){return t.getFullYear()-e.getFullYear()},function(e){return e.getFullYear()});Ce.every=function(e){return!isFinite(e=Math.floor(e))||!(e>0)?null:pe(function(t){t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},function(t,n){t.setFullYear(t.getFullYear()+n*e)})};Ce.range;var sn=pe(function(e){e.setUTCHours(0,0,0,0)},function(e,t){e.setUTCDate(e.getUTCDate()+t)},function(e,t){return(t-e)/sr},function(e){return e.getUTCDate()-1});sn.range;function _e(e){return pe(function(t){t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCDate(t.getUTCDate()+n*7)},function(t,n){return(n-t)/or})}var cr=_e(0),yt=_e(1),Ra=_e(2),Za=_e(3),ze=_e(4),qa=_e(5),Ba=_e(6);cr.range;yt.range;Ra.range;Za.range;ze.range;qa.range;Ba.range;var we=pe(function(e){e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},function(e,t){e.setUTCFullYear(e.getUTCFullYear()+t)},function(e,t){return t.getUTCFullYear()-e.getUTCFullYear()},function(e){return e.getUTCFullYear()});we.every=function(e){return!isFinite(e=Math.floor(e))||!(e>0)?null:pe(function(t){t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCFullYear(t.getUTCFullYear()+n*e)})};we.range;function Wt(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function Lt(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function Ze(e,t,n){return{y:e,m:t,d:n,H:0,M:0,S:0,L:0}}function Xa(e){var t=e.dateTime,n=e.date,r=e.time,i=e.periods,a=e.days,s=e.shortDays,f=e.months,D=e.shortMonths,p=qe(i),k=Be(i),A=qe(a),S=Be(a),U=qe(s),G=Be(s),O=qe(f),F=Be(f),E=qe(D),z=Be(D),W={a:y,A:I,b:l,B:h,c:null,d:Sn,e:Sn,f:Ti,g:Ui,G:Fi,H:mi,I:di,j:yi,L:lr,m:ki,M:vi,p:c,q:B,Q:_n,s:$n,S:pi,u:bi,U:xi,V:Ci,w:wi,W:Mi,x:null,X:null,y:Di,Y:Si,Z:Yi,"%":Yn},R={a:P,A:Z,b:te,B:j,c:null,d:Fn,e:Fn,f:Li,g:Ri,G:qi,H:_i,I:$i,j:Wi,L:hr,m:Ai,M:Ii,p:K,q:ue,Q:_n,s:$n,S:Ei,u:Hi,U:Oi,V:Ni,w:Vi,W:zi,x:null,X:null,y:Pi,Y:Zi,Z:Bi,"%":Yn},J={a:_,A:Y,b,B:L,c:g,d:Dn,e:Dn,f:li,g:Mn,G:wn,H:Un,I:Un,j:si,L:ci,m:ii,M:oi,p:$,q:ai,Q:hi,s:gi,S:ui,u:Ka,U:ei,V:ti,w:Ja,W:ni,x:m,X:v,y:Mn,Y:wn,Z:ri,"%":fi};W.x=w(n,W),W.X=w(r,W),W.c=w(t,W),R.x=w(n,R),R.X=w(r,R),R.c=w(t,R);function w(u,T){return function(C){var o=[],q=-1,x=0,V=u.length,H,ee,oe;for(C instanceof Date||(C=new Date(+C));++q<V;)u.charCodeAt(q)===37&&(o.push(u.slice(x,q)),(ee=Cn[H=u.charAt(++q)])!=null?H=u.charAt(++q):ee=H==="e"?" ":"0",(oe=T[H])&&(H=oe(C,ee)),o.push(H),x=q+1);return o.push(u.slice(x,q)),o.join("")}}function N(u,T){return function(C){var o=Ze(1900,void 0,1),q=M(o,u,C+="",0),x,V;if(q!=C.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(o.s*1e3+("L"in o?o.L:0));if(T&&!("Z"in o)&&(o.Z=0),"p"in o&&(o.H=o.H%12+o.p*12),o.m===void 0&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(x=Lt(Ze(o.y,0,1)),V=x.getUTCDay(),x=V>4||V===0?yt.ceil(x):yt(x),x=sn.offset(x,(o.V-1)*7),o.y=x.getUTCFullYear(),o.m=x.getUTCMonth(),o.d=x.getUTCDate()+(o.w+6)%7):(x=Wt(Ze(o.y,0,1)),V=x.getDay(),x=V>4||V===0?dt.ceil(x):dt(x),x=an.offset(x,(o.V-1)*7),o.y=x.getFullYear(),o.m=x.getMonth(),o.d=x.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:"W"in o?1:0),V="Z"in o?Lt(Ze(o.y,0,1)).getUTCDay():Wt(Ze(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+o.W*7-(V+5)%7:o.w+o.U*7-(V+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,Lt(o)):Wt(o)}}function M(u,T,C,o){for(var q=0,x=T.length,V=C.length,H,ee;q<x;){if(o>=V)return-1;if(H=T.charCodeAt(q++),H===37){if(H=T.charAt(q++),ee=J[H in Cn?T.charAt(q++):H],!ee||(o=ee(u,C,o))<0)return-1}else if(H!=C.charCodeAt(o++))return-1}return o}function $(u,T,C){var o=p.exec(T.slice(C));return o?(u.p=k[o[0].toLowerCase()],C+o[0].length):-1}function _(u,T,C){var o=U.exec(T.slice(C));return o?(u.w=G[o[0].toLowerCase()],C+o[0].length):-1}function Y(u,T,C){var o=A.exec(T.slice(C));return o?(u.w=S[o[0].toLowerCase()],C+o[0].length):-1}function b(u,T,C){var o=E.exec(T.slice(C));return o?(u.m=z[o[0].toLowerCase()],C+o[0].length):-1}function L(u,T,C){var o=O.exec(T.slice(C));return o?(u.m=F[o[0].toLowerCase()],C+o[0].length):-1}function g(u,T,C){return M(u,t,T,C)}function m(u,T,C){return M(u,n,T,C)}function v(u,T,C){return M(u,r,T,C)}function y(u){return s[u.getDay()]}function I(u){return a[u.getDay()]}function l(u){return D[u.getMonth()]}function h(u){return f[u.getMonth()]}function c(u){return i[+(u.getHours()>=12)]}function B(u){return 1+~~(u.getMonth()/3)}function P(u){return s[u.getUTCDay()]}function Z(u){return a[u.getUTCDay()]}function te(u){return D[u.getUTCMonth()]}function j(u){return f[u.getUTCMonth()]}function K(u){return i[+(u.getUTCHours()>=12)]}function ue(u){return 1+~~(u.getUTCMonth()/3)}return{format:function(u){var T=w(u+="",W);return T.toString=function(){return u},T},parse:function(u){var T=N(u+="",!1);return T.toString=function(){return u},T},utcFormat:function(u){var T=w(u+="",R);return T.toString=function(){return u},T},utcParse:function(u){var T=N(u+="",!0);return T.toString=function(){return u},T}}}var Cn={"-":"",_:" ",0:"0"},ie=/^\s*\d+/,Qa=/^%/,Ga=/[\\^$*+?|[\]().{}]/g;function X(e,t,n){var r=e<0?"-":"",i=(r?-e:e)+"",a=i.length;return r+(a<n?new Array(n-a+1).join(t)+i:i)}function ja(e){return e.replace(Ga,"\\$&")}function qe(e){return new RegExp("^(?:"+e.map(ja).join("|")+")","i")}function Be(e){for(var t={},n=-1,r=e.length;++n<r;)t[e[n].toLowerCase()]=n;return t}function Ja(e,t,n){var r=ie.exec(t.slice(n,n+1));return r?(e.w=+r[0],n+r[0].length):-1}function Ka(e,t,n){var r=ie.exec(t.slice(n,n+1));return r?(e.u=+r[0],n+r[0].length):-1}function ei(e,t,n){var r=ie.exec(t.slice(n,n+2));return r?(e.U=+r[0],n+r[0].length):-1}function ti(e,t,n){var r=ie.exec(t.slice(n,n+2));return r?(e.V=+r[0],n+r[0].length):-1}function ni(e,t,n){var r=ie.exec(t.slice(n,n+2));return r?(e.W=+r[0],n+r[0].length):-1}function wn(e,t,n){var r=ie.exec(t.slice(n,n+4));return r?(e.y=+r[0],n+r[0].length):-1}function Mn(e,t,n){var r=ie.exec(t.slice(n,n+2));return r?(e.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function ri(e,t,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(n,n+6));return r?(e.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function ai(e,t,n){var r=ie.exec(t.slice(n,n+1));return r?(e.q=r[0]*3-3,n+r[0].length):-1}function ii(e,t,n){var r=ie.exec(t.slice(n,n+2));return r?(e.m=r[0]-1,n+r[0].length):-1}function Dn(e,t,n){var r=ie.exec(t.slice(n,n+2));return r?(e.d=+r[0],n+r[0].length):-1}function si(e,t,n){var r=ie.exec(t.slice(n,n+3));return r?(e.m=0,e.d=+r[0],n+r[0].length):-1}function Un(e,t,n){var r=ie.exec(t.slice(n,n+2));return r?(e.H=+r[0],n+r[0].length):-1}function oi(e,t,n){var r=ie.exec(t.slice(n,n+2));return r?(e.M=+r[0],n+r[0].length):-1}function ui(e,t,n){var r=ie.exec(t.slice(n,n+2));return r?(e.S=+r[0],n+r[0].length):-1}function ci(e,t,n){var r=ie.exec(t.slice(n,n+3));return r?(e.L=+r[0],n+r[0].length):-1}function li(e,t,n){var r=ie.exec(t.slice(n,n+6));return r?(e.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function fi(e,t,n){var r=Qa.exec(t.slice(n,n+1));return r?n+r[0].length:-1}function hi(e,t,n){var r=ie.exec(t.slice(n));return r?(e.Q=+r[0],n+r[0].length):-1}function gi(e,t,n){var r=ie.exec(t.slice(n));return r?(e.s=+r[0],n+r[0].length):-1}function Sn(e,t){return X(e.getDate(),t,2)}function mi(e,t){return X(e.getHours(),t,2)}function di(e,t){return X(e.getHours()%12||12,t,2)}function yi(e,t){return X(1+an.count(Ce(e),e),t,3)}function lr(e,t){return X(e.getMilliseconds(),t,3)}function Ti(e,t){return lr(e,t)+"000"}function ki(e,t){return X(e.getMonth()+1,t,2)}function vi(e,t){return X(e.getMinutes(),t,2)}function pi(e,t){return X(e.getSeconds(),t,2)}function bi(e){var t=e.getDay();return t===0?7:t}function xi(e,t){return X(ur.count(Ce(e)-1,e),t,2)}function fr(e){var t=e.getDay();return t>=4||t===0?Ve(e):Ve.ceil(e)}function Ci(e,t){return e=fr(e),X(Ve.count(Ce(e),e)+(Ce(e).getDay()===4),t,2)}function wi(e){return e.getDay()}function Mi(e,t){return X(dt.count(Ce(e)-1,e),t,2)}function Di(e,t){return X(e.getFullYear()%100,t,2)}function Ui(e,t){return e=fr(e),X(e.getFullYear()%100,t,2)}function Si(e,t){return X(e.getFullYear()%1e4,t,4)}function Fi(e,t){var n=e.getDay();return e=n>=4||n===0?Ve(e):Ve.ceil(e),X(e.getFullYear()%1e4,t,4)}function Yi(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+X(t/60|0,"0",2)+X(t%60,"0",2)}function Fn(e,t){return X(e.getUTCDate(),t,2)}function _i(e,t){return X(e.getUTCHours(),t,2)}function $i(e,t){return X(e.getUTCHours()%12||12,t,2)}function Wi(e,t){return X(1+sn.count(we(e),e),t,3)}function hr(e,t){return X(e.getUTCMilliseconds(),t,3)}function Li(e,t){return hr(e,t)+"000"}function Ai(e,t){return X(e.getUTCMonth()+1,t,2)}function Ii(e,t){return X(e.getUTCMinutes(),t,2)}function Ei(e,t){return X(e.getUTCSeconds(),t,2)}function Hi(e){var t=e.getUTCDay();return t===0?7:t}function Oi(e,t){return X(cr.count(we(e)-1,e),t,2)}function gr(e){var t=e.getUTCDay();return t>=4||t===0?ze(e):ze.ceil(e)}function Ni(e,t){return e=gr(e),X(ze.count(we(e),e)+(we(e).getUTCDay()===4),t,2)}function Vi(e){return e.getUTCDay()}function zi(e,t){return X(yt.count(we(e)-1,e),t,2)}function Pi(e,t){return X(e.getUTCFullYear()%100,t,2)}function Ri(e,t){return e=gr(e),X(e.getUTCFullYear()%100,t,2)}function Zi(e,t){return X(e.getUTCFullYear()%1e4,t,4)}function qi(e,t){var n=e.getUTCDay();return e=n>=4||n===0?ze(e):ze.ceil(e),X(e.getUTCFullYear()%1e4,t,4)}function Bi(){return"+0000"}function Yn(){return"%"}function _n(e){return+e}function $n(e){return Math.floor(+e/1e3)}var Ae,mr;Xi({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function Xi(e){return Ae=Xa(e),mr=Ae.format,Ae.parse,Ae.utcFormat,Ae.utcParse,Ae}function Qi(e){return new Date(e)}function Gi(e){return e instanceof Date?+e:+new Date(+e)}function dr(e,t,n,r,i,a,s,f,D,p){var k=jr(),A=k.invert,S=k.domain,U=p(".%L"),G=p(":%S"),O=p("%I:%M"),F=p("%I %p"),E=p("%a %d"),z=p("%b %d"),W=p("%B"),R=p("%Y");function J(w){return(D(w)<w?U:f(w)<w?G:s(w)<w?O:a(w)<w?F:r(w)<w?i(w)<w?E:z:n(w)<w?W:R)(w)}return k.invert=function(w){return new Date(A(w))},k.domain=function(w){return arguments.length?S(Array.from(w,Gi)):S().map(Qi)},k.ticks=function(w){var N=S();return e(N[0],N[N.length-1],w??10)},k.tickFormat=function(w,N){return N==null?J:p(N)},k.nice=function(w){var N=S();return(!w||typeof w.range!="function")&&(w=t(N[0],N[N.length-1],w??10)),w?S(ka(N,w)):k},k.copy=function(){return Jr(k,dr(e,t,n,r,i,a,s,f,D,p))},k}function ji(){return ea.apply(dr(Ha,Oa,pt,rn,nn,tn,en,Kt,Ee,mr).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}const At=new Date,It=new Date;function ae(e,t,n,r){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const s=i(a),f=i.ceil(a);return a-s<f-a?s:f},i.offset=(a,s)=>(t(a=new Date(+a),s==null?1:Math.floor(s)),a),i.range=(a,s,f)=>{const D=[];if(a=i.ceil(a),f=f==null?1:Math.floor(f),!(a<s)||!(f>0))return D;let p;do D.push(p=new Date(+a)),t(a,f),e(a);while(p<a&&a<s);return D},i.filter=a=>ae(s=>{if(s>=s)for(;e(s),!a(s);)s.setTime(s-1)},(s,f)=>{if(s>=s)if(f<0)for(;++f<=0;)for(;t(s,-1),!a(s););else for(;--f>=0;)for(;t(s,1),!a(s););}),n&&(i.count=(a,s)=>(At.setTime(+a),It.setTime(+s),e(At),e(It),Math.floor(n(At,It))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(r?s=>r(s)%a===0:s=>i.count(0,s)%a===0):i)),i}const je=ae(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);je.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?ae(t=>{t.setTime(Math.floor(t/e)*e)},(t,n)=>{t.setTime(+t+n*e)},(t,n)=>(n-t)/e):je);je.range;const Je=1e3,xe=Je*60,Ke=xe*60,et=Ke*24,yr=et*7,Vt=ae(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*Je)},(e,t)=>(t-e)/Je,e=>e.getUTCSeconds());Vt.range;const zt=ae(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*Je)},(e,t)=>{e.setTime(+e+t*xe)},(e,t)=>(t-e)/xe,e=>e.getMinutes());zt.range;const Ji=ae(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*xe)},(e,t)=>(t-e)/xe,e=>e.getUTCMinutes());Ji.range;const Pt=ae(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*Je-e.getMinutes()*xe)},(e,t)=>{e.setTime(+e+t*Ke)},(e,t)=>(t-e)/Ke,e=>e.getHours());Pt.range;const Ki=ae(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*Ke)},(e,t)=>(t-e)/Ke,e=>e.getUTCHours());Ki.range;const tt=ae(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*xe)/et,e=>e.getDate()-1);tt.range;const on=ae(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/et,e=>e.getUTCDate()-1);on.range;const es=ae(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/et,e=>Math.floor(e/et));es.range;function $e(e){return ae(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,n)=>{t.setDate(t.getDate()+n*7)},(t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*xe)/yr)}const un=$e(0),nt=$e(1),Tr=$e(2),kr=$e(3),Me=$e(4),vr=$e(5),pr=$e(6);un.range;nt.range;Tr.range;kr.range;Me.range;vr.range;pr.range;function We(e){return ae(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n*7)},(t,n)=>(n-t)/yr)}const br=We(0),Tt=We(1),ts=We(2),ns=We(3),Pe=We(4),rs=We(5),as=We(6);br.range;Tt.range;ts.range;ns.range;Pe.range;rs.range;as.range;const Rt=ae(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Rt.range;const is=ae(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());is.range;const De=ae(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());De.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:ae(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,n)=>{t.setFullYear(t.getFullYear()+n*e)});De.range;const Ue=ae(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());Ue.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:ae(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n*e)});Ue.range;function Et(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function Ht(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function Xe(e,t,n){return{y:e,m:t,d:n,H:0,M:0,S:0,L:0}}function ss(e){var t=e.dateTime,n=e.date,r=e.time,i=e.periods,a=e.days,s=e.shortDays,f=e.months,D=e.shortMonths,p=Qe(i),k=Ge(i),A=Qe(a),S=Ge(a),U=Qe(s),G=Ge(s),O=Qe(f),F=Ge(f),E=Qe(D),z=Ge(D),W={a:y,A:I,b:l,B:h,c:null,d:Hn,e:Hn,f:Fs,g:Os,G:Vs,H:Ds,I:Us,j:Ss,L:xr,m:Ys,M:_s,p:c,q:B,Q:Vn,s:zn,S:$s,u:Ws,U:Ls,V:As,w:Is,W:Es,x:null,X:null,y:Hs,Y:Ns,Z:zs,"%":Nn},R={a:P,A:Z,b:te,B:j,c:null,d:On,e:On,f:qs,g:no,G:ao,H:Ps,I:Rs,j:Zs,L:wr,m:Bs,M:Xs,p:K,q:ue,Q:Vn,s:zn,S:Qs,u:Gs,U:js,V:Js,w:Ks,W:eo,x:null,X:null,y:to,Y:ro,Z:io,"%":Nn},J={a:_,A:Y,b,B:L,c:g,d:In,e:In,f:xs,g:An,G:Ln,H:En,I:En,j:ks,L:bs,m:Ts,M:vs,p:$,q:ys,Q:ws,s:Ms,S:ps,u:fs,U:hs,V:gs,w:ls,W:ms,x:m,X:v,y:An,Y:Ln,Z:ds,"%":Cs};W.x=w(n,W),W.X=w(r,W),W.c=w(t,W),R.x=w(n,R),R.X=w(r,R),R.c=w(t,R);function w(u,T){return function(C){var o=[],q=-1,x=0,V=u.length,H,ee,oe;for(C instanceof Date||(C=new Date(+C));++q<V;)u.charCodeAt(q)===37&&(o.push(u.slice(x,q)),(ee=Wn[H=u.charAt(++q)])!=null?H=u.charAt(++q):ee=H==="e"?" ":"0",(oe=T[H])&&(H=oe(C,ee)),o.push(H),x=q+1);return o.push(u.slice(x,q)),o.join("")}}function N(u,T){return function(C){var o=Xe(1900,void 0,1),q=M(o,u,C+="",0),x,V;if(q!=C.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(o.s*1e3+("L"in o?o.L:0));if(T&&!("Z"in o)&&(o.Z=0),"p"in o&&(o.H=o.H%12+o.p*12),o.m===void 0&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(x=Ht(Xe(o.y,0,1)),V=x.getUTCDay(),x=V>4||V===0?Tt.ceil(x):Tt(x),x=on.offset(x,(o.V-1)*7),o.y=x.getUTCFullYear(),o.m=x.getUTCMonth(),o.d=x.getUTCDate()+(o.w+6)%7):(x=Et(Xe(o.y,0,1)),V=x.getDay(),x=V>4||V===0?nt.ceil(x):nt(x),x=tt.offset(x,(o.V-1)*7),o.y=x.getFullYear(),o.m=x.getMonth(),o.d=x.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:"W"in o?1:0),V="Z"in o?Ht(Xe(o.y,0,1)).getUTCDay():Et(Xe(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+o.W*7-(V+5)%7:o.w+o.U*7-(V+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,Ht(o)):Et(o)}}function M(u,T,C,o){for(var q=0,x=T.length,V=C.length,H,ee;q<x;){if(o>=V)return-1;if(H=T.charCodeAt(q++),H===37){if(H=T.charAt(q++),ee=J[H in Wn?T.charAt(q++):H],!ee||(o=ee(u,C,o))<0)return-1}else if(H!=C.charCodeAt(o++))return-1}return o}function $(u,T,C){var o=p.exec(T.slice(C));return o?(u.p=k.get(o[0].toLowerCase()),C+o[0].length):-1}function _(u,T,C){var o=U.exec(T.slice(C));return o?(u.w=G.get(o[0].toLowerCase()),C+o[0].length):-1}function Y(u,T,C){var o=A.exec(T.slice(C));return o?(u.w=S.get(o[0].toLowerCase()),C+o[0].length):-1}function b(u,T,C){var o=E.exec(T.slice(C));return o?(u.m=z.get(o[0].toLowerCase()),C+o[0].length):-1}function L(u,T,C){var o=O.exec(T.slice(C));return o?(u.m=F.get(o[0].toLowerCase()),C+o[0].length):-1}function g(u,T,C){return M(u,t,T,C)}function m(u,T,C){return M(u,n,T,C)}function v(u,T,C){return M(u,r,T,C)}function y(u){return s[u.getDay()]}function I(u){return a[u.getDay()]}function l(u){return D[u.getMonth()]}function h(u){return f[u.getMonth()]}function c(u){return i[+(u.getHours()>=12)]}function B(u){return 1+~~(u.getMonth()/3)}function P(u){return s[u.getUTCDay()]}function Z(u){return a[u.getUTCDay()]}function te(u){return D[u.getUTCMonth()]}function j(u){return f[u.getUTCMonth()]}function K(u){return i[+(u.getUTCHours()>=12)]}function ue(u){return 1+~~(u.getUTCMonth()/3)}return{format:function(u){var T=w(u+="",W);return T.toString=function(){return u},T},parse:function(u){var T=N(u+="",!1);return T.toString=function(){return u},T},utcFormat:function(u){var T=w(u+="",R);return T.toString=function(){return u},T},utcParse:function(u){var T=N(u+="",!0);return T.toString=function(){return u},T}}}var Wn={"-":"",_:" ",0:"0"},se=/^\s*\d+/,os=/^%/,us=/[\\^$*+?|[\]().{}]/g;function Q(e,t,n){var r=e<0?"-":"",i=(r?-e:e)+"",a=i.length;return r+(a<n?new Array(n-a+1).join(t)+i:i)}function cs(e){return e.replace(us,"\\$&")}function Qe(e){return new RegExp("^(?:"+e.map(cs).join("|")+")","i")}function Ge(e){return new Map(e.map((t,n)=>[t.toLowerCase(),n]))}function ls(e,t,n){var r=se.exec(t.slice(n,n+1));return r?(e.w=+r[0],n+r[0].length):-1}function fs(e,t,n){var r=se.exec(t.slice(n,n+1));return r?(e.u=+r[0],n+r[0].length):-1}function hs(e,t,n){var r=se.exec(t.slice(n,n+2));return r?(e.U=+r[0],n+r[0].length):-1}function gs(e,t,n){var r=se.exec(t.slice(n,n+2));return r?(e.V=+r[0],n+r[0].length):-1}function ms(e,t,n){var r=se.exec(t.slice(n,n+2));return r?(e.W=+r[0],n+r[0].length):-1}function Ln(e,t,n){var r=se.exec(t.slice(n,n+4));return r?(e.y=+r[0],n+r[0].length):-1}function An(e,t,n){var r=se.exec(t.slice(n,n+2));return r?(e.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function ds(e,t,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(n,n+6));return r?(e.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function ys(e,t,n){var r=se.exec(t.slice(n,n+1));return r?(e.q=r[0]*3-3,n+r[0].length):-1}function Ts(e,t,n){var r=se.exec(t.slice(n,n+2));return r?(e.m=r[0]-1,n+r[0].length):-1}function In(e,t,n){var r=se.exec(t.slice(n,n+2));return r?(e.d=+r[0],n+r[0].length):-1}function ks(e,t,n){var r=se.exec(t.slice(n,n+3));return r?(e.m=0,e.d=+r[0],n+r[0].length):-1}function En(e,t,n){var r=se.exec(t.slice(n,n+2));return r?(e.H=+r[0],n+r[0].length):-1}function vs(e,t,n){var r=se.exec(t.slice(n,n+2));return r?(e.M=+r[0],n+r[0].length):-1}function ps(e,t,n){var r=se.exec(t.slice(n,n+2));return r?(e.S=+r[0],n+r[0].length):-1}function bs(e,t,n){var r=se.exec(t.slice(n,n+3));return r?(e.L=+r[0],n+r[0].length):-1}function xs(e,t,n){var r=se.exec(t.slice(n,n+6));return r?(e.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function Cs(e,t,n){var r=os.exec(t.slice(n,n+1));return r?n+r[0].length:-1}function ws(e,t,n){var r=se.exec(t.slice(n));return r?(e.Q=+r[0],n+r[0].length):-1}function Ms(e,t,n){var r=se.exec(t.slice(n));return r?(e.s=+r[0],n+r[0].length):-1}function Hn(e,t){return Q(e.getDate(),t,2)}function Ds(e,t){return Q(e.getHours(),t,2)}function Us(e,t){return Q(e.getHours()%12||12,t,2)}function Ss(e,t){return Q(1+tt.count(De(e),e),t,3)}function xr(e,t){return Q(e.getMilliseconds(),t,3)}function Fs(e,t){return xr(e,t)+"000"}function Ys(e,t){return Q(e.getMonth()+1,t,2)}function _s(e,t){return Q(e.getMinutes(),t,2)}function $s(e,t){return Q(e.getSeconds(),t,2)}function Ws(e){var t=e.getDay();return t===0?7:t}function Ls(e,t){return Q(un.count(De(e)-1,e),t,2)}function Cr(e){var t=e.getDay();return t>=4||t===0?Me(e):Me.ceil(e)}function As(e,t){return e=Cr(e),Q(Me.count(De(e),e)+(De(e).getDay()===4),t,2)}function Is(e){return e.getDay()}function Es(e,t){return Q(nt.count(De(e)-1,e),t,2)}function Hs(e,t){return Q(e.getFullYear()%100,t,2)}function Os(e,t){return e=Cr(e),Q(e.getFullYear()%100,t,2)}function Ns(e,t){return Q(e.getFullYear()%1e4,t,4)}function Vs(e,t){var n=e.getDay();return e=n>=4||n===0?Me(e):Me.ceil(e),Q(e.getFullYear()%1e4,t,4)}function zs(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+Q(t/60|0,"0",2)+Q(t%60,"0",2)}function On(e,t){return Q(e.getUTCDate(),t,2)}function Ps(e,t){return Q(e.getUTCHours(),t,2)}function Rs(e,t){return Q(e.getUTCHours()%12||12,t,2)}function Zs(e,t){return Q(1+on.count(Ue(e),e),t,3)}function wr(e,t){return Q(e.getUTCMilliseconds(),t,3)}function qs(e,t){return wr(e,t)+"000"}function Bs(e,t){return Q(e.getUTCMonth()+1,t,2)}function Xs(e,t){return Q(e.getUTCMinutes(),t,2)}function Qs(e,t){return Q(e.getUTCSeconds(),t,2)}function Gs(e){var t=e.getUTCDay();return t===0?7:t}function js(e,t){return Q(br.count(Ue(e)-1,e),t,2)}function Mr(e){var t=e.getUTCDay();return t>=4||t===0?Pe(e):Pe.ceil(e)}function Js(e,t){return e=Mr(e),Q(Pe.count(Ue(e),e)+(Ue(e).getUTCDay()===4),t,2)}function Ks(e){return e.getUTCDay()}function eo(e,t){return Q(Tt.count(Ue(e)-1,e),t,2)}function to(e,t){return Q(e.getUTCFullYear()%100,t,2)}function no(e,t){return e=Mr(e),Q(e.getUTCFullYear()%100,t,2)}function ro(e,t){return Q(e.getUTCFullYear()%1e4,t,4)}function ao(e,t){var n=e.getUTCDay();return e=n>=4||n===0?Pe(e):Pe.ceil(e),Q(e.getUTCFullYear()%1e4,t,4)}function io(){return"+0000"}function Nn(){return"%"}function Vn(e){return+e}function zn(e){return Math.floor(+e/1e3)}var Ie,Zt;so({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function so(e){return Ie=ss(e),Zt=Ie.format,Ie.parse,Ie.utcFormat,Ie.utcParse,Ie}var Dr={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(Gt,function(){var n="day";return function(r,i,a){var s=function(p){return p.add(4-p.isoWeekday(),n)},f=i.prototype;f.isoWeekYear=function(){return s(this).year()},f.isoWeek=function(p){if(!this.$utils().u(p))return this.add(7*(p-this.isoWeek()),n);var k,A,S,U,G=s(this),O=(k=this.isoWeekYear(),A=this.$u,S=(A?a.utc:a)().year(k).startOf("year"),U=4-S.isoWeekday(),S.isoWeekday()>4&&(U+=7),S.add(U,n));return G.diff(O,"week")+1},f.isoWeekday=function(p){return this.$utils().u(p)?this.day()||7:this.day(this.day()%7?p:p-7)};var D=f.startOf;f.startOf=function(p,k){var A=this.$utils(),S=!!A.u(k)||k;return A.p(p)==="isoweek"?S?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):D.bind(this)(p,k)}}})})(Dr);var oo=Dr.exports;const uo=jt(oo);var Ur={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(Gt,function(){var n={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},r=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,i=/\d/,a=/\d\d/,s=/\d\d?/,f=/\d*[^-_:/,()\s\d]+/,D={},p=function(F){return(F=+F)+(F>68?1900:2e3)},k=function(F){return function(E){this[F]=+E}},A=[/[+-]\d\d:?(\d\d)?|Z/,function(F){(this.zone||(this.zone={})).offset=function(E){if(!E||E==="Z")return 0;var z=E.match(/([+-]|\d\d)/g),W=60*z[1]+(+z[2]||0);return W===0?0:z[0]==="+"?-W:W}(F)}],S=function(F){var E=D[F];return E&&(E.indexOf?E:E.s.concat(E.f))},U=function(F,E){var z,W=D.meridiem;if(W){for(var R=1;R<=24;R+=1)if(F.indexOf(W(R,0,E))>-1){z=R>12;break}}else z=F===(E?"pm":"PM");return z},G={A:[f,function(F){this.afternoon=U(F,!1)}],a:[f,function(F){this.afternoon=U(F,!0)}],Q:[i,function(F){this.month=3*(F-1)+1}],S:[i,function(F){this.milliseconds=100*+F}],SS:[a,function(F){this.milliseconds=10*+F}],SSS:[/\d{3}/,function(F){this.milliseconds=+F}],s:[s,k("seconds")],ss:[s,k("seconds")],m:[s,k("minutes")],mm:[s,k("minutes")],H:[s,k("hours")],h:[s,k("hours")],HH:[s,k("hours")],hh:[s,k("hours")],D:[s,k("day")],DD:[a,k("day")],Do:[f,function(F){var E=D.ordinal,z=F.match(/\d+/);if(this.day=z[0],E)for(var W=1;W<=31;W+=1)E(W).replace(/\[|\]/g,"")===F&&(this.day=W)}],w:[s,k("week")],ww:[a,k("week")],M:[s,k("month")],MM:[a,k("month")],MMM:[f,function(F){var E=S("months"),z=(S("monthsShort")||E.map(function(W){return W.slice(0,3)})).indexOf(F)+1;if(z<1)throw new Error;this.month=z%12||z}],MMMM:[f,function(F){var E=S("months").indexOf(F)+1;if(E<1)throw new Error;this.month=E%12||E}],Y:[/[+-]?\d+/,k("year")],YY:[a,function(F){this.year=p(F)}],YYYY:[/\d{4}/,k("year")],Z:A,ZZ:A};function O(F){var E,z;E=F,z=D&&D.formats;for(var W=(F=E.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(_,Y,b){var L=b&&b.toUpperCase();return Y||z[b]||n[b]||z[L].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(g,m,v){return m||v.slice(1)})})).match(r),R=W.length,J=0;J<R;J+=1){var w=W[J],N=G[w],M=N&&N[0],$=N&&N[1];W[J]=$?{regex:M,parser:$}:w.replace(/^\[|\]$/g,"")}return function(_){for(var Y={},b=0,L=0;b<R;b+=1){var g=W[b];if(typeof g=="string")L+=g.length;else{var m=g.regex,v=g.parser,y=_.slice(L),I=m.exec(y)[0];v.call(Y,I),_=_.replace(I,"")}}return function(l){var h=l.afternoon;if(h!==void 0){var c=l.hours;h?c<12&&(l.hours+=12):c===12&&(l.hours=0),delete l.afternoon}}(Y),Y}}return function(F,E,z){z.p.customParseFormat=!0,F&&F.parseTwoDigitYear&&(p=F.parseTwoDigitYear);var W=E.prototype,R=W.parse;W.parse=function(J){var w=J.date,N=J.utc,M=J.args;this.$u=N;var $=M[1];if(typeof $=="string"){var _=M[2]===!0,Y=M[3]===!0,b=_||Y,L=M[2];Y&&(L=M[2]),D=this.$locale(),!_&&L&&(D=z.Ls[L]),this.$d=function(y,I,l,h){try{if(["x","X"].indexOf(I)>-1)return new Date((I==="X"?1e3:1)*y);var c=O(I)(y),B=c.year,P=c.month,Z=c.day,te=c.hours,j=c.minutes,K=c.seconds,ue=c.milliseconds,u=c.zone,T=c.week,C=new Date,o=Z||(B||P?1:C.getDate()),q=B||C.getFullYear(),x=0;B&&!P||(x=P>0?P-1:C.getMonth());var V,H=te||0,ee=j||0,oe=K||0,be=ue||0;return u?new Date(Date.UTC(q,x,o,H,ee,oe,be+60*u.offset*1e3)):l?new Date(Date.UTC(q,x,o,H,ee,oe,be)):(V=new Date(q,x,o,H,ee,oe,be),T&&(V=h(V).week(T).toDate()),V)}catch{return new Date("")}}(w,$,N,z),this.init(),L&&L!==!0&&(this.$L=this.locale(L).$L),b&&w!=this.format($)&&(this.$d=new Date("")),D={}}else if($ instanceof Array)for(var g=$.length,m=1;m<=g;m+=1){M[1]=$[m-1];var v=z.apply(this,M);if(v.isValid()){this.$d=v.$d,this.$L=v.$L,this.init();break}m===g&&(this.$d=new Date(""))}else R.call(this,J)}}})})(Ur);var co=Ur.exports;const lo=jt(co);var Sr={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(Gt,function(){return function(n,r){var i=r.prototype,a=i.format;i.format=function(s){var f=this,D=this.$locale();if(!this.isValid())return a.bind(this)(s);var p=this.$utils(),k=(s||"YYYY-MM-DDTHH:mm:ssZ").replace(/\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,function(A){switch(A){case"Q":return Math.ceil((f.$M+1)/3);case"Do":return D.ordinal(f.$D);case"gggg":return f.weekYear();case"GGGG":return f.isoWeekYear();case"wo":return D.ordinal(f.week(),"W");case"w":case"ww":return p.s(f.week(),A==="w"?1:2,"0");case"W":case"WW":return p.s(f.isoWeek(),A==="W"?1:2,"0");case"k":case"kk":return p.s(String(f.$H===0?24:f.$H),A==="k"?1:2,"0");case"X":return Math.floor(f.$d.getTime()/1e3);case"x":return f.$d.getTime();case"z":return"["+f.offsetName()+"]";case"zzz":return"["+f.offsetName("long")+"]";default:return A}});return a.bind(this)(k)}}})})(Sr);var fo=Sr.exports;const ho=jt(fo);var qt=function(){var e=d(function(L,g,m,v){for(m=m||{},v=L.length;v--;m[L[v]]=g);return m},"o"),t=[6,8,10,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28,29,30,31,33,35,36,38,40],n=[1,26],r=[1,27],i=[1,28],a=[1,29],s=[1,30],f=[1,31],D=[1,32],p=[1,33],k=[1,34],A=[1,9],S=[1,10],U=[1,11],G=[1,12],O=[1,13],F=[1,14],E=[1,15],z=[1,16],W=[1,19],R=[1,20],J=[1,21],w=[1,22],N=[1,23],M=[1,25],$=[1,35],_={trace:d(function(){},"trace"),yy:{},symbols_:{error:2,start:3,gantt:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NL:10,weekday:11,weekday_monday:12,weekday_tuesday:13,weekday_wednesday:14,weekday_thursday:15,weekday_friday:16,weekday_saturday:17,weekday_sunday:18,weekend:19,weekend_friday:20,weekend_saturday:21,dateFormat:22,inclusiveEndDates:23,topAxis:24,axisFormat:25,tickInterval:26,excludes:27,includes:28,todayMarker:29,title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,section:36,clickStatement:37,taskTxt:38,taskData:39,click:40,callbackname:41,callbackargs:42,href:43,clickStatementDebug:44,$accept:0,$end:1},terminals_:{2:"error",4:"gantt",6:"EOF",8:"SPACE",10:"NL",12:"weekday_monday",13:"weekday_tuesday",14:"weekday_wednesday",15:"weekday_thursday",16:"weekday_friday",17:"weekday_saturday",18:"weekday_sunday",20:"weekend_friday",21:"weekend_saturday",22:"dateFormat",23:"inclusiveEndDates",24:"topAxis",25:"axisFormat",26:"tickInterval",27:"excludes",28:"includes",29:"todayMarker",30:"title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"section",38:"taskTxt",39:"taskData",40:"click",41:"callbackname",42:"callbackargs",43:"href"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[19,1],[19,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,2],[37,2],[37,3],[37,3],[37,4],[37,3],[37,4],[37,2],[44,2],[44,3],[44,3],[44,4],[44,3],[44,4],[44,2]],performAction:d(function(g,m,v,y,I,l,h){var c=l.length-1;switch(I){case 1:return l[c-1];case 2:this.$=[];break;case 3:l[c-1].push(l[c]),this.$=l[c-1];break;case 4:case 5:this.$=l[c];break;case 6:case 7:this.$=[];break;case 8:y.setWeekday("monday");break;case 9:y.setWeekday("tuesday");break;case 10:y.setWeekday("wednesday");break;case 11:y.setWeekday("thursday");break;case 12:y.setWeekday("friday");break;case 13:y.setWeekday("saturday");break;case 14:y.setWeekday("sunday");break;case 15:y.setWeekend("friday");break;case 16:y.setWeekend("saturday");break;case 17:y.setDateFormat(l[c].substr(11)),this.$=l[c].substr(11);break;case 18:y.enableInclusiveEndDates(),this.$=l[c].substr(18);break;case 19:y.TopAxis(),this.$=l[c].substr(8);break;case 20:y.setAxisFormat(l[c].substr(11)),this.$=l[c].substr(11);break;case 21:y.setTickInterval(l[c].substr(13)),this.$=l[c].substr(13);break;case 22:y.setExcludes(l[c].substr(9)),this.$=l[c].substr(9);break;case 23:y.setIncludes(l[c].substr(9)),this.$=l[c].substr(9);break;case 24:y.setTodayMarker(l[c].substr(12)),this.$=l[c].substr(12);break;case 27:y.setDiagramTitle(l[c].substr(6)),this.$=l[c].substr(6);break;case 28:this.$=l[c].trim(),y.setAccTitle(this.$);break;case 29:case 30:this.$=l[c].trim(),y.setAccDescription(this.$);break;case 31:y.addSection(l[c].substr(8)),this.$=l[c].substr(8);break;case 33:y.addTask(l[c-1],l[c]),this.$="task";break;case 34:this.$=l[c-1],y.setClickEvent(l[c-1],l[c],null);break;case 35:this.$=l[c-2],y.setClickEvent(l[c-2],l[c-1],l[c]);break;case 36:this.$=l[c-2],y.setClickEvent(l[c-2],l[c-1],null),y.setLink(l[c-2],l[c]);break;case 37:this.$=l[c-3],y.setClickEvent(l[c-3],l[c-2],l[c-1]),y.setLink(l[c-3],l[c]);break;case 38:this.$=l[c-2],y.setClickEvent(l[c-2],l[c],null),y.setLink(l[c-2],l[c-1]);break;case 39:this.$=l[c-3],y.setClickEvent(l[c-3],l[c-1],l[c]),y.setLink(l[c-3],l[c-2]);break;case 40:this.$=l[c-1],y.setLink(l[c-1],l[c]);break;case 41:case 47:this.$=l[c-1]+" "+l[c];break;case 42:case 43:case 45:this.$=l[c-2]+" "+l[c-1]+" "+l[c];break;case 44:case 46:this.$=l[c-3]+" "+l[c-2]+" "+l[c-1]+" "+l[c];break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},e(t,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:17,12:n,13:r,14:i,15:a,16:s,17:f,18:D,19:18,20:p,21:k,22:A,23:S,24:U,25:G,26:O,27:F,28:E,29:z,30:W,31:R,33:J,35:w,36:N,37:24,38:M,40:$},e(t,[2,7],{1:[2,1]}),e(t,[2,3]),{9:36,11:17,12:n,13:r,14:i,15:a,16:s,17:f,18:D,19:18,20:p,21:k,22:A,23:S,24:U,25:G,26:O,27:F,28:E,29:z,30:W,31:R,33:J,35:w,36:N,37:24,38:M,40:$},e(t,[2,5]),e(t,[2,6]),e(t,[2,17]),e(t,[2,18]),e(t,[2,19]),e(t,[2,20]),e(t,[2,21]),e(t,[2,22]),e(t,[2,23]),e(t,[2,24]),e(t,[2,25]),e(t,[2,26]),e(t,[2,27]),{32:[1,37]},{34:[1,38]},e(t,[2,30]),e(t,[2,31]),e(t,[2,32]),{39:[1,39]},e(t,[2,8]),e(t,[2,9]),e(t,[2,10]),e(t,[2,11]),e(t,[2,12]),e(t,[2,13]),e(t,[2,14]),e(t,[2,15]),e(t,[2,16]),{41:[1,40],43:[1,41]},e(t,[2,4]),e(t,[2,28]),e(t,[2,29]),e(t,[2,33]),e(t,[2,34],{42:[1,42],43:[1,43]}),e(t,[2,40],{41:[1,44]}),e(t,[2,35],{43:[1,45]}),e(t,[2,36]),e(t,[2,38],{42:[1,46]}),e(t,[2,37]),e(t,[2,39])],defaultActions:{},parseError:d(function(g,m){if(m.recoverable)this.trace(g);else{var v=new Error(g);throw v.hash=m,v}},"parseError"),parse:d(function(g){var m=this,v=[0],y=[],I=[null],l=[],h=this.table,c="",B=0,P=0,Z=2,te=1,j=l.slice.call(arguments,1),K=Object.create(this.lexer),ue={yy:{}};for(var u in this.yy)Object.prototype.hasOwnProperty.call(this.yy,u)&&(ue.yy[u]=this.yy[u]);K.setInput(g,ue.yy),ue.yy.lexer=K,ue.yy.parser=this,typeof K.yylloc>"u"&&(K.yylloc={});var T=K.yylloc;l.push(T);var C=K.options&&K.options.ranges;typeof ue.yy.parseError=="function"?this.parseError=ue.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function o(le){v.length=v.length-2*le,I.length=I.length-le,l.length=l.length-le}d(o,"popStack");function q(){var le;return le=y.pop()||K.lex()||te,typeof le!="number"&&(le instanceof Array&&(y=le,le=y.pop()),le=m.symbols_[le]||le),le}d(q,"lex");for(var x,V,H,ee,oe={},be,he,vn,st;;){if(V=v[v.length-1],this.defaultActions[V]?H=this.defaultActions[V]:((x===null||typeof x>"u")&&(x=q()),H=h[V]&&h[V][x]),typeof H>"u"||!H.length||!H[0]){var bt="";st=[];for(be in h[V])this.terminals_[be]&&be>Z&&st.push("'"+this.terminals_[be]+"'");K.showPosition?bt="Parse error on line "+(B+1)+`:
`+K.showPosition()+`
Expecting `+st.join(", ")+", got '"+(this.terminals_[x]||x)+"'":bt="Parse error on line "+(B+1)+": Unexpected "+(x==te?"end of input":"'"+(this.terminals_[x]||x)+"'"),this.parseError(bt,{text:K.match,token:this.terminals_[x]||x,line:K.yylineno,loc:T,expected:st})}if(H[0]instanceof Array&&H.length>1)throw new Error("Parse Error: multiple actions possible at state: "+V+", token: "+x);switch(H[0]){case 1:v.push(x),I.push(K.yytext),l.push(K.yylloc),v.push(H[1]),x=null,P=K.yyleng,c=K.yytext,B=K.yylineno,T=K.yylloc;break;case 2:if(he=this.productions_[H[1]][1],oe.$=I[I.length-he],oe._$={first_line:l[l.length-(he||1)].first_line,last_line:l[l.length-1].last_line,first_column:l[l.length-(he||1)].first_column,last_column:l[l.length-1].last_column},C&&(oe._$.range=[l[l.length-(he||1)].range[0],l[l.length-1].range[1]]),ee=this.performAction.apply(oe,[c,P,B,ue.yy,H[1],I,l].concat(j)),typeof ee<"u")return ee;he&&(v=v.slice(0,-1*he*2),I=I.slice(0,-1*he),l=l.slice(0,-1*he)),v.push(this.productions_[H[1]][0]),I.push(oe.$),l.push(oe._$),vn=h[v[v.length-2]][v[v.length-1]],v.push(vn);break;case 3:return!0}}return!0},"parse")},Y=function(){var L={EOF:1,parseError:d(function(m,v){if(this.yy.parser)this.yy.parser.parseError(m,v);else throw new Error(m)},"parseError"),setInput:d(function(g,m){return this.yy=m||this.yy||{},this._input=g,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:d(function(){var g=this._input[0];this.yytext+=g,this.yyleng++,this.offset++,this.match+=g,this.matched+=g;var m=g.match(/(?:\r\n?|\n).*/g);return m?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),g},"input"),unput:d(function(g){var m=g.length,v=g.split(/(?:\r\n?|\n)/g);this._input=g+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-m),this.offset-=m;var y=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),v.length-1&&(this.yylineno-=v.length-1);var I=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:v?(v.length===y.length?this.yylloc.first_column:0)+y[y.length-v.length].length-v[0].length:this.yylloc.first_column-m},this.options.ranges&&(this.yylloc.range=[I[0],I[0]+this.yyleng-m]),this.yyleng=this.yytext.length,this},"unput"),more:d(function(){return this._more=!0,this},"more"),reject:d(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:d(function(g){this.unput(this.match.slice(g))},"less"),pastInput:d(function(){var g=this.matched.substr(0,this.matched.length-this.match.length);return(g.length>20?"...":"")+g.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:d(function(){var g=this.match;return g.length<20&&(g+=this._input.substr(0,20-g.length)),(g.substr(0,20)+(g.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:d(function(){var g=this.pastInput(),m=new Array(g.length+1).join("-");return g+this.upcomingInput()+`
`+m+"^"},"showPosition"),test_match:d(function(g,m){var v,y,I;if(this.options.backtrack_lexer&&(I={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(I.yylloc.range=this.yylloc.range.slice(0))),y=g[0].match(/(?:\r\n?|\n).*/g),y&&(this.yylineno+=y.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:y?y[y.length-1].length-y[y.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+g[0].length},this.yytext+=g[0],this.match+=g[0],this.matches=g,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(g[0].length),this.matched+=g[0],v=this.performAction.call(this,this.yy,this,m,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),v)return v;if(this._backtrack){for(var l in I)this[l]=I[l];return!1}return!1},"test_match"),next:d(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var g,m,v,y;this._more||(this.yytext="",this.match="");for(var I=this._currentRules(),l=0;l<I.length;l++)if(v=this._input.match(this.rules[I[l]]),v&&(!m||v[0].length>m[0].length)){if(m=v,y=l,this.options.backtrack_lexer){if(g=this.test_match(v,I[l]),g!==!1)return g;if(this._backtrack){m=!1;continue}else return!1}else if(!this.options.flex)break}return m?(g=this.test_match(m,I[y]),g!==!1?g:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:d(function(){var m=this.next();return m||this.lex()},"lex"),begin:d(function(m){this.conditionStack.push(m)},"begin"),popState:d(function(){var m=this.conditionStack.length-1;return m>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:d(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:d(function(m){return m=this.conditionStack.length-1-Math.abs(m||0),m>=0?this.conditionStack[m]:"INITIAL"},"topState"),pushState:d(function(m){this.begin(m)},"pushState"),stateStackSize:d(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:d(function(m,v,y,I){switch(y){case 0:return this.begin("open_directive"),"open_directive";case 1:return this.begin("acc_title"),31;case 2:return this.popState(),"acc_title_value";case 3:return this.begin("acc_descr"),33;case 4:return this.popState(),"acc_descr_value";case 5:this.begin("acc_descr_multiline");break;case 6:this.popState();break;case 7:return"acc_descr_multiline_value";case 8:break;case 9:break;case 10:break;case 11:return 10;case 12:break;case 13:break;case 14:this.begin("href");break;case 15:this.popState();break;case 16:return 43;case 17:this.begin("callbackname");break;case 18:this.popState();break;case 19:this.popState(),this.begin("callbackargs");break;case 20:return 41;case 21:this.popState();break;case 22:return 42;case 23:this.begin("click");break;case 24:this.popState();break;case 25:return 40;case 26:return 4;case 27:return 22;case 28:return 23;case 29:return 24;case 30:return 25;case 31:return 26;case 32:return 28;case 33:return 27;case 34:return 29;case 35:return 12;case 36:return 13;case 37:return 14;case 38:return 15;case 39:return 16;case 40:return 17;case 41:return 18;case 42:return 20;case 43:return 21;case 44:return"date";case 45:return 30;case 46:return"accDescription";case 47:return 36;case 48:return 38;case 49:return 39;case 50:return":";case 51:return 6;case 52:return"INVALID"}},"anonymous"),rules:[/^(?:%%\{)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:%%(?!\{)*[^\n]*)/i,/^(?:[^\}]%%*[^\n]*)/i,/^(?:%%*[^\n]*[\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:%[^\n]*)/i,/^(?:href[\s]+["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:call[\s]+)/i,/^(?:\([\s]*\))/i,/^(?:\()/i,/^(?:[^(]*)/i,/^(?:\))/i,/^(?:[^)]*)/i,/^(?:click[\s]+)/i,/^(?:[\s\n])/i,/^(?:[^\s\n]*)/i,/^(?:gantt\b)/i,/^(?:dateFormat\s[^#\n;]+)/i,/^(?:inclusiveEndDates\b)/i,/^(?:topAxis\b)/i,/^(?:axisFormat\s[^#\n;]+)/i,/^(?:tickInterval\s[^#\n;]+)/i,/^(?:includes\s[^#\n;]+)/i,/^(?:excludes\s[^#\n;]+)/i,/^(?:todayMarker\s[^\n;]+)/i,/^(?:weekday\s+monday\b)/i,/^(?:weekday\s+tuesday\b)/i,/^(?:weekday\s+wednesday\b)/i,/^(?:weekday\s+thursday\b)/i,/^(?:weekday\s+friday\b)/i,/^(?:weekday\s+saturday\b)/i,/^(?:weekday\s+sunday\b)/i,/^(?:weekend\s+friday\b)/i,/^(?:weekend\s+saturday\b)/i,/^(?:\d\d\d\d-\d\d-\d\d\b)/i,/^(?:title\s[^\n]+)/i,/^(?:accDescription\s[^#\n;]+)/i,/^(?:section\s[^\n]+)/i,/^(?:[^:\n]+)/i,/^(?::[^#\n;]+)/i,/^(?::)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[6,7],inclusive:!1},acc_descr:{rules:[4],inclusive:!1},acc_title:{rules:[2],inclusive:!1},callbackargs:{rules:[21,22],inclusive:!1},callbackname:{rules:[18,19,20],inclusive:!1},href:{rules:[15,16],inclusive:!1},click:{rules:[24,25],inclusive:!1},INITIAL:{rules:[0,1,3,5,8,9,10,11,12,13,14,17,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],inclusive:!0}}};return L}();_.lexer=Y;function b(){this.yy={}}return d(b,"Parser"),b.prototype=_,_.Parser=b,new b}();qt.parser=qt;var go=qt;ce.extend(uo);ce.extend(lo);ce.extend(ho);var Pn={friday:5,saturday:6},ge="",cn="",ln=void 0,fn="",rt=[],at=[],hn=new Map,gn=[],kt=[],Re="",mn="",Fr=["active","done","crit","milestone","vert"],dn=[],it=!1,yn=!1,Tn="sunday",vt="saturday",Bt=0,mo=d(function(){gn=[],kt=[],Re="",dn=[],lt=0,Qt=void 0,ft=void 0,ne=[],ge="",cn="",mn="",ln=void 0,fn="",rt=[],at=[],it=!1,yn=!1,Bt=0,hn=new Map,Xr(),Tn="sunday",vt="saturday"},"clear"),yo=d(function(e){cn=e},"setAxisFormat"),To=d(function(){return cn},"getAxisFormat"),ko=d(function(e){ln=e},"setTickInterval"),vo=d(function(){return ln},"getTickInterval"),po=d(function(e){fn=e},"setTodayMarker"),bo=d(function(){return fn},"getTodayMarker"),xo=d(function(e){ge=e},"setDateFormat"),Co=d(function(){it=!0},"enableInclusiveEndDates"),wo=d(function(){return it},"endDatesAreInclusive"),Mo=d(function(){yn=!0},"enableTopAxis"),Do=d(function(){return yn},"topAxisEnabled"),Uo=d(function(e){mn=e},"setDisplayMode"),So=d(function(){return mn},"getDisplayMode"),Fo=d(function(){return ge},"getDateFormat"),Yo=d(function(e){rt=e.toLowerCase().split(/[\s,]+/)},"setIncludes"),_o=d(function(){return rt},"getIncludes"),$o=d(function(e){at=e.toLowerCase().split(/[\s,]+/)},"setExcludes"),Wo=d(function(){return at},"getExcludes"),Lo=d(function(){return hn},"getLinks"),Ao=d(function(e){Re=e,gn.push(e)},"addSection"),Io=d(function(){return gn},"getSections"),Eo=d(function(){let e=Rn();const t=10;let n=0;for(;!e&&n<t;)e=Rn(),n++;return kt=ne,kt},"getTasks"),Yr=d(function(e,t,n,r){return r.includes(e.format(t.trim()))?!1:n.includes("weekends")&&(e.isoWeekday()===Pn[vt]||e.isoWeekday()===Pn[vt]+1)||n.includes(e.format("dddd").toLowerCase())?!0:n.includes(e.format(t.trim()))},"isInvalidDate"),Ho=d(function(e){Tn=e},"setWeekday"),Oo=d(function(){return Tn},"getWeekday"),No=d(function(e){vt=e},"setWeekend"),_r=d(function(e,t,n,r){if(!n.length||e.manualEndTime)return;let i;e.startTime instanceof Date?i=ce(e.startTime):i=ce(e.startTime,t,!0),i=i.add(1,"d");let a;e.endTime instanceof Date?a=ce(e.endTime):a=ce(e.endTime,t,!0);const[s,f]=Vo(i,a,t,n,r);e.endTime=s.toDate(),e.renderEndTime=f},"checkTaskDates"),Vo=d(function(e,t,n,r,i){let a=!1,s=null;for(;e<=t;)a||(s=t.toDate()),a=Yr(e,n,r,i),a&&(t=t.add(1,"d")),e=e.add(1,"d");return[t,s]},"fixTaskDates"),Xt=d(function(e,t,n){n=n.trim();const i=/^after\s+(?<ids>[\d\w- ]+)/.exec(n);if(i!==null){let s=null;for(const D of i.groups.ids.split(" ")){let p=Le(D);p!==void 0&&(!s||p.endTime>s.endTime)&&(s=p)}if(s)return s.endTime;const f=new Date;return f.setHours(0,0,0,0),f}let a=ce(n,t.trim(),!0);if(a.isValid())return a.toDate();{ht.debug("Invalid date:"+n),ht.debug("With date format:"+t.trim());const s=new Date(n);if(s===void 0||isNaN(s.getTime())||s.getFullYear()<-1e4||s.getFullYear()>1e4)throw new Error("Invalid date:"+n);return s}},"getStartDate"),$r=d(function(e){const t=/^(\d+(?:\.\d+)?)([Mdhmswy]|ms)$/.exec(e.trim());return t!==null?[Number.parseFloat(t[1]),t[2]]:[NaN,"ms"]},"parseDuration"),Wr=d(function(e,t,n,r=!1){n=n.trim();const a=/^until\s+(?<ids>[\d\w- ]+)/.exec(n);if(a!==null){let k=null;for(const S of a.groups.ids.split(" ")){let U=Le(S);U!==void 0&&(!k||U.startTime<k.startTime)&&(k=U)}if(k)return k.startTime;const A=new Date;return A.setHours(0,0,0,0),A}let s=ce(n,t.trim(),!0);if(s.isValid())return r&&(s=s.add(1,"d")),s.toDate();let f=ce(e);const[D,p]=$r(n);if(!Number.isNaN(D)){const k=f.add(D,p);k.isValid()&&(f=k)}return f.toDate()},"getEndDate"),lt=0,Ne=d(function(e){return e===void 0?(lt=lt+1,"task"+lt):e},"parseId"),zo=d(function(e,t){let n;t.substr(0,1)===":"?n=t.substr(1,t.length):n=t;const r=n.split(","),i={};kn(r,i,Fr);for(let s=0;s<r.length;s++)r[s]=r[s].trim();let a="";switch(r.length){case 1:i.id=Ne(),i.startTime=e.endTime,a=r[0];break;case 2:i.id=Ne(),i.startTime=Xt(void 0,ge,r[0]),a=r[1];break;case 3:i.id=Ne(r[0]),i.startTime=Xt(void 0,ge,r[1]),a=r[2];break}return a&&(i.endTime=Wr(i.startTime,ge,a,it),i.manualEndTime=ce(a,"YYYY-MM-DD",!0).isValid(),_r(i,ge,at,rt)),i},"compileData"),Po=d(function(e,t){let n;t.substr(0,1)===":"?n=t.substr(1,t.length):n=t;const r=n.split(","),i={};kn(r,i,Fr);for(let a=0;a<r.length;a++)r[a]=r[a].trim();switch(r.length){case 1:i.id=Ne(),i.startTime={type:"prevTaskEnd",id:e},i.endTime={data:r[0]};break;case 2:i.id=Ne(),i.startTime={type:"getStartDate",startData:r[0]},i.endTime={data:r[1]};break;case 3:i.id=Ne(r[0]),i.startTime={type:"getStartDate",startData:r[1]},i.endTime={data:r[2]};break}return i},"parseData"),Qt,ft,ne=[],Lr={},Ro=d(function(e,t){const n={section:Re,type:Re,processed:!1,manualEndTime:!1,renderEndTime:null,raw:{data:t},task:e,classes:[]},r=Po(ft,t);n.raw.startTime=r.startTime,n.raw.endTime=r.endTime,n.id=r.id,n.prevTaskId=ft,n.active=r.active,n.done=r.done,n.crit=r.crit,n.milestone=r.milestone,n.vert=r.vert,n.order=Bt,Bt++;const i=ne.push(n);ft=n.id,Lr[n.id]=i-1},"addTask"),Le=d(function(e){const t=Lr[e];return ne[t]},"findTaskById"),Zo=d(function(e,t){const n={section:Re,type:Re,description:e,task:e,classes:[]},r=zo(Qt,t);n.startTime=r.startTime,n.endTime=r.endTime,n.id=r.id,n.active=r.active,n.done=r.done,n.crit=r.crit,n.milestone=r.milestone,n.vert=r.vert,Qt=n,kt.push(n)},"addTaskOrg"),Rn=d(function(){const e=d(function(n){const r=ne[n];let i="";switch(ne[n].raw.startTime.type){case"prevTaskEnd":{const a=Le(r.prevTaskId);r.startTime=a.endTime;break}case"getStartDate":i=Xt(void 0,ge,ne[n].raw.startTime.startData),i&&(ne[n].startTime=i);break}return ne[n].startTime&&(ne[n].endTime=Wr(ne[n].startTime,ge,ne[n].raw.endTime.data,it),ne[n].endTime&&(ne[n].processed=!0,ne[n].manualEndTime=ce(ne[n].raw.endTime.data,"YYYY-MM-DD",!0).isValid(),_r(ne[n],ge,at,rt))),ne[n].processed},"compileTask");let t=!0;for(const[n,r]of ne.entries())e(n),t=t&&r.processed;return t},"compileTasks"),qo=d(function(e,t){let n=t;He().securityLevel!=="loose"&&(n=Br(t)),e.split(",").forEach(function(r){Le(r)!==void 0&&(Ir(r,()=>{window.open(n,"_self")}),hn.set(r,n))}),Ar(e,"clickable")},"setLink"),Ar=d(function(e,t){e.split(",").forEach(function(n){let r=Le(n);r!==void 0&&r.classes.push(t)})},"setClass"),Bo=d(function(e,t,n){if(He().securityLevel!=="loose"||t===void 0)return;let r=[];if(typeof n=="string"){r=n.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let a=0;a<r.length;a++){let s=r[a].trim();s.startsWith('"')&&s.endsWith('"')&&(s=s.substr(1,s.length-2)),r[a]=s}}r.length===0&&r.push(e),Le(e)!==void 0&&Ir(e,()=>{Qr.runFunc(t,...r)})},"setClickFun"),Ir=d(function(e,t){dn.push(function(){const n=document.querySelector(`[id="${e}"]`);n!==null&&n.addEventListener("click",function(){t()})},function(){const n=document.querySelector(`[id="${e}-text"]`);n!==null&&n.addEventListener("click",function(){t()})})},"pushFun"),Xo=d(function(e,t,n){e.split(",").forEach(function(r){Bo(r,t,n)}),Ar(e,"clickable")},"setClickEvent"),Qo=d(function(e){dn.forEach(function(t){t(e)})},"bindFunctions"),Go={getConfig:d(()=>He().gantt,"getConfig"),clear:mo,setDateFormat:xo,getDateFormat:Fo,enableInclusiveEndDates:Co,endDatesAreInclusive:wo,enableTopAxis:Mo,topAxisEnabled:Do,setAxisFormat:yo,getAxisFormat:To,setTickInterval:ko,getTickInterval:vo,setTodayMarker:po,getTodayMarker:bo,setAccTitle:Rr,getAccTitle:Pr,setDiagramTitle:zr,getDiagramTitle:Vr,setDisplayMode:Uo,getDisplayMode:So,setAccDescription:Nr,getAccDescription:Or,addSection:Ao,getSections:Io,getTasks:Eo,addTask:Ro,findTaskById:Le,addTaskOrg:Zo,setIncludes:Yo,getIncludes:_o,setExcludes:$o,getExcludes:Wo,setClickEvent:Xo,setLink:qo,getLinks:Lo,bindFunctions:Qo,parseDuration:$r,isInvalidDate:Yr,setWeekday:Ho,getWeekday:Oo,setWeekend:No};function kn(e,t,n){let r=!0;for(;r;)r=!1,n.forEach(function(i){const a="^\\s*"+i+"\\s*$",s=new RegExp(a);e[0].match(s)&&(t[i]=!0,e.shift(1),r=!0)})}d(kn,"getTaskTags");var jo=d(function(){ht.debug("Something is calling, setConf, remove the call")},"setConf"),Zn={monday:nt,tuesday:Tr,wednesday:kr,thursday:Me,friday:vr,saturday:pr,sunday:un},Jo=d((e,t)=>{let n=[...e].map(()=>-1/0),r=[...e].sort((a,s)=>a.startTime-s.startTime||a.order-s.order),i=0;for(const a of r)for(let s=0;s<n.length;s++)if(a.startTime>=n[s]){n[s]=a.endTime,a.order=s+t,s>i&&(i=s);break}return i},"getMaxIntersections"),de,Ko=d(function(e,t,n,r){const i=He().gantt,a=He().securityLevel;let s;a==="sandbox"&&(s=ot("#i"+t));const f=a==="sandbox"?ot(s.nodes()[0].contentDocument.body):ot("body"),D=a==="sandbox"?s.nodes()[0].contentDocument:document,p=D.getElementById(t);de=p.parentElement.offsetWidth,de===void 0&&(de=1200),i.useWidth!==void 0&&(de=i.useWidth);const k=r.db.getTasks();let A=[];for(const M of k)A.push(M.type);A=N(A);const S={};let U=2*i.topPadding;if(r.db.getDisplayMode()==="compact"||i.displayMode==="compact"){const M={};for(const _ of k)M[_.section]===void 0?M[_.section]=[_]:M[_.section].push(_);let $=0;for(const _ of Object.keys(M)){const Y=Jo(M[_],$)+1;$+=Y,U+=Y*(i.barHeight+i.barGap),S[_]=Y}}else{U+=k.length*(i.barHeight+i.barGap);for(const M of A)S[M]=k.filter($=>$.type===M).length}p.setAttribute("viewBox","0 0 "+de+" "+U);const G=f.select(`[id="${t}"]`),O=ji().domain([na(k,function(M){return M.startTime}),ta(k,function(M){return M.endTime})]).rangeRound([0,de-i.leftPadding-i.rightPadding]);function F(M,$){const _=M.startTime,Y=$.startTime;let b=0;return _>Y?b=1:_<Y&&(b=-1),b}d(F,"taskCompare"),k.sort(F),E(k,de,U),Zr(G,U,de,i.useMaxWidth),G.append("text").text(r.db.getDiagramTitle()).attr("x",de/2).attr("y",i.titleTopMargin).attr("class","titleText");function E(M,$,_){const Y=i.barHeight,b=Y+i.barGap,L=i.topPadding,g=i.leftPadding,m=Kr().domain([0,A.length]).range(["#00B9FA","#F95002"]).interpolate(Ta);W(b,L,g,$,_,M,r.db.getExcludes(),r.db.getIncludes()),R(g,L,$,_),z(M,b,L,g,Y,m,$),J(b,L),w(g,L,$,_)}d(E,"makeGantt");function z(M,$,_,Y,b,L,g){M.sort((h,c)=>h.vert===c.vert?0:h.vert?1:-1);const v=[...new Set(M.map(h=>h.order))].map(h=>M.find(c=>c.order===h));G.append("g").selectAll("rect").data(v).enter().append("rect").attr("x",0).attr("y",function(h,c){return c=h.order,c*$+_-2}).attr("width",function(){return g-i.rightPadding/2}).attr("height",$).attr("class",function(h){for(const[c,B]of A.entries())if(h.type===B)return"section section"+c%i.numberSectionStyles;return"section section0"}).enter();const y=G.append("g").selectAll("rect").data(M).enter(),I=r.db.getLinks();if(y.append("rect").attr("id",function(h){return h.id}).attr("rx",3).attr("ry",3).attr("x",function(h){return h.milestone?O(h.startTime)+Y+.5*(O(h.endTime)-O(h.startTime))-.5*b:O(h.startTime)+Y}).attr("y",function(h,c){return c=h.order,h.vert?i.gridLineStartPadding:c*$+_}).attr("width",function(h){return h.milestone?b:h.vert?.08*b:O(h.renderEndTime||h.endTime)-O(h.startTime)}).attr("height",function(h){return h.vert?k.length*(i.barHeight+i.barGap)+i.barHeight*2:b}).attr("transform-origin",function(h,c){return c=h.order,(O(h.startTime)+Y+.5*(O(h.endTime)-O(h.startTime))).toString()+"px "+(c*$+_+.5*b).toString()+"px"}).attr("class",function(h){const c="task";let B="";h.classes.length>0&&(B=h.classes.join(" "));let P=0;for(const[te,j]of A.entries())h.type===j&&(P=te%i.numberSectionStyles);let Z="";return h.active?h.crit?Z+=" activeCrit":Z=" active":h.done?h.crit?Z=" doneCrit":Z=" done":h.crit&&(Z+=" crit"),Z.length===0&&(Z=" task"),h.milestone&&(Z=" milestone "+Z),h.vert&&(Z=" vert "+Z),Z+=P,Z+=" "+B,c+Z}),y.append("text").attr("id",function(h){return h.id+"-text"}).text(function(h){return h.task}).attr("font-size",i.fontSize).attr("x",function(h){let c=O(h.startTime),B=O(h.renderEndTime||h.endTime);if(h.milestone&&(c+=.5*(O(h.endTime)-O(h.startTime))-.5*b,B=c+b),h.vert)return O(h.startTime)+Y;const P=this.getBBox().width;return P>B-c?B+P+1.5*i.leftPadding>g?c+Y-5:B+Y+5:(B-c)/2+c+Y}).attr("y",function(h,c){return h.vert?i.gridLineStartPadding+k.length*(i.barHeight+i.barGap)+60:(c=h.order,c*$+i.barHeight/2+(i.fontSize/2-2)+_)}).attr("text-height",b).attr("class",function(h){const c=O(h.startTime);let B=O(h.endTime);h.milestone&&(B=c+b);const P=this.getBBox().width;let Z="";h.classes.length>0&&(Z=h.classes.join(" "));let te=0;for(const[K,ue]of A.entries())h.type===ue&&(te=K%i.numberSectionStyles);let j="";return h.active&&(h.crit?j="activeCritText"+te:j="activeText"+te),h.done?h.crit?j=j+" doneCritText"+te:j=j+" doneText"+te:h.crit&&(j=j+" critText"+te),h.milestone&&(j+=" milestoneText"),h.vert&&(j+=" vertText"),P>B-c?B+P+1.5*i.leftPadding>g?Z+" taskTextOutsideLeft taskTextOutside"+te+" "+j:Z+" taskTextOutsideRight taskTextOutside"+te+" "+j+" width-"+P:Z+" taskText taskText"+te+" "+j+" width-"+P}),He().securityLevel==="sandbox"){let h;h=ot("#i"+t);const c=h.nodes()[0].contentDocument;y.filter(function(B){return I.has(B.id)}).each(function(B){var P=c.querySelector("#"+B.id),Z=c.querySelector("#"+B.id+"-text");const te=P.parentNode;var j=c.createElement("a");j.setAttribute("xlink:href",I.get(B.id)),j.setAttribute("target","_top"),te.appendChild(j),j.appendChild(P),j.appendChild(Z)})}}d(z,"drawRects");function W(M,$,_,Y,b,L,g,m){if(g.length===0&&m.length===0)return;let v,y;for(const{startTime:P,endTime:Z}of L)(v===void 0||P<v)&&(v=P),(y===void 0||Z>y)&&(y=Z);if(!v||!y)return;if(ce(y).diff(ce(v),"year")>5){ht.warn("The difference between the min and max time is more than 5 years. This will cause performance issues. Skipping drawing exclude days.");return}const I=r.db.getDateFormat(),l=[];let h=null,c=ce(v);for(;c.valueOf()<=y;)r.db.isInvalidDate(c,I,g,m)?h?h.end=c:h={start:c,end:c}:h&&(l.push(h),h=null),c=c.add(1,"d");G.append("g").selectAll("rect").data(l).enter().append("rect").attr("id",function(P){return"exclude-"+P.start.format("YYYY-MM-DD")}).attr("x",function(P){return O(P.start)+_}).attr("y",i.gridLineStartPadding).attr("width",function(P){const Z=P.end.add(1,"day");return O(Z)-O(P.start)}).attr("height",b-$-i.gridLineStartPadding).attr("transform-origin",function(P,Z){return(O(P.start)+_+.5*(O(P.end)-O(P.start))).toString()+"px "+(Z*M+.5*b).toString()+"px"}).attr("class","exclude-range")}d(W,"drawExcludeDays");function R(M,$,_,Y){let b=la(O).tickSize(-Y+$+i.gridLineStartPadding).tickFormat(Zt(r.db.getAxisFormat()||i.axisFormat||"%Y-%m-%d"));const g=/^([1-9]\d*)(millisecond|second|minute|hour|day|week|month)$/.exec(r.db.getTickInterval()||i.tickInterval);if(g!==null){const m=g[1],v=g[2],y=r.db.getWeekday()||i.weekday;switch(v){case"millisecond":b.ticks(je.every(m));break;case"second":b.ticks(Vt.every(m));break;case"minute":b.ticks(zt.every(m));break;case"hour":b.ticks(Pt.every(m));break;case"day":b.ticks(tt.every(m));break;case"week":b.ticks(Zn[y].every(m));break;case"month":b.ticks(Rt.every(m));break}}if(G.append("g").attr("class","grid").attr("transform","translate("+M+", "+(Y-50)+")").call(b).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10).attr("dy","1em"),r.db.topAxisEnabled()||i.topAxis){let m=ca(O).tickSize(-Y+$+i.gridLineStartPadding).tickFormat(Zt(r.db.getAxisFormat()||i.axisFormat||"%Y-%m-%d"));if(g!==null){const v=g[1],y=g[2],I=r.db.getWeekday()||i.weekday;switch(y){case"millisecond":m.ticks(je.every(v));break;case"second":m.ticks(Vt.every(v));break;case"minute":m.ticks(zt.every(v));break;case"hour":m.ticks(Pt.every(v));break;case"day":m.ticks(tt.every(v));break;case"week":m.ticks(Zn[I].every(v));break;case"month":m.ticks(Rt.every(v));break}}G.append("g").attr("class","grid").attr("transform","translate("+M+", "+$+")").call(m).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10)}}d(R,"makeGrid");function J(M,$){let _=0;const Y=Object.keys(S).map(b=>[b,S[b]]);G.append("g").selectAll("text").data(Y).enter().append(function(b){const L=b[0].split(qr.lineBreakRegex),g=-(L.length-1)/2,m=D.createElementNS("http://www.w3.org/2000/svg","text");m.setAttribute("dy",g+"em");for(const[v,y]of L.entries()){const I=D.createElementNS("http://www.w3.org/2000/svg","tspan");I.setAttribute("alignment-baseline","central"),I.setAttribute("x","10"),v>0&&I.setAttribute("dy","1em"),I.textContent=y,m.appendChild(I)}return m}).attr("x",10).attr("y",function(b,L){if(L>0)for(let g=0;g<L;g++)return _+=Y[L-1][1],b[1]*M/2+_*M+$;else return b[1]*M/2+$}).attr("font-size",i.sectionFontSize).attr("class",function(b){for(const[L,g]of A.entries())if(b[0]===g)return"sectionTitle sectionTitle"+L%i.numberSectionStyles;return"sectionTitle"})}d(J,"vertLabels");function w(M,$,_,Y){const b=r.db.getTodayMarker();if(b==="off")return;const L=G.append("g").attr("class","today"),g=new Date,m=L.append("line");m.attr("x1",O(g)+M).attr("x2",O(g)+M).attr("y1",i.titleTopMargin).attr("y2",Y-i.titleTopMargin).attr("class","today"),b!==""&&m.attr("style",b.replace(/,/g,";"))}d(w,"drawToday");function N(M){const $={},_=[];for(let Y=0,b=M.length;Y<b;++Y)Object.prototype.hasOwnProperty.call($,M[Y])||($[M[Y]]=!0,_.push(M[Y]));return _}d(N,"checkUnique")},"draw"),eu={setConf:jo,draw:Ko},tu=d(e=>`
  .mermaid-main-font {
        font-family: ${e.fontFamily};
  }

  .exclude-range {
    fill: ${e.excludeBkgColor};
  }

  .section {
    stroke: none;
    opacity: 0.2;
  }

  .section0 {
    fill: ${e.sectionBkgColor};
  }

  .section2 {
    fill: ${e.sectionBkgColor2};
  }

  .section1,
  .section3 {
    fill: ${e.altSectionBkgColor};
    opacity: 0.2;
  }

  .sectionTitle0 {
    fill: ${e.titleColor};
  }

  .sectionTitle1 {
    fill: ${e.titleColor};
  }

  .sectionTitle2 {
    fill: ${e.titleColor};
  }

  .sectionTitle3 {
    fill: ${e.titleColor};
  }

  .sectionTitle {
    text-anchor: start;
    font-family: ${e.fontFamily};
  }


  /* Grid and axis */

  .grid .tick {
    stroke: ${e.gridColor};
    opacity: 0.8;
    shape-rendering: crispEdges;
  }

  .grid .tick text {
    font-family: ${e.fontFamily};
    fill: ${e.textColor};
  }

  .grid path {
    stroke-width: 0;
  }


  /* Today line */

  .today {
    fill: none;
    stroke: ${e.todayLineColor};
    stroke-width: 2px;
  }


  /* Task styling */

  /* Default task */

  .task {
    stroke-width: 2;
  }

  .taskText {
    text-anchor: middle;
    font-family: ${e.fontFamily};
  }

  .taskTextOutsideRight {
    fill: ${e.taskTextDarkColor};
    text-anchor: start;
    font-family: ${e.fontFamily};
  }

  .taskTextOutsideLeft {
    fill: ${e.taskTextDarkColor};
    text-anchor: end;
  }


  /* Special case clickable */

  .task.clickable {
    cursor: pointer;
  }

  .taskText.clickable {
    cursor: pointer;
    fill: ${e.taskTextClickableColor} !important;
    font-weight: bold;
  }

  .taskTextOutsideLeft.clickable {
    cursor: pointer;
    fill: ${e.taskTextClickableColor} !important;
    font-weight: bold;
  }

  .taskTextOutsideRight.clickable {
    cursor: pointer;
    fill: ${e.taskTextClickableColor} !important;
    font-weight: bold;
  }


  /* Specific task settings for the sections*/

  .taskText0,
  .taskText1,
  .taskText2,
  .taskText3 {
    fill: ${e.taskTextColor};
  }

  .task0,
  .task1,
  .task2,
  .task3 {
    fill: ${e.taskBkgColor};
    stroke: ${e.taskBorderColor};
  }

  .taskTextOutside0,
  .taskTextOutside2
  {
    fill: ${e.taskTextOutsideColor};
  }

  .taskTextOutside1,
  .taskTextOutside3 {
    fill: ${e.taskTextOutsideColor};
  }


  /* Active task */

  .active0,
  .active1,
  .active2,
  .active3 {
    fill: ${e.activeTaskBkgColor};
    stroke: ${e.activeTaskBorderColor};
  }

  .activeText0,
  .activeText1,
  .activeText2,
  .activeText3 {
    fill: ${e.taskTextDarkColor} !important;
  }


  /* Completed task */

  .done0,
  .done1,
  .done2,
  .done3 {
    stroke: ${e.doneTaskBorderColor};
    fill: ${e.doneTaskBkgColor};
    stroke-width: 2;
  }

  .doneText0,
  .doneText1,
  .doneText2,
  .doneText3 {
    fill: ${e.taskTextDarkColor} !important;
  }


  /* Tasks on the critical line */

  .crit0,
  .crit1,
  .crit2,
  .crit3 {
    stroke: ${e.critBorderColor};
    fill: ${e.critBkgColor};
    stroke-width: 2;
  }

  .activeCrit0,
  .activeCrit1,
  .activeCrit2,
  .activeCrit3 {
    stroke: ${e.critBorderColor};
    fill: ${e.activeTaskBkgColor};
    stroke-width: 2;
  }

  .doneCrit0,
  .doneCrit1,
  .doneCrit2,
  .doneCrit3 {
    stroke: ${e.critBorderColor};
    fill: ${e.doneTaskBkgColor};
    stroke-width: 2;
    cursor: pointer;
    shape-rendering: crispEdges;
  }

  .milestone {
    transform: rotate(45deg) scale(0.8,0.8);
  }

  .milestoneText {
    font-style: italic;
  }
  .doneCritText0,
  .doneCritText1,
  .doneCritText2,
  .doneCritText3 {
    fill: ${e.taskTextDarkColor} !important;
  }

  .vert {
    stroke: ${e.vertLineColor};
  }

  .vertText {
    font-size: 15px;
    text-anchor: middle;
    fill: ${e.vertLineColor} !important;
  }

  .activeCritText0,
  .activeCritText1,
  .activeCritText2,
  .activeCritText3 {
    fill: ${e.taskTextDarkColor} !important;
  }

  .titleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${e.titleColor||e.textColor};
    font-family: ${e.fontFamily};
  }
`,"getStyles"),nu=tu,su={parser:go,db:Go,renderer:eu,styles:nu};export{su as diagram};
