import{_ as p,p as xt,q as vt,s as wt,g as bt,b as St,a as Lt,c as lt,z as Et,d as H,V as At,y as Tt,k as Mt}from"./index-D3E3rxcK.js";import{o as Nt}from"./ordinal-Cboi1Yqb.js";import"./init-Gi6I4Gst.js";var rt=Math.PI,st=2*rt,F=1e-6,It=st-F;function ot(){this._x0=this._y0=this._x1=this._y1=null,this._=""}function pt(){return new ot}ot.prototype=pt.prototype={constructor:ot,moveTo:function(t,e){this._+="M"+(this._x0=this._x1=+t)+","+(this._y0=this._y1=+e)},closePath:function(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")},lineTo:function(t,e){this._+="L"+(this._x1=+t)+","+(this._y1=+e)},quadraticCurveTo:function(t,e,s,i){this._+="Q"+ +t+","+ +e+","+(this._x1=+s)+","+(this._y1=+i)},bezierCurveTo:function(t,e,s,i,r,a){this._+="C"+ +t+","+ +e+","+ +s+","+ +i+","+(this._x1=+r)+","+(this._y1=+a)},arcTo:function(t,e,s,i,r){t=+t,e=+e,s=+s,i=+i,r=+r;var a=this._x1,k=this._y1,_=s-t,o=i-e,c=a-t,u=k-e,x=c*c+u*u;if(r<0)throw new Error("negative radius: "+r);if(this._x1===null)this._+="M"+(this._x1=t)+","+(this._y1=e);else if(x>F)if(!(Math.abs(u*_-o*c)>F)||!r)this._+="L"+(this._x1=t)+","+(this._y1=e);else{var v=s-a,g=i-k,w=_*_+o*o,M=v*v+g*g,T=Math.sqrt(w),N=Math.sqrt(x),C=r*Math.tan((rt-Math.acos((w+x-M)/(2*T*N)))/2),D=C/N,R=C/T;Math.abs(D-1)>F&&(this._+="L"+(t+D*c)+","+(e+D*u)),this._+="A"+r+","+r+",0,0,"+ +(u*v>c*g)+","+(this._x1=t+R*_)+","+(this._y1=e+R*o)}},arc:function(t,e,s,i,r,a){t=+t,e=+e,s=+s,a=!!a;var k=s*Math.cos(i),_=s*Math.sin(i),o=t+k,c=e+_,u=1^a,x=a?i-r:r-i;if(s<0)throw new Error("negative radius: "+s);this._x1===null?this._+="M"+o+","+c:(Math.abs(this._x1-o)>F||Math.abs(this._y1-c)>F)&&(this._+="L"+o+","+c),s&&(x<0&&(x=x%st+st),x>It?this._+="A"+s+","+s+",0,1,"+u+","+(t-k)+","+(e-_)+"A"+s+","+s+",0,1,"+u+","+(this._x1=o)+","+(this._y1=c):x>F&&(this._+="A"+s+","+s+",0,"+ +(x>=rt)+","+u+","+(this._x1=t+s*Math.cos(r))+","+(this._y1=e+s*Math.sin(r))))},rect:function(t,e,s,i){this._+="M"+(this._x0=this._x1=+t)+","+(this._y0=this._y1=+e)+"h"+ +s+"v"+ +i+"h"+-s+"Z"},toString:function(){return this._}};function Pt(t){for(var e=t.length/6|0,s=new Array(e),i=0;i<e;)s[i]="#"+t.slice(i*6,++i*6);return s}const Ct=Pt("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab");function kt(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}function Ot(t){return t.length===1&&(t=zt(t)),{left:function(e,s,i,r){for(i==null&&(i=0),r==null&&(r=e.length);i<r;){var a=i+r>>>1;t(e[a],s)<0?i=a+1:r=a}return i},right:function(e,s,i,r){for(i==null&&(i=0),r==null&&(r=e.length);i<r;){var a=i+r>>>1;t(e[a],s)>0?r=a:i=a+1}return i}}}function zt(t){return function(e,s){return kt(t(e),s)}}Ot(kt);function ct(t,e){var s=t.length,i=-1,r,a;if(e==null){for(;++i<s;)if((r=t[i])!=null&&r>=r)for(a=r;++i<s;)(r=t[i])!=null&&r>a&&(a=r)}else for(;++i<s;)if((r=e(t[i],i,t))!=null&&r>=r)for(a=r;++i<s;)(r=e(t[i],i,t))!=null&&r>a&&(a=r);return a}function mt(t,e){var s=t.length,i=-1,r,a;if(e==null){for(;++i<s;)if((r=t[i])!=null&&r>=r)for(a=r;++i<s;)(r=t[i])!=null&&a>r&&(a=r)}else for(;++i<s;)if((r=e(t[i],i,t))!=null&&r>=r)for(a=r;++i<s;)(r=e(t[i],i,t))!=null&&a>r&&(a=r);return a}function nt(t,e){var s=t.length,i=-1,r,a=0;if(e==null)for(;++i<s;)(r=+t[i])&&(a+=r);else for(;++i<s;)(r=+e(t[i],i,t))&&(a+=r);return a}function Dt(t){return t.target.depth}function $t(t){return t.depth}function jt(t,e){return e-1-t.height}function _t(t,e){return t.sourceLinks.length?t.depth:e-1}function Bt(t){return t.targetLinks.length?t.depth:t.sourceLinks.length?mt(t.sourceLinks,Dt)-1:0}function X(t){return function(){return t}}function ut(t,e){return Q(t.source,e.source)||t.index-e.index}function ht(t,e){return Q(t.target,e.target)||t.index-e.index}function Q(t,e){return t.y0-e.y0}function it(t){return t.value}function Rt(t){return t.index}function Vt(t){return t.nodes}function Ft(t){return t.links}function ft(t,e){const s=t.get(e);if(!s)throw new Error("missing: "+e);return s}function yt({nodes:t}){for(const e of t){let s=e.y0,i=s;for(const r of e.sourceLinks)r.y0=s+r.width/2,s+=r.width;for(const r of e.targetLinks)r.y1=i+r.width/2,i+=r.width}}function Wt(){let t=0,e=0,s=1,i=1,r=24,a=8,k,_=Rt,o=_t,c,u,x=Vt,v=Ft,g=6;function w(){const n={nodes:x.apply(null,arguments),links:v.apply(null,arguments)};return M(n),T(n),N(n),C(n),S(n),yt(n),n}w.update=function(n){return yt(n),n},w.nodeId=function(n){return arguments.length?(_=typeof n=="function"?n:X(n),w):_},w.nodeAlign=function(n){return arguments.length?(o=typeof n=="function"?n:X(n),w):o},w.nodeSort=function(n){return arguments.length?(c=n,w):c},w.nodeWidth=function(n){return arguments.length?(r=+n,w):r},w.nodePadding=function(n){return arguments.length?(a=k=+n,w):a},w.nodes=function(n){return arguments.length?(x=typeof n=="function"?n:X(n),w):x},w.links=function(n){return arguments.length?(v=typeof n=="function"?n:X(n),w):v},w.linkSort=function(n){return arguments.length?(u=n,w):u},w.size=function(n){return arguments.length?(t=e=0,s=+n[0],i=+n[1],w):[s-t,i-e]},w.extent=function(n){return arguments.length?(t=+n[0][0],s=+n[1][0],e=+n[0][1],i=+n[1][1],w):[[t,e],[s,i]]},w.iterations=function(n){return arguments.length?(g=+n,w):g};function M({nodes:n,links:y}){for(const[h,l]of n.entries())l.index=h,l.sourceLinks=[],l.targetLinks=[];const f=new Map(n.map((h,l)=>[_(h,l,n),h]));for(const[h,l]of y.entries()){l.index=h;let{source:m,target:b}=l;typeof m!="object"&&(m=l.source=ft(f,m)),typeof b!="object"&&(b=l.target=ft(f,b)),m.sourceLinks.push(l),b.targetLinks.push(l)}if(u!=null)for(const{sourceLinks:h,targetLinks:l}of n)h.sort(u),l.sort(u)}function T({nodes:n}){for(const y of n)y.value=y.fixedValue===void 0?Math.max(nt(y.sourceLinks,it),nt(y.targetLinks,it)):y.fixedValue}function N({nodes:n}){const y=n.length;let f=new Set(n),h=new Set,l=0;for(;f.size;){for(const m of f){m.depth=l;for(const{target:b}of m.sourceLinks)h.add(b)}if(++l>y)throw new Error("circular link");f=h,h=new Set}}function C({nodes:n}){const y=n.length;let f=new Set(n),h=new Set,l=0;for(;f.size;){for(const m of f){m.height=l;for(const{source:b}of m.targetLinks)h.add(b)}if(++l>y)throw new Error("circular link");f=h,h=new Set}}function D({nodes:n}){const y=ct(n,l=>l.depth)+1,f=(s-t-r)/(y-1),h=new Array(y);for(const l of n){const m=Math.max(0,Math.min(y-1,Math.floor(o.call(null,l,y))));l.layer=m,l.x0=t+m*f,l.x1=l.x0+r,h[m]?h[m].push(l):h[m]=[l]}if(c)for(const l of h)l.sort(c);return h}function R(n){const y=mt(n,f=>(i-e-(f.length-1)*k)/nt(f,it));for(const f of n){let h=e;for(const l of f){l.y0=h,l.y1=h+l.value*y,h=l.y1+k;for(const m of l.sourceLinks)m.width=m.value*y}h=(i-h+k)/(f.length+1);for(let l=0;l<f.length;++l){const m=f[l];m.y0+=h*(l+1),m.y1+=h*(l+1)}A(f)}}function S(n){const y=D(n);k=Math.min(a,(i-e)/(ct(y,f=>f.length)-1)),R(y);for(let f=0;f<g;++f){const h=Math.pow(.99,f),l=Math.max(1-h,(f+1)/g);B(y,h,l),P(y,h,l)}}function P(n,y,f){for(let h=1,l=n.length;h<l;++h){const m=n[h];for(const b of m){let L=0,V=0;for(const{source:Y,value:et}of b.targetLinks){let q=et*(b.layer-Y.layer);L+=$(Y,b)*q,V+=q}if(!(V>0))continue;let G=(L/V-b.y0)*y;b.y0+=G,b.y1+=G,E(b)}c===void 0&&m.sort(Q),O(m,f)}}function B(n,y,f){for(let h=n.length,l=h-2;l>=0;--l){const m=n[l];for(const b of m){let L=0,V=0;for(const{target:Y,value:et}of b.sourceLinks){let q=et*(Y.layer-b.layer);L+=I(b,Y)*q,V+=q}if(!(V>0))continue;let G=(L/V-b.y0)*y;b.y0+=G,b.y1+=G,E(b)}c===void 0&&m.sort(Q),O(m,f)}}function O(n,y){const f=n.length>>1,h=n[f];d(n,h.y0-k,f-1,y),z(n,h.y1+k,f+1,y),d(n,i,n.length-1,y),z(n,e,0,y)}function z(n,y,f,h){for(;f<n.length;++f){const l=n[f],m=(y-l.y0)*h;m>1e-6&&(l.y0+=m,l.y1+=m),y=l.y1+k}}function d(n,y,f,h){for(;f>=0;--f){const l=n[f],m=(l.y1-y)*h;m>1e-6&&(l.y0-=m,l.y1-=m),y=l.y0-k}}function E({sourceLinks:n,targetLinks:y}){if(u===void 0){for(const{source:{sourceLinks:f}}of y)f.sort(ht);for(const{target:{targetLinks:f}}of n)f.sort(ut)}}function A(n){if(u===void 0)for(const{sourceLinks:y,targetLinks:f}of n)y.sort(ht),f.sort(ut)}function $(n,y){let f=n.y0-(n.sourceLinks.length-1)*k/2;for(const{target:h,width:l}of n.sourceLinks){if(h===y)break;f+=l+k}for(const{source:h,width:l}of y.targetLinks){if(h===n)break;f-=l}return f}function I(n,y){let f=y.y0-(y.targetLinks.length-1)*k/2;for(const{source:h,width:l}of y.targetLinks){if(h===n)break;f+=l+k}for(const{target:h,width:l}of n.sourceLinks){if(h===y)break;f-=l}return f}return w}function gt(t){return function(){return t}}function Ut(t){return t[0]}function Gt(t){return t[1]}var Yt=Array.prototype.slice;function qt(t){return t.source}function Ht(t){return t.target}function Xt(t){var e=qt,s=Ht,i=Ut,r=Gt,a=null;function k(){var _,o=Yt.call(arguments),c=e.apply(this,o),u=s.apply(this,o);if(a||(a=_=pt()),t(a,+i.apply(this,(o[0]=c,o)),+r.apply(this,o),+i.apply(this,(o[0]=u,o)),+r.apply(this,o)),_)return a=null,_+""||null}return k.source=function(_){return arguments.length?(e=_,k):e},k.target=function(_){return arguments.length?(s=_,k):s},k.x=function(_){return arguments.length?(i=typeof _=="function"?_:gt(+_),k):i},k.y=function(_){return arguments.length?(r=typeof _=="function"?_:gt(+_),k):r},k.context=function(_){return arguments.length?(a=_??null,k):a},k}function Qt(t,e,s,i,r){t.moveTo(e,s),t.bezierCurveTo(e=(e+i)/2,s,e,r,i,r)}function Kt(){return Xt(Qt)}function Zt(t){return[t.source.x1,t.y0]}function Jt(t){return[t.target.x0,t.y1]}function te(){return Kt().source(Zt).target(Jt)}var at=function(){var t=p(function(_,o,c,u){for(c=c||{},u=_.length;u--;c[_[u]]=o);return c},"o"),e=[1,9],s=[1,10],i=[1,5,10,12],r={trace:p(function(){},"trace"),yy:{},symbols_:{error:2,start:3,SANKEY:4,NEWLINE:5,csv:6,opt_eof:7,record:8,csv_tail:9,EOF:10,"field[source]":11,COMMA:12,"field[target]":13,"field[value]":14,field:15,escaped:16,non_escaped:17,DQUOTE:18,ESCAPED_TEXT:19,NON_ESCAPED_TEXT:20,$accept:0,$end:1},terminals_:{2:"error",4:"SANKEY",5:"NEWLINE",10:"EOF",11:"field[source]",12:"COMMA",13:"field[target]",14:"field[value]",18:"DQUOTE",19:"ESCAPED_TEXT",20:"NON_ESCAPED_TEXT"},productions_:[0,[3,4],[6,2],[9,2],[9,0],[7,1],[7,0],[8,5],[15,1],[15,1],[16,3],[17,1]],performAction:p(function(o,c,u,x,v,g,w){var M=g.length-1;switch(v){case 7:const T=x.findOrCreateNode(g[M-4].trim().replaceAll('""','"')),N=x.findOrCreateNode(g[M-2].trim().replaceAll('""','"')),C=parseFloat(g[M].trim());x.addLink(T,N,C);break;case 8:case 9:case 11:this.$=g[M];break;case 10:this.$=g[M-1];break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},{5:[1,3]},{6:4,8:5,15:6,16:7,17:8,18:e,20:s},{1:[2,6],7:11,10:[1,12]},t(s,[2,4],{9:13,5:[1,14]}),{12:[1,15]},t(i,[2,8]),t(i,[2,9]),{19:[1,16]},t(i,[2,11]),{1:[2,1]},{1:[2,5]},t(s,[2,2]),{6:17,8:5,15:6,16:7,17:8,18:e,20:s},{15:18,16:7,17:8,18:e,20:s},{18:[1,19]},t(s,[2,3]),{12:[1,20]},t(i,[2,10]),{15:21,16:7,17:8,18:e,20:s},t([1,5,10],[2,7])],defaultActions:{11:[2,1],12:[2,5]},parseError:p(function(o,c){if(c.recoverable)this.trace(o);else{var u=new Error(o);throw u.hash=c,u}},"parseError"),parse:p(function(o){var c=this,u=[0],x=[],v=[null],g=[],w=this.table,M="",T=0,N=0,C=2,D=1,R=g.slice.call(arguments,1),S=Object.create(this.lexer),P={yy:{}};for(var B in this.yy)Object.prototype.hasOwnProperty.call(this.yy,B)&&(P.yy[B]=this.yy[B]);S.setInput(o,P.yy),P.yy.lexer=S,P.yy.parser=this,typeof S.yylloc>"u"&&(S.yylloc={});var O=S.yylloc;g.push(O);var z=S.options&&S.options.ranges;typeof P.yy.parseError=="function"?this.parseError=P.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function d(L){u.length=u.length-2*L,v.length=v.length-L,g.length=g.length-L}p(d,"popStack");function E(){var L;return L=x.pop()||S.lex()||D,typeof L!="number"&&(L instanceof Array&&(x=L,L=x.pop()),L=c.symbols_[L]||L),L}p(E,"lex");for(var A,$,I,n,y={},f,h,l,m;;){if($=u[u.length-1],this.defaultActions[$]?I=this.defaultActions[$]:((A===null||typeof A>"u")&&(A=E()),I=w[$]&&w[$][A]),typeof I>"u"||!I.length||!I[0]){var b="";m=[];for(f in w[$])this.terminals_[f]&&f>C&&m.push("'"+this.terminals_[f]+"'");S.showPosition?b="Parse error on line "+(T+1)+`:
`+S.showPosition()+`
Expecting `+m.join(", ")+", got '"+(this.terminals_[A]||A)+"'":b="Parse error on line "+(T+1)+": Unexpected "+(A==D?"end of input":"'"+(this.terminals_[A]||A)+"'"),this.parseError(b,{text:S.match,token:this.terminals_[A]||A,line:S.yylineno,loc:O,expected:m})}if(I[0]instanceof Array&&I.length>1)throw new Error("Parse Error: multiple actions possible at state: "+$+", token: "+A);switch(I[0]){case 1:u.push(A),v.push(S.yytext),g.push(S.yylloc),u.push(I[1]),A=null,N=S.yyleng,M=S.yytext,T=S.yylineno,O=S.yylloc;break;case 2:if(h=this.productions_[I[1]][1],y.$=v[v.length-h],y._$={first_line:g[g.length-(h||1)].first_line,last_line:g[g.length-1].last_line,first_column:g[g.length-(h||1)].first_column,last_column:g[g.length-1].last_column},z&&(y._$.range=[g[g.length-(h||1)].range[0],g[g.length-1].range[1]]),n=this.performAction.apply(y,[M,N,T,P.yy,I[1],v,g].concat(R)),typeof n<"u")return n;h&&(u=u.slice(0,-1*h*2),v=v.slice(0,-1*h),g=g.slice(0,-1*h)),u.push(this.productions_[I[1]][0]),v.push(y.$),g.push(y._$),l=w[u[u.length-2]][u[u.length-1]],u.push(l);break;case 3:return!0}}return!0},"parse")},a=function(){var _={EOF:1,parseError:p(function(c,u){if(this.yy.parser)this.yy.parser.parseError(c,u);else throw new Error(c)},"parseError"),setInput:p(function(o,c){return this.yy=c||this.yy||{},this._input=o,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:p(function(){var o=this._input[0];this.yytext+=o,this.yyleng++,this.offset++,this.match+=o,this.matched+=o;var c=o.match(/(?:\r\n?|\n).*/g);return c?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),o},"input"),unput:p(function(o){var c=o.length,u=o.split(/(?:\r\n?|\n)/g);this._input=o+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-c),this.offset-=c;var x=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),u.length-1&&(this.yylineno-=u.length-1);var v=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:u?(u.length===x.length?this.yylloc.first_column:0)+x[x.length-u.length].length-u[0].length:this.yylloc.first_column-c},this.options.ranges&&(this.yylloc.range=[v[0],v[0]+this.yyleng-c]),this.yyleng=this.yytext.length,this},"unput"),more:p(function(){return this._more=!0,this},"more"),reject:p(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:p(function(o){this.unput(this.match.slice(o))},"less"),pastInput:p(function(){var o=this.matched.substr(0,this.matched.length-this.match.length);return(o.length>20?"...":"")+o.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:p(function(){var o=this.match;return o.length<20&&(o+=this._input.substr(0,20-o.length)),(o.substr(0,20)+(o.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:p(function(){var o=this.pastInput(),c=new Array(o.length+1).join("-");return o+this.upcomingInput()+`
`+c+"^"},"showPosition"),test_match:p(function(o,c){var u,x,v;if(this.options.backtrack_lexer&&(v={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(v.yylloc.range=this.yylloc.range.slice(0))),x=o[0].match(/(?:\r\n?|\n).*/g),x&&(this.yylineno+=x.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:x?x[x.length-1].length-x[x.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+o[0].length},this.yytext+=o[0],this.match+=o[0],this.matches=o,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(o[0].length),this.matched+=o[0],u=this.performAction.call(this,this.yy,this,c,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),u)return u;if(this._backtrack){for(var g in v)this[g]=v[g];return!1}return!1},"test_match"),next:p(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var o,c,u,x;this._more||(this.yytext="",this.match="");for(var v=this._currentRules(),g=0;g<v.length;g++)if(u=this._input.match(this.rules[v[g]]),u&&(!c||u[0].length>c[0].length)){if(c=u,x=g,this.options.backtrack_lexer){if(o=this.test_match(u,v[g]),o!==!1)return o;if(this._backtrack){c=!1;continue}else return!1}else if(!this.options.flex)break}return c?(o=this.test_match(c,v[x]),o!==!1?o:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:p(function(){var c=this.next();return c||this.lex()},"lex"),begin:p(function(c){this.conditionStack.push(c)},"begin"),popState:p(function(){var c=this.conditionStack.length-1;return c>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:p(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:p(function(c){return c=this.conditionStack.length-1-Math.abs(c||0),c>=0?this.conditionStack[c]:"INITIAL"},"topState"),pushState:p(function(c){this.begin(c)},"pushState"),stateStackSize:p(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:p(function(c,u,x,v){switch(x){case 0:return this.pushState("csv"),4;case 1:return 10;case 2:return 5;case 3:return 12;case 4:return this.pushState("escaped_text"),18;case 5:return 20;case 6:return this.popState("escaped_text"),18;case 7:return 19}},"anonymous"),rules:[/^(?:sankey-beta\b)/i,/^(?:$)/i,/^(?:((\u000D\u000A)|(\u000A)))/i,/^(?:(\u002C))/i,/^(?:(\u0022))/i,/^(?:([\u0020-\u0021\u0023-\u002B\u002D-\u007E])*)/i,/^(?:(\u0022)(?!(\u0022)))/i,/^(?:(([\u0020-\u0021\u0023-\u002B\u002D-\u007E])|(\u002C)|(\u000D)|(\u000A)|(\u0022)(\u0022))*)/i],conditions:{csv:{rules:[1,2,3,4,5,6,7],inclusive:!1},escaped_text:{rules:[6,7],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7],inclusive:!0}}};return _}();r.lexer=a;function k(){this.yy={}}return p(k,"Parser"),k.prototype=r,r.Parser=k,new k}();at.parser=at;var K=at,J=[],tt=[],Z=new Map,ee=p(()=>{J=[],tt=[],Z=new Map,Tt()},"clear"),W,ne=(W=class{constructor(e,s,i=0){this.source=e,this.target=s,this.value=i}},p(W,"SankeyLink"),W),ie=p((t,e,s)=>{J.push(new ne(t,e,s))},"addLink"),U,re=(U=class{constructor(e){this.ID=e}},p(U,"SankeyNode"),U),se=p(t=>{t=Mt.sanitizeText(t,lt());let e=Z.get(t);return e===void 0&&(e=new re(t),Z.set(t,e),tt.push(e)),e},"findOrCreateNode"),oe=p(()=>tt,"getNodes"),ae=p(()=>J,"getLinks"),le=p(()=>({nodes:tt.map(t=>({id:t.ID})),links:J.map(t=>({source:t.source.ID,target:t.target.ID,value:t.value}))}),"getGraph"),ce={nodesMap:Z,getConfig:p(()=>lt().sankey,"getConfig"),getNodes:oe,getLinks:ae,getGraph:le,addLink:ie,findOrCreateNode:se,getAccTitle:Lt,setAccTitle:St,getAccDescription:bt,setAccDescription:wt,getDiagramTitle:vt,setDiagramTitle:xt,clear:ee},j,dt=(j=class{static next(e){return new j(e+ ++j.count)}constructor(e){this.id=e,this.href=`#${e}`}toString(){return"url("+this.href+")"}},p(j,"Uid"),j.count=0,j),ue={left:$t,right:jt,center:Bt,justify:_t},he=p(function(t,e,s,i){const{securityLevel:r,sankey:a}=lt(),k=Et.sankey;let _;r==="sandbox"&&(_=H("#i"+e));const o=r==="sandbox"?H(_.nodes()[0].contentDocument.body):H("body"),c=r==="sandbox"?o.select(`[id="${e}"]`):H(`[id="${e}"]`),u=(a==null?void 0:a.width)??k.width,x=(a==null?void 0:a.height)??k.width,v=(a==null?void 0:a.useMaxWidth)??k.useMaxWidth,g=(a==null?void 0:a.nodeAlignment)??k.nodeAlignment,w=(a==null?void 0:a.prefix)??k.prefix,M=(a==null?void 0:a.suffix)??k.suffix,T=(a==null?void 0:a.showValues)??k.showValues,N=i.db.getGraph(),C=ue[g];Wt().nodeId(d=>d.id).nodeWidth(10).nodePadding(10+(T?15:0)).nodeAlign(C).extent([[0,0],[u,x]])(N);const S=Nt(Ct);c.append("g").attr("class","nodes").selectAll(".node").data(N.nodes).join("g").attr("class","node").attr("id",d=>(d.uid=dt.next("node-")).id).attr("transform",function(d){return"translate("+d.x0+","+d.y0+")"}).attr("x",d=>d.x0).attr("y",d=>d.y0).append("rect").attr("height",d=>d.y1-d.y0).attr("width",d=>d.x1-d.x0).attr("fill",d=>S(d.id));const P=p(({id:d,value:E})=>T?`${d}
${w}${Math.round(E*100)/100}${M}`:d,"getText");c.append("g").attr("class","node-labels").attr("font-size",14).selectAll("text").data(N.nodes).join("text").attr("x",d=>d.x0<u/2?d.x1+6:d.x0-6).attr("y",d=>(d.y1+d.y0)/2).attr("dy",`${T?"0":"0.35"}em`).attr("text-anchor",d=>d.x0<u/2?"start":"end").text(P);const B=c.append("g").attr("class","links").attr("fill","none").attr("stroke-opacity",.5).selectAll(".link").data(N.links).join("g").attr("class","link").style("mix-blend-mode","multiply"),O=(a==null?void 0:a.linkColor)??"gradient";if(O==="gradient"){const d=B.append("linearGradient").attr("id",E=>(E.uid=dt.next("linearGradient-")).id).attr("gradientUnits","userSpaceOnUse").attr("x1",E=>E.source.x1).attr("x2",E=>E.target.x0);d.append("stop").attr("offset","0%").attr("stop-color",E=>S(E.source.id)),d.append("stop").attr("offset","100%").attr("stop-color",E=>S(E.target.id))}let z;switch(O){case"gradient":z=p(d=>d.uid,"coloring");break;case"source":z=p(d=>S(d.source.id),"coloring");break;case"target":z=p(d=>S(d.target.id),"coloring");break;default:z=O}B.append("path").attr("d",te()).attr("stroke",z).attr("stroke-width",d=>Math.max(1,d.width)),At(void 0,c,0,v)},"draw"),fe={draw:he},ye=p(t=>t.replaceAll(/^[^\S\n\r]+|[^\S\n\r]+$/g,"").replaceAll(/([\n\r])+/g,`
`).trim(),"prepareTextForParsing"),ge=p(t=>`.label {
      font-family: ${t.fontFamily};
    }`,"getStyles"),de=ge,pe=K.parse.bind(K);K.parse=t=>pe(ye(t));var xe={styles:de,parser:K,db:ce,renderer:fe};export{xe as diagram};
