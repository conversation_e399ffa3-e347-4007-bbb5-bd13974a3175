import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  console.log(`Vite running in mode: ${mode}`);

  let apiTarget = '';
  let port = 8080; // 默认端口
  if (mode === 'development') {
    apiTarget = 'http://***********:8000'; // 开发环境
  } else if (mode === 'test') {
    port = 80; // 默认端口
    apiTarget = 'http://***********:8001'; // 测试环境，请替换为您的真实地址
  } else if (mode === 'production') {
    port = 80; // 默认端口
    apiTarget = 'http://your-production-api.com'; // 生产环境，请替换为您的真实地址
  }
  console.log(`Vite running in apiTarget: ${apiTarget}`);


  return {
    plugins: [react()],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    server: {
      host: '0.0.0.0',
      port: port,
      proxy: {
        '/api/v1/device-reports/optimize-mermaid': {
          target: apiTarget, 
          changeOrigin: true,
        },
        '/api/v1/device-reports': {
          target: apiTarget, 
          changeOrigin: true,
        },
        '/api/v1': {
          target: apiTarget, 
          changeOrigin: true,
        }
      }
    },
  }
}) 