# LLM输出格式规范适配说明

## 概述

本次更新根据LLM输出要求对Mermaid图表格式进行了完全适配，支持两种不同的输出格式：**需要代码块标识的图表**和**直接语法图表**。

## 📋 格式规范映射

### 需要 ```mermaid 标识的图表
```
pie（饼图）
graph（流程图） 
flowchart（流程图）
journey（用户旅程图）
gantt（甘特图）
```

### 不需要 ```mermaid 标识的图表
```
sequenceDiagram（序列图）
xychart-beta（XY图表）
timeline（时间线）
architecture-beta（架构图）
gitGraph（Git图）
```

## 🔧 主要改动

### 1. SmartMermaidRenderer 升级

**新增功能：**
- ✅ 双重检测机制：同时支持代码块和直接语法
- ✅ 图表类型自动识别
- ✅ 格式规范映射表
- ✅ 完整性检测增强（支持新图表类型）
- ✅ 状态指示器优化

**关键方法：**
```typescript
// 检测任何Mermaid内容（带或不带代码块）
static hasMermaidContent(content: string): boolean

// 提取所有类型的Mermaid内容块
static extractMermaidBlocks(content: string): Array<{
  code: string;
  type: string; 
  hasCodeBlock: boolean;
}>
```

### 2. MermaidDataCleaner 扩展

**新增功能：**
- ✅ 图表类型智能检测
- ✅ 格式规范自动转换
- ✅ 新图表类型语法修复
- ✅ 混合内容智能处理

**新增方法：**
```typescript
// 根据格式规范清理所有Mermaid内容
static cleanAllMermaidContent(content: string): string

// 检测图表类型
static detectDiagramType(code: string): string | null

// 检查是否需要代码块
static needsCodeBlock(diagramType: string): boolean

// 获取图表类型信息
static getDiagramTypeInfo(code: string): {
  type: string | null;
  needsCodeBlock: boolean;
  description: string;
}
```

**支持的语法修复：**
- Timeline格式转换（时间:事件 → section格式）
- 序列图参与者处理
- XY图表中文标题
- 架构图组件名称
- Git图提交信息
- 用户旅程图中文处理
- 甘特图任务名称

### 3. EnhancedMarkdownRenderer 更新

**改进功能：**
- ✅ 混合格式内容检测
- ✅ 智能内容清理（避免重复显示）
- ✅ 直接图表语法处理
- ✅ 状态指示器增强

## 🧪 测试功能

### 新增测试页面：FormatTestPage

访问路径：`/format-test`

**测试示例包括：**

1. **混合格式** - 包含各种格式的完整示例
2. **仅代码块格式** - 只包含需要```mermaid的图表
3. **仅直接语法** - 只包含不需要代码块的图表  
4. **问题格式** - 自动修复测试

**功能特色：**
- 📊 实时格式分析
- 🔍 内容诊断信息
- 🧹 数据清理器测试
- 📋 格式规范说明

## 💡 使用示例

### 正确的LLM输出格式

#### 需要代码块的图表：
```markdown
## 使用分布饼图

```mermaid
pie
    title 功能使用分布
    "访客接待": 45
    "智能问答": 30
    "参观服务": 25
```

## 时间流程图

```mermaid
graph LR
    A["开始服务"] --> B["身份验证"]
    B --> C["服务选择"]
    C --> D["完成服务"]
```
```

#### 不需要代码块的图表：
```markdown
## 用户交互序列

sequenceDiagram
    participant 用户
    participant 机器人
    participant 系统
    
    用户->>机器人: 提出问题
    机器人->>系统: 处理请求
    系统-->>机器人: 返回结果
    机器人-->>用户: 提供答案

## 服务时间线

timeline
    title 今日服务亮点
    section 上午
        09:00 : 服务启动
        10:30 : 客户高峰
    section 下午  
        14:00 : VIP服务
        16:30 : 维护时间
```

## 🚀 兼容性保证

### 向后兼容
- ✅ 旧版本代码完全兼容
- ✅ 现有API接口不变
- ✅ 原有功能正常工作

### 自动适配
- ✅ 错误格式自动修复
- ✅ 混合格式智能处理
- ✅ 中文内容特殊处理

## 📖 技术细节

### 检测算法
1. **代码块检测**：使用正则表达式`/```mermaid[\s\S]*?```/g`
2. **直接语法检测**：逐行扫描特定图表类型开头
3. **类型识别**：首行关键词匹配
4. **完整性验证**：基于图表类型的结构检查

### 渲染流程
```
内容输入 → 格式检测 → 类型识别 → 语法清理 → 完整性验证 → Mermaid渲染 → 结果输出
```

### 错误处理
- 🛡️ 语法错误自动修复
- 🔄 渲染失败重试机制  
- 📝 详细错误日志
- 🚨 用户友好错误提示

## 🎯 使用建议

### 对于开发者
1. 使用`FormatTestPage`测试新格式
2. 查看控制台日志了解处理过程
3. 根据图表类型选择正确格式

### 对于LLM配置
1. 严格按照格式规范输出
2. 饼图/流程图使用代码块
3. 序列图/时间线使用直接语法
4. 避免混合错误格式

## 🔗 相关页面

- **主报告页面**：`/device-reports`
- **格式测试页面**：`/format-test` 
- **修复演示页面**：`/mermaid-fix`
- **综合测试页面**：`/test`

## 📝 总结

本次适配实现了：

✅ **完全兼容** - 新旧格式都支持  
✅ **自动识别** - 智能检测图表类型  
✅ **格式规范** - 严格按照LLM要求  
✅ **错误修复** - 常见问题自动处理  
✅ **性能优化** - 高效的检测算法  
✅ **用户体验** - 清晰的状态指示  

现在系统可以无缝处理LLM按照新格式规范输出的各种Mermaid图表，确保正确渲染和良好的用户体验。 