/**
 * This file contains mappings from IDs and codes to human-readable names.
 * It acts as a centralized configuration for display transformations.
 */

// --- Product Mappings ---
const PRODUCT_INFO: { [key: number]: { name: string; model: string } } = {
  100000: { name: 'pid测试-2', model: '测试型号' },
  100001: { name: '山西联通电话助理', model: '定制版' },
  100002: { name: '美的带屏音箱_上海', model: '美的合作版' },
  100003: { name: '小雅带屏音箱_上海', model: '小雅合作版' },
  100004: { name: '内部自动标注用_微软中文', model: '内部专用' },
  100005: { name: '内部自动标注用_谷歌中文', model: '内部专用' },
  100006: { name: '北京擎盾_12368_TTS', model: '定制TTS' },
  100007: { name: '中南民族大学协同育人合作', model: '教育合作版' },
  100008: { name: '自助智能终端机语音测试', model: '测试型号' },
  100009: { name: '猎豹AI定制闹钟-tts', model: '定制TTS' },
  100010: { name: 'mini_ToB', model: 'OS-R-SD01B (标准版)' },
  100011: { name: '酒店送货机器人', model: 'OS-R-SD02C (增强版)' },
  100012: { name: '招财豹_餐厅机器人', model: '餐饮专用' },
  100013: { name: '测试环境同步测试PID', model: '测试型号' },
  100014: { name: '豹小秘-接待后台tts', model: '定制TTS' },
  100015: { name: '博看AI硬件TTS组件', model: '硬件组件' },
  100016: { name: '满满科技电话助理', model: '定制版' },
  100017: { name: '合成个性化定制', model: '定制TTS' },
  100018: { name: '豹小秘电话助理_定制TTS', model: '定制TTS' },
  100019: { name: '豹大屏_外售版', model: '外售版' },
  100020: { name: '招财豹-海外版', model: '海外版' },
  100021: { name: '智慧文旅讲解', model: '文旅专用' },
  100022: { name: '我是答题王', model: '娱乐应用' },
  100023: { name: '消毒豹', model: '医疗健康' },
  100024: { name: '豹小秘miniToB海外版', model: '海外版' },
  100025: { name: '铁科院[私有化]', model: '私有化部署' },
  100026: { name: '豹小秘plus', model: '增强版' },
  100027: { name: '招财豹Pro-海外版', model: '海外专业版' },
  100028: { name: '豹小秘plus海外版', model: '海外增强版' },
  // Add other product IDs here
};

export const getProductInfo = (productId: number): { name: string; model: string } => {
  return PRODUCT_INFO[productId] || { name: `未知产品 (${productId})`, model: '未知型号' };
};


// --- Client Mappings ---
const CLIENT_NAMES: { [key: string]: string } = {
  'orion.ovs.client.1503470121484': '猎豹音箱',
  'orion.ovs.client.1507867446289': '美的音响-小豹版-(正式版)',
  'orion.ovs.client.1508751991541': '小雅音箱',
  'orion.ovs.client.1514259512471': '豹小秘家族',
  'orion.ovs.client.1515750369275': '豹豹龙桌面版',
  'orion.ovs.client.1529920911135': '小豹音箱-台湾版',
  'orion.ovs.client.1542176414891': '豹花瓶（商场机器人）',
  'orion.ovs.client.1545723838119': '小豹AI电话手表（4G）',
  'orion.ovs.client.1552992031264': '猎户外呼机器人',
  'orion.ovs.client.1560480338709': '半月谈AI音箱',
  'orion.ovs.client.1574998718607': 'MiniToC',
  'orion.ovs.client.1576152375555': '酒店送货机器人',
  'orion.ovs.client.1592293715505': '豹小递',
  'orion.ovs.client.1597807388298': 'MiniToB',
  'orion.ovs.client.1600162384983': '招财豹',
  'orion.ovs.client.1616055157647': '招财豹-海外版',
  'orion.ovs.client.1617687372221': '智慧文旅讲解',
  'orion.ovs.client.1626163091809': '备用业务线',
  'ysxk.ovs.client.1628045025661': '易视腾-教育项目',
  'orion.ovs.client.1647247816426': '消毒豹',
  'orion.ovs.client.1647585211412': 'MiniToB-海外版',
  'orion.ovs.client.1649320762930': '研发专用',
  'ysxk.ovs.client.1652322635470': '易视星空-移动项目',
  'ysxk.ovs.client.1652439663514': '易视星空-移动项目2',
  'orion.ovs.client.1663659504621': '豹小秘plus',
  'orion.ovs.client.1676460768165': '招财豹Pro-海外版',
  'orion.ovs.client.1684999503219': '豹小秘plus海外版',
  'orion.ovs.client.1684999519675': '豹小秘A95',
  'orion.ovs.client.1748418024858': 'CMG',
  // Add other client IDs here
};

export const getClientName = (clientId: string): string => {
  return CLIENT_NAMES[clientId] || clientId;
};


// --- Group Mappings ---
const GROUP_NAMES: { [key: string]: string } = {
  'ovs.group.158623137963780': '北京总部集群',
  'ovs.group.158623137963781': '上海分部集群',
  // Add other group IDs here
};

export const getGroupName = (groupId: string): string => {
  return GROUP_NAMES[groupId] || groupId;
};


// --- Model/业务线 Mappings ---
const MODEL_INFO: { [key: string]: { name: string; business: string } } = {
  'OS-R-DG02S': { name: '豹小递845', business: '豹小递' },
  'OS-R-SD01C': { name: '豹小秘Mini2C政企', business: 'MiniToB' },
  'OS-R-SD01B': { name: '豹小秘mini_toB', business: 'MiniToB' },
  'OS-R-SG04B': { name: '豹小秘2', business: 'MiniToB' },
  'OS-R-DR01S': { name: '招财豹', business: '招财豹' },
  'OS-R-DR03S': { name: '招财豹Slim', business: '招财豹' },
  'OS-R-DR02G': { name: '招财豹Pro-Carry国内', business: '招财豹' },
  'OS-R-SD01S': { name: '招财豹超极版', business: '招财豹' },
  'OS-RA-SD01S': { name: '招财豹-大屏版', business: '招财豹' },
  'OS-R-DR02S': { name: '招财豹Plus', business: '招财豹' },
  'OS-R-DR01': { name: '招财豹-海外版', business: '招财豹-海外版' },
  'OS-R-XD01': { name: '消毒机器人', business: '消毒豹' },
  'OS-R-SG04': { name: '豹小秘2海外版', business: 'MiniToB-海外版' },
  'OS-R-SD03': { name: 'miniToB海外版', business: 'MiniToB-海外版' },
  'OS-R-PD01S': { name: '豹小秘Plus', business: '豹小秘plus' },
  'OS-R-DR02': { name: '招财豹Pro海外版', business: '招财豹Pro-海外版' },
  'OS-R-DR03': { name: 'Lucki zip海外', business: '招财豹Pro-海外版' },
  'OS-R-DR02C': { name: '招财豹Pro Carry海外', business: '招财豹Pro-海外版' },
  'OS-R-DR04': { name: 'LuckiBot Slim Pro海外', business: '招财豹Pro-海外版' },
  'OS-R-DR03P': { name: 'LuckiBot Slim Pro', business: '招财豹Pro-海外版' },
  'OS-R-PD01': { name: '豹小秘plus海外版', business: '豹小秘plus海外版' },
  'OS-R-SG04S': { name: '豹小秘A95', business: '豹小秘A95' },
  'OS-W-MP01': { name: 'WheelC', business: 'CMG' },
  // ... 其余配置 ...
};

export const getModelInfo = (modelId: string): { name: string; business: string } => {
  return MODEL_INFO[modelId] || { name: modelId || '未知型号', business: '未知业务线' };
};

// --- ClientId → Model/业务线 关联 ---
const CLIENT_MODEL_INFO: { [key: string]: { model_id: string; model_name: string; business: string } } = {
  'orion.ovs.client.1503470121484': { model_id: 'OS-R-SD01B', model_name: '豹小秘mini_toB', business: 'MiniToB' },
  'orion.ovs.client.1507867446289': { model_id: 'OS-R-SD01C', model_name: '豹小秘Mini2C政企', business: 'MiniToB' },
  'orion.ovs.client.1514259512471': { model_id: 'OS-R-SG04B', model_name: '豹小秘2', business: 'MiniToB' },
  // ... 其余 client_id 映射 ...
};

export const getClientModelInfo = (clientId: string): { model_id: string; model_name: string; business: string } => {
  return CLIENT_MODEL_INFO[clientId] || { model_id: '', model_name: '未知型号', business: '未知业务线' };
};

// --- ProductId → Model/业务线 关联 ---
const PRODUCT_MODEL_INFO: { [key: number]: { model_id: string; model_name: string; business: string } } = {
  100010: { model_id: 'OS-R-SD01B', model_name: '豹小秘mini_toB', business: 'MiniToB' },
  100011: { model_id: 'OS-R-SG04B', model_name: '豹小秘2', business: 'MiniToB' },
  // ... 其余 product_id 映射 ...
};

export const getProductModelInfo = (productId: number): { model_id: string; model_name: string; business: string } => {
  return PRODUCT_MODEL_INFO[productId] || { model_id: '', model_name: '未知型号', business: '未知业务线' };
}; 