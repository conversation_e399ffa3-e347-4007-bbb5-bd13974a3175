// Mermaid优化服务
export interface MermaidOptimizeRequest {
  mermaid_code: string;
  prompt_version: string;
}

export interface MermaidOptimizeResponse {
  status: 'success' | 'error';
  original_code: string;
  optimized_code: string;
  optimization_summary: {
    changes_detected: string[];
    statistics: {
      lines_changed: number;
      chars_changed: number;
      original_lines: number;
      optimized_lines: number;
      original_chars: number;
      optimized_chars: number;
    };
    optimization_needed: boolean;
  };
  timestamp: string;
  message: string;
  error?: string;
}

export class MermaidOptimizer {
  private static getApiUrl(): string {
    // 使用相对路径，让vite代理处理
    // 根据vite.config.ts的配置，/api/v1/device-reports/optimize-mermaid会被代理到对应的API服务器
    return '/api/v1/device-reports/optimize-mermaid';
  }
  
  private static cache = new Map<string, string>(); // 缓存优化结果
  private static maxCacheSize = 100; // 最大缓存数量

  /**
   * 优化Mermaid代码
   * @param mermaidCode 原始Mermaid代码
   * @returns 优化后的代码，如果失败则返回原始代码
   */
  static async optimize(mermaidCode: string): Promise<string> {
    try {
      // 检查缓存
      const cacheKey = this.generateCacheKey(mermaidCode);
      if (this.cache.has(cacheKey)) {
        console.log('🎯 使用缓存的Mermaid优化结果');
        return this.cache.get(cacheKey)!;
      }

      console.log('🚀 开始调用Mermaid优化API...');
      
      const request: MermaidOptimizeRequest = {
        mermaid_code: mermaidCode,
        prompt_version: 'MERMAID_OPTIMIZE'
      };

      const response = await fetch(this.getApiUrl(), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result: MermaidOptimizeResponse = await response.json();
      
      if (result.status === 'success') {
        console.log('✅ Mermaid优化成功:', {
          changes: result.optimization_summary.changes_detected,
          statistics: result.optimization_summary.statistics
        });
        
        // 缓存结果
        this.cacheResult(cacheKey, result.optimized_code);
        
        return result.optimized_code;
      } else {
        console.warn('⚠️ Mermaid优化失败:', result.message);
        return mermaidCode; // 返回原始代码
      }
      
    } catch (error) {
      console.error('❌ Mermaid优化API调用失败:', error);
      return mermaidCode; // 返回原始代码，不影响正常流程
    }
  }

  /**
   * 批量优化多个Mermaid代码块
   * @param mermaidBlocks Mermaid代码块数组
   * @returns 优化后的代码块数组
   */
  static async optimizeBatch(mermaidBlocks: string[]): Promise<string[]> {
    const optimizedBlocks: string[] = [];
    
    // 并发优化，但限制并发数量
    const batchSize = 3; // 最多同时处理3个
    for (let i = 0; i < mermaidBlocks.length; i += batchSize) {
      const batch = mermaidBlocks.slice(i, i + batchSize);
      const batchPromises = batch.map(code => this.optimize(code));
      
      try {
        const batchResults = await Promise.all(batchPromises);
        optimizedBlocks.push(...batchResults);
      } catch (error) {
        console.error(`❌ 批量优化第${i/batchSize + 1}批失败:`, error);
        // 如果批量失败，使用原始代码
        optimizedBlocks.push(...batch);
      }
    }
    
    return optimizedBlocks;
  }

  /**
   * 生成缓存键
   */
  private static generateCacheKey(mermaidCode: string): string {
    // 使用简单的哈希函数
    let hash = 0;
    for (let i = 0; i < mermaidCode.length; i++) {
      const char = mermaidCode.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString();
  }

  /**
   * 缓存优化结果
   */
  private static cacheResult(key: string, optimizedCode: string): void {
          // 如果缓存已满，删除最旧的条目
      if (this.cache.size >= this.maxCacheSize) {
        const firstKey = this.cache.keys().next().value;
        if (firstKey !== undefined) {
          this.cache.delete(firstKey);
        }
      }
    
    this.cache.set(key, optimizedCode);
  }

  /**
   * 清空缓存
   */
  static clearCache(): void {
    this.cache.clear();
    console.log('🧹 Mermaid优化缓存已清空');
  }

  /**
   * 获取缓存统计信息
   */
  static getCacheStats(): { size: number; maxSize: number } {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize
    };
  }
} 