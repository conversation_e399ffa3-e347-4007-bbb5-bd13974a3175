import React, { useState } from 'react';
import EnhancedMarkdownRenderer from '../components/EnhancedMarkdownRenderer';

const TestPage: React.FC = () => {
  const [testContent, setTestContent] = useState(`# 🚀 重构后测试页面

这是一个用于测试重构后组件的页面。

## 📊 测试Mermaid图表

下面是一个简单的饼图：

\`\`\`mermaid
pie title 系统使用统计
    "正常运行" : 85
    "维护中" : 10
    "故障" : 5
\`\`\`

## 📋 测试流程图

\`\`\`mermaid
graph TD
    A[开始] --> B{检查状态}
    B -->|正常| C[执行任务]
    B -->|异常| D[错误处理]
    C --> E[完成]
    D --> E
    E --> F[结束]
\`\`\`

## ✅ 功能特点

- **智能Mermaid检测**: 自动检测并等待完整的Mermaid代码
- **原始数据展示**: 可以查看接收到的原始数据
- **错误处理**: 友好的错误提示和重试机制
- **清洁架构**: 删除了所有冗余文件

## 📝 文档内容

这是普通的Markdown文本内容：

1. 第一项功能
2. 第二项功能  
3. 第三项功能

> **注意**: 这是一个引用块，用于强调重要信息。

### 代码示例

\`\`\`javascript
// 这是普通的JavaScript代码块
function hello() {
  console.log("Hello, World!");
}
\`\`\`

### 表格示例

| 功能 | 状态 | 说明 |
|------|------|------|
| Mermaid渲染 | ✅ 完成 | 支持多种图表类型 |
| 数据展示 | ✅ 完成 | 实时显示原始数据 |
| 错误处理 | ✅ 完成 | 友好的错误提示 |
`);

  const [isEditing, setIsEditing] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">🧪 重构测试页面</h1>
          <p className="text-lg text-gray-600">验证新的智能Mermaid渲染器和增强版Markdown渲染器</p>
          
          <div className="mt-6 flex justify-center gap-4">
            <button
              onClick={() => setIsEditing(!isEditing)}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                isEditing 
                  ? 'bg-green-600 text-white shadow-lg'
                  : 'bg-blue-600 text-white shadow-lg hover:bg-blue-700'
              }`}
            >
              {isEditing ? '💾 保存内容' : '✏️ 编辑内容'}
            </button>
          </div>
        </div>

        {isEditing ? (
          <div className="bg-white rounded-lg border shadow-sm">
            <div className="p-4 border-b bg-gray-50">
              <h3 className="font-semibold text-gray-900">编辑测试内容</h3>
              <p className="text-sm text-gray-600 mt-1">修改下面的内容来测试不同的功能</p>
            </div>
            <div className="p-4">
              <textarea
                value={testContent}
                onChange={(e) => setTestContent(e.target.value)}
                className="w-full h-96 p-4 border border-gray-300 rounded-lg font-mono text-sm resize-y focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="输入Markdown内容..."
              />
            </div>
          </div>
        ) : (
          <div>
            <EnhancedMarkdownRenderer 
              content={testContent}
              showRawData={true}
              onMermaidError={(error) => {
                console.error('测试页面Mermaid错误:', error);
                alert(`Mermaid渲染错误: ${error}`);
              }}
            />
          </div>
        )}

        {/* 说明信息 */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-blue-900 font-semibold mb-3">🎯 测试指南</h3>
          <div className="space-y-2 text-blue-800 text-sm">
            <p>• <strong>图表渲染</strong>: 观察Mermaid图表是否正确显示</p>
            <p>• <strong>状态指示</strong>: 查看渲染状态指示器的变化</p>
            <p>• <strong>原始数据</strong>: 点击"显示原始数据"查看接收到的内容</p>
            <p>• <strong>错误处理</strong>: 尝试编辑内容，输入错误的Mermaid语法测试错误处理</p>
            <p>• <strong>完整性检测</strong>: 逐步输入Mermaid代码，观察何时开始渲染</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestPage; 