import React, { useState, useMemo } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getDeviceStatistics } from '@/api/stats';
import { StatisticsDataItem } from '@/api/types';
import SessionDetailsModal from '@/components/SessionDetailsModal';
import { 
  ChevronLeft, 
  Users, 
  MessageSquare, 
  Activity, 
  CheckCircle,
  XCircle,
  Zap,
  RefreshCw,
  BarChart3,
  Target,
  TrendingUp,
  Clock,
  Calendar,
  Download,
  PieChart,
  Settings
} from 'lucide-react';
import ReactECharts from 'echarts-for-react';
import 'echarts-wordcloud';
import ErrorBoundary from '../components/ErrorBoundary';
import DevicePageHeader from '../components/DevicePageHeader';
import { KEY_FOCUSED_DEVICE_IDS } from '../config';
import { usePageTitle } from '../lib/usePageTitle';
import { useDeviceAndEnterpriseInfo } from '../lib/useDeviceAndEnterpriseInfo';

const DeviceAnalysisPage: React.FC = () => {
  const { enterpriseId, deviceId } = useParams<{ enterpriseId: string; deviceId: string }>();
  const { info: deviceInfo, isLoading: isDeviceLoading } = useDeviceAndEnterpriseInfo(enterpriseId, deviceId);

  const pageTitle = useMemo(() => {
    if (isDeviceLoading) {
      return '加载中...';
    }
    if (deviceInfo) {
      return `${deviceInfo.enterprise_name} - ${deviceInfo.device_name} - 设备分析`;
    }
    return '设备分析';
  }, [deviceInfo, isDeviceLoading]);

  usePageTitle(pageTitle);

  const navigate = useNavigate();
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');
  const [hourTimeRange, setHourTimeRange] = useState<'24h' | '48h' | '72h'>('24h');
  const [groupBy, setGroupBy] = useState<'day' | 'hour'>('day');
  const [selectedSessionIds, setSelectedSessionIds] = useState<string[]>([]);
  const [showSessionDetails, setShowSessionDetails] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalSessionIds, setModalSessionIds] = useState<string[]>([]);

  const parseSessionIds = (data: unknown): string[] => {
    if (!data) {
      return [];
    }
    if (Array.isArray(data)) {
      // If it's an array, map over it and flatten the result.
      // This handles nested arrays and arrays containing JSON strings.
      return data.flatMap(item => parseSessionIds(item));
    }
    if (typeof data === 'string') {
      // If it's a string, try to parse it as JSON.
      try {
        const parsed = JSON.parse(data);
        // If parsing is successful, recursively call parse on the result.
        // This handles stringified arrays like '["a", "b"]'.
        return parseSessionIds(parsed);
      } catch (e) {
        // If parsing fails, it's just a regular string ID.
        if (data) {
          return [data];
        }
        return [];
      }
    }
    // For any other type, return empty array.
    return [];
  };

  const formatAxisLabel = (timeBucket: string | undefined): string => {
    const date = new Date(timeBucket || new Date());
    if (groupBy === 'hour') {
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const hour = date.getHours();
      return `${month}-${day} ${hour}点`;
    }
    return date.toLocaleDateString();
  };

  const toBeijingISOString = (date: Date): string => {
    const beijingTimeFormatter = new Intl.DateTimeFormat('sv-SE', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hourCycle: 'h23',
    });
    return beijingTimeFormatter.format(date).replace(' ', 'T');
  };

  // 计算时间范围
  const getTimeRange = () => {
    const now = new Date();
    let startTime: Date;
    let endTime: Date;
    
    if (groupBy === 'hour') {
      // 按小时统计：使用较短的时间范围
      endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours(), now.getMinutes(), now.getSeconds());
      
      switch (hourTimeRange) {
        case '24h':
          startTime = new Date(endTime.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '48h':
          startTime = new Date(endTime.getTime() - 48 * 60 * 60 * 1000);
          break;
        case '72h':
          startTime = new Date(endTime.getTime() - 72 * 60 * 60 * 1000);
          break;
        default:
          startTime = new Date(endTime.getTime() - 24 * 60 * 60 * 1000);
      }
    } else {
      // 按天统计：使用较长的时间范围
      endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
      
      switch (timeRange) {
        case '7d':
          startTime = new Date(endTime.getTime() - 6 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startTime = new Date(endTime.getTime() - 29 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          startTime = new Date(endTime.getTime() - 89 * 24 * 60 * 60 * 1000);
          break;
        default:
          startTime = new Date(endTime.getTime() - 29 * 24 * 60 * 60 * 1000);
      }
    }
    
    return {
      startTime: toBeijingISOString(startTime),
      endTime: toBeijingISOString(endTime),
    };
  };

  const { startTime, endTime } = getTimeRange();

  const { data: statistics, isLoading: isLoadingStats, refetch } = useQuery({
    queryKey: ['deviceStatistics', enterpriseId, deviceId, startTime, endTime, groupBy, groupBy === 'hour' ? hourTimeRange : timeRange],
    queryFn: () => getDeviceStatistics(enterpriseId!, deviceId!, startTime, endTime, groupBy),
    enabled: !!enterpriseId && !!deviceId,
  });

  // 汇总所有天或小时的数据，生成卡片用的汇总对象
  const getSummaryFromStatistics = () => {
    if (!statistics?.data || statistics.data.length === 0) return null;
    const dataArr = statistics.data;

    let total_sessions = 0;
    let valid_sessions = 0;
    let no_response_sessions = 0;
    let action_trigger_count = 0;
    let user_messages = 0;
    let assistant_messages = 0;
    let total_conversation_turns = 0;
    let total_session_duration = 0;
    let session_count_for_avg = 0;
    let total_consecutive_active_hours = 0;
    let event_trigger_count = 0;
    let total_assistant_first_ratio = 0;
    let total_greeting_ratio = 0;

    const all_session_ids_total = new Set<string>();
    const all_session_ids_valid = new Set<string>();
    const all_session_ids_no_response = new Set<string>();
    const all_session_ids_action_triggered = new Set<string>();
    const all_session_ids_event_triggered = new Set<string>();
    const all_session_ids_user = new Set<string>();
    const all_session_ids_assistant = new Set<string>();

    dataArr.forEach(item => {
      const current_total_sessions = Number(item.session_behavior_summary.total_sessions) || 0;
      total_sessions += current_total_sessions;
      valid_sessions += Number(item.session_behavior_summary.valid_sessions) || 0;
      no_response_sessions += Number(item.session_behavior_summary.no_response_sessions) || 0;
      action_trigger_count += Number(item.message_behavior_summary.action_trigger_count) || 0;
      event_trigger_count += Number(item.message_behavior_summary.event_trigger_count) || 0;
      user_messages += Number(item.message_behavior_summary.user_messages) || 0;
      assistant_messages += Number(item.message_behavior_summary.assistant_messages) || 0;
      total_conversation_turns += (Number(item.session_behavior_summary.avg_conversation_turns) || 0) * current_total_sessions;
      total_session_duration += (Number(item.session_behavior_summary.avg_session_duration) || 0) * current_total_sessions;
      session_count_for_avg += current_total_sessions;
      
      const safeAddIds = (idSource: unknown, targetSet: Set<string>) => {
        const ids = parseSessionIds(idSource);
        ids.forEach(id => targetSet.add(id));
      };
      
      safeAddIds(item.session_behavior_summary.session_ids_total, all_session_ids_total);
      safeAddIds(item.session_behavior_summary.session_ids_valid, all_session_ids_valid);
      safeAddIds(item.session_behavior_summary.session_ids_no_response, all_session_ids_no_response);
      safeAddIds(item.message_behavior_summary.session_ids_action_triggered, all_session_ids_action_triggered);
      safeAddIds(item.message_behavior_summary.session_ids_event_triggered, all_session_ids_event_triggered);
      safeAddIds(item.message_behavior_summary.session_ids_user, all_session_ids_user);
      safeAddIds(item.message_behavior_summary.session_ids_assistant, all_session_ids_assistant);

      if (current_total_sessions > 0 && item.active_hours_summary) {
        total_consecutive_active_hours += (Number(item.active_hours_summary.avg_consecutive_active_hours) || 0) * current_total_sessions;
        total_assistant_first_ratio += (Number(item.active_hours_summary.avg_assistant_first_ratio) || 0) * current_total_sessions;
        total_greeting_ratio += (Number(item.active_hours_summary.avg_greeting_ratio) || 0) * current_total_sessions;
      }
    });

    return {
      total_sessions,
      valid_sessions,
      no_response_sessions,
      action_trigger_count,
      event_trigger_count,
      user_messages,
      assistant_messages,
      avg_conversation_turns: session_count_for_avg ? total_conversation_turns / session_count_for_avg : 0,
      avg_session_duration: session_count_for_avg ? total_session_duration / session_count_for_avg : 0,
      avg_consecutive_active_hours: total_sessions ? total_consecutive_active_hours / total_sessions : 0,
      avg_assistant_first_ratio: total_sessions ? total_assistant_first_ratio / total_sessions : 0,
      avg_greeting_ratio: total_sessions ? total_greeting_ratio / total_sessions : 0,
      session_ids_total: Array.from(all_session_ids_total),
      session_ids_valid: Array.from(all_session_ids_valid),
      session_ids_no_response: Array.from(all_session_ids_no_response),
      session_ids_action_triggered: Array.from(all_session_ids_action_triggered),
      session_ids_event_triggered: Array.from(all_session_ids_event_triggered),
      session_ids_user: Array.from(all_session_ids_user),
      session_ids_assistant: Array.from(all_session_ids_assistant),
    };
  };

  const handleCardClick = () => {
    if (groupBy === 'hour') {
      setSelectedSessionIds(['session1', 'session2', 'session3']); // Placeholder
      setShowSessionDetails(true);
    }
  };

  const openSessionModal = (sessionIds: string[] | undefined) => {
    if (sessionIds && sessionIds.length > 0) {
      setModalSessionIds(sessionIds);
      setIsModalOpen(true);
    }
  };

  const renderDayMetricCards = () => {
    const summary = getSummaryFromStatistics();
    if (!summary) return null;

    const cards = [
      { title: '累计会话数', value: String(summary.total_sessions), icon: <Users className="w-6 h-6" />, color: 'bg-blue-500', trendColor: 'text-green-600', sessionIds: summary.session_ids_total },
      { title: '累计有效会话', value: String(summary.valid_sessions), icon: <CheckCircle className="w-6 h-6" />, color: 'bg-green-500', trendColor: 'text-green-600', sessionIds: summary.session_ids_valid },
      { title: '累计消息数', value: String(summary.user_messages + summary.assistant_messages), icon: <MessageSquare className="w-6 h-6" />, color: 'bg-purple-500',  trendColor: 'text-green-600', sessionIds: summary.session_ids_total },
      { title: '平均对话轮数', value: summary.avg_conversation_turns.toFixed(1), icon: <Activity className="w-6 h-6" />, color: 'bg-orange-500', trendColor: 'text-green-600', sessionIds: summary.session_ids_total },
      { title: '平均会话时长(s)', value: summary.avg_session_duration.toFixed(1), icon: <Clock className="w-6 h-6" />, color: 'bg-teal-500', trendColor: 'text-green-600', sessionIds: summary.session_ids_total },
      { title: '累计Action触发', value: String(summary.action_trigger_count), icon: <Zap className="w-6 h-6" />, color: 'bg-yellow-500', trendColor: 'text-green-600', sessionIds: summary.session_ids_action_triggered },
      { title: '累计无响应会话', value: String(summary.no_response_sessions), icon: <XCircle className="w-6 h-6" />, color: 'bg-red-500',  trendColor: 'text-red-500', sessionIds: summary.session_ids_no_response },
    ];

    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7 gap-4 mb-8">
        {cards.map((card, index) => (
          <div key={index} className="bg-white rounded-xl shadow-lg p-4 border border-gray-100 hover:shadow-xl transition-shadow cursor-pointer" onClick={() => openSessionModal(card.sessionIds)}>
            <div className="flex items-center">
              <div className={`p-3 rounded-lg ${card.color} text-white`}>{card.icon}</div>
              <div className="ml-4 text-base font-medium text-gray-600">{card.title}</div>
            </div>
            <div className="mt-2 flex items-baseline justify-between">
              <span className="text-3xl font-extrabold text-gray-900">{card.value}</span>
              <span className={`text-sm font-medium ${card.trendColor}`}>{card.trend} vs 上期</span>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderHourMetricCards = () => {
    const summary = getSummaryFromStatistics();
    if (!summary) return null;

    const cards = [
      { title: '当前时段会话数', value: String(summary.total_sessions), icon: <Clock className="w-6 h-6" />, color: 'bg-blue-500', trendColor: 'text-green-600', sessionIds: summary.session_ids_total },
      { title: '当前时段消息数', value: String(summary.user_messages + summary.assistant_messages), icon: <MessageSquare className="w-6 h-6" />, color: 'bg-purple-500',  trendColor: 'text-green-600', sessionIds: summary.session_ids_total },
      { title: '当前时段Action触发', value: String(summary.action_trigger_count), icon: <Zap className="w-6 h-6" />, color: 'bg-yellow-500', trendColor: 'text-green-600', sessionIds: summary.session_ids_action_triggered },
      { title: '平均对话轮数', value: summary.avg_conversation_turns.toFixed(1), icon: <Activity className="w-6 h-6" />, color: 'bg-orange-500', trendColor: 'text-green-600', sessionIds: summary.session_ids_total },
    ];

    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {cards.map((card, index) => (
          <div key={index} className="bg-white rounded-xl shadow-lg p-4 border border-gray-100 hover:shadow-xl transition-shadow cursor-pointer" onClick={() => openSessionModal(card.sessionIds)}>
            <div className="flex items-center">
              <div className={`p-3 rounded-lg ${card.color} text-white`}>{card.icon}</div>
              <div className="ml-4 text-base font-medium text-gray-600">{card.title}</div>
            </div>
            <div className="mt-2 flex items-baseline justify-between">
              <span className="text-3xl font-extrabold text-gray-900">{card.value}</span>
              <span className={`text-sm font-medium ${card.trendColor}`}>{card.trend} vs 上期</span>
            </div>
          </div>
        ))}
      </div>
    );
  };
  
  const renderTrendChart = () => {
    if (!statistics?.data) return null;

    const dates = statistics.data.map(d => formatAxisLabel(d.time_bucket));
    const totalSessions = statistics.data.map(d => Number(d.session_behavior_summary.total_sessions));
    const validSessions = statistics.data.map(d => Number(d.session_behavior_summary.valid_sessions));
    const noResponseSessions = statistics.data.map(d => Number(d.session_behavior_summary.no_response_sessions));
    const avgDurations = statistics.data.map(d => Number(d.session_behavior_summary.avg_session_duration).toFixed(2));
    const avgTurns = statistics.data.map(d => Number(d.session_behavior_summary.avg_conversation_turns).toFixed(2));

    const onChartClick = (params: any) => {
      if (params.seriesName === '总会話数' && statistics.data[params.dataIndex]?.session_behavior_summary.session_ids_total) {
        openSessionModal(parseSessionIds(statistics.data[params.dataIndex].session_behavior_summary.session_ids_total));
      } else if (params.seriesName === '有效会话数' && statistics.data[params.dataIndex]?.session_behavior_summary.session_ids_valid) {
        openSessionModal(parseSessionIds(statistics.data[params.dataIndex].session_behavior_summary.session_ids_valid));
      } else if (params.seriesName === '无响应会话数' && statistics.data[params.dataIndex]?.session_behavior_summary.session_ids_no_response) {
        openSessionModal(parseSessionIds(statistics.data[params.dataIndex].session_behavior_summary.session_ids_no_response));
      }
    };
    
    const onEvents = {
      'click': onChartClick,
    };

    const option = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['总会话数', '有效会话数', '无响应会话数', '平均时长(秒)', '平均对话轮数']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: dates
      },
      yAxis: [
        {
          type: 'value',
          name: '会话数',
          position: 'left',
        },
        {
          type: 'value',
          name: '平均值',
          position: 'right',
        }
      ],
      series: [
        {
          name: '总会话数',
          type: 'line',
          yAxisIndex: 0,
          data: totalSessions,
          smooth: true,
          symbol: 'circle',
          symbolSize: 8
        },
        {
          name: '有效会话数',
          type: 'line',
          yAxisIndex: 0,
          data: validSessions,
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            type: 'dashed'
          }
        },
        {
          name: '无响应会话数',
          type: 'line',
          yAxisIndex: 0,
          data: noResponseSessions,
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            type: 'dotted'
          }
        },
        {
          name: '平均时长(秒)',
          type: 'line',
          yAxisIndex: 1,
          data: avgDurations,
          smooth: true,
          symbol: 'triangle',
          symbolSize: 8
        },
        {
          name: '平均对话轮数',
          type: 'line',
          yAxisIndex: 1,
          data: avgTurns,
          smooth: true,
          symbol: 'diamond',
          symbolSize: 8
        }
      ]
    };

    return (
      <ReactECharts option={option} style={{ height: 400 }} onEvents={onEvents} />
    );
  };

  const renderMessageTrendChart = () => {
    if (!statistics?.data) return null;

    const dates = statistics.data.map(d => formatAxisLabel(d.time_bucket));
    const userMessages = statistics.data.map(d => Number(d.message_behavior_summary.user_messages));
    const assistantMessages = statistics.data.map(d => Number(d.message_behavior_summary.assistant_messages));
    const actionTriggers = statistics.data.map(d => Number(d.message_behavior_summary.action_trigger_count));
    const eventTriggers = statistics.data.map(d => Number(d.message_behavior_summary.event_trigger_count));

    const onChartClick = (params: any) => {
      const dataIndex = params.dataIndex;
      if (!statistics.data[dataIndex]) return;

      const messageSummary = statistics.data[dataIndex].message_behavior_summary;
      let sessionIds: string[] = [];

      switch (params.seriesName) {
        case '用户消息数':
          sessionIds = parseSessionIds(messageSummary.session_ids_user);
          break;
        case '助手消息数':
          sessionIds = parseSessionIds(messageSummary.session_ids_assistant);
          break;
        case 'Action触发数':
          sessionIds = parseSessionIds(messageSummary.session_ids_action_triggered);
          break;
        case 'Event触发数':
          sessionIds = parseSessionIds(messageSummary.session_ids_event_triggered);
          break;
      }
      
      if (sessionIds.length > 0) {
        openSessionModal(sessionIds);
      }
    };
    
    const onEvents = {
      'click': onChartClick,
    };

    const option = {
      tooltip: { trigger: 'axis' },
      legend: {
        data: ['用户消息数', '助手消息数', 'Action触发数', 'Event触发数']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: { type: 'category', boundaryGap: false, data: dates },
      yAxis: { type: 'value', name: '数量' },
      series: [
        { name: '用户消息数', type: 'line', data: userMessages, smooth: true },
        { name: '助手消息数', type: 'line', data: assistantMessages, smooth: true },
        { name: 'Action触发数', type: 'line', data: actionTriggers, smooth: true, lineStyle: { type: 'dashed' } },
        { name: 'Event触发数', type: 'line', data: eventTriggers, smooth: true, lineStyle: { type: 'dotted' } }
      ]
    };

    return (
      <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
        <div className="flex items-center mb-4">
          <BarChart3 className="w-6 h-6 text-gray-700" />
          <h3 className="text-lg font-semibold ml-2 text-gray-800">消息行为趋势</h3>
        </div>
        <ReactECharts option={option} style={{ height: 400 }} onEvents={onEvents} />
      </div>
    );
  };

  const renderDurationChart = () => {
    const data = statistics?.data;
    if (!data || data.length === 0) return <div className="chart-placeholder">暂无会话时长数据</div>;

    const durationData = data.reduce((acc, item) => {
      acc.bucket_lt_30s += Number(item.duration_distribution.bucket_lt_30s) || 0;
      acc.bucket_30_60s += Number(item.duration_distribution.bucket_30_60s) || 0;
      acc.bucket_1_3min += Number(item.duration_distribution.bucket_1_3min) || 0;
      acc.bucket_3_5min += Number(item.duration_distribution.bucket_3_5min) || 0;
      acc.bucket_5_10min += Number(item.duration_distribution.bucket_5_10min) || 0;
      acc.over_10min += (Number(item.duration_distribution.bucket_10_20min) || 0) + (Number(item.duration_distribution.bucket_gt_20min) || 0);
      return acc;
    }, {
      bucket_lt_30s: 0,
      bucket_30_60s: 0,
      bucket_1_3min: 0,
      bucket_3_5min: 0,
      bucket_5_10min: 0,
      over_10min: 0,
    });
    
    const sessionIdsByBucket = data.reduce<Record<string, string[]>>((acc, item) => {
      if (item.duration_distribution.session_ids) {
        Object.entries(item.duration_distribution.session_ids).forEach(([key, ids]) => {
          if (!acc[key]) acc[key] = [];
          acc[key].push(...parseSessionIds(ids as any));
        });
      }
      return acc;
    }, {});

    const totalSessions = Object.values(durationData).reduce((sum, value) => sum + value, 0);
    if (totalSessions === 0) {
      return <div className="chart-placeholder">暂无会话时长数据</div>;
    }
    
    const chartData = [
      { value: durationData.bucket_lt_30s, name: '<30秒', session_ids: sessionIdsByBucket['bucket_lt_30s'] },
      { value: durationData.bucket_30_60s, name: '30-60秒', session_ids: sessionIdsByBucket['bucket_30_60s'] },
      { value: durationData.bucket_1_3min, name: '1-3分钟', session_ids: sessionIdsByBucket['bucket_1_3min'] },
      { value: durationData.bucket_3_5min, name: '3-5分钟', session_ids: sessionIdsByBucket['bucket_3_5min'] },
      { value: durationData.bucket_5_10min, name: '5-10分钟', session_ids: sessionIdsByBucket['bucket_5_10min'] },
      { value: durationData.over_10min, name: '>10分钟', session_ids: [...(sessionIdsByBucket['bucket_10_20min'] || []), ...(sessionIdsByBucket['bucket_gt_20min'] || [])] },
    ].filter(d => d.value > 0);

    const onChartClick = (params: any) => {
      if (params.data && params.data.session_ids) {
        openSessionModal(parseSessionIds(params.data.session_ids));
      }
    };

    const chartEvents = {
      'click': onChartClick,
    };

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'center',
        data: chartData.map(d => d.name)
      },
      series: [
        {
          name: '会话时长',
          type: 'pie',
          radius: ['50%', '70%'],
          center: ['65%', '50%'],
          avoidLabelOverlap: true,
          label: {
            show: true,
            formatter: '{b}\n{d}%',
            position: 'outside'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: true,
            length: 10,
            length2: 15
          },
          data: chartData,
        }
      ],
      xAxis: { show: false },
      yAxis: { show: false },
    };
    return (
      <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 h-full">
        <div className="flex items-center mb-4">
          <Target className="w-6 h-6 text-gray-700" />
          <h3 className="text-lg font-semibold ml-2 text-gray-800">会话时长分布</h3>
        </div>
        <ReactECharts option={option} style={{ height: 400 }} onEvents={chartEvents} />
      </div>
    );
  };

  const getActionChartData = () => {
    if (!statistics?.data) return [];
    const actionData = statistics.data
      .flatMap(d => d.action_behavior_summary || [])
      .reduce<Record<string, { name: string; value: number; sessionIds: string[] }>>((acc, action) => {
        if (action.name) {
          if (!acc[action.name]) {
            acc[action.name] = {
              name: action.display_name || action.name,
              value: 0,
              sessionIds: []
            };
          }
          acc[action.name].value += Number(action.count) || 0;
          acc[action.name].sessionIds.push(...parseSessionIds(action.session_ids));
        }
        return acc;
      }, {});

    return Object.values(actionData).sort((a, b) => b.value - a.value);
  }

  const renderActionChart = () => {
    const chartData = getActionChartData();
    if (chartData.length === 0) return null;

    const onChartClick = (params: any) => {
      const actionName = params.name;
      const clickedAction = chartData.find(d => d.name === actionName);
      if (clickedAction && clickedAction.sessionIds.length > 0) {
        openSessionModal(clickedAction.sessionIds);
      }
    };
    
    const onEvents = {
      'click': onChartClick,
    };

    const chartDataReversed = [...chartData].reverse();
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        min: 0,
        boundaryGap: [0, 0.01]
      },
      yAxis: {
        type: 'category',
        data: chartDataReversed.map(d => d.name),
        axisLabel: {
          interval: 0,
          formatter: (value: string) => value.length > 10 ? `${value.slice(0, 10)}...` : value
        }
      },
      series: [
        {
          name: '使用频次',
          type: 'bar',
          data: chartDataReversed.map(d => ({ value: d.value, itemStyle: { color: '#5470C6' } })),
        }
      ]
    };
    return (
      <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 h-full w-full">
        <div className="flex items-center mb-4">
            <BarChart3 className="w-6 h-6 text-gray-700" />
            <h3 className="text-lg font-semibold ml-2 text-gray-800">累计功能使用频次</h3>
        </div>
        <ReactECharts option={option} style={{ height: '100%', width: '100%' }} onEvents={onEvents} />
      </div>
    );
  };

  const renderActionPieChart = () => {
    // 需要合并的关键词
    const excludeKeywords = ["退出", "取消", "暂停", "移动", "确认", "返回", "下一步", "继续服务", "继续", "播放", "重播"];
    const baseActionName = "基础操作";
    const actionDataRaw = getActionChartData();
    let baseAction = { name: baseActionName, value: 0, sessionIds: [] as string[], detailNames: [] as string[] };
    const filteredActionData: typeof actionDataRaw = [];
    for (const d of actionDataRaw) {
      if (excludeKeywords.includes(d.name)) {
        baseAction.value += d.value;
        baseAction.sessionIds.push(...d.sessionIds);
        baseAction.detailNames.push(d.name);
      } else {
        filteredActionData.push(d);
      }
    }
    if (baseAction.value > 0) {
      filteredActionData.push(baseAction);
    }
    if (filteredActionData.length === 0) return null;

    const onChartClick = (params: any) => {
      const action = filteredActionData.find(d => d.name === params.name);
      if (action && action.sessionIds.length > 0) {
        openSessionModal(action.sessionIds);
      }
    };
    const onEvents = { 'click': onChartClick };
    const pieColors = [
      '#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC',
      '#FFB300', '#00B0FF', '#FF7043', '#8D6E63', '#26A69A', '#D4E157', '#7E57C2', '#789262', '#F06292',
      '#A1887F', '#BA68C8', '#FFD600', '#43A047', '#F4511E', '#6D4C41', '#0288D1', '#C2185B', '#388E3C'
    ];
    const option = {
      color: pieColors,
      tooltip: {
        trigger: 'item',
        formatter: function(params: any) {
          if (params.name === baseActionName && params.data.detailNames) {
            return `${params.name}: ${params.value} (${params.percent}%)<br/>包含：<br/>${params.data.detailNames.join('、')}`;
          }
          return `${params.name}: ${params.value} (${params.percent}%)`;
        }
      },
      legend: {
        type: 'scroll',
        orient: 'vertical',
        right: 10,
        top: 20,
        bottom: 20,
      },
      series: [
        {
          name: '功能使用分布',
          type: 'pie',
          radius: '70%',
          center: ['40%', '50%'],
          data: filteredActionData.map(d => ({ name: d.name, value: d.value, sessionIds: d.sessionIds, detailNames: (d as any).detailNames || [] })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    return (
      <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 h-full">
        <div className="flex items-center mb-4">
          <PieChart className="w-6 h-6 text-gray-700" />
          <h3 className="text-lg font-semibold ml-2 text-gray-800">功能使用分布</h3>
        </div>
        <ReactECharts option={option} style={{ height: 400 }} onEvents={onEvents} />
      </div>
    );
  };

  const renderQuestionWordCloud = () => {
    const data = statistics?.data;
    if (!data || data.length === 0) return <div className="chart-placeholder">暂无用户问题词云数据</div>;

    const questionData = data.reduce<Record<string, { question: string; count: number; session_ids: string[] }>>((acc, item) => {
      item.user_question_summary.forEach(q => {
        const parsedIds = parseSessionIds(q.session_ids);
        if (acc[q.question]) {
          acc[q.question].count += q.count;
          acc[q.question].session_ids.push(...parsedIds);
        } else {
          acc[q.question] = { ...q, session_ids: parsedIds };
        }
      });
      return acc;
    }, {});

    const wordCloudData = Object.values(questionData).map(q => ({
      name: q.question,
      value: q.count,
      session_ids: q.session_ids
    }));

    if (wordCloudData.length === 0) {
      return <div className="chart-placeholder">暂无用户问题词云数据</div>;
    }

    const onChartClick = (params: any) => {
      if (params.data && params.data.session_ids) {
        openSessionModal(parseSessionIds(params.data.session_ids));
      }
    };

    const chartEvents = {
      'click': onChartClick,
    };
    
    const option = {
      tooltip: {
        show: true,
        formatter: function (params: any) {
          return `${params.name}: ${params.value}`;
        }
      },
      series: [{
        type: 'wordCloud',
        shape: 'circle',
        keepAspect: false,
        left: 'center',
        top: 'center',
        width: '100%',
        height: '100%',
        right: null,
        bottom: null,
        sizeRange: [12, 60],
        rotationRange: [-90, 90],
        rotationStep: 45,
        gridSize: 8,
        drawOutOfBound: false,
        textStyle: {
          fontFamily: 'sans-serif',
          fontWeight: 'bold',
          color: function () {
            return 'rgb(' + [
              Math.round(Math.random() * 160),
              Math.round(Math.random() * 160),
              Math.round(Math.random() * 160)
            ].join(',') + ')';
          }
        },
        data: wordCloudData
      }]
    };
    return <ReactECharts option={option} style={{ height: 400 }} onEvents={chartEvents} />;
  };

  const renderIntervalChart = () => {
    const data = statistics?.data;
    if (!data || data.length === 0) return <div className="chart-placeholder">暂无消息间隔分布数据</div>;

    const intervalData = data.reduce((acc, item) => {
      acc.bucket_lt_10s += Number(item.interval_distribution.bucket_lt_10s) || 0;
      acc.bucket_10_20s += Number(item.interval_distribution.bucket_10_20s) || 0;
      acc.bucket_1_3min += Number(item.interval_distribution.bucket_1_3min) || 0;
      acc.bucket_3_5min += Number(item.interval_distribution.bucket_3_5min) || 0;
      acc.bucket_5_10min += Number(item.interval_distribution.bucket_5_10min) || 0;
      acc.bucket_10_20min += Number(item.interval_distribution.bucket_10_20min) || 0;
      acc.bucket_gt_20min += Number(item.interval_distribution.bucket_gt_20min) || 0;
      return acc;
    }, {
      bucket_lt_10s: 0,
      bucket_10_20s: 0,
      bucket_1_3min: 0,
      bucket_3_5min: 0,
      bucket_5_10min: 0,
      bucket_10_20min: 0,
      bucket_gt_20min: 0,
    });
    
    const sessionIdsByBucketInterval = data.reduce<Record<string, string[]>>((acc, item) => {
      if (item.interval_distribution.session_ids) {
        Object.entries(item.interval_distribution.session_ids).forEach(([key, ids]) => {
          if (!acc[key]) acc[key] = [];
          acc[key].push(...parseSessionIds(ids as any));
        });
      }
      return acc;
    }, {});

    const totalMessages = Object.values(intervalData).reduce((sum, value) => sum + value, 0);
    if (totalMessages === 0) {
      return <div className="chart-placeholder">暂无消息间隔分布数据</div>;
    }

    const chartData = [
      { value: intervalData.bucket_lt_10s, name: '<10秒', session_ids: sessionIdsByBucketInterval['bucket_lt_10s'] },
      { value: intervalData.bucket_10_20s, name: '10-20秒', session_ids: sessionIdsByBucketInterval['bucket_10_20s'] },
      { value: intervalData.bucket_1_3min, name: '1-3分钟', session_ids: sessionIdsByBucketInterval['bucket_1_3min'] },
      { value: intervalData.bucket_3_5min, name: '3-5分钟', session_ids: sessionIdsByBucketInterval['bucket_3_5min'] },
      { value: intervalData.bucket_5_10min, name: '5-10分钟', session_ids: sessionIdsByBucketInterval['bucket_5_10min'] },
      { value: intervalData.bucket_10_20min, name: '10-20分钟', session_ids: sessionIdsByBucketInterval['bucket_10_20min'] },
      { value: intervalData.bucket_gt_20min, name: '>20分钟', session_ids: sessionIdsByBucketInterval['bucket_gt_20min'] },
    ].filter(d => d.value > 0);
    
    const onChartClick = (params: any) => {
      if (params.data && params.data.session_ids) {
        openSessionModal(parseSessionIds(params.data.session_ids));
      }
    };

    const chartEvents = {
      'click': onChartClick,
    };

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'center',
        data: chartData.map(d => d.name)
      },
      series: [
        {
          name: '消息间隔',
          type: 'pie',
          radius: ['50%', '70%'],
          center: ['65%', '50%'],
          avoidLabelOverlap: true,
          label: {
            show: true,
            formatter: '{b}\n{d}%',
            position: 'outside'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: true,
            length: 10,
            length2: 15
          },
          data: chartData,
        }
      ],
      xAxis: { show: false },
      yAxis: { show: false },
    };
    return <ReactECharts option={option} style={{ height: 400 }} onEvents={chartEvents} />;
  };

  const renderActiveHoursSummaryCard = () => {
    const summaryData = getSummaryFromStatistics();

    if (!summaryData || !summaryData.avg_consecutive_active_hours) {
      return (
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 flex items-center justify-center">
          <div className="text-gray-500">暂无活跃度数据</div>
        </div>
      );
    }
    
    const metrics = [
      { label: '平均连续活跃小时', value: summaryData.avg_consecutive_active_hours.toFixed(2) + ' 小时' },
      { label: '平均助手首先响应率', value: `${(summaryData.avg_assistant_first_ratio * 100).toFixed(1)}%` },
      { label: '平均问候语使用率', value: `${(summaryData.avg_greeting_ratio * 100).toFixed(1)}%` },
    ];

    return (
      <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 flex flex-col justify-between h-full">
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4">活跃度分析</h3>
          <div className="space-y-4">
            {metrics.map(metric => (
              <div key={metric.label} className="flex justify-between items-center bg-gray-50 p-3 rounded-lg">
                <span className="text-sm text-gray-600">{metric.label}</span>
                <span className="text-lg font-bold text-gray-900">{metric.value}</span>
              </div>
            ))}
          </div>
        </div>
        <div className="text-xs text-gray-400 mt-4 pt-4 border-t border-gray-200">
          该卡片展示了设备在选定时间范围内的平均活跃度指标。
        </div>
      </div>
    );
  };

  const renderUserPreferenceRadar = () => {
    const data = statistics?.data;
    if (!data || data.length === 0) return <div className="chart-placeholder">暂无用户偏好数据</div>;

    const preferenceData = data.reduce<{[key: string]: {field_name: string, field_value: string, count: number}}>((acc, item) => {
      item.user_preference_summary.forEach(p => {
        const key = `${p.field_name}_${p.field_value}`;
        if (acc[key]) {
          acc[key].count += p.count;
        } else {
          acc[key] = { ...p };
        }
      });
      return acc;
    }, {});

    const radarData = Object.values(preferenceData);

    if (radarData.length === 0) {
      return <div className="chart-placeholder">暂无用户偏好数据</div>;
    }
    
    const maxCount = Math.max(...radarData.map(p => p.count));

    const option = {
      tooltip: {},
      radar: {
        indicator: radarData.map(item => ({ name: `${item.field_name}: ${item.field_value}`, max: maxCount })),
      },
      series: [{
        name: '用户偏好',
        type: 'radar',
        data: [{ value: radarData.map(item => item.count), name: '偏好分布' }],
      }],
    };
    return <ReactECharts option={option} style={{ height: 400 }} />;
  };

  const renderEventSankey = () => {
    const data = statistics?.data;
    if (!data || data.length === 0) return <div className="chart-placeholder">暂无事件流转路径数据</div>;

    const pathFrequency = data.reduce<Record<string, number>>((acc, item) => {
      if (item.event_behavior_summary && item.event_behavior_summary.path_frequency) {
        for (const path in item.event_behavior_summary.path_frequency) {
          acc[path] = (acc[path] || 0) + item.event_behavior_summary.path_frequency[path];
        }
      }
      return acc;
    }, {});

    if (Object.keys(pathFrequency).length === 0) {
      return <div className="chart-placeholder">暂无事件流转路径数据</div>;
    }

    const nodes = new Set<string>();
    const links: Array<{ source: string; target: string; value: number }> = [];

    for (const path in pathFrequency) {
      const pathParts = path.split('->');
      for (let i = 0; i < pathParts.length - 1; i++) {
        const source = pathParts[i].trim();
        const target = pathParts[i + 1].trim();
        if (source && target) {
          nodes.add(source);
          nodes.add(target);
          links.push({
            source: source,
            target: target,
            value: pathFrequency[path],
          });
        }
      }
    }

    if (links.length === 0) {
      return <div className="chart-placeholder">暂无事件流转路径数据</div>;
    }

    const option = {
      tooltip: { trigger: 'item', triggerOn: 'mousemove' },
      series: [{
        type: 'sankey',
        data: Array.from(nodes).map(name => ({ name })),
        links: links,
        emphasis: { focus: 'adjacency' },
        lineStyle: { color: 'gradient', curveness: 0.5 }
      }]
    };
    return <ReactECharts option={option} style={{ height: 400 }} />;
  };

  const renderSessionDetails = () => <div>Session Details for: {selectedSessionIds.join(', ')}</div>;
  
  const showCheetahRobotButton = import.meta.env.DEV && deviceId && KEY_FOCUSED_DEVICE_IDS.includes(deviceId);

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <DevicePageHeader 
        showAnalysisLink={false} 
        headerActions={
          showCheetahRobotButton ? (
            <Link to="/device-reports" className="px-3 py-1.5 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 transition-colors">
              📊 设备报告生成
            </Link>
          ) : null
        }
      />
      
      <div className="flex justify-end items-center mb-6 space-x-2">
          <button onClick={() => refetch()} className="p-2 rounded-md hover:bg-gray-200" disabled={isLoadingStats}>
            <RefreshCw className={`w-5 h-5 ${isLoadingStats ? 'animate-spin' : ''}`} />
          </button>
          <button className="p-2 rounded-md hover:bg-gray-200">
            <Download className="w-5 h-5" />
          </button>
      </div>

      {/* Main Content */}
      <div className="bg-white rounded-2xl shadow-sm p-6 border border-gray-100 mt-6">
        {/* Toggles */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            {(['day', 'hour'] as const).map(g => (
              <button
                key={g}
                onClick={() => setGroupBy(g)}
                className={`px-4 py-1.5 text-sm font-medium rounded-md transition-colors ${
                  groupBy === g ? 'bg-white shadow text-gray-800' : 'text-gray-500 hover:bg-gray-200'
                }`}
              >
                {g === 'day' ? <Calendar className="w-4 h-4 inline mr-2" /> : <Clock className="w-4 h-4 inline mr-2" />}
                {g === 'day' ? '按天统计' : '按小时统计'}
              </button>
            ))}
          </div>
          {groupBy === 'day' ? (
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {(['7d', '30d', '90d'] as const).map(t => (
                <button
                  key={t}
                  onClick={() => setTimeRange(t)}
                  className={`px-3 py-1 text-sm rounded-md ${
                    timeRange === t ? 'bg-white shadow text-gray-800' : 'text-gray-500'
                  }`}
                >
                  {t === '7d' ? '最近7天' : t === '30d' ? '最近30天' : '最近90天'}
                </button>
              ))}
            </div>
          ) : (
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {(['24h', '48h', '72h'] as const).map(t => (
                <button
                  key={t}
                  onClick={() => setHourTimeRange(t)}
                  className={`px-3 py-1 text-sm rounded-md ${
                    hourTimeRange === t ? 'bg-white shadow text-gray-800' : 'text-gray-500'
                  }`}
                >
                   {t === '24h' ? '最近24小时' : t === '48h' ? '最近48小时' : '最近72小时'}
                </button>
              ))}
            </div>
          )}
        </div>
        
        {isLoadingStats ? (
          <div className="text-center py-20">正在加载统计数据...</div>
        ) : statistics ? (
          <>
            {groupBy === 'day' ? renderDayMetricCards() : renderHourMetricCards()}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              <ErrorBoundary>
                <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                  <div className="flex items-center mb-4">
                    <TrendingUp className="w-6 h-6 text-gray-700" />
                    <h3 className="text-lg font-semibold ml-2 text-gray-800">会话趋势</h3>
                  </div>
                  {renderTrendChart()}
                </div>
              </ErrorBoundary>
              <ErrorBoundary>
                {renderMessageTrendChart()}
              </ErrorBoundary>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 md:grid-rows-2 gap-8 mb-8">
              <ErrorBoundary>
                <div className="md:row-span-2 h-full">
                  {renderActionChart()}
                </div>
              </ErrorBoundary>
              <div className="h-full">
                <div className="grid grid-cols-1 xl:grid-cols-1 gap-8 h-full">
                  <ErrorBoundary>
                    {renderActionPieChart()}
                  </ErrorBoundary>
                </div>
              </div>
              <div className="h-full">
                <div className="grid grid-cols-1 xl:grid-cols-1 gap-8 h-full">
                  <ErrorBoundary>
                    {renderDurationChart()}
                  </ErrorBoundary>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              <ErrorBoundary>
                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 h-full">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">累计用户问题词云</h3>
                  <ErrorBoundary>{renderQuestionWordCloud()}</ErrorBoundary>
                </div>
              </ErrorBoundary>
              <ErrorBoundary>
                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 h-full">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">累计消息间隔分布</h3>
                  <ErrorBoundary>{renderIntervalChart()}</ErrorBoundary>
                </div>
              </ErrorBoundary>
              <ErrorBoundary>
                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 h-full">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">用户偏好分布</h3>
                  <ErrorBoundary>{renderUserPreferenceRadar()}</ErrorBoundary>
                </div>
              </ErrorBoundary>
               <ErrorBoundary>
                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 h-full">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">事件流转路径</h3>
                  <ErrorBoundary>{renderEventSankey()}</ErrorBoundary>
                </div>
              </ErrorBoundary>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="lg:col-span-1">
                 <ErrorBoundary>{renderActiveHoursSummaryCard()}</ErrorBoundary>
              </div>
              <div className="lg:col-span-2 bg-white p-4 rounded-xl shadow-lg border border-gray-100 flex flex-col items-center justify-center text-center h-full">
                <Settings className="w-8 h-8 text-gray-300 mb-2" />
                <h3 className="text-base font-semibold text-gray-700">更多图表配置</h3>
                <p className="text-xs text-gray-400 mt-1">敬请期待</p>
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-20 text-red-500">无法加载统计数据</div>
        )}
      </div>
      <SessionDetailsModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        sessionIds={modalSessionIds}
      />
    </div>
  );
};

export default DeviceAnalysisPage;
