import React, { useState } from 'react';
import { MermaidDataCleaner } from '../components/MermaidDataCleaner';
import EnhancedMarkdownRenderer from '../components/EnhancedMarkdownRenderer';

const MermaidFixDemo: React.FC = () => {
  // 问题数据（从用户提供的原始数据中提取）
  const [problemData] = useState(`# 问题数据示例

## ❌ 原始Timeline（有语法错误）

\`\`\`mermaid
timeline
    title 今日服务亮点时刻
    10:24 : 多语言服务
          : 韩语客户接待
    11:30 : 高效引导  
          : VIP客户专属路线
    14:15 : 智能问答
          : 复杂政策咨询
    16:45 : 团队协作
          : 机器人间任务分配
\`\`\`

## ❌ 问题分析
- Timeline语法不支持 \`10:24 : 多语言服务\` 这种格式
- 缩进的附加事件 \`: 韩语客户接待\` 格式不正确
- 缺少section分组结构`);

  const [fixedData, setFixedData] = useState('');
  const [showComparison, setShowComparison] = useState(false);

  const generateFixedData = () => {
    const cleaned = MermaidDataCleaner.cleanAllMermaidBlocks(problemData);
    
    const fixedDataContent = `# 修复后的数据

## ✅ 修复后的Timeline（正确语法）

${cleaned.replace('# 问题数据示例', '').replace('## ❌ 原始Timeline（有语法错误）', '').replace('## ❌ 问题分析', '## ✅ 修复说明').replace('- Timeline语法不支持', '- ✅ 已转换为正确的section格式').replace('- 缩进的附加事件', '- ✅ 已合并多个事件到单行').replace('- 缺少section分组结构', '- ✅ 已按时间段自动分组')}

## ✅ 修复说明
- ✅ 已转换为正确的section格式
- ✅ 已合并多个事件到单行  
- ✅ 已按时间段自动分组

## 📋 其他支持的修复

### 饼图修复示例
\`\`\`mermaid
pie title "功能使用分布"
    "访客接待" : 35
    "智能问答" : 28
    "找人服务" : 20
    "带路引领" : 12
    "多语言服务" : 3
    "人工服务" : 2
\`\`\`

### 流程图修复示例
\`\`\`mermaid
graph LR
    A["08:00-09:00<br/>13人"] --> B["09:00-12:00<br/>45人"]
    B --> C["13:00-14:00<br/>8人"]
    C --> D["14:00-17:00<br/>52人"] 
    D --> E["17:00-18:00<br/>38人"]
\`\`\``;

    setFixedData(fixedDataContent);
    setShowComparison(true);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">🛠️ Mermaid语法修复演示</h1>
          <p className="text-lg text-gray-600">演示Timeline语法错误的自动修复过程</p>
          
          <div className="mt-6">
            <button
              onClick={generateFixedData}
              className="px-8 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-lg"
            >
              🔧 生成修复后的数据
            </button>
          </div>
        </div>

        {/* 错误信息展示 */}
        <div className="mb-8 bg-red-50 border border-red-200 rounded-lg p-6">
          <h3 className="text-red-900 font-semibold mb-3">🚨 原始错误信息</h3>
          <div className="space-y-2 text-red-800 text-sm font-mono">
            <p>Parse error on line 3:</p>
            <p>...itle 今日服务亮点时刻    10:24 : 多语言服务</p>
            <p>----------------------^</p>
            <p>Expecting 'EOF', 'SPACE', 'NEWLINE', 'title', 'section', 'period', 'event', got 'INVALID'</p>
          </div>
        </div>

        <div className="grid gap-8">
          {/* 问题数据 */}
          <div className="bg-white rounded-lg border shadow-sm">
            <div className="p-4 border-b bg-red-50">
              <h3 className="text-red-900 font-semibold">❌ 问题数据（会渲染失败）</h3>
              <p className="text-red-700 text-sm mt-1">包含语法错误的Timeline代码</p>
            </div>
            <div className="p-6">
              <EnhancedMarkdownRenderer 
                content={problemData}
                onMermaidError={(error) => {
                  console.log('预期的错误:', error);
                }}
              />
            </div>
          </div>

          {/* 修复后数据 */}
          {showComparison && (
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="p-4 border-b bg-green-50">
                <h3 className="text-green-900 font-semibold">✅ 修复后数据（正常渲染）</h3>
                <p className="text-green-700 text-sm mt-1">自动修复后的正确语法</p>
              </div>
              <div className="p-6">
                <EnhancedMarkdownRenderer 
                  content={fixedData}
                  showRawData={true}
                  onMermaidError={(error) => {
                    console.error('修复后仍有错误:', error);
                  }}
                />
              </div>
            </div>
          )}
        </div>

        {/* 修复规则说明 */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-blue-900 font-semibold mb-4">🔧 自动修复规则</h3>
          <div className="grid md:grid-cols-2 gap-6 text-sm">
            <div>
              <h4 className="font-medium text-blue-900 mb-2">Timeline修复</h4>
              <ul className="space-y-1 text-blue-800">
                <li>• 将 <code>10:24 : 事件</code> 转换为section格式</li>
                <li>• 合并缩进的附加事件</li>
                <li>• 按时间段自动分组（上午/下午）</li>
                <li>• 添加正确的section结构</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-blue-900 mb-2">其他图表修复</h4>
              <ul className="space-y-1 text-blue-800">
                <li>• 饼图：自动添加引号到中文标签</li>
                <li>• 流程图：修复中文节点语法</li>
                <li>• 通用：清理HTML标签和空白</li>
                <li>• 验证：语法检查和错误提示</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 解决方案总结 */}
        <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-6">
          <h3 className="text-green-900 font-semibold mb-4">🎯 完整解决方案</h3>
          <div className="space-y-3 text-green-800 text-sm">
            <p><strong>1. 智能语法检测</strong>：检测Timeline语法完整性</p>
            <p><strong>2. 自动数据清理</strong>：修复常见的语法错误</p>
            <p><strong>3. 错误处理优化</strong>：友好的错误提示和重试</p>
            <p><strong>4. 兼容性修复</strong>：剪贴板API降级方案</p>
            <p><strong>5. 原始数据展示</strong>：便于调试和分析</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MermaidFixDemo; 