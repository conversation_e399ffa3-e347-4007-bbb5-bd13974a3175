import React, { useState, useRef, useCallback, useEffect } from 'react';
import EnhancedMarkdownRenderer from '../components/EnhancedMarkdownRenderer';
import PromptEditorModal from '../components/PromptEditorModal';
import ModelSelector from '../components/ModelSelector';
import { Loader2, Play, Square, FileText, Wifi, CheckCircle2, XCircle, Clock, Edit } from 'lucide-react';
import { getDefaultPrompt } from '../config/promptTemplates';

interface SSEMessage {
  event: string;
  data: any;
}

interface ReportFormData {
  enterpriseId: string;
  deviceId: string;
  promptVersion: string;
  // 单天模式
  date?: string;
  // 多天模式
  startDate?: string;
  endDate?: string;
  // 自定义prompt
  customPrompt?: string;
  useCustomPrompt?: boolean;
  // 模型选择
  modelName?: string;
}



type ReportMode = 'single' | 'multiple';

type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'error';
type GenerationStatus = 'idle' | 'generating' | 'completed' | 'error';

// 企业设备数据配置
interface Device {
  id: string;
  name: string;
}

interface Enterprise {
  id: string;
  name: string;
  devices: Device[];
}

const ENTERPRISE_DATA: Enterprise[] = [
  {
    id: 'orion.ovs.entprise.4498860269',
    name: '猎豹移动',
    devices: [
      { id: 'M03SCN2A170252017BB1', name: '猎豹展厅AgentOS' },
      { id: 'MC1BCNC016021348B693', name: '猎豹展厅AgentOS-Mini' }
    ]
  },
  {
    id: 'orion.ovs.entprise.2808534718',
    name: '成都极地海洋公园',
    devices: [
      { id: 'M03SCN2A23025122K86C', name: '川溪Agent' },
      { id: 'M03SCN2A23025122HB6A', name: '鲸豚馆' }
    ]
  }
];

const DeviceReportPage: React.FC = () => {
  // 生成默认日期的辅助函数 - 限制最近7天
  const getDefaultDates = () => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    
    // 7天前的日期（限制范围）
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 7);
    
    // 从昨天开始往前推3天（昨天、前天、大前天）
    const threeDaysAgo = new Date(today);
    threeDaysAgo.setDate(today.getDate() - 3);
    
    const formatDate = (date: Date) => {
      return date.toISOString().split('T')[0];
    };
    
    return {
      yesterday: formatDate(yesterday),
      threeDaysAgo: formatDate(threeDaysAgo),
      sevenDaysAgo: formatDate(sevenDaysAgo),
      today: formatDate(today)
    };
  };
  
  const defaultDates = getDefaultDates();
  
  // 获取当前企业的设备列表
  const getCurrentEnterpriseDevices = useCallback((enterpriseId: string) => {
    const enterprise = ENTERPRISE_DATA.find(e => e.id === enterpriseId);
    return enterprise ? enterprise.devices : [];
  }, []);
  
  // 报告模式
  const [reportMode, setReportMode] = useState<ReportMode>('single');
  
  // 表单状态
  const [formData, setFormData] = useState<ReportFormData>({
    enterpriseId: 'orion.ovs.entprise.4498860269',
    deviceId: 'MC1BCNC016021348B693',
    promptVersion: 'UNIFIED_PROMPT', // 单天模式统一使用V1版本
    date: defaultDates.yesterday, // 单天模式默认昨天
    startDate: defaultDates.threeDaysAgo, // 多天模式默认3天前
    endDate: defaultDates.yesterday, // 多天模式默认昨天（从昨天开始算3天）
    customPrompt: getDefaultPrompt('single'), // 默认使用单天模板
    useCustomPrompt: false, // 默认不使用自定义prompt
    modelName: undefined // 默认使用后端默认模型
  });

  // 当前企业的设备列表
  const [currentDevices, setCurrentDevices] = useState<Device[]>(
    getCurrentEnterpriseDevices('orion.ovs.entprise.4498860269')
  );

  // 自定义prompt编辑器状态
  const [isPromptEditorOpen, setIsPromptEditorOpen] = useState(false);
  

  
  // API服务器地址配置 - 使用相对路径，让vite代理处理
  const [apiServerUrl, setApiServerUrl] = useState<string>('');

  // 连接和生成状态
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [generationStatus, setGenerationStatus] = useState<GenerationStatus>('idle');
  const [reportContent, setReportContent] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [currentBatch, setCurrentBatch] = useState<number | null>(null);
  const [receivedChunks, setReceivedChunks] = useState<number>(0);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [keepPreviousContent, setKeepPreviousContent] = useState<boolean>(true); // 是否保留之前的内容

  // SSE连接引用
  const eventSourceRef = useRef<EventSource | null>(null);



  // 构建API请求配置 - 统一使用POST请求，所有参数放在请求体中
  const buildApiRequest = useCallback((data: ReportFormData, mode: ReportMode) => {
    // 使用相对路径，让vite代理处理
    const baseUrl = `/api/v1/device-reports`;
    
    console.log(`🔗 使用vite代理访问API`);
    console.log(`🔗 Base URL: ${baseUrl}`);
    console.log(`📅 报告模式: ${mode}`);
    
    const url = `${baseUrl}/${data.enterpriseId}/${data.deviceId}/generate`;
    console.log(`📡 最终API URL: ${url}`);
    
    // 构建完整的请求体 - 所有参数都放在JSON中
    const requestBody: any = {
      prompt_version: data.promptVersion
    };
    
    // 根据模式添加不同的日期参数
    if (mode === 'single' && data.date) {
      requestBody.date = data.date;
      console.log(`📅 单天模式: ${data.date}`);
    } else if (mode === 'multiple' && data.startDate && data.endDate) {
      requestBody.start_date = data.startDate;
      requestBody.end_date = data.endDate;
      console.log(`📅 多天模式: ${data.startDate} 到 ${data.endDate}`);
    }
    
    // 如果有自定义prompt，添加到请求体
    if (data.useCustomPrompt && data.customPrompt) {
      requestBody.custom_user_prompt = data.customPrompt;
    }
    
    // 添加模型选择参数
    if (data.modelName) {
      requestBody.model_name = data.modelName;
      console.log(`🤖 选择模型: ${data.modelName}`);
    }
    
    console.log(`📤 请求体:`, requestBody);
    
    return {
      url: url,
      body: requestBody
    };
  }, [apiServerUrl]);

  // 处理SSE消息
  const handleSSEMessage = useCallback((event: MessageEvent) => {
    try {
      console.log('🔍 原始SSE数据:', {
        type: event.type,
        data: event.data,
        dataType: typeof event.data,
        eventType: event.type
      });
      
      // 特殊处理error事件
      if (event.type === 'error') {
        console.error('❌ 收到服务端错误事件');
        
        // 尝试解析错误数据
        let errorMessage = '服务端返回未知错误';
        
        if (event.data && event.data !== 'undefined') {
          try {
            const errorData = JSON.parse(event.data);
            errorMessage = errorData.error || errorData.message || errorData.detail || '服务端错误';
          } catch (parseError) {
            // 如果JSON解析失败，直接使用原始数据
            errorMessage = event.data;
          }
        } else {
          // 如果data为空，检查是否有其他错误信息
          console.error('🔍 错误事件详情:', {
            readyState: eventSourceRef.current?.readyState,
            url: eventSourceRef.current?.url,
            lastEventId: event.lastEventId,
            origin: event.origin
          });
          
          // 根据readyState判断错误类型
          if (eventSourceRef.current?.readyState === EventSource.CLOSED) {
            errorMessage = '连接已关闭，可能是服务端主动断开';
          } else {
            errorMessage = '服务端返回错误，但未提供具体错误信息';
          }
        }
        
        setGenerationStatus('error');
        setErrorMessage(errorMessage);
        return;
      }
      
      // 处理其他正常事件
      if (!event.data || event.data === 'undefined') {
        console.warn('⚠️ 收到空的或未定义的SSE数据，跳过处理');
        return;
      }
      
      const data = JSON.parse(event.data);
      const message: SSEMessage = { event: event.type, data };

      console.log('✅ 解析成功的SSE消息:', message);

      switch (event.type) {
        case 'start':
          setGenerationStatus('generating');
          // 根据用户选择决定是否清空之前的内容
          if (!keepPreviousContent) {
            setReportContent('');
            console.log('🔄 清空之前的内容，开始新生成');
          } else {
            console.log('📝 保留之前的内容，继续追加新内容');
          }
          setReceivedChunks(0);
          setStartTime(new Date());
          setCurrentBatch(data.batch || null);
          break;

        case 'content':
          setReceivedChunks(prev => prev + 1);
          setReportContent(prev => prev + (data.content || ''));
          break;

        case 'batch_start':
          setCurrentBatch(data.batch);
          break;

        case 'batch_complete':
          console.log(`批次 ${data.batch} 完成`);
          break;

        case 'complete':
          setGenerationStatus('completed');
          setCurrentBatch(null);
          // 收到complete事件时，如果连接还在，主动关闭
          if (eventSourceRef.current) {
            console.log('✅ 收到complete事件，关闭SSE连接');
            eventSourceRef.current.close();
            eventSourceRef.current = null;
            setConnectionStatus('disconnected');
          }
          break;

        default:
          console.log('📝 未处理的事件类型:', event.type, data);
      }
    } catch (error) {
      console.error('解析SSE消息失败:', error);
      setGenerationStatus('error');
      setErrorMessage(`消息解析失败: ${error}`);
    }
  }, []);

  // 建立SSE连接 - 统一使用POST请求
  const connectSSE = useCallback((requestConfig: { url: string; body: any }) => {
    try {
      setConnectionStatus('connecting');
      setErrorMessage('');

      console.log('📤 使用POST请求');
      console.log('📤 请求URL:', requestConfig.url);
      console.log('📤 请求体:', requestConfig.body);
      
      fetch(requestConfig.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestConfig.body)
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        if (!response.body) {
          throw new Error('Response body is null');
        }
        
        setConnectionStatus('connected');
        console.log('POST SSE连接已建立');
        
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        
        const processStream = () => {
          reader.read().then(({ done, value }) => {
            if (done) {
              console.log('✅ POST SSE流结束');
              setConnectionStatus('disconnected');
              setGenerationStatus('completed');
              return;
            }
            
            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';
            
            lines.forEach(line => {
              if (line.trim() === '') return;
              
              // 解析SSE格式
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data.trim() !== '') {
                  try {
                    const parsedData = JSON.parse(data);
                    // 模拟SSE事件
                    const fakeEvent = {
                      type: parsedData.event || 'content',
                      data: JSON.stringify(parsedData.data || parsedData) // 确保data是字符串格式
                    };
                    handleSSEMessage(fakeEvent as MessageEvent);
                  } catch (e) {
                    console.error('解析SSE数据失败:', e);
                  }
                }
              }
            });
            
            processStream();
          }).catch(error => {
            console.error('读取SSE流失败:', error);
            setConnectionStatus('error');
            setGenerationStatus('error');
            setErrorMessage('读取数据流失败');
          });
        };
        
        processStream();
      })
      .catch(error => {
        console.error('POST请求失败:', error);
        setConnectionStatus('error');
        setGenerationStatus('error');
        setErrorMessage(`请求失败: ${error.message}`);
      });

    } catch (error) {
      console.error('创建SSE连接失败:', error);
      setConnectionStatus('error');
      setErrorMessage('创建连接失败');
    }
  }, [handleSSEMessage]);

  // 断开SSE连接
  const disconnectSSE = useCallback(() => {
    if (eventSourceRef.current) {
      console.log('🛑 手动断开SSE连接');
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    setConnectionStatus('disconnected');
    setGenerationStatus('idle');
    setCurrentBatch(null);
  }, []);

  // 处理自定义prompt保存
  const handleCustomPromptSave = useCallback((prompt: string) => {
    setFormData(prev => ({
      ...prev,
      customPrompt: prompt,
      useCustomPrompt: true
    }));
    console.log('✅ 自定义prompt已保存');
  }, []);

  // 处理企业选择变化
  const handleEnterpriseChange = useCallback((enterpriseId: string) => {
    const devices = getCurrentEnterpriseDevices(enterpriseId);
    setCurrentDevices(devices);
    
    // 更新企业ID，并重置设备ID为第一个设备
    setFormData(prev => ({
      ...prev,
      enterpriseId: enterpriseId,
      deviceId: devices.length > 0 ? devices[0].id : ''
    }));
  }, [getCurrentEnterpriseDevices]);

  // 处理模式切换时的prompt更新
  const handleModeChange = useCallback((newMode: ReportMode) => {
    setReportMode(newMode);
    
    // 根据模式更新默认prompt和promptVersion
    const defaultPrompt = getDefaultPrompt(newMode);
    const defaultPromptVersion = newMode === 'single' ? 'UNIFIED_PROMPT' : 'MULTI_DAY_PROMPT';
    
    setFormData(prev => ({
      ...prev,
      customPrompt: defaultPrompt,
      useCustomPrompt: false, // 切换模式时重置为不使用自定义prompt
      promptVersion: defaultPromptVersion
    }));
  }, []);

  // 开始生成报告
  const startGeneration = useCallback(() => {
    // 验证基本字段
    if (!formData.enterpriseId || !formData.deviceId) {
      setErrorMessage('请填写企业ID和设备ID');
      return;
    }

    // 验证Prompt版本与模式匹配
    if (reportMode === 'single') {
      if (formData.promptVersion !== 'UNIFIED_PROMPT') {
        setErrorMessage('单天模式统一使用UNIFIED_PROMPT V1版本');
        return;
      }
    } else if (reportMode === 'multiple') {
      if (formData.promptVersion !== 'MULTI_DAY_PROMPT') {
        setErrorMessage('多天模式只支持MULTI_DAY_PROMPT');
        return;
      }
    }

    // 根据模式验证日期字段
    if (reportMode === 'single') {
      if (!formData.date) {
        setErrorMessage('请选择单天日期');
        return;
      }
      
      // 验证单天日期是否在7天范围内
      const selectedDate = new Date(formData.date);
      const sevenDaysAgo = new Date(defaultDates.sevenDaysAgo);
      const today = new Date(defaultDates.today);
      
      if (selectedDate < sevenDaysAgo || selectedDate > today) {
        setErrorMessage(`单天日期必须在最近7天内（${defaultDates.sevenDaysAgo} 至 ${defaultDates.today}）`);
        return;
      }
    } else if (reportMode === 'multiple') {
      if (!formData.startDate || !formData.endDate) {
        setErrorMessage('请选择开始日期和结束日期');
        return;
      }
      
      // 验证日期范围
      const startDate = new Date(formData.startDate);
      const endDate = new Date(formData.endDate);
      if (startDate > endDate) {
        setErrorMessage('开始日期不能晚于结束日期');
        return;
      }
      
      // 验证多天日期是否在7天范围内
      const sevenDaysAgo = new Date(defaultDates.sevenDaysAgo);
      const today = new Date(defaultDates.today);
      
      if (startDate < sevenDaysAgo || endDate > today) {
        setErrorMessage(`多天日期范围必须在最近7天内（${defaultDates.sevenDaysAgo} 至 ${defaultDates.today}）`);
        return;
      }
      
      // 限制日期范围（最多7天）
      const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      if (daysDiff > 7) {
        setErrorMessage('日期范围不能超过7天');
        return;
      }
    }

    const requestConfig = buildApiRequest(formData, reportMode);
    connectSSE(requestConfig);
  }, [formData, reportMode, buildApiRequest, connectSSE]);

  // 停止生成
  const stopGeneration = useCallback(() => {
    disconnectSSE();
  }, [disconnectSSE]);
  


  // 清理连接
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        console.log('🧹 组件卸载，清理SSE连接');
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
    };
  }, []);

  // 计算运行时间
  const getElapsedTime = useCallback(() => {
    if (!startTime) return null;
    const now = new Date();
    const elapsed = Math.floor((now.getTime() - startTime.getTime()) / 1000);
    return `${Math.floor(elapsed / 60)}:${(elapsed % 60).toString().padStart(2, '0')}`;
  }, [startTime]);

  // 获取状态图标和颜色
  const getStatusInfo = (status: ConnectionStatus | GenerationStatus) => {
    switch (status) {
      case 'connected':
      case 'completed':
        return { icon: CheckCircle2, color: 'text-green-500' };
      case 'connecting':
      case 'generating':
        return { icon: Loader2, color: 'text-blue-500' };
      case 'error':
        return { icon: XCircle, color: 'text-red-500' };
      default:
        return { icon: Clock, color: 'text-gray-400' };
    }
  };

  const connectionInfo = getStatusInfo(connectionStatus);
  const generationInfo = getStatusInfo(generationStatus);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* 页面标题 */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">🤖 设备使用报告生成</h1>
          <p className="text-gray-600">支持单天和多天报告生成，实时分析设备使用情况，支持Markdown和图表展示</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧控制面板 */}
          <div className="lg:col-span-1 space-y-6">
            {/* 表单卡片 */}
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  报告配置
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  配置要生成报告的设备和时间信息
                </p>
              </div>
              <div className="p-6 space-y-4">
                <div className="space-y-2">
                  <label htmlFor="enterpriseId" className="text-sm font-medium text-gray-700">企业名称</label>
                  <select
                    id="enterpriseId"
                    value={formData.enterpriseId}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleEnterpriseChange(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {ENTERPRISE_DATA.map(enterprise => (
                      <option key={enterprise.id} value={enterprise.id}>
                        {enterprise.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <label htmlFor="deviceId" className="text-sm font-medium text-gray-700">设备名称</label>
                  <select
                    id="deviceId"
                    value={formData.deviceId}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setFormData(prev => ({ ...prev, deviceId: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {currentDevices.map(device => (
                      <option key={device.id} value={device.id}>
                        {device.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* 报告模式选择 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">报告模式</label>
                  <div className="flex space-x-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value="single"
                        checked={reportMode === 'single'}
                        onChange={(e) => {
                          handleModeChange(e.target.value as ReportMode);
                          // 切换到单天模式时，设置默认日期
                          setFormData(prev => ({
                            ...prev,
                            date: defaultDates.yesterday
                          }));
                        }}
                        className="mr-2"
                      />
                      <span className="text-sm">单天报告</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value="multiple"
                        checked={reportMode === 'multiple'}
                        onChange={(e) => {
                          handleModeChange(e.target.value as ReportMode);
                          // 切换到多天模式时，设置默认日期范围
                          setFormData(prev => ({
                            ...prev,
                            startDate: defaultDates.threeDaysAgo,
                            endDate: defaultDates.yesterday
                          }));
                        }}
                        className="mr-2"
                      />
                      <span className="text-sm">多天报告</span>
                    </label>
                  </div>
                </div>

                {/* 单天模式日期选择 */}
                {reportMode === 'single' && (
                  <div className="space-y-2">
                    <label htmlFor="date" className="text-sm font-medium text-gray-700">日期</label>
                    <input
                      id="date"
                      type="date"
                      value={formData.date || ''}
                      min={defaultDates.sevenDaysAgo}
                      max={defaultDates.today}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <p className="text-xs text-gray-500">
                      📅 只能选择最近8天的数据（{defaultDates.sevenDaysAgo} 至 {defaultDates.today}）
                    </p>
                  </div>
                )}

                {/* 多天模式日期范围选择 */}
                {reportMode === 'multiple' && (
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <div className="space-y-2">
                        <label htmlFor="startDate" className="text-sm font-medium text-gray-700">开始日期</label>
                        <input
                          id="startDate"
                          type="date"
                          value={formData.startDate || ''}
                          min={defaultDates.sevenDaysAgo}
                          max={defaultDates.today}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="endDate" className="text-sm font-medium text-gray-700">结束日期</label>
                        <input
                          id="endDate"
                          type="date"
                          value={formData.endDate || ''}
                          min={defaultDates.sevenDaysAgo}
                          max={defaultDates.today}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>
                    <p className="text-xs text-gray-500">
                      📅 只能选择最近8天的数据（{defaultDates.sevenDaysAgo} 至 {defaultDates.today}）
                    </p>
                  </div>
                )}

                <div className="space-y-2">
                  <label htmlFor="promptVersion" className="text-sm font-medium text-gray-700">Prompt版本</label>
                  <select
                    id="promptVersion"
                    value={formData.promptVersion}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setFormData(prev => ({ ...prev, promptVersion: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    disabled={reportMode === 'multiple'} // 多天模式下禁用选择
                  >
                    {reportMode === 'single' ? (
                      <option value="UNIFIED_PROMPT">统一Prompt V1</option>
                    ) : (
                      <option value="MULTI_DAY_PROMPT">多天Prompt (专用)</option>
                    )}
                  </select>
                  <p className="text-xs text-gray-500">
                    {reportMode === 'single' 
                      ? '单天模式统一使用统一Prompt V1版本'
                      : '多天模式使用专用的MULTI_DAY_PROMPT，专门针对多天数据分析和汇总优化'
                    }
                  </p>
                </div>

                {/* 模型选择 */}
                <ModelSelector
                  value={formData.modelName}
                  onChange={(modelName) => setFormData(prev => ({ ...prev, modelName }))}
                  disabled={false}
                />

                {/* 自定义Prompt配置 */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-700">自定义Prompt</label>
                    <button
                      type="button"
                      onClick={() => setIsPromptEditorOpen(true)}
                      className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-50 hover:bg-blue-100 text-blue-700 rounded transition-colors"
                    >
                      <Edit className="h-3 w-3" />
                      编辑
                    </button>
                  </div>
                  
                  {/* 自定义Prompt状态显示 */}
                  <div className="p-3 bg-gray-50 rounded-md">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs font-medium text-gray-700">
                        {formData.useCustomPrompt ? '使用自定义Prompt' : '使用默认Prompt'}
                      </span>
                      {formData.useCustomPrompt && (
                        <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                          已启用
                        </span>
                      )}
                    </div>
                    
                    {formData.useCustomPrompt && formData.customPrompt && (
                      <div className="text-xs text-gray-600">
                        <div className="font-medium mb-1">当前Prompt预览:</div>
                        <div className="bg-white p-2 rounded border max-h-20 overflow-y-auto">
                          {formData.customPrompt.substring(0, 100)}
                          {formData.customPrompt.length > 100 && '...'}
                        </div>
                      </div>
                    )}
                    
                    {!formData.useCustomPrompt && (
                      <div className="text-xs text-gray-500">
                        点击"编辑"按钮来自定义Prompt内容
                      </div>
                    )}
                  </div>
                </div>





                {/* 重新生成选项 */}
                {reportContent && (connectionStatus === 'disconnected' || connectionStatus === 'error') && (
                  <div className="pt-4 pb-2">
                    <label className="flex items-center space-x-2 text-sm text-gray-700">
                      <input
                        type="checkbox"
                        checked={keepPreviousContent}
                        onChange={(e) => setKeepPreviousContent(e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span>保留之前生成的内容，继续追加</span>
                    </label>
                    <p className="text-xs text-gray-500 mt-1">
                      {keepPreviousContent ? '✅ 将保留现有内容并追加新内容' : '🔄 将清空现有内容重新生成'}
                    </p>
                  </div>
                )}

                <div className="pt-4">
                  {connectionStatus === 'disconnected' || connectionStatus === 'error' ? (
                    <button 
                      onClick={startGeneration} 
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition-colors flex items-center justify-center gap-2"
                    >
                      <Play className="h-4 w-4" />
                      {reportContent ? '重新生成报告' : '开始生成报告'}
                    </button>
                  ) : (
                    <button 
                      onClick={stopGeneration} 
                      className="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-md transition-colors flex items-center justify-center gap-2"
                    >
                      <Square className="h-4 w-4" />
                      停止生成
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* 状态监控卡片 */}
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <Wifi className="h-5 w-5" />
                  实时状态
                </h3>
              </div>
              <div className="p-6 space-y-4">
                {/* 连接状态 */}
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">连接状态</span>
                  <div className="flex items-center gap-2">
                    <connectionInfo.icon className={`h-4 w-4 ${connectionInfo.color} ${connectionStatus === 'connecting' ? 'animate-spin' : ''}`} />
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      connectionStatus === 'connected' ? 'bg-green-100 text-green-800' : 
                      connectionStatus === 'error' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {connectionStatus === 'connected' ? '已连接' : 
                       connectionStatus === 'connecting' ? '连接中' :
                       connectionStatus === 'error' ? '连接失败' : '未连接'}
                    </span>
                    {eventSourceRef.current && (
                      <span className="text-xs text-gray-500">
                        (SSE: {eventSourceRef.current.readyState === EventSource.OPEN ? '开启' : 
                               eventSourceRef.current.readyState === EventSource.CONNECTING ? '连接中' : '关闭'})
                      </span>
                    )}
                  </div>
                </div>

                {/* 生成状态 */}
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">生成状态</span>
                  <div className="flex items-center gap-2">
                    <generationInfo.icon className={`h-4 w-4 ${generationInfo.color} ${generationStatus === 'generating' ? 'animate-spin' : ''}`} />
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      generationStatus === 'completed' ? 'bg-green-100 text-green-800' : 
                      generationStatus === 'error' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {generationStatus === 'generating' ? '生成中' :
                       generationStatus === 'completed' ? '已完成' :
                       generationStatus === 'error' ? '生成失败' : '空闲'}
                    </span>
                  </div>
                </div>

                {/* 当前批次 */}
                {currentBatch && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">当前批次</span>
                    <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                      批次 {currentBatch}/3
                    </span>
                  </div>
                )}

                {/* 接收数据量 */}
                {receivedChunks > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">数据块</span>
                    <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                      {receivedChunks} 个
                    </span>
                  </div>
                )}

                {/* 运行时间 */}
                {startTime && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">运行时间</span>
                    <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                      {getElapsedTime()}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* 错误提示 */}
            {errorMessage && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex items-center gap-2">
                  <XCircle className="h-4 w-4 text-red-500" />
                  <span className="text-sm text-red-700">{errorMessage}</span>
                </div>
                
                {/* 手动重试按钮 */}
                <div className="mt-3">
                  <button 
                    onClick={() => {
                      setErrorMessage('');
                      setConnectionStatus('disconnected');
                      setGenerationStatus('idle');
                    }}
                    className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors"
                  >
                    清除错误状态
                  </button>
                </div>
                
                {/* 调试信息 */}
                <div className="mt-2 p-2 bg-red-100 rounded text-xs">
                  <p className="text-red-800">调试信息:</p>
                  <p className="text-red-700">API代理: 使用vite代理配置</p>
                  <p className="text-red-700">报告模式: {reportMode === 'single' ? '单天' : '多天'}</p>
                  <p className="text-red-700">Prompt版本: {formData.promptVersion}</p>
                  <p className="text-red-700">使用自定义Prompt: {formData.useCustomPrompt ? '是' : '否'}</p>
                  {reportMode === 'single' && (
                    <p className="text-red-700">日期: {formData.date || '未选择'}</p>
                  )}
                  {reportMode === 'multiple' && (
                    <p className="text-red-700">日期范围: {formData.startDate || '未选择'} 到 {formData.endDate || '未选择'}</p>
                  )}
                  <p className="text-red-700">连接状态: {connectionStatus}</p>
                  <p className="text-red-700">生成状态: {generationStatus}</p>
                  {eventSourceRef.current && (
                    <p className="text-red-700">SSE状态: {eventSourceRef.current.readyState}</p>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* 右侧报告展示 */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm h-full">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    报告内容
                  </span>
                  {generationStatus === 'generating' && (
                    <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                  )}
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  实时展示生成的markdown报告内容，支持图表渲染
                </p>
              </div>
              <div className="p-6">
                <div className="min-h-[800px] max-h-[1200px] overflow-y-auto relative">
                  {reportContent ? (
                    <EnhancedMarkdownRenderer 
                      content={reportContent}
                      className="animate-fade-in"
                      showRawData={false}
                      onMermaidError={(error: string) => {
                         console.error('🚨 Mermaid渲染错误:', error);
                         // 不再停止SSE连接，让报告继续生成
                         // 错误会在SmartMermaidRenderer内部处理
                       }}
                    />
                  ) : (
                    <div className="flex items-center justify-center h-64 text-gray-500">
                      <div className="text-center">
                        <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p className="text-lg font-medium">暂无报告内容</p>
                        <p className="text-sm">点击"开始生成报告"开始生成</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 自定义Prompt编辑器弹窗 */}
      <PromptEditorModal
        isOpen={isPromptEditorOpen}
        onClose={() => setIsPromptEditorOpen(false)}
        onSave={handleCustomPromptSave}
        mode={reportMode}
        currentPrompt={formData.customPrompt || getDefaultPrompt(reportMode)}
      />
    </div>
  );
};

export default DeviceReportPage; 