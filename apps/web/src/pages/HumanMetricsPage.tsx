import React, { useState, useMemo } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getHumanMetricsStats } from '@/api/stats';
import { HumanMetricsDataItem } from '@/api/types';
import {
  Users,
  Eye,
  BarChart,
  TrendingUp,
  Clock,
  PieChart,
  ChevronLeft,
  Users2,
  Smile,
  Meh,
  Frown,
} from 'lucide-react';
import ReactECharts from 'echarts-for-react';
import { usePageTitle } from '../lib/usePageTitle';
import { useDeviceAndEnterpriseInfo } from '../lib/useDeviceAndEnterpriseInfo';

const HumanMetricsPage: React.FC = () => {
  const { enterpriseId, deviceId } = useParams<{ enterpriseId: string; deviceId: string }>();
  const { info: deviceInfo, isLoading: isDeviceLoading } = useDeviceAndEnterpriseInfo(enterpriseId, deviceId);

  const pageTitle = useMemo(() => {
    if (deviceId) {
      if (isDeviceLoading) {
        return '加载中...';
      }
      if (deviceInfo) {
        return `${deviceInfo.enterprise_name} - ${deviceInfo.device_name} - 人类指标`;
      }
    }
    return '人类指标';
  }, [deviceId, deviceInfo, isDeviceLoading]);

  usePageTitle(pageTitle);

  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');
  const [groupBy, setGroupBy] = useState<'day' | 'hour'>('day');

  const getTimeRange = () => {
    const now = new Date();
    const endTime = now;
    let startTime: Date;
    
    switch (timeRange) {
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startTime = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
    
    return {
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
    };
  };

  const { startTime, endTime } = getTimeRange();

  const { data: statsResponse, isLoading, error, refetch } = useQuery({
    queryKey: ['humanMetrics', deviceId, startTime, endTime, groupBy],
    queryFn: () => getHumanMetricsStats(
      enterpriseId!, 
      deviceId ?? null,
      startTime,
      endTime,
      groupBy
    ),
    enabled: !!enterpriseId,
  });

  const aggregatedData = React.useMemo(() => {
    if (!statsResponse || !statsResponse.data || statsResponse.data.length === 0) {
      return null;
    }

    const initialSummary = {
      total_records: 0,
      recognized_records_count: 0,
      unrecognized_records_count: 0,
      male_count: 0,
      female_count: 0,
      unknown_gender_count: 0,
      ages: [] as number[],
      recognized_visitors: {} as Record<string, number>,
      records_by_hour: Array(24).fill(0),
      time_series: [] as {
        time_bucket: string;
        recognized: number;
        unrecognized: number
      }[],
      recognition_success_rate_percent: 0,
      age_group_distribution: {} as Record<string, number>,
      top_visitors: {} as Record<string, { name: string; count: number }>,
      hourly_records: Array(24).fill(0).map((_, i) => ({ hour: `${String(i).padStart(2, '0')}:00`, count: 0 })),
    };

    const summary = statsResponse.data.reduce((acc, item) => {
      acc.total_records += item.overall_summary.total_records;
      acc.recognized_records_count += item.recognition_performance_metrics.recognized_records_count;
      acc.unrecognized_records_count += item.recognition_performance_metrics.unrecognized_records_count;

      acc.male_count += item.gender_distribution_metrics.male_count;
      acc.female_count += item.gender_distribution_metrics.female_count;
      acc.unknown_gender_count += item.gender_distribution_metrics.unknown_gender_count;

      for (const [group, count] of Object.entries(item.age_distribution_metrics.age_group_distribution)) {
        acc.age_group_distribution[group] = (acc.age_group_distribution[group] || 0) + count;
      }

      item.recognized_visitors_summary.forEach(v => {
        if (!acc.top_visitors[v.visitor_name]) {
          acc.top_visitors[v.visitor_name] = { name: v.visitor_name, count: 0 };
        }
        acc.top_visitors[v.visitor_name].count += v.visit_count;
      });

      if (item.time_bucket) {
        const timeBucket = new Date(item.time_bucket).toISOString();
        let timeEntry = acc.time_series.find(t => t.time_bucket === timeBucket);
        if (!timeEntry) {
          timeEntry = { time_bucket: timeBucket, recognized: 0, unrecognized: 0 };
          acc.time_series.push(timeEntry);
        }
        timeEntry.recognized += item.recognition_performance_metrics.recognized_records_count;
        timeEntry.unrecognized += item.recognition_performance_metrics.unrecognized_records_count;
      }
      
      item.records_by_hour_of_day.forEach(hourlyRecord => {
        const hourIndex = parseInt(hourlyRecord.hour_of_day.split(':')[0], 10);
        if (hourIndex >= 0 && hourIndex < 24) {
          acc.hourly_records[hourIndex].count += hourlyRecord.records_count;
        }
      });

      return acc;
    }, initialSummary);

    const totalRecognition = summary.recognized_records_count + summary.unrecognized_records_count;
    summary.recognition_success_rate_percent = totalRecognition > 0 ? (summary.recognized_records_count / totalRecognition) * 100 : 0;

    summary.time_series.sort((a, b) => new Date(a.time_bucket).getTime() - new Date(b.time_bucket).getTime());

    return summary;
  }, [statsResponse]);


  const renderOverallSummary = () => {
    if (!aggregatedData) return null;

    const cards = [
      { title: '总记录数', value: aggregatedData.total_records, icon: <Users className="w-8 h-8" />, color: 'text-blue-500' },
      { title: '识别成功', value: aggregatedData.recognized_records_count, icon: <Smile className="w-8 h-8" />, color: 'text-green-500' },
      { title: '识别失败', value: aggregatedData.unrecognized_records_count, icon: <Frown className="w-8 h-8" />, color: 'text-red-500' },
    ];

    const recognitionRate = aggregatedData.recognition_success_rate_percent;
    const rateColor = recognitionRate >= 80 ? 'text-green-500' : recognitionRate >= 60 ? 'text-yellow-500' : 'text-red-500';

    return (
      <div className="md:col-span-2 lg:col-span-3 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {cards.map(card => (
          <div key={card.title} className="bg-white rounded-xl shadow-lg p-6 flex items-center space-x-4">
            <div className={card.color}>{card.icon}</div>
            <div>
              <p className="text-gray-500 text-sm">{card.title}</p>
              <p className="text-2xl font-bold text-gray-800">{card.value}</p>
            </div>
          </div>
        ))}
        <div className="bg-white rounded-xl shadow-lg p-6 flex items-center space-x-4">
          <div className={rateColor}><TrendingUp className="w-8 h-8" /></div>
          <div>
            <p className="text-gray-500 text-sm">识别成功率</p>
            <p className={`text-2xl font-bold ${rateColor}`}>{recognitionRate.toFixed(1)}%</p>
          </div>
        </div>
      </div>
    );
  };
  
  const renderVisitorRecognitionChart = () => {
    if (!aggregatedData || !aggregatedData.time_series || aggregatedData.time_series.length === 0) return null;

    const option = {
      tooltip: { trigger: 'axis' },
      legend: { data: ['识别成功', '识别失败'] },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: aggregatedData.time_series.map(item => new Date(item.time_bucket).toLocaleDateString()),
      },
      yAxis: { type: 'value' },
      series: [
        { name: '识别成功', type: 'line', data: aggregatedData.time_series.map(item => item.recognized), smooth: true },
        { name: '识别失败', type: 'line', data: aggregatedData.time_series.map(item => item.unrecognized), smooth: true }
      ]
    };
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">访客识别趋势</h3>
        <ReactECharts option={option} style={{ height: 300 }} />
      </div>
    );
  };

  const renderGenderDistributionChart = () => {
    if (!aggregatedData) return null;
    const genderData = [
      { value: aggregatedData.male_count, name: '男性' },
      { value: aggregatedData.female_count, name: '女性' },
      { value: aggregatedData.unknown_gender_count, name: '未知' },
    ].filter(d => d.value > 0);

    const option = {
      tooltip: { trigger: 'item', formatter: '{b}: {c} ({d}%)' },
      legend: { top: 'bottom' },
      series: [{
          name: '性别分布',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          label: { show: false, position: 'center' },
          emphasis: { label: { show: true, fontSize: '20', fontWeight: 'bold' } },
          labelLine: { show: false },
          data: genderData
        }]
    };
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">性别分布</h3>
        <ReactECharts option={option} style={{ height: 300 }} />
      </div>
    );
  };

  const renderHourlyDistributionChart = () => {
    if (!aggregatedData || !aggregatedData.hourly_records) return null;
    const option = {
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: aggregatedData.hourly_records.map(h => h.hour)
      },
      yAxis: { type: 'value' },
      series: [{
        name: '记录数',
        type: 'bar',
        data: aggregatedData.hourly_records.map(h => h.count)
      }]
    };
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">每小时记录分布</h3>
        <ReactECharts option={option} style={{ height: 300 }} />
      </div>
    );
  };

  const renderTopVisitorsChart = () => {
    if (!aggregatedData || Object.keys(aggregatedData.top_visitors).length === 0) return null;

    const visitors = Object.values(aggregatedData.top_visitors)
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    const option = {
      tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
      grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
      xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01]
      },
      yAxis: { type: 'category', data: visitors.map(v => v.name), inverse: true },
      series: [{ name: '出现次数', type: 'bar', data: visitors.map(v => v.count) }]
    };
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Top 10 访客</h3>
        <ReactECharts option={option} style={{ height: 300 }} />
      </div>
    );
  };

  const renderAgeDistributionChart = () => {
    if (!aggregatedData || Object.keys(aggregatedData.age_group_distribution).length === 0) return null;
    
    const ageData = Object.entries(aggregatedData.age_group_distribution).map(([age_group, count]) => ({
      name: age_group,
      value: count
    }));
    
    const option = {
      tooltip: { trigger: 'item', formatter: '{b}: {c} ({d}%)' },
      legend: {
        type: 'scroll',
        orient: 'vertical',
        right: 10,
        top: 20,
        bottom: 20,
      },
      series: [
        {
          name: '年龄段',
          type: 'pie',
          radius: '70%',
          data: ageData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">年龄段分布</h3>
        <ReactECharts option={option} style={{ height: 300 }} />
      </div>
    );
  };

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <Link to={`/enterprise/${enterpriseId}/device/${deviceId}`} className="flex items-center text-lg font-semibold text-gray-700 hover:text-blue-600 transition-colors">
          <ChevronLeft className="w-5 h-5 mr-2" />
          返回设备详情
        </Link>
        <div className="flex items-center space-x-2">
            <div className="flex space-x-1 bg-gray-200 p-1 rounded-lg">
                {(['7d', '30d', '90d'] as const).map(t => (
                <button
                    key={t}
                    onClick={() => setTimeRange(t)}
                    className={`px-3 py-1 text-sm rounded-md ${
                    timeRange === t ? 'bg-white shadow text-gray-800' : 'text-gray-600'
                    }`}
                >
                    {t === '7d' ? '最近7天' : t === '30d' ? '最近30天' : '最近90天'}
                </button>
                ))}
            </div>
            <div className="flex space-x-1 bg-gray-200 p-1 rounded-lg">
                {(['day', 'hour'] as const).map(g => (
                    <button
                        key={g}
                        onClick={() => setGroupBy(g)}
                        className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                            groupBy === g ? 'bg-white shadow text-gray-800' : 'text-gray-500 hover:bg-gray-300'
                        }`}
                    >
                        {g === 'day' ? '按天' : '按小时'}
                    </button>
                ))}
            </div>
            <button onClick={() => refetch()} className="p-2 rounded-lg hover:bg-gray-200" disabled={isLoading}>
                {isLoading ? <Clock className="w-5 h-5 animate-spin" /> : <TrendingUp className="w-5 h-5" />}
            </button>
        </div>
      </div>

      <div className="space-y-6">
        {isLoading && <div className="text-center py-20">正在加载数据...</div>}
        {error && <div className="text-center py-20 text-red-500">加载数据失败: {error.message}</div>}
        
        {statsResponse && aggregatedData ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {renderOverallSummary()}
            {renderVisitorRecognitionChart()}
            {renderGenderDistributionChart()}
            <div className="lg:col-span-2">
              {renderHourlyDistributionChart()}
            </div>
            <div className="lg:col-span-1">
              {renderAgeDistributionChart()}
            </div>
            <div className="lg:col-span-3">
              {renderTopVisitorsChart()}
            </div>
          </div>
        ) : !isLoading && (
          <div className="text-center py-20 text-gray-500">没有找到数据</div>
        )}
      </div>
    </div>
  );
};

export default HumanMetricsPage; 