import React, { useState, useMemo, useEffect, useRef } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getDeviceConversations, Session, Message } from '../api/queries';
import { ChevronLeft, Clock, Calendar, User, Bot, Paperclip, FileJson, Music, Image as ImageIcon, ChevronsLeft, ChevronsRight, ChevronRight, Search, Filter, AlertTriangle, Download } from 'lucide-react';
import { format, formatDistance, subMonths } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import DevicePageHeader from '../components/DevicePageHeader';
import { usePageTitle } from '../lib/usePageTitle';
import { useDeviceAndEnterpriseInfo } from '../lib/useDeviceAndEnterpriseInfo';


// Chat接口定义
interface Chat {
  chat_id: string;
  session_ids: string[];
  sessions: Session[];
  all_messages: Message[];
  start_time: string;
  end_time: string;
  total_turns: number;
}

// 合并会话为Chat的逻辑
function mergeSessionsToChats(sessions: Session[], timeThresholdSeconds: number = 30): Chat[] {
  if (!sessions || sessions.length === 0) return [];

  // 按每个session的第一条消息时间排序
  const sortedSessions = [...sessions].sort((a, b) => {
    const aFirst = a.messages[0]?.message_timestamp || a.session_start_time;
    const bFirst = b.messages[0]?.message_timestamp || b.session_start_time;
    return new Date(aFirst).getTime() - new Date(bFirst).getTime();
  });

  const chats: Chat[] = [];
  let currentChat: Chat | null = null;
  let lastMessageTime: string | null = null;

  for (const session of sortedSessions) {
    if (!session.messages || session.messages.length === 0) continue;
    const sessionFirstMsgTime = session.messages[0].message_timestamp;
    const sessionLastMsgTime = session.messages[session.messages.length - 1].message_timestamp;

    if (!currentChat) {
      // 新建chat
      currentChat = {
        chat_id: session.session_id,
        session_ids: [session.session_id],
        sessions: [session],
        all_messages: [...session.messages],
        start_time: sessionFirstMsgTime,
        end_time: sessionLastMsgTime,
        total_turns: session.conversation_turns
      };
      lastMessageTime = sessionLastMsgTime;
    } else {
      // 用上一个chat的最后一条消息时间和当前session第一条消息时间做间隔判断
      const timeDiff = (new Date(sessionFirstMsgTime).getTime() - new Date(lastMessageTime!).getTime()) / 1000;
      if (timeDiff <= timeThresholdSeconds) {
        // 合并到当前chat
        currentChat.session_ids.push(session.session_id);
        currentChat.sessions.push(session);
        currentChat.all_messages.push(...session.messages);
        currentChat.end_time = sessionLastMsgTime;
        currentChat.total_turns += session.conversation_turns;
        currentChat.chat_id = currentChat.session_ids.join('_');
        lastMessageTime = sessionLastMsgTime;
      } else {
        // 保存当前chat并新建
        chats.push(currentChat);
        currentChat = {
          chat_id: session.session_id,
          session_ids: [session.session_id],
          sessions: [session],
          all_messages: [...session.messages],
          start_time: sessionFirstMsgTime,
          end_time: sessionLastMsgTime,
          total_turns: session.conversation_turns
        };
        lastMessageTime = sessionLastMsgTime;
      }
    }
  }
  if (currentChat) {
    chats.push(currentChat);
  }
  // 对每个chat的all_messages按message_timestamp排序
  chats.forEach(chat => {
    chat.all_messages.sort((a, b) => new Date(a.message_timestamp).getTime() - new Date(b.message_timestamp).getTime());
  });
  return chats;
}

// MessageCard component to render special data like actions, events, audio, and images
const MessageCard: React.FC<{ title: string; icon: React.ReactNode; data: any }> = ({ title, icon, data }) => (
  <div className="mt-2 border border-gray-200 bg-gray-50 rounded-lg p-3">
    <h4 className="text-xs font-bold text-gray-600 flex items-center mb-1">
      {icon}
      <span className="ml-2">{title}</span>
    </h4>
    {typeof data === 'string' ? (
      <a href={data} rel="noopener noreferrer" className="text-blue-500 hover:underline text-sm break-all">{data}</a>
    ) : typeof data === 'object' && data !== null ? (
      <pre className="text-xs bg-white p-2 rounded-md overflow-x-auto">
        {JSON.stringify(data, null, 2)}
      </pre>
    ) : null}
  </div>
);

const MAX_PAGE_SIZE = 2000;

const DeviceQueryPage: React.FC = () => {
  const { enterpriseId, deviceId } = useParams<{ enterpriseId: string; deviceId: string }>();
  const { info: deviceInfo, isLoading: isDeviceLoading } = useDeviceAndEnterpriseInfo(enterpriseId, deviceId);

  const pageTitle = useMemo(() => {
    if (isDeviceLoading) {
      return '加载中...';
    }
    if (deviceInfo) {
      return `${deviceInfo.enterprise_name} - ${deviceInfo.device_name} - 设备查询`;
    }
    return '设备查询';
  }, [deviceInfo, isDeviceLoading]);

  usePageTitle(pageTitle);

  const [page, setPage] = useState(1);
  const [selectedSession, setSelectedSession] = useState<Session | null>(null);
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [detailTab, setDetailTab] = useState<'detail' | 'analyze'>('detail');
  
  const [timeThresholdSeconds, setTimeThresholdSeconds] = useState(30);
  const [appliedTimeThresholdSeconds, setAppliedTimeThresholdSeconds] = useState(30);
  const [manualPageSize, setManualPageSize] = useState<number | ''>('');
  const [appliedManualPageSize, setAppliedManualPageSize] = useState<number | ''>('');
  const [autoNavCountdownSeconds, setAutoNavCountdownSeconds] = useState(2);
  const [appliedAutoNavCountdownSeconds, setAppliedAutoNavCountdownSeconds] = useState(2);

  const [showChatView, setShowChatView] = useState(true);
  const [turnFilter, setTurnFilter] = useState<string>('All');
  
  // 自动翻页相关状态
  const lastPageActionRef = useRef<'next' | 'prev'>('next');
  const [autoNavInfo, setAutoNavInfo] = useState<{ show: boolean; countdown: number; direction: 'next' | 'prev' }>({
    show: false,
    countdown: 3,
    direction: 'next',
  });

  const now = new Date();
  const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

  const defaultSearchParams = {
    startTime: format(sevenDaysAgo, "yyyy-MM-dd'T'HH:mm:ss"),
    endTime: format(now, "yyyy-MM-dd'T'HH:mm:ss"),
    pageSize: 200
  };

  // Chat模式的时间搜索参数
  const [chatSearchParams, setChatSearchParams] = useState(defaultSearchParams);
  const [appliedChatSearchParams, setAppliedChatSearchParams] = useState(defaultSearchParams);

  // 搜索状态
  const [searchParams, setSearchParams] = useState(defaultSearchParams);
  const [appliedSearchParams, setAppliedSearchParams] = useState(defaultSearchParams);
  const [showSearch, setShowSearch] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  // 推荐page_size计算逻辑
  const appliedRecommendedPageSize = 200;
  const appliedActualPageSize = appliedManualPageSize && appliedManualPageSize > 0 ? Math.min(Number(appliedManualPageSize), MAX_PAGE_SIZE) : appliedRecommendedPageSize;
  
  const queryPageSize = showChatView ? appliedActualPageSize : appliedSearchParams.pageSize;

  // 根据当前视图选择使用的时间搜索参数
  const currentTimeParams = showChatView ? appliedChatSearchParams : appliedSearchParams;

  const { data: conversationsData, isLoading: isLoadingConversations, error: conversationsError } = useQuery({
    queryKey: ['deviceConversations', enterpriseId, deviceId, page, queryPageSize, currentTimeParams.startTime, currentTimeParams.endTime],
    queryFn: () => getDeviceConversations(enterpriseId!, deviceId!, page, queryPageSize, currentTimeParams.startTime, currentTimeParams.endTime),
    enabled: !!enterpriseId && !!deviceId,
  });

  const filteredSessions = useMemo(() => {
    if (!conversationsData?.items) {
      return [];
    }
    if (turnFilter === 'All') {
      return conversationsData.items;
    }
    const turn = parseInt(turnFilter, 10);
    if (turnFilter.endsWith('+')) {
      return conversationsData.items.filter(session => session.conversation_turns >= turn);
    }
    return conversationsData.items.filter(session => session.conversation_turns === turn);
  }, [conversationsData?.items, turnFilter]);

  // 公共属性提取
  const firstSession = conversationsData?.items?.[0];

  const handleOpenSession = (session: Session) => {
    setSelectedSession(session);
    setDetailTab('detail');
  };

  // 下载功能 - 获取所有符合筛选条件的数据
  const handleDownload = async () => {
    try {
      setIsDownloading(true);

      // 获取当前筛选条件
      const currentTimeParams = showChatView ? appliedChatSearchParams : appliedSearchParams;
      const maxPageSize = 2000; // API支持的最大页面大小
      
      // 首先获取第一页来确定总数
      const firstPageData = await getDeviceConversations(
        enterpriseId!, 
        deviceId!, 
        1, 
        maxPageSize, 
        currentTimeParams.startTime, 
        currentTimeParams.endTime
      );
      
      const totalItems = firstPageData.total;
      const totalPages = Math.ceil(totalItems / maxPageSize);
      
      // 收集所有数据
      let allSessions: Session[] = [];
      
      // 如果只有一页，直接使用第一页数据
      if (totalPages <= 1) {
        allSessions = firstPageData.items;
      } else {
        // 多页数据，需要分批获取
        allSessions = [...firstPageData.items];
        
        for (let page = 2; page <= totalPages; page++) {
          const pageData = await getDeviceConversations(
            enterpriseId!, 
            deviceId!, 
            page, 
            maxPageSize, 
            currentTimeParams.startTime, 
            currentTimeParams.endTime
          );
          allSessions.push(...pageData.items);
        }
      }
      
      // 应用轮次过滤
      let filteredSessions = allSessions;
      if (turnFilter !== 'All') {
        const turn = parseInt(turnFilter, 10);
        if (turnFilter.endsWith('+')) {
          filteredSessions = allSessions.filter(session => session.conversation_turns >= turn);
        } else {
          filteredSessions = allSessions.filter(session => session.conversation_turns === turn);
        }
      }
      
      // 如果是Chat模式，需要合并会话
      let conversations;
      if (showChatView) {
        const chats = mergeSessionsToChats(filteredSessions, appliedTimeThresholdSeconds);
        conversations = chats.map(chat => ({
          chat_id: chat.chat_id,
          start_time: chat.start_time,
          end_time: chat.end_time,
          total_turns: chat.total_turns,
          messages: chat.all_messages.map(msg => ({
            role: msg.role,
            content: msg.content,
            message_timestamp: msg.message_timestamp,
            ...(msg.images_path && { images_path: msg.images_path }),
            ...(msg.audio_path && { audio_path: msg.audio_path }),
            ...(msg.action_data && { action_data: msg.action_data }),
            ...(msg.event_data && { event_data: msg.event_data })
          }))
        }));
      } else {
        conversations = filteredSessions.map(session => ({
          session_id: session.session_id,
          session_start_time: session.session_start_time,
          session_end_time: session.session_end_time,
          conversation_turns: session.conversation_turns,
          messages: session.messages.map(msg => ({
            role: msg.role,
            content: msg.content,
            message_timestamp: msg.message_timestamp,
            ...(msg.images_path && { images_path: msg.images_path }),
            ...(msg.audio_path && { audio_path: msg.audio_path }),
            ...(msg.action_data && { action_data: msg.action_data }),
            ...(msg.event_data && { event_data: msg.event_data })
          }))
        }));
      }

      const downloadData = {
        exportTime: new Date().toISOString(),
        deviceName: deviceInfo?.device_name,
        enterpriseName: deviceInfo?.enterprise_name,
        viewType: showChatView ? 'chat' : 'session',
        searchParams: {
          startTime: currentTimeParams.startTime,
          endTime: currentTimeParams.endTime,
          turnFilter: turnFilter,
          ...(showChatView && {
            timeThresholdSeconds: appliedTimeThresholdSeconds,
            manualPageSize: appliedManualPageSize,
            autoNavCountdownSeconds: appliedAutoNavCountdownSeconds,
          })
        },
        conversations: conversations,
        totalCount: conversations.length,
        originalTotalCount: totalItems
      };

      // 创建下载
      const blob = new Blob([JSON.stringify(downloadData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      
      // 格式化文件名：{企业名称}_{企业ID}_{设备名称}_{设备ID}_{chat|session}_{数据开始时间}_{数据结束时间}.json
      const startTimeFormatted = format(new Date(currentTimeParams.startTime), 'yyyy-MM-dd');
      const endTimeFormatted = format(new Date(currentTimeParams.endTime), 'yyyy-MM-dd');
      const viewType = showChatView ? 'chat' : 'session';
      
      const fileName = `${deviceInfo?.enterprise_name || 'unknown'}_${enterpriseId}_${deviceInfo?.device_name || 'unknown'}_${deviceId}_${viewType}_${startTimeFormatted}_${endTimeFormatted}.json`;
      
      a.download = fileName;
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('下载失败:', error);
      alert('下载失败，请重试');
    } finally {
      setIsDownloading(false);
    }
  };

  // 统计所有会话的所有消息
  function getAllMessages(): Message[] {
    return filteredSessions?.flatMap(s => s.messages) || [];
  }

  const allMessages = getAllMessages();

  const dailyStats = getDailyStats(filteredSessions || []);

  // 统计全量有效交互会话数（对话轮次大于0的全量会话数）
  const allSessions = conversationsData?.items || [];
  
  // 合并为Chat
  const chats = useMemo(() => mergeSessionsToChats(filteredSessions || [], appliedTimeThresholdSeconds), [filteredSessions, appliedTimeThresholdSeconds]);
  
  const filteredChats = useMemo(() => {
    if (turnFilter === 'All') {
      return chats;
    }
    const turn = parseInt(turnFilter, 10);
    if (turnFilter.endsWith('+')) {
      return chats.filter(chat => chat.total_turns >= turn);
    }
    return chats.filter(chat => chat.total_turns === turn);
  }, [chats, turnFilter]);

  const sortedChats = useMemo(() => [...filteredChats].sort((a, b) => new Date(b.end_time).getTime() - new Date(a.end_time).getTime()), [filteredChats]);

  useEffect(() => {
    // 如果不是Chat视图，或者没有开启0轮对话过滤，或者正在加载，则不执行
    if (!showChatView || turnFilter === 'All' || isLoadingConversations) {
      return;
    }
    
    // 如果过滤后的chat列表为空，但原始session列表不为空
    if (sortedChats.length === 0 && conversationsData && conversationsData.items.length > 0) {
      const totalPages = Math.ceil(conversationsData.total / conversationsData.page_size);
      const direction = lastPageActionRef.current;
      
      // 如果是向后翻页但已是最后一页，或向前翻页已是第一页，则不处理
      if ((direction === 'next' && page >= totalPages) || (direction === 'prev' && page <= 1)) {
        return;
      }
      
      setAutoNavInfo({ show: true, countdown: appliedAutoNavCountdownSeconds, direction });

      const countdownInterval = setInterval(() => {
        setAutoNavInfo(prev => {
          if (prev.countdown <= 1) {
            clearInterval(countdownInterval);
            // 执行翻页
            if (direction === 'next') {
              setPage(p => Math.min(totalPages, p + 1));
            } else {
              setPage(p => Math.max(1, p - 1));
            }
            return { ...prev, show: false, countdown: appliedAutoNavCountdownSeconds };
          }
          return { ...prev, countdown: prev.countdown - 1 };
        });
      }, 1000);

      // 组件卸载时清除计时器
      return () => clearInterval(countdownInterval);
    }
  }, [sortedChats, turnFilter, conversationsData, showChatView, isLoadingConversations, page, appliedAutoNavCountdownSeconds]);


  // 统计近3天有效会话数、对话交流次数，按天分组
  function getDailyStats(sessions: Session[]): { date: string; sessionCount: number; messageCount: number }[] {
    const dayMap: Record<string, { sessionIds: Set<string>; messageCount: number }> = {};
    sessions.forEach(session => {
      const day = session.session_start_time.slice(0, 10); // yyyy-MM-dd
      if (!dayMap[day]) {
        dayMap[day] = { sessionIds: new Set(), messageCount: 0 };
      }
      dayMap[day].sessionIds.add(session.session_id);
      dayMap[day].messageCount += session.messages.length;
    });
    return Object.entries(dayMap)
      .map(([date, { sessionIds, messageCount }]) => ({ date, sessionCount: sessionIds.size, messageCount }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }

  const renderChatModal = () => {
    if (!selectedChat) return null;

    // 生成带有session分隔符的消息列表（只显示新会话开始和结束信息）
    function generateMessagesWithSessionSeparators(): Array<{ type: 'message' | 'session_start' | 'session_end'; data: any }> {
      if (!selectedChat) return [];
      const result: Array<{ type: 'message' | 'session_start' | 'session_end'; data: any }> = [];
      // 为每个session创建消息到session的映射
      const messageToSessionMap = new Map<string, Session>();
      selectedChat.sessions.forEach(session => {
        session.messages.forEach(msg => {
          messageToSessionMap.set(msg.message_timestamp + msg.content, session);
        });
      });
      let currentSessionId: string | null = null;
      selectedChat.all_messages.forEach((msg, index) => {
        const session = messageToSessionMap.get(msg.message_timestamp + msg.content);
        // 新会话开始分隔符
        if (session && session.session_id !== currentSessionId) {
          result.push({ type: 'session_start', data: { session } });
          currentSessionId = session.session_id;
        }
        // 消息
        result.push({ type: 'message', data: { message: msg, index } });
        // 会话结束分隔符（session最后一条消息后）
        if (session && session.messages.length > 0 && msg === session.messages[session.messages.length - 1]) {
          result.push({ type: 'session_end', data: { session } });
        }
      });
      return result;
    }

    const messagesWithSeparators = generateMessagesWithSessionSeparators();

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4" onClick={() => setSelectedChat(null)}>
        <div className="bg-white rounded-2xl shadow-xl w-full max-w-4xl h-[80vh] flex flex-col" onClick={(e) => e.stopPropagation()}>
          <header className="p-4 border-b">
            <h3 className="font-bold text-lg">Chat详情</h3>
            <div
              className="text-xs text-gray-700 font-mono truncate max-w-[480px]"
              title={selectedChat.chat_id}
            >
              CHAT_ID: {selectedChat.chat_id}
            </div>
            <p className="text-sm text-gray-500">包含 {selectedChat.sessions.length} 个会话</p>
          </header>
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messagesWithSeparators.map((item, index) => {
              if (item.type === 'session_start') {
                const { session } = item.data;
                return (
                  <div key={`session-start-${index}`} className="my-6 text-xs text-gray-400 flex justify-center">
                    <span className="flex items-center gap-1">
                      <Calendar size={14} className="text-gray-300" />
                      新会话开始
                      <span className="ml-1">{format(new Date(session.session_start_time), 'yyyy-MM-dd HH:mm:ss')}</span>
                    </span>
                  </div>
                );
              } else if (item.type === 'session_end') {
                const { session } = item.data;
                return (
                  <div key={`session-end-${index}`} className="my-4 text-xs text-gray-400 flex justify-center">
                    <span className="flex items-center gap-1">
                      <Clock size={14} className="text-gray-300" />
                      结束：{format(new Date(session.session_end_time), 'yyyy-MM-dd HH:mm:ss')}
                      <span className="ml-2">轮数：{session.conversation_turns}</span>
                    </span>
                  </div>
                );
              } else {
                const { message: msg } = item.data;
                return (
                  <div key={`message-${index}`} className={`flex items-end gap-2 ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                    {msg.role === 'assistant' && <Bot className="w-8 h-8 text-blue-500 flex-shrink-0" />}
                    <div className={`max-w-[70%] p-3 rounded-2xl ${msg.role === 'user' ? 'bg-blue-500 text-white rounded-br-none' : 'bg-gray-100 text-gray-800 rounded-bl-none'}`}>
                      <p>{msg.content}</p>
                      {msg.images_path && msg.images_path.map((src: string) => <MessageCard key={src} title="图片附件" icon={<ImageIcon size={14}/>} data={src} />)}
                      {msg.audio_path && <MessageCard title="音频附件" icon={<Music size={14}/>} data={msg.audio_path} />}
                      {msg.action_data && <MessageCard title="Action Data" icon={<FileJson size={14}/>} data={msg.action_data} />}
                      {msg.event_data && <MessageCard title="Event Data" icon={<Paperclip size={14}/>} data={msg.event_data} />}
                      <div className={`text-xs mt-2 ${msg.role === 'user' ? 'text-blue-200' : 'text-gray-400'}`}>{format(new Date(msg.message_timestamp), 'HH:mm')}</div>
                    </div>
                    {msg.role === 'user' && <User className="w-8 h-8 text-green-500 flex-shrink-0" />}
                  </div>
                );
              }
            })}
          </div>
          <footer className="p-4 border-t text-center">
            <button onClick={() => setSelectedChat(null)} className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300">关闭</button>
          </footer>
        </div>
      </div>
    );
  };

  const renderConversationModal = () => {
    if (!selectedSession) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4" onClick={() => setSelectedSession(null)}>
        <div className="bg-white rounded-2xl shadow-xl w-full max-w-2xl h-[80vh] flex flex-col" onClick={(e) => e.stopPropagation()}>
          <header className="p-4 border-b">
            <h3 className="font-bold text-lg">会话记录</h3>
            <p className="text-sm text-gray-500">Session ID: {selectedSession.session_id}</p>
          </header>
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {selectedSession.messages.map((msg, index) => (
              <div key={index} className={`flex items-end gap-2 ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                {msg.role === 'assistant' && <Bot className="w-8 h-8 text-blue-500 flex-shrink-0" />}
                <div className={`max-w-[70%] p-3 rounded-2xl ${msg.role === 'user' ? 'bg-blue-500 text-white rounded-br-none' : 'bg-gray-100 text-gray-800 rounded-bl-none'}`}>
                  <p>{msg.content}</p>
                  {msg.images_path && msg.images_path.map(src => <MessageCard key={src} title="图片附件" icon={<ImageIcon size={14}/>} data={src} />)}
                  {msg.audio_path && <MessageCard title="音频附件" icon={<Music size={14}/>} data={msg.audio_path} />}
                  {msg.action_data && <MessageCard title="Action Data" icon={<FileJson size={14}/>} data={msg.action_data} />}
                  {msg.event_data && <MessageCard title="Event Data" icon={<Paperclip size={14}/>} data={msg.event_data} />}
                  <div className={`text-xs mt-2 ${msg.role === 'user' ? 'text-blue-200' : 'text-gray-400'}`}>
                    {format(new Date(msg.message_timestamp), 'HH:mm')}
                  </div>
                </div>
                {msg.role === 'user' && <User className="w-8 h-8 text-green-500 flex-shrink-0" />}
              </div>
            ))}
          </div>
          <footer className="p-4 border-t text-center">
            <button onClick={() => setSelectedSession(null)} className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300">关闭</button>
          </footer>
        </div>
      </div>
    );
  };

  const renderSearchForm = () => {
    if (!showSearch) return null;
    
    if (showChatView) {
      // Chat视图的推荐page_size计算逻辑（用于UI显示）
      const recommendedPageSize = 200;
      const actualPageSize = manualPageSize && manualPageSize > 0 ? Math.min(Number(manualPageSize), MAX_PAGE_SIZE) : recommendedPageSize;

      return (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
              <Filter size={20} />
              Chat配置
            </h3>
            <button
              onClick={() => setShowSearch(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ×
            </button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                开始时间
              </label>
              <input
                type="datetime-local"
                value={chatSearchParams.startTime.slice(0, 16)}
                onChange={(e) => setChatSearchParams(prev => ({
                  ...prev,
                  startTime: e.target.value + ':00'
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                结束时间
              </label>
              <input
                type="datetime-local"
                value={chatSearchParams.endTime.slice(0, 16)}
                onChange={(e) => setChatSearchParams(prev => ({
                  ...prev,
                  endTime: e.target.value + ':59'
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                会话合并时间阈值（秒）
              </label>
              <input
                type="number"
                min="1"
                max="300"
                value={timeThresholdSeconds}
                onChange={e => {
                  const v = parseInt(e.target.value) || 30;
                  setTimeThresholdSeconds(v);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                推荐拉取条数：<span className="font-bold text-blue-700">{recommendedPageSize}</span> 条
                <br />相邻会话间隔小于等于此时间将被合并为一个Chat
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                手动设置拉取条数（可选，最大{MAX_PAGE_SIZE}）
              </label>
              <input
                type="number"
                min="1"
                max={MAX_PAGE_SIZE}
                value={manualPageSize}
                onChange={e => {
                  const val = e.target.value;
                  if (val === '') {
                    setManualPageSize('');
                  } else {
                    const num = Math.max(1, Math.min(Number(val), MAX_PAGE_SIZE));
                    setManualPageSize(num);
                  }
                }}
                placeholder={`不填则用推荐值${recommendedPageSize}`}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                {manualPageSize && Number(manualPageSize) > MAX_PAGE_SIZE && (
                  <span className="text-red-500">不能超过最大{MAX_PAGE_SIZE}条</span>
                )}
                {manualPageSize ? (
                  <span>当前使用手动设置：<span className="font-bold text-green-700">{actualPageSize}</span> 条</span>
                ) : (
                  <span>当前使用推荐值：<span className="font-bold text-blue-700">{recommendedPageSize}</span> 条</span>
                )}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                自动翻页倒计时（秒）
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={autoNavCountdownSeconds}
                onChange={e => {
                  const v = parseInt(e.target.value) || 3;
                  setAutoNavCountdownSeconds(Math.max(1, Math.min(v, 10)));
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                无内容时自动翻页的等待时间 (1-10秒).
              </p>
            </div>
          </div>
          <div className="flex justify-end gap-3 mt-4 pt-4 border-t border-gray-200">
            <button
              onClick={() => {
                setTimeThresholdSeconds(appliedTimeThresholdSeconds);
                setManualPageSize(appliedManualPageSize);
                setAutoNavCountdownSeconds(appliedAutoNavCountdownSeconds);
                setChatSearchParams(appliedChatSearchParams);
              }}
              className="px-4 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
            >
              重置
            </button>
            <button
              onClick={() => {
                setAppliedTimeThresholdSeconds(timeThresholdSeconds);
                setAppliedManualPageSize(manualPageSize);
                setAppliedAutoNavCountdownSeconds(autoNavCountdownSeconds);
                setAppliedChatSearchParams(chatSearchParams);
                setPage(1);
                setShowSearch(false);
              }}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center gap-2"
            >
              <Search size={16} />
              确定
            </button>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
            <Filter size={20} />
            {showChatView ? 'Chat配置' : '搜索条件'}
          </h3>
          <button
            onClick={() => setShowSearch(false)}
            className="text-gray-500 hover:text-gray-700"
          >
            ×
          </button>
        </div>
      
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              开始时间
            </label>
            <input
              type="datetime-local"
              value={searchParams.startTime.slice(0, 16)}
              onChange={(e) => setSearchParams(prev => ({
                ...prev,
                startTime: e.target.value + ':00'
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              结束时间
            </label>
            <input
              type="datetime-local"
              value={searchParams.endTime.slice(0, 16)}
              onChange={(e) => setSearchParams(prev => ({
                ...prev,
                endTime: e.target.value + ':59'
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              每页数量
            </label>
            <select
              value={searchParams.pageSize}
              onChange={(e) => setSearchParams(prev => ({
                ...prev,
                pageSize: parseInt(e.target.value)
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={10}>10条</option>
              <option value={20}>20条</option>
              <option value={50}>50条</option>
              <option value={100}>100条</option>
              <option value={200}>200条</option>
            </select>
          </div>
        </div>
        
        <div className="flex justify-end gap-3 mt-4 pt-4 border-t border-gray-200">
          <button
            onClick={() => {
              setSearchParams(appliedSearchParams);
              // 重置只恢复表单状态，不触发搜索
            }}
            className="px-4 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
          >
            重置
          </button>
          <button
            onClick={() => {
              setAppliedSearchParams(searchParams);
              setPage(1);
              setShowSearch(false);
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <Search size={16} />
            搜索
          </button>
        </div>
      </div>
    );
  };

  const renderPagination = () => {
    if (!conversationsData || conversationsData.total === 0) return null;

    const totalPages = Math.ceil(conversationsData.total / conversationsData.page_size);

    const handlePageChange = (action: 'first' | 'prev' | 'next' | 'last') => {
      switch (action) {
        case 'first':
          lastPageActionRef.current = 'next';
          setPage(1);
          break;
        case 'prev':
          lastPageActionRef.current = 'prev';
          setPage(p => Math.max(1, p - 1));
          break;
        case 'next':
          lastPageActionRef.current = 'next';
          setPage(p => Math.min(totalPages, p + 1));
          break;
        case 'last':
          lastPageActionRef.current = 'next';
          setPage(totalPages);
          break;
      }
    };

    return (
      <div className="flex justify-center items-center gap-2 mt-6 text-sm">
        <button 
          onClick={() => handlePageChange('first')} 
          disabled={page === 1} 
          className="p-2 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronsLeft size={16}/>
        </button>
        <button 
          onClick={() => handlePageChange('prev')} 
          disabled={page === 1} 
          className="p-2 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft size={16}/>
        </button>
        <span className="font-medium">
          第 {page} / {totalPages} 页
        </span>
        <button 
          onClick={() => handlePageChange('next')} 
          disabled={page === totalPages || page >= totalPages} 
          className="p-2 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronsRight size={16}/>
        </button>
        <button 
          onClick={() => handlePageChange('last')} 
          disabled={page === totalPages} 
          className="p-2 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronsRight size={16}/>
        </button>
      </div>
    );
  }

  const renderAutoNavModal = () => {
    if (!autoNavInfo.show) return null;

    const directionText = autoNavInfo.direction === 'next' ? '下' : '上';

    return (
      <div className="fixed inset-0 bg-black bg-opacity-60 z-50 flex justify-center items-center">
        <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-sm text-center">
          <div className="flex justify-center items-center mx-auto bg-yellow-100 rounded-full h-12 w-12 mb-4">
            <AlertTriangle className="h-6 w-6 text-yellow-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900">无符合记录</h3>
          <p className="mt-2 text-sm text-gray-600">
            当前页面在过滤后没有内容。
            将在 <span className="font-bold text-blue-600">{autoNavInfo.countdown}</span> 秒后自动翻到{directionText}一页。
          </p>
        </div>
      </div>
    );
  };
  
  return (
    <div className="bg-gray-50 min-h-screen">
      <DevicePageHeader showAnalysisLink={true} />
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-bold">
              {showChatView ? `Chat列表 (${sortedChats.length} 个)` : `Session列表 (${filteredSessions.length} 个)`}
            </h2>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <label htmlFor="turnFilter" className="text-sm font-medium">按轮次过滤:</label>
                <select
                  id="turnFilter"
                  value={turnFilter}
                  onChange={e => setTurnFilter(e.target.value)}
                  className="px-2 py-1 border border-gray-300 rounded-md text-sm"
                >
                  <option value="All">全部</option>
                  <option value="0">0 轮</option>
                  <option value="1">1 轮</option>
                  <option value="2">2 轮</option>
                  <option value="3+">3 轮及以上</option>
                </select>
              </div>
              <button
                onClick={() => setShowChatView(!showChatView)}
                className="px-3 py-2 text-sm font-semibold text-white bg-blue-500 rounded-md hover:bg-blue-600"
              >
                {showChatView ? '会话视图' : 'Chat视图'}
              </button>
              <button
                onClick={() => setShowSearch(!showSearch)}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors flex items-center gap-2"
              >
                <Filter size={16} />
                {showSearch ? '隐藏配置' : (showChatView ? 'Chat配置' : '搜索')}
              </button>
              <button
                onClick={handleDownload}
                disabled={isLoadingConversations || isDownloading || (showChatView ? sortedChats.length === 0 : filteredSessions.length === 0)}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
                title="下载所有符合筛选条件的数据为JSON文件"
              >
                {isDownloading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    下载中...
                  </>
                ) : (
                  <>
                    <Download size={16} />
                    下载全部
                  </>
                )}
              </button>
            </div>
          </div>
          {renderSearchForm()}
          {showChatView ? (
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="text-sm text-green-800">
                <span className="font-medium">当前Chat配置：</span>
                时间范围：{format(new Date(appliedChatSearchParams.startTime), 'yyyy-MM-dd HH:mm')} 至 {format(new Date(appliedChatSearchParams.endTime), 'yyyy-MM-dd HH:mm')} | 
                时间阈值：{appliedTimeThresholdSeconds}秒 | 
                合并结果：{chats.length}个Chat | 
                原始会话：{conversationsData?.items?.length || 0}个
              </div>
            </div>
          ) : (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="text-sm text-blue-800">
                <span className="font-medium">当前搜索条件：</span>
                时间范围：{format(new Date(appliedSearchParams.startTime), 'yyyy-MM-dd HH:mm')} 至 {format(new Date(appliedSearchParams.endTime), 'yyyy-MM-dd HH:mm')} | 
                每页：{queryPageSize} 条
              </div>
            </div>
          )}
          <div className="space-y-4">
            {isLoadingConversations ? (
              <p>加载会话记录中...</p>
            ) : conversationsError ? (
              <p className="text-red-500">加载会话失败: {conversationsError.message}</p>
            ) : filteredSessions.length === 0 ? (
              <p>没有会话记录</p>
            ) : showChatView ? (
              // Chat视图
              sortedChats.length === 0 ? (
                <p className="text-gray-500 text-center py-4">没有符合条件的Chat记录</p>
              ) : (
                sortedChats.map((chat) => (
                  <div 
                    key={chat.chat_id} 
                    className="p-4 border rounded-lg hover:shadow-md hover:border-green-500 cursor-pointer transition-all"
                    onClick={() => setSelectedChat(chat)}
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <div
                          className="text-xs text-gray-700 font-mono truncate max-w-[480px]"
                          title={chat.chat_id}
                        >
                          CHAT_ID: {chat.chat_id}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          包含 {chat.sessions.length} 个会话: {chat.session_ids.join(', ')}
                        </p>
                      </div>
                      <span className="px-3 py-1 text-xs font-semibold text-green-800 bg-green-100 rounded-full flex-shrink-0">
                        {chat.total_turns} 轮对话
                      </span>
                    </div>
                    
                    {/* 显示各个session的时间 */}
                    <div className="mb-3 p-2 bg-gray-50 rounded text-xs">
                      <div className="font-semibold text-gray-700 mb-1">会话时间线：</div>
                      {chat.sessions.map((session, idx) => (
                        <div key={session.session_id} className="flex items-center gap-2 text-gray-600">
                          <span className="font-mono">{session.session_id.slice(-8)}</span>
                          <span>→</span>
                          <span>{format(new Date(session.session_start_time), 'HH:mm:ss')}</span>
                          {idx < chat.sessions.length - 1 && <span className="text-gray-400">|</span>}
                        </div>
                      ))}
                    </div>
                    
                    <div className="mt-2 space-y-1">
                      {chat.all_messages.slice(0, 4).map((msg, idx) => (
                        <div key={idx} className="flex items-center gap-2 text-xs truncate">
                          <span className={msg.role === 'user' ? 'text-green-600 font-semibold' : 'text-blue-600 font-semibold'}>
                            {msg.role === 'user' ? '用户' : '助手'}:
                          </span>
                          <span className="truncate text-gray-700 max-w-[80%]">{msg.content.length > 40 ? msg.content.slice(0, 40) + '…' : msg.content}</span>
                        </div>
                      ))}
                      {chat.all_messages.length > 4 && (
                        <div className="text-xs text-gray-500">... 还有 {chat.all_messages.length - 4} 条消息</div>
                      )}
                    </div>
                    
                    <div className="mt-3 pt-3 border-t flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Calendar size={14} />
                        <span>{format(new Date(chat.start_time), 'yyyy-MM-dd HH:mm:ss')} - {format(new Date(chat.end_time), 'HH:mm:ss')}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock size={14} />
                        <span>持续 {formatDistance(new Date(chat.end_time), new Date(chat.start_time), { locale: zhCN })}</span>
                      </div>
                    </div>
                  </div>
                ))
              )
            ) : (
              // 原始会话视图
              filteredSessions.length === 0 ? (
                <p className="text-gray-500 text-center py-4">没有符合条件的会话记录</p>
              ) : (
              filteredSessions.map((session) => (
                <div 
                  key={session.session_id} 
                  className="p-4 border rounded-lg hover:shadow-md hover:border-blue-500 cursor-pointer transition-all"
                  onClick={() => handleOpenSession(session)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-mono text-sm text-gray-600">SESSION_ID: {session.session_id}</p>
                    </div>
                    <span className="px-3 py-1 text-xs font-semibold text-blue-800 bg-blue-100 rounded-full flex-shrink-0">{session.conversation_turns} 轮对话</span>
                  </div>
                  <div className="mt-2 space-y-1">
                    {session.messages.slice(0, 3).map((msg, idx) => (
                      <div key={idx} className="flex items-center gap-2 text-xs truncate">
                        <span className={msg.role === 'user' ? 'text-green-600 font-semibold' : 'text-blue-600 font-semibold'}>
                          {msg.role === 'user' ? '用户' : '助手'}:
                        </span>
                        <span className="truncate text-gray-700 max-w-[80%]">{msg.content.length > 40 ? msg.content.slice(0, 40) + '…' : msg.content}</span>
                      </div>
                    ))}
                  </div>
                  <div className="mt-3 pt-3 border-t flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center gap-1">
                      <Calendar size={14} />
                      <span>{format(new Date(session.session_start_time), 'yyyy-MM-dd HH:mm:ss')}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock size={14} />
                      <span>持续 {formatDistance(new Date(session.session_end_time), new Date(session.session_start_time), { locale: zhCN })}</span>
                    </div>
                  </div>
                </div>
              ))
              )
            )}
          </div>
          {renderPagination()}
          {renderConversationModal()}
          {renderChatModal()}
          {renderAutoNavModal()}
        </div>
      </main>
    </div>
  );
};

export default DeviceQueryPage;
