import React, { useState } from 'react';
import EnhancedMarkdownRenderer from '../components/EnhancedMarkdownRenderer';
import { MermaidDataCleaner } from '../components/MermaidDataCleaner';

const FormatTestPage: React.FC = () => {
  const [selectedExample, setSelectedExample] = useState<string>('mixed');

  // 根据LLM输出要求的不同格式示例
  const examples = {
    mixed: {
      title: '混合格式 - 完整示例',
      content: `# 设备使用报告格式测试

## 需要代码块格式的图表

### 饼图示例 (需要 \`\`\`mermaid)
\`\`\`mermaid
pie
    title Usage by Function
    "访客接待登记": 45
    "找人/留言发消息": 30
    "参观讲解服务": 20
    "智能问答": 15
    "带路引领": 10
    "多语言服务": 5
    "人工服务": 2
\`\`\`

### 流程图示例 (需要 \`\`\`mermaid)
\`\`\`mermaid
graph LR
    A[08:00-09:00<br/>13人] --> B[09:00-12:00<br/>45人]
    B --> C[13:00-14:00<br/>8人]
    C --> D[14:00-17:00<br/>52人]
    D --> E[17:00-18:00<br/>38人]
\`\`\`

## 不需要代码块格式的图表

### 序列图示例 (直接语法)
sequenceDiagram
    Alice ->> Bob: Hello Bob, how are you?
    Bob-->>John: How about you John?
    Bob--x Alice: I am good thanks!
    Bob-x John: I am good thanks!
    Note right of John: Bob thinks a long<br/>long time, so long<br/>that the text does<br/>not fit on a row.

    Bob-->Alice: Checking with John...
    Alice->John: Yes... John, how are you?

### 时间线示例 (直接语法)
timeline
    title History of Social Media Platform
    2002 : LinkedIn
    2004 : Facebook
         : Google
    2005 : YouTube
    2006 : Twitter

### XY图表示例 (直接语法)
xychart-beta
    title "Sales Revenue"
    x-axis [jan, feb, mar, apr, may, jun, jul, aug, sep, oct, nov, dec]
    y-axis "Revenue (in $)" 4000 --> 11000
    bar [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]
    line [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]

### Git图示例 (直接语法)  
gitGraph:
    commit "Ashish"
    branch newbranch
    checkout newbranch
    commit id:"1111"
    commit tag:"test"
    checkout main
    commit type: HIGHLIGHT
    commit
    merge newbranch
    commit
    branch b2
    commit

## 文档说明

根据新的LLM输出格式规范：

**需要 \`\`\`mermaid 标识的图表：**
- pie（饼图）
- graph（流程图）
- flowchart（流程图）
- journey（用户旅程图）
- gantt（甘特图）

**不需要 \`\`\`mermaid 标识的图表：**
- sequenceDiagram（序列图）
- xychart-beta（XY图表）
- timeline（时间线）
- architecture-beta（架构图）
- gitGraph（Git图）

这样可以保证LLM输出的内容能够正确渲染，同时避免格式混乱。`
    },
    
    codeBlockOnly: {
      title: '仅代码块格式图表',
      content: `# 需要代码块格式的图表测试

\`\`\`mermaid
pie
    title 功能使用分布
    "访客接待": 45
    "智能问答": 30
    "带路服务": 25
\`\`\`

\`\`\`mermaid
graph TD
    A["开始"] --> B["用户输入"]
    B --> C{"输入类型"}
    C -->|"问答"| D["智能问答"]
    C -->|"导航"| E["路线规划"]
    D --> F["返回答案"]
    E --> F
    F --> G["结束"]
\`\`\`

\`\`\`mermaid
journey
    title 用户服务旅程
    section 入场
      刷卡进入: 5: 用户
      身份验证: 3: 系统
    section 服务
      咨询问题: 4: 用户, 机器人
      获得答案: 5: 用户
\`\`\`

\`\`\`mermaid
gantt
    title 项目进度安排
    dateFormat YYYY-MM-DD
    section 开发阶段
        需求分析    :a1, 2024-01-01, 30d
        系统设计    :after a1, 20d
    section 测试阶段
        功能测试    :2024-02-15, 12d
        性能测试    :24d
\`\`\`

这些图表都需要使用 \`\`\`mermaid 代码块格式。`
    },
    
    directOnly: {
      title: '仅直接语法图表',
      content: `# 不需要代码块格式的图表测试

## 序列图
sequenceDiagram
    participant 用户
    participant 机器人
    participant 后台系统
    
    用户->>机器人: 询问问题
    机器人->>后台系统: 处理请求
    后台系统-->>机器人: 返回结果
    机器人-->>用户: 提供答案

## 时间线
timeline
    title 今日服务亮点时刻
    section 上午时段
        09:15 : 多语言服务启动
        10:24 : 韩语客户接待
        11:30 : 智能问答高峰
    section 下午时段
        14:20 : VIP客户服务
        15:45 : 团体参观接待
        16:30 : 系统维护完成

## XY图表
xychart-beta
    title "每小时访问量统计"
    x-axis ["08:00", "09:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00"]
    y-axis "访问次数" 0 --> 100
    bar [15, 25, 45, 60, 35, 20, 55, 70, 65, 40]
    line [15, 25, 45, 60, 35, 20, 55, 70, 65, 40]

## 架构图
architecture-beta
    group api(cloud)[API服务层]
    
    service db(database)[数据库] in api
    service cache(disk)[缓存] in api
    service auth(server)[认证服务] in api
    
    db:L -- R:auth
    cache:T -- B:auth

## Git分支图
gitGraph:
    commit "初始提交"
    branch feature
    checkout feature
    commit "新功能开发"
    commit "功能完善"
    checkout main
    commit "主线更新"
    merge feature
    commit "发布版本"

这些图表都使用直接语法，不需要 \`\`\`mermaid 代码块。`
    },
    
    problematic: {
      title: '问题格式 - 需要修复',
      content: `# 格式问题示例（自动修复测试）

## 错误的timeline格式（会被自动修复）
timeline
    title 今日服务亮点时刻
    10:24 : 多语言服务
         : 韩语客户接待
         : 客户满意度提升
    15:30 : VIP接待服务
         : 专属通道引导

## 缺少引号的饼图（会被自动修复）
\`\`\`mermaid
pie
    title 服务类型分布
    访客接待: 45
    智能问答: 30
    参观服务: 25
\`\`\`

## 中文节点流程图（会被自动修复）
\`\`\`mermaid
graph LR
    A[用户进入] --> B[身份验证]
    B --> C[服务选择]
    C --> D[智能服务]
    D --> E[服务完成]
\`\`\`

这些内容包含常见的格式问题，我们的清理器会自动修复它们。`
    }
  };

  const currentExample = examples[selectedExample as keyof typeof examples];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* 页面标题 */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🧪 LLM输出格式规范测试
          </h1>
          <p className="text-gray-600">
            测试不同Mermaid图表格式的自动检测和渲染功能
          </p>
        </div>

        {/* 示例选择器 */}
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">选择测试示例</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Object.entries(examples).map(([key, example]) => (
              <button
                key={key}
                onClick={() => setSelectedExample(key)}
                className={`p-4 rounded-lg border-2 transition-all text-left ${
                  selectedExample === key
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="font-medium text-gray-900">{example.title}</div>
                <div className="text-sm text-gray-600 mt-1">
                  {key === 'mixed' && '包含各种格式'}
                  {key === 'codeBlockOnly' && '仅```mermaid格式'}
                  {key === 'directOnly' && '仅直接语法格式'}
                  {key === 'problematic' && '自动修复测试'}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* 格式规范说明 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">📋 格式规范说明</h3>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-blue-800 mb-2">需要 ```mermaid 标识：</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• pie（饼图）</li>
                <li>• graph（流程图）</li>
                <li>• flowchart（流程图）</li>
                <li>• journey（用户旅程图）</li>
                <li>• gantt（甘特图）</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-blue-800 mb-2">不需要 ```mermaid 标识：</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• sequenceDiagram（序列图）</li>
                <li>• xychart-beta（XY图表）</li>
                <li>• timeline（时间线）</li>
                <li>• architecture-beta（架构图）</li>
                <li>• gitGraph（Git图）</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 内容渲染 */}
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              当前示例：{currentExample.title}
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              展示如何正确处理不同格式的Mermaid图表
            </p>
          </div>
          <div className="p-6">
            <EnhancedMarkdownRenderer 
              content={currentExample.content}
              showRawData={true}
              onMermaidError={(error) => {
                console.error('图表渲染错误:', error);
                alert(`图表渲染错误: ${error}`);
              }}
            />
          </div>
        </div>

        {/* 诊断信息 */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">🔍 诊断信息</h3>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-800 mb-2">内容分析：</h4>
              <div className="text-sm text-gray-600">
                <p>• 内容长度: {currentExample.content.length} 字符</p>
                <p>• 代码块数量: {(currentExample.content.match(/```mermaid/g) || []).length}</p>
                <p>• 直接图表数量: {
                  ['sequenceDiagram', 'timeline', 'xychart-beta', 'architecture-beta', 'gitGraph']
                    .reduce((count, type) => {
                      const regex = new RegExp(`^${type}`, 'gm');
                      return count + (currentExample.content.match(regex) || []).length;
                    }, 0)
                }</p>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-800 mb-2">清理器测试：</h4>
              <button
                onClick={() => {
                  const cleaned = MermaidDataCleaner.cleanAllMermaidContent(currentExample.content);
                  console.log('原始内容:', currentExample.content);
                  console.log('清理后内容:', cleaned);
                  alert('清理结果已输出到控制台，请查看开发者工具');
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                测试数据清理器
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FormatTestPage; 