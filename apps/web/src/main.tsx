import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import App from './App.tsx'
import DeviceQueryPage from './pages/DeviceQueryPage.tsx';
import DeviceAnalysisPage from './pages/DeviceAnalysisPage.tsx';
import HumanMetricsPage from './pages/HumanMetricsPage.tsx';
import DeviceReportPage from './pages/DeviceReportPage.tsx';
import TestPage from './pages/TestPage.tsx';
import MermaidFixDemo from './pages/MermaidFixDemo.tsx';
import FormatTestPage from './pages/FormatTestPage.tsx';
import './index.css'

const queryClient = new QueryClient();

const router = createBrowserRouter([
  {
    path: "/",
    element: <App />,
  },
  {
    path: "/enterprise/:enterpriseId/device/:deviceId/queries",
    element: <DeviceQueryPage />,
  },
  {
    path: "/enterprise/:enterpriseId/device/:deviceId/analysis",
    element: <DeviceAnalysisPage />,
  },
  {
    path: "/enterprise/:enterpriseId/human-metrics",
    element: <HumanMetricsPage />,
  },
  {
    path: "/enterprise/:enterpriseId/human-metrics/:deviceId",
    element: <HumanMetricsPage />,
  },
  {
    path: "/device-reports",
    element: <DeviceReportPage />,
  },
  {
    path: "/test",
    element: <TestPage />,
  },
  {
    path: "/mermaid-fix",
    element: <MermaidFixDemo />,
  },
  {
    path: "/format-test",
    element: <FormatTestPage />,
  },
]);

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <RouterProvider router={router} />
    </QueryClientProvider>
  </React.StrictMode>,
) 