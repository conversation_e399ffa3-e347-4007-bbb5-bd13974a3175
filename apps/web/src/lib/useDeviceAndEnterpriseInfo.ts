import { useQuery } from '@tanstack/react-query';
import { getDeviceBasicInfo, DeviceBasicInfo } from '../api/devices';

export function useDeviceAndEnterpriseInfo(enterpriseId: string | undefined, deviceId: string | undefined) {
  const { data, isLoading, error } = useQuery<DeviceBasicInfo, Error>({
    queryKey: ['deviceBasicInfo', enterpriseId, deviceId],
    queryFn: () => getDeviceBasicInfo(enterpriseId!, deviceId!),
    enabled: !!enterpriseId && !!deviceId,
    staleTime: 1000 * 60 * 5, // Cache for 5 minutes
  });

  return {
    info: data,
    isLoading,
    error,
  };
} 