import React, { useEffect, useRef, useState } from 'react';
import { Eye, EyeOff, Download, Copy, RotateCcw } from 'lucide-react';

interface RawDataViewerProps {
  data: string;
  title?: string;
  maxHeight?: string;
  showStats?: boolean;
}

interface DataStats {
  totalLength: number;
  lineCount: number;
  wordCount: number;
  mermaidBlocks: number;
  lastUpdate: Date;
}

const RawDataViewer: React.FC<RawDataViewerProps> = ({ 
  data, 
  title = "原始数据", 
  maxHeight = "400px",
  showStats = true 
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isAutoScroll, setIsAutoScroll] = useState(true);
  const [stats, setStats] = useState<DataStats>({
    totalLength: 0,
    lineCount: 0,
    wordCount: 0,
    mermaidBlocks: 0,
    lastUpdate: new Date()
  });

  const contentRef = useRef<HTMLPreElement>(null);
  const previousDataRef = useRef<string>('');

  // 计算数据统计
  useEffect(() => {
    const lines = data.split('\n');
    const words = data.split(/\s+/).filter(word => word.length > 0);
    const mermaidMatches = data.match(/```mermaid[\s\S]*?```/g) || [];

    setStats({
      totalLength: data.length,
      lineCount: lines.length,
      wordCount: words.length,
      mermaidBlocks: mermaidMatches.length,
      lastUpdate: new Date()
    });

    // 自动滚动到底部（仅当数据发生变化时）
    if (isAutoScroll && data !== previousDataRef.current && contentRef.current) {
      setTimeout(() => {
        if (contentRef.current) {
          contentRef.current.scrollTop = contentRef.current.scrollHeight;
        }
      }, 50);
    }

    previousDataRef.current = data;
  }, [data, isAutoScroll]);

  // 复制到剪贴板
  const copyToClipboard = async () => {
    try {
      // 优先使用现代剪贴板API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(data);
        console.log('✅ 复制成功');
      } else {
        // 降级方案：使用传统的document.execCommand
        const textArea = document.createElement('textarea');
        textArea.value = data;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        if (document.execCommand('copy')) {
          console.log('✅ 复制成功（降级方案）');
        } else {
          throw new Error('复制命令执行失败');
        }
        
        document.body.removeChild(textArea);
      }
    } catch (err) {
      console.error('❌ 复制失败:', err);
      // 用户友好的提示
      alert('复制失败，请手动选择文本复制');
    }
  };

  // 下载为文件
  const downloadAsFile = () => {
    const blob = new Blob([data], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `raw-data-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // 清空数据
  const clearData = () => {
    // 这里应该通过props传递一个清空函数
    console.log('清空数据（需要父组件实现）');
  };

  return (
    <div className="raw-data-viewer border border-gray-200 rounded-lg bg-white shadow-sm">
      {/* 标题栏 */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center gap-2">
          <span className="font-medium text-gray-900">{title}</span>
          {data.length > 0 && (
            <span className="px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded-full">
              实时更新
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {/* 自动滚动切换 */}
          <button
            onClick={() => setIsAutoScroll(!isAutoScroll)}
            className={`p-1 rounded text-xs ${
              isAutoScroll 
                ? 'bg-blue-100 text-blue-600' 
                : 'bg-gray-100 text-gray-600'
            }`}
            title={isAutoScroll ? '关闭自动滚动' : '开启自动滚动'}
          >
            {isAutoScroll ? '🔄' : '⏸️'}
          </button>
          
          {/* 复制 */}
          <button
            onClick={copyToClipboard}
            className="p-1 hover:bg-gray-200 rounded"
            title="复制到剪贴板"
          >
            <Copy className="h-4 w-4 text-gray-600" />
          </button>
          
          {/* 下载 */}
          <button
            onClick={downloadAsFile}
            className="p-1 hover:bg-gray-200 rounded"
            title="下载为文件"
          >
            <Download className="h-4 w-4 text-gray-600" />
          </button>
          
          {/* 清空 */}
          <button
            onClick={clearData}
            className="p-1 hover:bg-gray-200 rounded"
            title="清空数据"
          >
            <RotateCcw className="h-4 w-4 text-gray-600" />
          </button>
          
          {/* 显示/隐藏 */}
          <button
            onClick={() => setIsVisible(!isVisible)}
            className="p-1 hover:bg-gray-200 rounded"
            title={isVisible ? '隐藏内容' : '显示内容'}
          >
            {isVisible ? (
              <EyeOff className="h-4 w-4 text-gray-600" />
            ) : (
              <Eye className="h-4 w-4 text-gray-600" />
            )}
          </button>
        </div>
      </div>

      {/* 统计信息 */}
      {showStats && isVisible && (
        <div className="px-3 py-2 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center gap-4 text-xs text-gray-600">
            <span>长度: {stats.totalLength.toLocaleString()}</span>
            <span>行数: {stats.lineCount.toLocaleString()}</span>
            <span>词数: {stats.wordCount.toLocaleString()}</span>
            {stats.mermaidBlocks > 0 && (
              <span className="text-blue-600">
                Mermaid块: {stats.mermaidBlocks}
              </span>
            )}
            <span className="ml-auto">
              更新: {stats.lastUpdate.toLocaleTimeString()}
            </span>
          </div>
        </div>
      )}

      {/* 内容区域 */}
      {isVisible && (
        <div className="relative">
          <pre
            ref={contentRef}
            className="p-4 text-sm font-mono text-gray-800 whitespace-pre-wrap overflow-auto bg-white"
            style={{ maxHeight }}
          >
            {data || (
              <span className="text-gray-400 italic">
                暂无数据，等待接收...
              </span>
            )}
          </pre>
          
          {/* 滚动指示器 */}
          {data.length > 0 && (
            <div className="absolute bottom-2 right-2">
              <div className={`px-2 py-1 text-xs rounded-full border ${
                isAutoScroll 
                  ? 'bg-blue-100 text-blue-600 border-blue-200' 
                  : 'bg-gray-100 text-gray-600 border-gray-200'
              }`}>
                {isAutoScroll ? '自动滚动' : '手动滚动'}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 折叠状态显示 */}
      {!isVisible && data.length > 0 && (
        <div className="p-3 text-center text-sm text-gray-500">
          数据已隐藏 ({stats.totalLength.toLocaleString()} 字符)
        </div>
      )}
    </div>
  );
};

export default RawDataViewer; 