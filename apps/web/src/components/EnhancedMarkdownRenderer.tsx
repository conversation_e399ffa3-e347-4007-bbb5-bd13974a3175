import React, { useState, useMemo, useRef, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import SmartMermaidRenderer from './SmartMermaidRenderer';
import RawDataViewer from './RawDataViewer';
import { MermaidDataCleaner } from './MermaidDataCleaner';

// Mermaid完整性检测函数
const isMermaidComplete = (mermaidCode: string, diagramType: string): boolean => {
  const lines = mermaidCode.trim().split('\n').filter(line => line.trim());
  if (lines.length === 0) return false;

  // 基本完整性检查
  switch (diagramType) {
    case 'timeline':
      return isTimelineComplete(lines);
    case 'xychart-beta':
      return isXYChartComplete(lines);
    case 'sequenceDiagram':
      return isSequenceComplete(lines);
    case 'pie':
      return isPieChartComplete(lines);
    case 'graph':
    case 'flowchart':
      return isFlowchartComplete(lines);
    default:
      return lines.length >= 2; // 至少有标题和一行内容
  }
};

// Timeline完整性检测
// Timeline完整性检测 - 简化逻辑，依赖Mermaid优化接口处理格式问题
const isTimelineComplete = (lines: string[]): boolean => {
  const hasTitle = lines.some(line => line.includes('title'));
  const hasTimeEntries = lines.some(line => line.includes(':') && line.trim().length > 0);
  return hasTitle && hasTimeEntries;
};

// XY图表完整性检测
const isXYChartComplete = (lines: string[]): boolean => {
  const hasTitle = lines.some(line => line.includes('title'));
  const hasXAxis = lines.some(line => line.includes('x-axis'));
  const hasYAxis = lines.some(line => line.includes('y-axis'));
  
  // 过滤掉只有bar或line但没有数据的不完整行，并清理反引号
  const validLines = lines.filter(line => {
    const trimmed = line.trim().replace(/^`+|`+$/g, ''); // 清理反引号
    // 跳过只有bar或line但没有数据数组的行
    if (/^(bar|line)\s*$/.test(trimmed)) {
      console.log(`⏳ XYChart跳过不完整行: ${trimmed} (只有类型，缺少数据)`);
      return false;
    }
    return true;
  });
  
  // 检查数据行是否完整（必须有完整的数组）
  const dataLines = validLines.filter(line => 
    (line.includes('bar') || line.includes('line')) && 
    line.includes('[')
  );
  
  const hasCompleteData = dataLines.length > 0 && dataLines.every(line => {
    const cleanedLine = line.trim().replace(/^`+|`+$/g, ''); // 清理反引号
    const arrayMatch = cleanedLine.match(/\[([^\]]*)\]/);
    if (!arrayMatch) {
      console.log(`⏳ XYChart数据不完整: ${cleanedLine} (缺少完整数组)`);
      return false;
    }
    
    const arrayContent = arrayMatch[1].trim();
    if (!arrayContent || arrayContent.endsWith(',')) {
      console.log(`⏳ XYChart数据不完整: ${cleanedLine} (数组内容为空或未结束)`);
      return false;
    }
    
    // 检查数组内容是否完整（数字之间用逗号分隔）
    const values = arrayContent.split(',').map(v => v.trim());
    if (values.some(v => !v || v === '')) {
      console.log(`⏳ XYChart数据不完整: ${cleanedLine} (数组中有空值)`);
      return false;
    }
    
    return true;
  });
  
  return hasTitle && hasXAxis && hasYAxis && hasCompleteData;
};

// 序列图完整性检测
const isSequenceComplete = (lines: string[]): boolean => {
  const validLines = lines.filter(line => {
    const trimmed = line.trim();
    // 跳过只有参与者名称的行
    if (/^[A-Za-z0-9\u4e00-\u9fff_]+\s*$/.test(trimmed)) {
      return false;
    }
    return true;
  });
  
  const messageLines = validLines.filter(line => /->>|-->>|->|--x|-x/.test(line));
  if (messageLines.length === 0) {
    return false;
  }
  
  // 放宽条件：只要有消息行就认为是完整的
  return messageLines.length > 0;
};

// 饼图完整性检测
const isPieChartComplete = (lines: string[]): boolean => {
  const hasTitle = lines.some(line => line.includes('title'));
  const hasData = lines.some(line => line.includes(':') && /\d+/.test(line));
  return hasTitle && hasData;
};

// 流程图完整性检测
const isFlowchartComplete = (lines: string[]): boolean => {
  const hasNodes = lines.some(line => /\w+\[.*\]|\w+\(.*\)|\w+\{.*\}/.test(line));
  const hasConnections = lines.some(line => /-->|->/.test(line));
  return hasNodes && hasConnections;
};

interface EnhancedMarkdownRendererProps {
  content: string;
  className?: string;
  showRawData?: boolean;
  onMermaidError?: (error: string) => void;
}

interface ContentBlock {
  type: 'markdown' | 'mermaid';
  content: string;
}

// 图表类型格式规范 - 与SmartMermaidRenderer保持一致
const DIAGRAM_FORMAT_SPECS = {
  // 需要```mermaid标识的图表（来自LLM生成规则）
  WITH_CODE_BLOCK: ['pie', 'graph', 'flowchart', 'journey', 'gantt'],
  // 不需要```mermaid标识的图表（来自LLM生成规则）
  WITHOUT_CODE_BLOCK: ['sequenceDiagram', 'xychart-beta', 'timeline', 'architecture-beta', 'gitGraph']
};

const EnhancedMarkdownRenderer: React.FC<EnhancedMarkdownRendererProps> = ({ 
  content, 
  className = '',
  showRawData = false,
  onMermaidError
}) => {
  const [rawDataVisible, setRawDataVisible] = useState(showRawData);
  const [autoScroll, setAutoScroll] = useState(true);
  const [isPaused, setIsPaused] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  // 检查内容是否包含任何Mermaid内容
  const hasMermaidContent = () => {
    // 检测```mermaid代码块
    if (/```mermaid[\s\S]*?```/g.test(content)) {
      return true;
    }
    
    // 检测直接的图表语法（不带代码块）- 使用正确的LLM规范
    const lines = content.split('\n');
    
    return lines.some(line => {
      const trimmed = line.trim().toLowerCase();
      return DIAGRAM_FORMAT_SPECS.WITHOUT_CODE_BLOCK.some(type => 
        trimmed.startsWith(type.toLowerCase())
      );
    });
  };

  const hasMermaid = hasMermaidContent();

  // 分析内容并分离Markdown和Mermaid块
  const contentBlocks = useMemo(() => {
    const blocks: ContentBlock[] = [];
    
    // 首先处理```mermaid代码块
    const mermaidCodeBlockRegex = /```mermaid\n([\s\S]*?)```/g;
    const parts: Array<{type: 'text' | 'mermaid', content: string, start: number, end: number}> = [];
    
    let match;
    let lastIndex = 0;
    
    // 找到所有mermaid代码块
    while ((match = mermaidCodeBlockRegex.exec(content)) !== null) {
      // 添加之前的文本部分
      if (match.index > lastIndex) {
        parts.push({
          type: 'text',
          content: content.slice(lastIndex, match.index),
          start: lastIndex,
          end: match.index
        });
      }
      
      // 检查mermaid代码块是否完整
      const mermaidCode = match[1].trim();
      const diagramType = MermaidDataCleaner.detectDiagramType(mermaidCode);
      
      if (diagramType && isMermaidComplete(mermaidCode, diagramType)) {
        // 添加完整的mermaid块（需要加回```mermaid包装，因为SmartMermaidRenderer需要完整格式）
        console.log(`✅ Mermaid代码块完整，准备渲染: ${diagramType}`);
        parts.push({
          type: 'mermaid',
          content: `\`\`\`mermaid\n${mermaidCode}\n\`\`\``,
          start: match.index,
          end: match.index + match[0].length
        });
      } else {
        // 如果不完整，将其作为文本处理，等待更多数据
        console.log(`⏳ Mermaid代码块不完整，等待更多数据: ${diagramType || '未知类型'}`);
        console.log(`📝 不完整代码块内容:`, mermaidCode);
        // 重要：将不完整的代码块作为纯文本处理，不进行Mermaid渲染
        // 使用markdown代码块格式显示，避免被误认为Mermaid内容
        parts.push({
          type: 'text',
          content: '```\n' + mermaidCode + '\n```', // 转换为普通代码块，避免Mermaid解析
          start: match.index,
          end: match.index + match[0].length
        });
      }
      
      lastIndex = match.index + match[0].length;
    }
    
    // 添加最后的文本部分
    if (lastIndex < content.length) {
      parts.push({
        type: 'text',
        content: content.slice(lastIndex),
        start: lastIndex,
        end: content.length
      });
    }
    
    // 如果没有mermaid代码块，整个内容都是文本
    if (parts.length === 0) {
      parts.push({
        type: 'text',
        content: content,
        start: 0,
        end: content.length
      });
    }
    
    // 现在处理文本部分中的直接mermaid语法（按LLM规范）
    for (const part of parts) {
      if (part.type === 'mermaid') {
        blocks.push({ type: 'mermaid', content: part.content });
      } else {
        // 分析文本部分是否包含直接mermaid语法
        const lines = part.content.split('\n');
        let currentMarkdown: string[] = [];
        let i = 0;
        
        while (i < lines.length) {
          const line = lines[i];
          const trimmed = line.trim().toLowerCase();
          
          // 检测是否是直接图表的开始（使用LLM规范）
          const detectedType = DIAGRAM_FORMAT_SPECS.WITHOUT_CODE_BLOCK.find(type => 
            trimmed.startsWith(type.toLowerCase())
          );
          
          if (detectedType) {
            console.log(`🎯 检测到${detectedType}图表开始:`, line.trim());
            // 先保存之前的markdown内容
            if (currentMarkdown.length > 0) {
              const markdownContent = currentMarkdown.join('\n').trim();
              if (markdownContent) {
                blocks.push({ type: 'markdown', content: markdownContent });
              }
              currentMarkdown = [];
            }
            
            // 收集完整的图表内容
            const diagramLines = [line];
            let j = i + 1;
            let emptyLineCount = 0;
            
            while (j < lines.length) {
              const nextLine = lines[j];
              const nextTrimmed = nextLine.trim().toLowerCase();
              
              // 检查是否是新图表的开始
              const isNewDiagram = DIAGRAM_FORMAT_SPECS.WITHOUT_CODE_BLOCK.some(type => 
                nextTrimmed.startsWith(type.toLowerCase())
              );
              
              // 检查是否是明显的markdown内容的开始
              const isMarkdownContent = nextTrimmed.startsWith('#') || 
                                       (nextTrimmed.startsWith('*') && nextTrimmed.length > 1) ||
                                       (nextTrimmed.startsWith('-') && nextTrimmed.length > 1 && !nextTrimmed.match(/^-{1,2}$/)) ||
                                       nextTrimmed.startsWith('1.') ||
                                       nextTrimmed.includes('**') ||
                                       nextTrimmed.startsWith('>') ||
                                       nextTrimmed.match(/^\d+\./);
              
              if (isNewDiagram || isMarkdownContent) {
                break;
              }
              
              if (nextLine.trim()) {
                emptyLineCount = 0;
                diagramLines.push(nextLine);
              } else {
                emptyLineCount++;
                if (emptyLineCount >= 2) {
                  break;
                }
                diagramLines.push(nextLine);
              }
              
              j++;
            }
            
            // 添加mermaid块（直接语法，不需要```mermaid包装）
            const diagramContent = diagramLines.join('\n').trim();
            
            // 检查Mermaid内容是否完整
            const diagramType = MermaidDataCleaner.detectDiagramType(diagramContent);
            if (diagramType) {
              // 使用完整性检测逻辑
              const isComplete = isMermaidComplete(diagramContent, diagramType);
              
              if (isComplete) {
                console.log(`✅ Mermaid内容完整，准备渲染: ${diagramType}`);
                blocks.push({ 
                  type: 'mermaid', 
                  content: diagramContent
                });
              } else {
                // 如果不完整，将其作为markdown内容处理，等待更多数据
                console.log(`⏳ Mermaid内容不完整，等待更多数据: ${diagramType}`);
                console.log(`📝 不完整内容:`, diagramLines);
                currentMarkdown.push(...diagramLines);
              }
            } else {
              // 无法检测类型，作为markdown处理
              console.log(`❓ 无法检测图表类型，作为markdown处理`);
              currentMarkdown.push(...diagramLines);
            }
            
            i = j;
          } else {
            currentMarkdown.push(line);
            i++;
          }
        }
        
        // 处理剩余的markdown内容
        if (currentMarkdown.length > 0) {
          const markdownContent = currentMarkdown.join('\n').trim();
          if (markdownContent) {
            blocks.push({ type: 'markdown', content: markdownContent });
          }
        }
      }
    }
    
    console.log('🔍 内容分析结果:', blocks.map((block, index) => ({
      index,
      type: block.type,
      preview: block.content.substring(0, 100) + (block.content.length > 100 ? '...' : ''),
      firstLine: block.content.split('\n')[0]?.trim()
    })));
    
    return blocks;
  }, [content]);

  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && !isPaused && contentRef.current) {
      const scrollToBottom = () => {
        contentRef.current?.scrollTo({
          top: contentRef.current.scrollHeight,
          behavior: 'smooth'
        });
      };
      
      // 立即滚动一次
      scrollToBottom();
      
      // 延迟滚动，确保内容已渲染
      const timeoutId = setTimeout(scrollToBottom, 50);
      
      // 再次延迟滚动，确保Mermaid图表等复杂内容已渲染
      const timeoutId2 = setTimeout(scrollToBottom, 200);
      
      return () => {
        clearTimeout(timeoutId);
        clearTimeout(timeoutId2);
      };
    }
  }, [content, autoScroll, isPaused]);

  // 额外的滚动触发：当内容块数量变化时
  useEffect(() => {
    if (autoScroll && !isPaused && contentRef.current && contentBlocks.length > 0) {
      const scrollToBottom = () => {
        contentRef.current?.scrollTo({
          top: contentRef.current.scrollHeight,
          behavior: 'smooth'
        });
      };
      
      // 使用requestAnimationFrame确保在下一帧滚动
      const rafId = requestAnimationFrame(() => {
        scrollToBottom();
      });
      
      // 延迟滚动，确保新内容块已渲染
      const timeoutId = setTimeout(scrollToBottom, 100);
      
      return () => {
        cancelAnimationFrame(rafId);
        clearTimeout(timeoutId);
      };
    }
  }, [contentBlocks.length, autoScroll, isPaused]);

  // 监听内容高度变化，触发滚动
  useEffect(() => {
    if (!autoScroll || isPaused || !contentRef.current) return;

    const observer = new ResizeObserver(() => {
      if (autoScroll && !isPaused && contentRef.current) {
        contentRef.current.scrollTo({
          top: contentRef.current.scrollHeight,
          behavior: 'smooth'
        });
      }
    });

    observer.observe(contentRef.current);

    return () => {
      observer.disconnect();
    };
  }, [autoScroll, isPaused]);

  return (
    <div className={`enhanced-markdown-renderer ${className}`}>


      {/* 三个主要区域：内容渲染、原始数据、设备使用报告 */}
      <div className="space-y-4">
        {/* 1. 内容渲染控制栏 - 固定在上边 */}
        <div className="sticky top-0 z-10 flex items-center justify-between p-3 bg-gray-50 rounded-lg border shadow-sm">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium">内容渲染</span>
            </div>
            {hasMermaid && (
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm text-blue-600">包含图表 ({contentBlocks.filter(b => b.type === 'mermaid').length} 个)</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span className="text-sm text-purple-600">AI优化可用</span>
            </div>
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${isPaused ? 'bg-yellow-500' : 'bg-green-500'}`}></div>
              <span className={`text-sm ${isPaused ? 'text-yellow-600' : 'text-green-600'}`}>
                {isPaused ? '滚动已暂停' : '自动滚动中'}
              </span>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={() => setRawDataVisible(!rawDataVisible)}
              className={`px-3 py-1 text-sm rounded transition-colors ${
                rawDataVisible 
                  ? 'bg-red-100 text-red-700 border border-red-200 hover:bg-red-200'
                  : 'bg-gray-100 text-gray-600 border border-gray-200 hover:bg-gray-200'
              }`}
            >
              {rawDataVisible ? '隐藏原始数据' : '显示原始数据'}
            </button>
            <button
              onClick={() => setIsPaused(!isPaused)}
              className={`px-3 py-1 text-sm rounded transition-colors ${
                isPaused 
                  ? 'bg-yellow-100 text-yellow-700 border border-yellow-200 hover:bg-yellow-200'
                  : 'bg-green-100 text-green-700 border border-green-200 hover:bg-green-200'
              }`}
            >
              {isPaused ? '继续滚动' : '暂停滚动'}
            </button>
            <button
              onClick={() => {
                contentRef.current?.scrollTo({
                  top: contentRef.current.scrollHeight,
                  behavior: 'smooth'
                });
              }}
              className="px-3 py-1 text-sm rounded transition-colors bg-blue-100 text-blue-700 border border-blue-200 hover:bg-blue-200"
            >
              滚动到底部
            </button>
          </div>
        </div>

        {/* 2. 原始数据区域 - 固定在上边 */}
        {rawDataVisible && (
          <div className="sticky top-14 z-10 bg-white border border-gray-200 rounded-lg shadow-sm">
            <div className="flex items-center p-3 border-b border-gray-200">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span className="text-sm font-medium">原始数据</span>
                </div>
                {hasMermaid && (
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm text-blue-600">包含图表 ({contentBlocks.filter(b => b.type === 'mermaid').length} 个)</span>
                  </div>
                )}
              </div>
            </div>
            <div className="max-h-[300px] overflow-y-auto">
              <RawDataViewer 
                data={content}
                title="接收到的原始数据"
                maxHeight="300px"
                showStats={true}
              />
            </div>
          </div>
        )}

        {/* 3. 设备使用报告内容区域 - 可滚动 */}
        <div className="border border-gray-200 rounded-lg bg-white">
          <div ref={contentRef} className="p-6 markdown-content max-h-[1000px] overflow-y-auto">
            {/* 按顺序渲染所有内容块 */}
            {contentBlocks.map((block, index) => (
              <div key={index} className="content-block">
                {block.type === 'markdown' ? (
                  <div className="prose prose-sm max-w-none prose-gray">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        // 自定义代码块组件
                        code({ children, className, ...props }) {
                          const inline = 'inline' in props && props.inline;

                          // 普通代码块
                          if (!inline) {
                            return (
                              <pre className="bg-gray-50 border rounded-lg p-4 overflow-auto my-4">
                                <code className={`text-sm ${className || ''}`}>
                                  {children}
                                </code>
                              </pre>
                            );
                          }

                          // 行内代码
                          return (
                            <code className="bg-gray-100 px-1.5 py-0.5 rounded text-sm font-mono">
                              {children}
                            </code>
                          );
                        },

                        // 自定义表格样式
                        table({ children }) {
                          return (
                            <div className="overflow-x-auto my-4">
                              <table className="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
                                {children}
                              </table>
                            </div>
                          );
                        },

                        thead({ children }) {
                          return (
                            <thead className="bg-gray-50">
                              {children}
                            </thead>
                          );
                        },

                        th({ children }) {
                          return (
                            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">
                              {children}
                            </th>
                          );
                        },

                        td({ children }) {
                          return (
                            <td className="px-4 py-3 text-sm text-gray-900 border-b border-gray-200">
                              {children}
                            </td>
                          );
                        },

                        // 自定义链接样式
                        a({ children, href }) {
                          return (
                            <a 
                              href={href} 
                              className="text-blue-600 hover:text-blue-800 underline"
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              {children}
                            </a>
                          );
                        },

                        // 自定义列表样式
                        ul({ children }) {
                          return (
                            <ul className="list-disc list-inside space-y-1 my-4 text-gray-700">
                              {children}
                            </ul>
                          );
                        },

                        ol({ children }) {
                          return (
                            <ol className="list-decimal list-inside space-y-1 my-4 text-gray-700">
                              {children}
                            </ol>
                          );
                        },

                        // 自定义引用样式
                        blockquote({ children }) {
                          return (
                            <blockquote className="border-l-4 border-blue-400 bg-blue-50 pl-4 py-2 my-4 italic text-blue-800">
                              {children}
                            </blockquote>
                          );
                        },

                        // 自定义标题样式
                        h1({ children }) {
                          return <h1 className="text-2xl font-bold text-gray-900 mt-6 mb-4">{children}</h1>;
                        },

                        h2({ children }) {
                          return <h2 className="text-xl font-bold text-gray-900 mt-5 mb-3">{children}</h2>;
                        },

                        h3({ children }) {
                          return <h3 className="text-lg font-semibold text-gray-900 mt-4 mb-2">{children}</h3>;
                        }
                      }}
                    >
                      {block.content}
                    </ReactMarkdown>
                  </div>
                ) : (
                  <div className="my-6">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                      <div className="text-xs text-blue-600">
                        📊 Mermaid图表 #{index + 1} ({block.content.startsWith('```mermaid') ? '代码块格式' : '直接语法格式'})
                      </div>
                    </div>
                    <SmartMermaidRenderer 
                      content={block.content}
                      onError={(error) => {
                        console.error('SmartMermaidRenderer错误:', error);
                        // 不调用onMermaidError，避免影响整体流程
                        // onMermaidError?.(error);
                      }}
                      onSuccess={() => {
                        console.log('Mermaid渲染成功');
                      }}
                    />
                  </div>
                )}
              </div>
            ))}
            
            {/* 如果没有内容块，显示空状态 */}
            {contentBlocks.length === 0 && (
              <div className="text-center text-gray-500 py-8">
                <p>暂无内容</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedMarkdownRenderer; 