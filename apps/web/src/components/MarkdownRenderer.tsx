import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import SmartMermaidRenderer from './SmartMermaidRenderer';

interface MarkdownRendererProps {
  content: string;
  className?: string;
  onMermaidError?: (error: string) => void;
  }



// 自定义表格组件
const Table: React.FC<any> = ({ children }) => (
  <div className="overflow-x-auto my-4">
    <table className="min-w-full border-collapse border border-gray-300">
      {children}
    </table>
  </div>
);

const TableHeader: React.FC<any> = ({ children }) => (
  <thead className="bg-gray-50">
    {children}
  </thead>
);

const TableHeaderCell: React.FC<any> = ({ children }) => (
  <th className="border border-gray-300 px-4 py-2 text-left font-semibold text-gray-900">
    {children}
  </th>
);

const TableRow: React.FC<any> = ({ children }) => (
  <tr className="border-b border-gray-200">
    {children}
  </tr>
);

const TableCell: React.FC<any> = ({ children }) => (
  <td className="border border-gray-300 px-4 py-2 text-left">
    {children}
  </td>
);

export const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ 
  content, 
  className = '',
  onMermaidError
}) => {
  // 内部代码块组件，可以访问onMermaidError
  const InternalCodeBlock: React.FC<any> = ({ children, className, inline }) => {
    const language = className?.replace('language-', '') || '';
    const code = String(children).replace(/\n$/, '');

    // 如果是mermaid代码块，返回代码块让SmartMermaidRenderer处理
    if (language === 'mermaid' && !inline) {
      return (
        <div className="mermaid-code-block bg-gray-50 border rounded-lg p-4 my-4">
          <div className="text-sm text-gray-600 mb-2">Mermaid代码块</div>
          <pre className="text-sm font-mono">{code}</pre>
        </div>
      );
    }

    // 普通代码块
    if (!inline) {
      return (
        <pre className="bg-gray-50 border rounded-lg p-4 overflow-auto my-4">
          <code className={`text-sm ${className || ''}`}>
            {children}
          </code>
        </pre>
      );
    }

    // 行内代码
    return (
      <code className="bg-gray-100 px-1.5 py-0.5 rounded text-sm font-mono">
        {children}
      </code>
    );
  };

  return (
    <div className={`markdown-content ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          // 代码块处理
          code: InternalCodeBlock,
          
          // 表格组件
          table: Table,
          thead: TableHeader,
          tr: TableRow,
          td: TableCell,
          th: TableHeaderCell,
          
          // 标题样式
          h1: ({ children }) => (
            <h1 className="text-3xl font-bold text-gray-900 mb-4 mt-6 border-b border-gray-200 pb-2">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-2xl font-semibold text-gray-900 mb-3 mt-5">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-xl font-semibold text-gray-900 mb-2 mt-4">
              {children}
            </h3>
          ),
          h4: ({ children }) => (
            <h4 className="text-lg font-semibold text-gray-900 mb-2 mt-3">
              {children}
            </h4>
          ),
          
          // 段落样式
          p: ({ children }) => (
            <p className="text-gray-700 leading-relaxed mb-4">
              {children}
            </p>
          ),
          
          // 列表样式
          ul: ({ children }) => (
            <ul className="list-disc list-inside mb-4 space-y-1 text-gray-700">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="list-decimal list-inside mb-4 space-y-1 text-gray-700">
              {children}
            </ol>
          ),
          li: ({ children }) => (
            <li className="text-gray-700">
              {children}
            </li>
          ),
          
          // 引用样式
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-blue-400 pl-4 py-2 my-4 bg-blue-50 text-gray-700 italic">
              {children}
            </blockquote>
          ),
          
          // 强调样式
          strong: ({ children }) => (
            <strong className="font-semibold text-gray-900">
              {children}
            </strong>
          ),
          
          // 链接样式
          a: ({ children, href }) => (
            <a 
              href={href} 
              className="text-blue-600 hover:text-blue-800 underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              {children}
            </a>
          ),
          
          // 分隔线
          hr: () => (
            <hr className="border-t border-gray-300 my-6" />
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}; 