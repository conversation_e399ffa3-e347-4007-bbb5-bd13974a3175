import React, { useState, useEffect } from 'react';
import { X, Save, RotateCcw, Eye, EyeOff } from 'lucide-react';
import { DEFAULT_PROMPTS, PromptTemplate, getTemplatesByMode, getTemplateById } from '../config/promptTemplates';

interface PromptEditorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (prompt: string) => void;
  mode: 'single' | 'multiple';
  currentPrompt: string;
}

const PromptEditorModal: React.FC<PromptEditorModalProps> = ({
  isOpen,
  onClose,
  onSave,
  mode,
  currentPrompt
}) => {
  const [prompt, setPrompt] = useState(currentPrompt);
  const [showPreview, setShowPreview] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');

  // 根据模式过滤模板
  const availableTemplates = getTemplatesByMode(mode);

  useEffect(() => {
    if (isOpen) {
      setPrompt(currentPrompt);
      setSelectedTemplate('');
    }
  }, [isOpen, currentPrompt]);

  const handleTemplateSelect = (templateId: string) => {
    const template = getTemplateById(templateId);
    if (template) {
      setPrompt(template.content);
      setSelectedTemplate(templateId);
    }
  };

  const handleReset = () => {
    // 根据模式选择默认模板
    const defaultTemplate = mode === 'single' 
      ? getTemplateById('single-v2')
      : getTemplateById('multiple');
    
    if (defaultTemplate) {
      setPrompt(defaultTemplate.content);
      setSelectedTemplate(defaultTemplate.id);
    }
  };

  const handleSave = () => {
    onSave(prompt);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-[90vh] flex flex-col">
        {/* 头部 */}
        <div className="border-b border-gray-200">
          <div className="flex items-center justify-between p-6">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Prompt 编辑器
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                {mode === 'single' ? '单天报告' : '多天报告'} Prompt 模板编辑
              </p>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>
          
          {/* 图表支持提示 */}
          <div className="px-6 pb-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">
                📊 当前支持以下图表类型：
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-blue-800">
                <div className="flex items-center gap-1">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  <span>饼图 pie</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  <span>柱状图 graph</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  <span>序列图 sequenceDiagram</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  <span>折线图 xychart-beta</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  <span>流程图 journey</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  <span>甘特图 gantt</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  <span>时间线 timeline</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  <span>架构图 architecture-beta</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex-1 flex overflow-hidden">
          {/* 左侧模板选择 */}
          <div className="w-80 border-r border-gray-200 p-4 overflow-y-auto">
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">模板选择</h3>
                <button
                  onClick={handleReset}
                  className="w-full flex items-center gap-2 px-3 py-2 text-sm bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-md transition-colors"
                >
                  <RotateCcw className="h-4 w-4" />
                  重置为默认模板
                </button>
              </div>

              <div className="space-y-2">
                {availableTemplates.map((template) => (
                  <div
                    key={template.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedTemplate === template.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleTemplateSelect(template.id)}
                  >
                    <h4 className="font-medium text-gray-900">{template.name}</h4>
                    <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 右侧编辑区域 */}
          <div className="flex-1 flex flex-col">
            {showPreview ? (
              /* 预览模式 */
              <div className="flex-1 p-6 overflow-y-auto">
                <div className="prose max-w-none">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Prompt 预览</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                      {prompt}
                    </pre>
                  </div>
                </div>
              </div>
            ) : (
              /* 编辑模式 */
              <div className="flex-1 p-6">
                <div className="h-full flex flex-col">
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Prompt 内容
                    </label>
                  </div>
                  <textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    className="flex-1 w-full p-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm resize-none"
                    placeholder="请输入prompt内容..."
                  />
                </div>
              </div>
            )}

            {/* 底部操作按钮 */}
            <div className="p-6 border-t border-gray-200 flex items-center justify-end gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleSave}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                保存
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromptEditorModal; 