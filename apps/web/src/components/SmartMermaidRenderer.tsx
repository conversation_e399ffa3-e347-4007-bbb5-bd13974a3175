import React, { useEffect, useRef, useState } from 'react';
import mermaid from 'mermaid';
import { MermaidDataCleaner } from './MermaidDataCleaner';
import { MermaidOptimizer } from '../services/MermaidOptimizer';

interface SmartMermaidRendererProps {
  content: string;
  onError?: (error: string) => void;
  onSuccess?: () => void;
}

// Mermaid语法检测器
class MermaidSyntaxDetector {
  // 图表类型格式规范 - 根据LLM输出要求
  private static DIAGRAM_FORMAT_SPECS = {
    // 需要```mermaid标识的图表
    WITH_CODE_BLOCK: ['pie', 'graph', 'flowchart', 'journey', 'gantt'],
    // 不需要```mermaid标识的图表  
    WITHOUT_CODE_BLOCK: ['sequenceDiagram', 'xychart-beta', 'timeline', 'architecture-beta', 'gitGraph']
  };
  
  private static ALL_DIAGRAM_TYPES = [
    ...MermaidSyntaxDetector.DIAGRAM_FORMAT_SPECS.WITH_CODE_BLOCK,
    ...MermaidSyntaxDetector.DIAGRAM_FORMAT_SPECS.WITHOUT_CODE_BLOCK,
    'classDiagram', 'stateDiagram', 'erDiagram', 'quadrantChart', 'requirement'
  ];

  // 检测是否包含任何Mermaid内容（带或不带代码块）
  static hasMermaidContent(content: string): boolean {
    // 检测```mermaid代码块
    if (/```mermaid[\s\S]*?```/g.test(content)) {
      return true;
    }
    
    // 检测直接的图表语法（不带代码块）
    const lines = content.split('\n');
    return lines.some(line => {
      const trimmed = line.trim().toLowerCase();
      return this.DIAGRAM_FORMAT_SPECS.WITHOUT_CODE_BLOCK.some(type => 
        trimmed.startsWith(type.toLowerCase())
      );
    });
  }

  // 提取所有Mermaid内容块
  static extractMermaidBlocks(content: string): Array<{code: string; type: string; hasCodeBlock: boolean}> {
    const blocks: Array<{code: string; type: string; hasCodeBlock: boolean}> = [];
    
    // 提取```mermaid代码块
    const codeBlockRegex = /```mermaid\n([\s\S]*?)```/g;
    let match;
    
    while ((match = codeBlockRegex.exec(content)) !== null) {
      const code = match[1].trim();
      const type = this.detectDiagramType(code);
      if (type) {
        blocks.push({ code, type, hasCodeBlock: true });
      }
    }
    
    // 提取不带代码块的图表（逐行扫描）
    const lines = content.split('\n');
    let currentBlock: string[] = [];
    let currentType: string | null = null;
    let inCodeBlock = false;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim().toLowerCase();
      
      // 跳过已经处理的```mermaid代码块
      if (line.includes('```mermaid')) {
        inCodeBlock = true;
        continue;
      }
      if (inCodeBlock && line.includes('```')) {
        inCodeBlock = false;
        continue;
      }
      if (inCodeBlock) continue;
      
      // 检测新图表开始
      const detectedType = this.DIAGRAM_FORMAT_SPECS.WITHOUT_CODE_BLOCK.find(type => 
        trimmed.startsWith(type.toLowerCase())
      );
      
      if (detectedType) {
        // 保存之前的块
        if (currentType && currentBlock.length > 0) {
          blocks.push({ 
            code: currentBlock.join('\n').trim(), 
            type: currentType, 
            hasCodeBlock: false 
          });
        }
        
        // 开始新块
        currentType = detectedType;
        currentBlock = [line];
      } else if (currentType && line.trim()) {
        // 继续当前块
        currentBlock.push(line);
      } else if (currentType && !line.trim()) {
        // 空行可能表示块结束
        if (currentBlock.length > 1) {
          blocks.push({ 
            code: currentBlock.join('\n').trim(), 
            type: currentType, 
            hasCodeBlock: false 
          });
          currentType = null;
          currentBlock = [];
        }
      }
    }
    
    // 处理最后一个块
    if (currentType && currentBlock.length > 0) {
      blocks.push({ 
        code: currentBlock.join('\n').trim(), 
        type: currentType, 
        hasCodeBlock: false 
      });
    }
    
    return blocks;
  }

  // 检测图表类型
  private static detectDiagramType(code: string): string | null {
    const firstLine = code.trim().split('\n')[0].trim().toLowerCase();
    
    return this.ALL_DIAGRAM_TYPES.find(type => 
      firstLine.startsWith(type.toLowerCase())
    ) || null;
  }

  // 检测Mermaid代码是否完整
  static isComplete(mermaidCode: string, diagramType: string): boolean {
    const lines = mermaidCode.trim().split('\n').filter(line => line.trim());
    if (lines.length === 0) return false;

    // 基本完整性检查
    switch (diagramType) {
      case 'pie':
        return this.isPieChartComplete(lines);
      case 'graph':
      case 'flowchart':
        return this.isFlowchartComplete(lines);
      case 'sequenceDiagram':
        return this.isSequenceComplete(lines);
      case 'timeline':
        return this.isTimelineComplete(lines);
      case 'xychart-beta':
        return this.isXYChartComplete(lines);
      case 'architecture-beta':
        return this.isArchitectureComplete(lines);
      case 'gitGraph':
        return this.isGitGraphComplete(lines);
      default:
        return lines.length >= 2; // 至少有标题和一行内容
    }
  }

  private static isPieChartComplete(lines: string[]): boolean {
    const hasTitle = lines.some(line => line.includes('title'));
    const hasData = lines.some(line => /:\s*\d+/.test(line));
    return hasTitle && hasData;
  }

  private static isFlowchartComplete(lines: string[]): boolean {
    const hasNodes = lines.some(line => /\w+\[.*\]|\w+\(.*\)|\w+\{.*\}/.test(line));
    const hasConnections = lines.some(line => /-->|->/.test(line));
    return hasNodes && hasConnections;
  }

  private static isSequenceComplete(lines: string[]): boolean {
    // 过滤掉只有参与者名称的行（如 "User"）
    const validLines = lines.filter(line => {
      const trimmed = line.trim();
      // 跳过只有参与者名称的行
      if (/^[A-Za-z0-9\u4e00-\u9fff_]+\s*$/.test(trimmed)) {
        console.log(`⏳ 序列图跳过不完整行: ${trimmed} (只有参与者名称)`);
        return false;
      }
      return true;
    });
    
    const messageLines = validLines.filter(line => /->>|-->>|->|--x|-x/.test(line));
    if (messageLines.length === 0) {
      console.log(`⏳ 序列图不完整: 没有找到有效的消息行`);
      return false;
    }
    
    // 检查所有箭头是否都有消息内容（冒号后面有内容）
    const allComplete = messageLines.every(line => {
      const arrowMatch = line.match(/(->>|-->>|->|--x|-x)/);
      if (!arrowMatch) return false;
      
      const afterArrow = line.substring(line.indexOf(arrowMatch[0]) + arrowMatch[0].length);
      // 检查是否有冒号和消息内容
      const hasMessage = afterArrow.includes(':') && afterArrow.split(':')[1]?.trim();
      
      if (!hasMessage) {
        console.log(`⏳ 序列图不完整: ${line.trim()} (缺少消息内容)`);
        return false;
      }
      return true;
    });
    
    console.log(`🔍 序列图完整性检查: 有效行数=${validLines.length}, 消息行数=${messageLines.length}, 所有完整=${allComplete}`);
    
    // 放宽条件：只要有消息行就认为是完整的
    return messageLines.length > 0;
  }

  // Timeline完整性检测 - 简化逻辑，依赖Mermaid优化接口处理格式问题
  private static isTimelineComplete(lines: string[]): boolean {
    const hasTitle = lines.some(line => line.includes('title'));
    const hasTimeEntries = lines.some(line => line.includes(':') && line.trim().length > 0);
    return hasTitle && hasTimeEntries;
  }

  private static isXYChartComplete(lines: string[]): boolean {
    const hasTitle = lines.some(line => line.includes('title'));
    const hasXAxis = lines.some(line => line.includes('x-axis'));
    const hasYAxis = lines.some(line => line.includes('y-axis'));
    
    // 过滤掉只有bar或line但没有数据的不完整行，并清理反引号
    const validLines = lines.filter(line => {
      const trimmed = line.trim().replace(/^`+|`+$/g, ''); // 清理反引号
      // 跳过只有bar或line但没有数据数组的行
      if (/^(bar|line)\s*$/.test(trimmed)) {
        console.log(`⏳ XYChart跳过不完整行: ${trimmed} (只有类型，缺少数据)`);
        return false;
      }
      return true;
    });
    
    // 检查数据行是否完整（必须有完整的数组）
    const dataLines = validLines.filter(line => 
      (line.includes('bar') || line.includes('line')) && 
      line.includes('[')
    );
    
    const hasCompleteData = dataLines.length > 0 && dataLines.every(line => {
      const cleanedLine = line.trim().replace(/^`+|`+$/g, ''); // 清理反引号
      const arrayMatch = cleanedLine.match(/\[([^\]]*)\]/);
      if (!arrayMatch) {
        console.log(`⏳ XYChart数据不完整: ${cleanedLine} (缺少完整数组)`);
        return false;
      }
      
      const arrayContent = arrayMatch[1].trim();
      if (!arrayContent || arrayContent.endsWith(',')) {
        console.log(`⏳ XYChart数据不完整: ${cleanedLine} (数组内容为空或未结束)`);
        return false;
      }
      
      // 检查数组内容是否完整（数字之间用逗号分隔）
      const values = arrayContent.split(',').map(v => v.trim());
      if (values.some(v => !v || v === '')) {
        console.log(`⏳ XYChart数据不完整: ${cleanedLine} (数组中有空值)`);
        return false;
      }
      
      return true;
    });
    
    // 检查x-axis数组是否完整
    const xAxisLine = validLines.find(line => line.includes('x-axis'));
    let hasCompleteXAxis = false;
    if (xAxisLine) {
      const cleanedXAxisLine = xAxisLine.trim().replace(/^`+|`+$/g, ''); // 清理反引号
      const xAxisMatch = cleanedXAxisLine.match(/x-axis\s+\[([^\]]*)\]/);
      if (xAxisMatch) {
        const xAxisContent = xAxisMatch[1].trim();
        const xValues = xAxisContent.split(',').map(v => v.trim());
        hasCompleteXAxis = xValues.length > 0 && !xValues.some(v => !v || v === '');
      }
    }
    
    console.log(`🔍 XYChart完整性检查: hasTitle=${hasTitle}, hasXAxis=${hasXAxis}, hasYAxis=${hasYAxis}, hasCompleteData=${hasCompleteData}, hasCompleteXAxis=${hasCompleteXAxis}`);
    console.log(`📊 XYChart有效行数: ${validLines.length}, 数据行数: ${dataLines.length}`);
    console.log(`📊 XYChart数据行:`, dataLines.map(line => line.trim().replace(/^`+|`+$/g, '')));
    console.log(`📊 XYChart x-axis:`, xAxisLine?.trim().replace(/^`+|`+$/g, ''));
    
    return hasTitle && hasXAxis && hasYAxis && hasCompleteData && hasCompleteXAxis;
  }

  private static isArchitectureComplete(lines: string[]): boolean {
    const hasGroups = lines.some(line => line.includes('group'));
    const hasServices = lines.some(line => line.includes('service'));
    return hasGroups || hasServices;
  }

  private static isGitGraphComplete(lines: string[]): boolean {
    const hasCommits = lines.some(line => line.includes('commit'));
    return hasCommits;
  }
}

// 初始化Mermaid（仅一次）
mermaid.initialize({
  startOnLoad: false,
  theme: 'default',
  securityLevel: 'loose',
  fontFamily: '"Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", sans-serif',
  fontSize: 14,
  flowchart: {
    useMaxWidth: true,
    htmlLabels: true
  },
  // 启用日志以便调试
  logLevel: 1 // 启用错误日志
});

const SmartMermaidRenderer: React.FC<SmartMermaidRendererProps> = ({ 
  content, 
  onError, 
  onSuccess 
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [renderState, setRenderState] = useState<'waiting' | 'rendering' | 'success' | 'error' | 'fallback'>('waiting');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [detectedBlocks, setDetectedBlocks] = useState<Array<{code: string; type: string; hasCodeBlock: boolean}>>([]);
  const [retryAttempts, setRetryAttempts] = useState<Map<string, number>>(new Map());
  const [fallbackContent, setFallbackContent] = useState<string>('');
  const [optimizationStats, setOptimizationStats] = useState<{optimized: number; total: number}>({optimized: 0, total: 0});

  useEffect(() => {
    // 检测Mermaid内容
    if (!MermaidSyntaxDetector.hasMermaidContent(content)) {
      setRenderState('waiting');
      setDetectedBlocks([]);
      return;
    }

    const blocks = MermaidSyntaxDetector.extractMermaidBlocks(content);
    setDetectedBlocks(blocks);

    console.log('🔍 检测到的Mermaid块:', blocks.map((b, index) => ({
      index,
      type: b.type, 
      hasCodeBlock: b.hasCodeBlock,
      preview: b.code.substring(0, 100) + (b.code.length > 100 ? '...' : ''),
      contentWithNewlines: JSON.stringify(b.code)
    })));

    // 检查是否所有块都完整
    const allComplete = blocks.every(block => 
      MermaidSyntaxDetector.isComplete(block.code, block.type)
    );
    
    if (!allComplete) {
      console.log('⏳ 图表数据不完整，等待更多内容...');
      setRenderState('waiting');
      return;
    }

    // 开始渲染 - 带重试机制
    renderMermaidBlocksWithRetry(blocks);
  }, [content]);

  // 带重试机制的渲染函数
  const renderMermaidBlocksWithRetry = async (blocks: Array<{code: string; type: string; hasCodeBlock: boolean}>) => {
    if (!containerRef.current) return;

    setRenderState('rendering');
    const container = containerRef.current;
    
    // 为每个块尝试渲染，最多重试3次
    const maxRetries = 3;
    const successfulBlocks: Array<{index: number; svg: string}> = [];
    const failedBlocks: Array<{index: number; code: string; error: string}> = [];
    let optimizedCount = 0;
    
    for (let i = 0; i < blocks.length; i++) {
      const block = blocks[i];
      const blockKey = `block-${i}-${block.type}`;
      const currentRetries = retryAttempts.get(blockKey) || 0;
      
      console.log(`🎨 开始渲染第${i+1}个图表:`, {
        type: block.type, 
        hasCodeBlock: block.hasCodeBlock, 
        retries: currentRetries,
        codeLength: block.code.length,
        codePreview: block.code.substring(0, 200) + '...'
      });
      
      let success = false;
      let lastError = '';
      let optimizedCode = '';
      
      // 尝试渲染，最多重试maxRetries次
      for (let attempt = 0; attempt <= maxRetries && !success; attempt++) {
        try {
          // 数据清理和修复
          const originalCode = block.code;
          let cleanedCode = MermaidDataCleaner.cleanMermaidCode(originalCode);
          
          console.log(`🔧 第${attempt + 1}次尝试 - 清理后的代码:`, {
            originalLength: originalCode.length,
            cleanedLength: cleanedCode.length,
            cleanedPreview: cleanedCode.substring(0, 200) + '...'
          });
          
          // 如果已经重试过，尝试更激进的清理
          if (attempt > 0) {
            console.log(`🔄 第${attempt + 1}次重试，尝试更激进的语法修复...`);
            // 移除可能导致问题的字符
            cleanedCode = cleanedCode
              .replace(/[^\x00-\x7F]/g, '') // 移除非ASCII字符
              .replace(/\s+/g, ' ') // 规范化空白字符
              .trim();
          }
          
          // 如果这是最后一次尝试且还没有优化过，尝试调用优化API
          if (attempt === maxRetries - 1 && !optimizedCode) {
            console.log(`🤖 最后一次尝试，调用Mermaid优化API...`);
            try {
              optimizedCode = await MermaidOptimizer.optimize(cleanedCode);
              if (optimizedCode !== cleanedCode) {
                console.log('✨ 使用优化后的代码进行渲染');
                cleanedCode = optimizedCode;
                optimizedCount++;
              }
            } catch (optimizeError) {
              console.warn('⚠️ Mermaid优化API调用失败，使用原始代码:', optimizeError);
            }
          }
          
          if (cleanedCode !== originalCode) {
            console.log('🔧 Mermaid语法已修复:', {
              原始: originalCode.substring(0, 100) + '...',
              修复后: cleanedCode.substring(0, 100) + '...'
            });
          }

          // 渲染图表
          const { svg } = await mermaid.render(`chart-${i}-${Date.now()}-${attempt}`, cleanedCode);
          successfulBlocks.push({ index: i, svg });
          success = true;
          console.log(`✅ 第${i+1}个图表渲染成功 (尝试${attempt + 1}次)`);
          
        } catch (error) {
          lastError = error instanceof Error ? error.message : '渲染失败';
          console.warn(`⚠️ 第${i+1}个图表渲染失败 (尝试${attempt + 1}次):`, lastError);
          console.warn(`📝 失败的代码:`, block.code);
          
          if (attempt === maxRetries) {
            failedBlocks.push({ index: i, code: block.code, error: lastError });
            console.error(`❌ 第${i+1}个图表最终渲染失败:`, lastError);
          } else {
            // 等待一段时间再重试
            await new Promise(resolve => setTimeout(resolve, 500 * (attempt + 1)));
          }
        }
      }
      
      // 更新重试计数
      if (!success) {
        setRetryAttempts(prev => new Map(prev.set(blockKey, currentRetries + 1)));
      }
    }
    
    // 处理渲染结果
    if (successfulBlocks.length === blocks.length) {
      // 全部成功
      container.innerHTML = '';
      successfulBlocks.forEach(({ index, svg }) => {
        const chartDiv = document.createElement('div');
        chartDiv.id = `mermaid-chart-${index}`;
        chartDiv.className = 'mermaid-chart mb-4';
        chartDiv.innerHTML = svg;
        container.appendChild(chartDiv);
      });
      
      setRenderState('success');
      setOptimizationStats({optimized: optimizedCount, total: blocks.length});
      onSuccess?.();
      console.log(`✅ 所有Mermaid图表渲染成功 (优化了${optimizedCount}个)`);
      
    } else if (successfulBlocks.length > 0) {
      // 部分成功，显示成功的图表和失败的代码块
      container.innerHTML = '';
      
      // 按顺序显示所有块
      for (let i = 0; i < blocks.length; i++) {
        const successfulBlock = successfulBlocks.find(b => b.index === i);
        const failedBlock = failedBlocks.find(b => b.index === i);
        
        if (successfulBlock) {
          const chartDiv = document.createElement('div');
          chartDiv.id = `mermaid-chart-${i}`;
          chartDiv.className = 'mermaid-chart mb-4';
          chartDiv.innerHTML = successfulBlock.svg;
          container.appendChild(chartDiv);
        } else if (failedBlock) {
          // 只在控制台记录错误，界面上不显示
          console.error(`❌ 图表${i+1}渲染失败:`, {
            type: blocks[i].type,
            error: failedBlock.error,
            code: failedBlock.code
          });
          
          // 显示原始代码（不显示错误信息）
          const codeDiv = document.createElement('div');
          codeDiv.className = 'bg-gray-50 border border-gray-200 rounded p-4 mb-4';
          codeDiv.innerHTML = `
            <pre class="text-xs text-gray-700 bg-white p-3 rounded border overflow-auto">${failedBlock.code}</pre>
          `;
          container.appendChild(codeDiv);
        }
      }
      
      setRenderState('success');
      setOptimizationStats({optimized: optimizedCount, total: blocks.length});
      onSuccess?.();
      console.log(`✅ 部分Mermaid图表渲染成功 (${successfulBlocks.length}/${blocks.length}, 优化了${optimizedCount}个)`);
      
    } else {
      // 全部失败，降级为显示原始代码
      console.log('🔄 所有图表渲染失败，降级为显示原始代码');
      setFallbackContent(content);
      setRenderState('fallback');
      // 不调用onError，避免影响整体流程
    }
  };

  const renderMermaidBlocks = async (blocks: Array<{code: string; type: string; hasCodeBlock: boolean}>) => {
    // 保留原函数作为备用
    await renderMermaidBlocksWithRetry(blocks);
  };

  // 渲染状态指示器
  const renderStatusIndicator = () => {
    switch (renderState) {
      case 'waiting':
        return (
          <div className="flex items-center gap-2 text-gray-500 text-sm mb-4">
            <div className="animate-pulse w-2 h-2 bg-gray-400 rounded-full"></div>
            等待图表数据完整... 
            {detectedBlocks.length > 0 && `(已检测到 ${detectedBlocks.length} 个图表块)`}
          </div>
        );
      case 'rendering':
        return (
          <div className="flex items-center gap-2 text-blue-500 text-sm mb-4">
            <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            正在渲染 {detectedBlocks.length} 个Mermaid图表...
          </div>
        );
      case 'success':
        return (
          <div className="flex items-center gap-2 text-green-600 text-sm mb-4">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            已成功渲染 {detectedBlocks.length} 个图表
            {optimizationStats.optimized > 0 && (
              <span className="text-blue-600">
                (AI优化了 {optimizationStats.optimized} 个)
              </span>
            )}
          </div>
        );
      case 'error':
        return (
          <div className="flex items-center gap-2 text-red-600 text-sm mb-4">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            渲染失败: {errorMessage}
          </div>
        );
      case 'fallback':
        return (
          <div className="flex items-center gap-2 text-gray-500 text-sm mb-4">
            <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
            显示原始内容
          </div>
        );
    }
  };

  return (
    <div className="mermaid-renderer">
      {renderStatusIndicator()}
      {renderState === 'fallback' ? (
        <div className="bg-gray-50 border border-gray-200 rounded p-4">
          <pre className="text-xs text-gray-700 bg-white p-3 rounded border overflow-auto whitespace-pre-wrap">
            {fallbackContent}
          </pre>
        </div>
      ) : (
        <div ref={containerRef} className="mermaid-container"></div>
      )}
    </div>
  );
};

export default SmartMermaidRenderer; 