// Mermaid数据清理和格式修复工具
export class MermaidDataCleaner {
  
  // 图表类型格式规范映射 - 与SmartMermaidRenderer保持一致
  private static DIAGRAM_FORMAT_SPECS = {
    // 需要```mermaid标识的图表
    WITH_CODE_BLOCK: ['pie', 'graph', 'flowchart', 'journey', 'gantt'],
    // 不需要```mermaid标识的图表  
    WITHOUT_CODE_BLOCK: ['sequenceDiagram', 'xychart-beta', 'timeline', 'architecture-beta', 'gitGraph']
  };

  // 检测图表类型
  static detectDiagramType(code: string): string | null {
    const firstLine = code.trim().split('\n')[0].trim().toLowerCase();
    
    const allTypes = [
      ...this.DIAGRAM_FORMAT_SPECS.WITH_CODE_BLOCK,
      ...this.DIAGRAM_FORMAT_SPECS.WITHOUT_CODE_BLOCK
    ];
    
    return allTypes.find(type => firstLine.startsWith(type.toLowerCase())) || null;
  }

  // 检查图表类型是否需要代码块标识
  static needsCodeBlock(diagramType: string): boolean {
    return this.DIAGRAM_FORMAT_SPECS.WITH_CODE_BLOCK.includes(diagramType);
  }

  // 根据新格式规范清理所有Mermaid内容
  static cleanAllMermaidContent(content: string): string {
    let result = content;
    
    // 1. 处理现有的```mermaid代码块
    result = result.replace(/```mermaid\n([\s\S]*?)```/g, (match, mermaidCode) => {
      const cleaned = this.cleanMermaidCode(mermaidCode);
      const diagramType = this.detectDiagramType(cleaned);
      
      if (diagramType && this.needsCodeBlock(diagramType)) {
        // 需要保持代码块格式
        return `\`\`\`mermaid\n${cleaned}\n\`\`\``;
      } else {
        // 转换为直接格式
        return cleaned;
      }
    });
    
    // 2. 检测并包装需要代码块的直接图表
    const lines = result.split('\n');
    const processedLines: string[] = [];
    let i = 0;
    
    while (i < lines.length) {
      const line = lines[i];
      const trimmed = line.trim().toLowerCase();
      
      // 检测是否是需要代码块的图表类型
      const needsCodeBlockType = this.DIAGRAM_FORMAT_SPECS.WITH_CODE_BLOCK.find(type => 
        trimmed.startsWith(type.toLowerCase())
      );
      
      if (needsCodeBlockType) {
        // 收集完整的图表代码
        const diagramLines = [lines[i]];
        let j = i + 1;
        
        // 继续收集后续行，直到遇到空行或新的图表
        while (j < lines.length) {
          const nextLine = lines[j];
          const nextTrimmed = nextLine.trim().toLowerCase();
          
          // 检查是否是新图表的开始
          const isNewDiagram = [...this.DIAGRAM_FORMAT_SPECS.WITH_CODE_BLOCK, ...this.DIAGRAM_FORMAT_SPECS.WITHOUT_CODE_BLOCK]
            .some(type => nextTrimmed.startsWith(type.toLowerCase()));
          
          if (isNewDiagram && j > i + 1) {
            // 遇到新图表，停止收集
            break;
          }
          
          if (nextLine.trim()) {
            diagramLines.push(nextLine);
          } else if (diagramLines.length > 1) {
            // 遇到空行且已有内容，可能是图表结束
            break;
          }
          
          j++;
        }
        
        // 清理图表代码并包装为代码块
        const diagramCode = diagramLines.join('\n');
        const cleanedCode = this.cleanMermaidCode(diagramCode);
        
        processedLines.push(`\`\`\`mermaid`);
        processedLines.push(cleanedCode);
        processedLines.push(`\`\`\``);
        
        i = j;
      } else {
        processedLines.push(line);
        i++;
      }
    }
    
    return processedLines.join('\n');
  }

  // 清理整个内容中的所有Mermaid块（向后兼容的API）
  static cleanAllMermaidBlocks(content: string): string {
    return this.cleanAllMermaidContent(content);
  }

  // 清理单个Mermaid代码块
  static cleanMermaidCode(code: string): string {
    const trimmed = code.trim();
    const lines = trimmed.split('\n');
    
    if (lines.length === 0) return trimmed;
    
    const firstLine = lines[0].trim().toLowerCase();
    
    // 根据图表类型进行特定清理
    if (firstLine.startsWith('timeline')) {
      return this.fixTimelineSyntax(lines);
    } else if (firstLine.startsWith('pie')) {
      return this.fixPieSyntax(lines);
    } else if (firstLine.startsWith('graph') || firstLine.startsWith('flowchart')) {
      return this.fixFlowchartSyntax(lines);
    } else if (firstLine.startsWith('sequencediagram')) {
      return this.fixSequenceDiagramSyntax(lines);
    } else if (firstLine.startsWith('xychart-beta')) {
      return this.fixXYChartSyntax(lines);
    } else if (firstLine.startsWith('architecture-beta')) {
      return this.fixArchitectureSyntax(lines);
    } else if (firstLine.startsWith('gitgraph')) {
      return this.fixGitGraphSyntax(lines);
    } else if (firstLine.startsWith('journey')) {
      return this.fixJourneySyntax(lines);
    } else if (firstLine.startsWith('gantt')) {
      return this.fixGanttSyntax(lines);
    }
    
    return trimmed;
  }

  // Timeline语法修复 - 简化逻辑，依赖Mermaid优化接口处理格式问题
  private static fixTimelineSyntax(lines: string[]): string {
    const result: string[] = [];
    let hasSection = false;
    
    // 时间格式转换函数：将 HH:MM 转换为 HH点MM分
    const convertTimeFormat = (timeStr: string): string => {
      // 匹配 HH:MM 格式的时间
      const timeMatch = timeStr.match(/(\d{1,2}):(\d{2})/);
      if (timeMatch) {
        const [, hours, minutes] = timeMatch;
        return timeStr.replace(/(\d{1,2}):(\d{2})/g, `${hours}点${minutes}分`);
      }
      return timeStr;
    };
    
    for (const line of lines) {
      const trimmed = line.trim();
      
      // 处理timeline声明
      if (trimmed.toLowerCase().startsWith('timeline')) {
        result.push('timeline');
        continue;
      }
      
      // 处理标题行
      if (trimmed.toLowerCase().includes('title')) {
        const titleMatch = trimmed.match(/title\s+(.+)/i);
        if (titleMatch) {
          const title = titleMatch[1].replace(/^["']|["']$/g, ''); // 移除已有引号
          result.push(`    title ${title}`);
        } else {
          result.push(trimmed);
        }
        continue;
      }
      
      // 检查是否已经有section
      if (trimmed.toLowerCase().startsWith('section')) {
        hasSection = true;
        result.push(trimmed);
        continue;
      }
      
      // 处理时间条目: 日期 : 数值
      const timeMatch = trimmed.match(/^(\d{4}-\d{2}-\d{2})\s*:\s*(.+)$/);
      if (timeMatch) {
        const [, date, value] = timeMatch;
        // 如果没有section，添加一个默认section
        if (!hasSection) {
          result.push('    section 数据趋势');
          hasSection = true;
        }
        result.push(`        ${date} : ${value.trim()}`);
        continue;
      }
      
      // 处理其他格式的时间条目（包含时间格式转换）
      const generalTimeMatch = trimmed.match(/^(.+?)\s*:\s*(.+)$/);
      if (generalTimeMatch) {
        const [, time, value] = generalTimeMatch;
        // 转换时间格式
        const convertedTime = convertTimeFormat(time.trim());
        
        // 如果没有section，添加一个默认section
        if (!hasSection) {
          result.push('    section 数据趋势');
          hasSection = true;
        }
        result.push(`        ${convertedTime} : ${value.trim()}`);
        continue;
      }
      
      if (trimmed) {
        result.push(trimmed);
      }
    }
    
    return result.join('\n');
  }











  // 修复饼图语法
  private static fixPieSyntax(lines: string[]): string {
    const result: string[] = [];
    
    for (const line of lines) {
      const trimmed = line.trim();
      
      // 处理pie声明
      if (trimmed.toLowerCase().startsWith('pie')) {
        result.push('pie');
        continue;
      }
      
      // 处理标题行
      if (trimmed.toLowerCase().includes('title')) {
        const titleMatch = trimmed.match(/title\s+(.+)/i);
        if (titleMatch) {
          const title = titleMatch[1].replace(/^["']|["']$/g, ''); // 移除已有引号
          result.push(`    title ${title}`);
        } else {
          result.push(trimmed);
        }
        continue;
      }
      
      // 处理数据行: "项目" : 数值 或 "项目" : 数值%
      const dataMatch = trimmed.match(/^(.+?)\s*:\s*(\d+)%?\s*$/);
      if (dataMatch) {
        const [, label, value] = dataMatch;
        const cleanLabel = label.replace(/^["']|["']$/g, '').trim();
        // 移除百分号，只保留数字
        const numericValue = value.replace('%', '');
        result.push(`    "${cleanLabel}" : ${numericValue}`);
        continue;
      }
      
      // 处理其他可能包含百分号的行
      if (trimmed.includes('%')) {
        // 移除行中的百分号
        const cleanedLine = trimmed.replace(/%/g, '');
        if (cleanedLine.trim()) {
          result.push(cleanedLine.trim());
        }
        continue;
      }
      
      if (trimmed) {
        result.push(trimmed);
      }
    }
    
    return result.join('\n');
  }

  // 修复流程图语法
  private static fixFlowchartSyntax(lines: string[]): string {
    const result: string[] = [];
    
    for (const line of lines) {
      let processedLine = line.trim();
      
      // 处理中文节点: A[中文文本] -> A["中文文本"]
      processedLine = processedLine.replace(
        /(\w+)\[([^\]]*[\u4e00-\u9fff][^\]]*)\]/g,
        (match, nodeId, nodeText) => {
          const cleanText = nodeText.trim().replace(/^["']|["']$/g, '');
          return `${nodeId}["${cleanText}"]`;
        }
      );
      
      // 处理中文圆括号节点: A(中文文本) -> A("中文文本")
      processedLine = processedLine.replace(
        /(\w+)\(([^)]*[\u4e00-\u9fff][^)]*)\)/g,
        (match, nodeId, nodeText) => {
          const cleanText = nodeText.trim().replace(/^["']|["']$/g, '');
          return `${nodeId}("${cleanText}")`;
        }
      );
      
      if (processedLine) {
        result.push(processedLine);
      }
    }
    
    return result.join('\n');
  }

  // 修复序列图语法
  private static fixSequenceDiagramSyntax(lines: string[]): string {
    const result: string[] = [];
    
    for (const line of lines) {
      let processedLine = line.trim();
      
      // 确保序列图声明正确
      if (processedLine.toLowerCase() === 'sequencediagram') {
        result.push('sequenceDiagram');
        continue;
      }
      
      // 处理只有参与者名称但没有箭头的行（如 "User"）
      const participantOnlyPattern = /^(\s*)([A-Za-z0-9\u4e00-\u9fff_]+)\s*$/;
      const participantMatch = processedLine.match(participantOnlyPattern);
      if (participantMatch) {
        const [, indent, participant] = participantMatch;
        // 如果这一行只有参与者名称，跳过它（不添加到结果中）
        console.log(`🔧 跳过不完整的序列图行: ${line.trim()} (只有参与者名称)`);
        continue;
      }
      
      // 修复不完整的箭头语法
      const incompleteArrowPatterns = [
        // 完整箭头但缺少消息内容：User->>Bot
        /^(\s*)([A-Za-z0-9\u4e00-\u9fff_]+)\s*(->|-->|->>|-->>|-x|--x)\s*([A-Za-z0-9\u4e00-\u9fff_]+)\s*$/,
        // 不完整的箭头：Bot-- 或 Bot->
        /^(\s*)([A-Za-z0-9\u4e00-\u9fff_]+)\s*(--|->|-->>|->>)\s*$/,
      ];
      
      for (const pattern of incompleteArrowPatterns) {
        const match = processedLine.match(pattern);
        if (match) {
          const [, indent, from, arrow, to] = match;
          
          if (to) {
            // 有目标但缺少消息内容
            processedLine = `${indent}${from}${arrow}${to}: 消息`;
          } else {
            // 箭头不完整，添加默认目标和消息
            if (arrow === '--' || arrow === '-->>') {
              processedLine = `${indent}${from}-->>User: 响应`;
            } else if (arrow === '->' || arrow === '->>') {
              processedLine = `${indent}${from}->>User: 响应`;
            } else {
              processedLine = `${indent}${from}-->>User: 响应`;
            }
          }
          
          console.log(`🔧 修复不完整的序列图箭头: ${line.trim()} -> ${processedLine.trim()}`);
          break;
        }
      }
      
      // 处理中文参与者名称
      processedLine = processedLine.replace(
        /(participant\s+)([^\s:]+)(\s+as\s+)?([^:\n]*)/gi,
        (match, participant, name, as, alias) => {
          if (alias && alias.trim()) {
            // 如果有中文别名，加引号
            const cleanAlias = alias.trim().replace(/^["']|["']$/g, '');
            if (/[\u4e00-\u9fff]/.test(cleanAlias)) {
              return `${participant}${name}${as || ' as '}"${cleanAlias}"`;
            }
          }
          return match;
        }
      );
      
      if (processedLine) {
        result.push(processedLine);
      }
    }
    
    return result.join('\n');
  }

  // 修复XY图表语法
  private static fixXYChartSyntax(lines: string[]): string {
    const result: string[] = [];
    
    for (const line of lines) {
      let processedLine = line.trim();
      
      // 清理反引号 - 移除行首和行尾的反引号
      processedLine = processedLine.replace(/^`+|`+$/g, '');
      
      // 处理xychart-beta声明
      if (processedLine.toLowerCase().startsWith('xychart-beta')) {
        result.push('xychart-beta');
        continue;
      }
      
      // 处理标题行 - 确保中文标题有引号
      if (processedLine.includes('title')) {
        const titleMatch = processedLine.match(/title\s+(.+)/);
        if (titleMatch) {
          const titleText = titleMatch[1].trim();
          // 如果标题没有引号且包含中文，添加引号
          if (!titleText.startsWith('"') && !titleText.startsWith("'") && /[\u4e00-\u9fff]/.test(titleText)) {
            const cleanTitle = titleText.replace(/^["']|["']$/g, '');
            result.push(`    title "${cleanTitle}"`);
            continue;
          }
        }
      }
      
      // 处理x-axis - 确保日期格式正确
      if (processedLine.includes('x-axis')) {
        const axisMatch = processedLine.match(/x-axis\s+(.+)/);
        if (axisMatch) {
          const axisContent = axisMatch[1].trim();
          // 检查是否是数组格式
          if (axisContent.startsWith('[') && axisContent.endsWith(']')) {
            // 处理数组内的日期格式，确保用引号包围
            const dates = axisContent.slice(1, -1).split(',').map(date => {
              const cleanDate = date.trim();
              // 如果日期包含斜杠且没有引号，添加引号
              if (cleanDate.includes('/') && !cleanDate.startsWith('"') && !cleanDate.startsWith("'")) {
                return `"${cleanDate}"`;
              }
              return cleanDate;
            });
            result.push(`    x-axis [${dates.join(', ')}]`);
            continue;
          }
        }
      }
      
      // 处理y-axis - 确保中文标签有引号
      if (processedLine.includes('y-axis')) {
        const axisMatch = processedLine.match(/y-axis\s+(.+)/);
        if (axisMatch) {
          const axisContent = axisMatch[1].trim();
          // 如果y-axis标签包含中文且没有引号，添加引号
          if (/[\u4e00-\u9fff]/.test(axisContent) && !axisContent.startsWith('"') && !axisContent.startsWith("'")) {
            const parts = axisContent.split(/\s+-->\s+/);
            if (parts.length === 2) {
              const label = parts[0].replace(/^["']|["']$/g, '');
              const range = parts[1];
              result.push(`    y-axis "${label}" ${range}`);
              continue;
            }
          }
        }
      }
      
      // 处理bar和line数据
      if (processedLine.includes('bar') || processedLine.includes('line')) {
        const dataMatch = processedLine.match(/(bar|line)\s+(.+)/);
        if (dataMatch) {
          const [, type, data] = dataMatch;
          // 确保数据是数组格式
          if (data.startsWith('[') && data.endsWith(']')) {
            result.push(`    ${type} ${data}`);
            continue;
          }
        }
        
        // 处理只有bar或line但没有数据的情况
        const onlyTypeMatch = processedLine.match(/^(bar|line)\s*$/);
        if (onlyTypeMatch) {
          const [, type] = onlyTypeMatch;
          console.log(`🔧 跳过不完整的${type}行: ${processedLine.trim()} (缺少数据数组)`);
          continue; // 跳过这一行，不添加到结果中
        }
      }
      
      if (processedLine) {
        result.push(processedLine);
      }
    }
    
    return result.join('\n');
  }

  // 修复架构图语法
  private static fixArchitectureSyntax(lines: string[]): string {
    const result: string[] = [];
    
    for (const line of lines) {
      let processedLine = line.trim();
      
      // 处理中文组和服务名称
      processedLine = processedLine.replace(
        /(group|service)\s+([^\[\(]+)(\[[^\]]+\]|\([^)]+\))/g,
        (match, type, name, bracket) => {
          const cleanName = name.trim();
          if (/[\u4e00-\u9fff]/.test(cleanName)) {
            return `${type} "${cleanName}"${bracket}`;
          }
          return match;
        }
      );
      
      if (processedLine) {
        result.push(processedLine);
      }
    }
    
    return result.join('\n');
  }

  // 修复Git图语法
  private static fixGitGraphSyntax(lines: string[]): string {
    const result: string[] = [];
    
    for (const line of lines) {
      let processedLine = line.trim();
      
      // 确保gitGraph声明正确
      if (processedLine.toLowerCase().includes('gitgraph')) {
        result.push('gitGraph:');
        continue;
      }
      
      // 处理中文提交信息
      if (processedLine.includes('commit') && /[\u4e00-\u9fff]/.test(processedLine)) {
        processedLine = processedLine.replace(
          /commit\s+(.+)/,
          (match, message) => {
            const cleanMessage = message.replace(/^["']|["']$/g, '').trim();
            return `commit "${cleanMessage}"`;
          }
        );
      }
      
      if (processedLine) {
        result.push(processedLine);
      }
    }
    
    return result.join('\n');
  }

  // 修复用户旅程图语法
  private static fixJourneySyntax(lines: string[]): string {
    const result: string[] = [];
    
    for (const line of lines) {
      const trimmed = line.trim();
      
      // 处理中文标题和节
      if (trimmed.includes('title') || trimmed.includes('section')) {
        const match = trimmed.match(/(title|section)\s+(.+)/);
        if (match) {
          const [, keyword, text] = match;
          const cleanText = text.replace(/^["']|["']$/g, '');
          if (/[\u4e00-\u9fff]/.test(cleanText)) {
            result.push(`    ${keyword} ${cleanText}`);
            continue;
          }
        }
      }
      
      if (trimmed) {
        result.push(trimmed);
      }
    }
    
    return result.join('\n');
  }

  // 修复甘特图语法
  private static fixGanttSyntax(lines: string[]): string {
    const result: string[] = [];
    
    for (const line of lines) {
      const trimmed = line.trim();
      
      // 处理中文标题和任务
      if (trimmed.includes('title') || trimmed.includes('section')) {
        const match = trimmed.match(/(title|section)\s+(.+)/);
        if (match) {
          const [, keyword, text] = match;
          const cleanText = text.replace(/^["']|["']$/g, '');
          if (/[\u4e00-\u9fff]/.test(cleanText)) {
            result.push(`    ${keyword} ${cleanText}`);
            continue;
          }
        }
      }
      
      if (trimmed) {
        result.push(trimmed);
      }
    }
    
    return result.join('\n');
  }

  // 验证Mermaid语法（简化版）
  static validateMermaidSyntax(code: string): { isValid: boolean; error?: string } {
    try {
      const trimmed = code.trim();
      if (!trimmed) {
        return { isValid: false, error: '代码为空' };
      }
      
      const lines = trimmed.split('\n').filter(line => line.trim());
      if (lines.length === 0) {
        return { isValid: false, error: '没有有效内容' };
      }
      
      // 基本语法检查已经在各个fix方法中完成
      return { isValid: true };
    } catch (error) {
      return { 
        isValid: false, 
        error: error instanceof Error ? error.message : '语法验证失败' 
      };
    }
  }

  // 获取图表类型信息
  static getDiagramTypeInfo(code: string): { type: string | null; needsCodeBlock: boolean; description: string } {
    const type = this.detectDiagramType(code);
    
    if (!type) {
      return { type: null, needsCodeBlock: false, description: '未知图表类型' };
    }
    
    const needsCodeBlock = this.needsCodeBlock(type);
    const descriptions: Record<string, string> = {
      'pie': '饼图',
      'graph': '流程图',
      'flowchart': '流程图',
      'journey': '用户旅程图',
      'gantt': '甘特图',
      'sequenceDiagram': '序列图',
      'xychart-beta': 'XY图表',
      'timeline': '时间线',
      'architecture-beta': '架构图',
      'gitGraph': 'Git图'
    };
    
    return {
      type,
      needsCodeBlock,
      description: descriptions[type] || type
    };
  }
} 