import React, { useState, useEffect } from 'react';
import { Loader2, Info, CheckCircle, XCircle } from 'lucide-react';

interface ModelInfo {
  name: string;
  provider: string;
  description: string;
  max_tokens: number;
  temperature: number;
  cost_per_1k_tokens: number;
  supports_streaming: boolean;
  supports_vision: boolean;
  is_available: boolean;
  is_default: boolean;
}

interface ModelSelectorProps {
  value?: string;
  onChange: (modelName: string | undefined) => void;
  apiServerUrl?: string; // 改为可选，使用相对路径
  disabled?: boolean;
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  value,
  onChange,
  apiServerUrl,
  disabled = false
}) => {
  const [availableModels, setAvailableModels] = useState<Record<string, ModelInfo>>({});
  const [modelsByProvider, setModelsByProvider] = useState<Record<string, ModelInfo[]>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取可用模型列表
  const fetchModels = async () => {
    try {
      setIsLoading(true);
      setError(null);
      // 使用相对路径，让vite代理处理
      const apiUrl = apiServerUrl ? `${apiServerUrl}/api/v1/device-reports/models` : '/api/v1/device-reports/models';
      const response = await fetch(apiUrl);
      if (response.ok) {
        const result = await response.json();
        if (result.status === 'success') {
          setAvailableModels(result.data.all_models);
          setModelsByProvider(result.data.models_by_provider);
          console.log('✅ 获取模型列表成功:', result.data.total_count, '个模型');
          
          // 自动选中默认模型
          const defaultModel = Object.values(result.data.all_models).find((model: any) => model.is_default) as ModelInfo | undefined;
          if (defaultModel && !value) {
            onChange(defaultModel.name);
            console.log('✅ 自动选中默认模型:', defaultModel.name);
          }
        } else {
          setError(result.message || '获取模型列表失败');
        }
      } else {
        setError(`请求失败: ${response.status}`);
      }
    } catch (err) {
      setError(`网络错误: ${err instanceof Error ? err.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchModels();
  }, [apiServerUrl]);

  const handleModelChange = (modelName: string) => {
    onChange(modelName || undefined);
  };

  const getProviderIcon = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'openai':
        return '🤖';
      case 'google':
        return '🔍';
      case 'anthropic':
        return '🧠';
      default:
        return '🤖';
    }
  };

  const getAvailabilityIcon = (isAvailable: boolean) => {
    return isAvailable ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
        🤖 AI模型选择
        {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
        {error && (
          <button
            onClick={fetchModels}
            className="text-xs text-blue-600 hover:text-blue-800 underline"
            title="重新加载"
          >
            重试
          </button>
        )}
      </label>

      <select
        value={value || ''}
        onChange={(e) => handleModelChange(e.target.value)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        disabled={disabled || isLoading}
      >
        <option value="">使用默认模型</option>
        
        {/* 按提供商分组的模型 */}
        {Object.entries(modelsByProvider).map(([provider, models]) => (
          <optgroup key={provider} label={`${getProviderIcon(provider)} ${provider.toUpperCase()} 模型`}>
            {models.map(model => (
              <option key={model.name} value={model.name} disabled={!model.is_available}>
                {model.name} - {model.description}
                {model.is_default && ' (默认)'}
                {!model.is_available && ' (不可用)'}
              </option>
            ))}
          </optgroup>
        ))}
      </select>

      {/* 错误信息 */}
      {error && (
        <div className="text-xs text-red-600 bg-red-50 p-2 rounded border border-red-200">
          <div className="flex items-center gap-1">
            <XCircle className="h-3 w-3" />
            {error}
          </div>
        </div>
      )}

      {/* 选中模型的信息 */}
      {value && availableModels[value] && (
        <div className="text-xs text-gray-600 bg-gray-50 p-3 rounded border">
          <div className="flex items-center gap-2 mb-2">
            <Info className="h-3 w-3" />
            <span className="font-medium">模型信息</span>
            {getAvailabilityIcon(availableModels[value].is_available)}
            {availableModels[value].is_default && (
              <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                默认
              </span>
            )}
          </div>
          <div className="grid grid-cols-2 gap-1">
            <div>提供商: {getProviderIcon(availableModels[value].provider)} {availableModels[value].provider}</div>
            <div>最大Token: {availableModels[value].max_tokens.toLocaleString()}</div>
            <div>成本: ${availableModels[value].cost_per_1k_tokens}/1K tokens</div>
            <div>温度: {availableModels[value].temperature}</div>
          </div>
          {availableModels[value].supports_vision && (
            <div className="mt-1 text-blue-600">✨ 支持图像理解</div>
          )}
        </div>
      )}

      <p className="text-xs text-gray-500">
        选择不同的AI模型来生成报告，不同模型在性能、成本和功能上有所差异
      </p>
    </div>
  );
};

export default ModelSelector; 