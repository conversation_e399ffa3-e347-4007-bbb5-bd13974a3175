import React from 'react';
import { Link, useNavigate, useParams, useLocation } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getDeviceBasicInfo } from '@/api/devices';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface DevicePageHeaderProps {
  showAnalysisLink?: boolean;
  headerActions?: React.ReactNode;
}

const DevicePageHeader: React.FC<DevicePageHeaderProps> = ({ showAnalysisLink = false, headerActions }) => {
  const { enterpriseId, deviceId } = useParams<{ enterpriseId: string; deviceId: string }>();
  const navigate = useNavigate();
  const location = useLocation();

  const { data: basicInfo, isLoading, error } = useQuery({
    queryKey: ['deviceBasicInfo', enterpriseId, deviceId],
    queryFn: () => getDeviceBasicInfo(enterpriseId!, deviceId!),
    enabled: !!enterpriseId && !!deviceId,
  });

  const renderHeaderLinks = () => (
    <div className="flex justify-between items-center mb-2">
      <button
        onClick={() => {
          if (location.key !== 'default') {
            navigate(-1);
          } else {
            navigate('/');
          }
        }}
        className="text-blue-600 hover:text-blue-800 flex items-center text-sm"
      >
        <ChevronLeft size={16} className="mr-1" />
        返回
      </button>
      {showAnalysisLink && (
        <Link 
          to={`/enterprise/${enterpriseId}/device/${deviceId}/analysis`} 
          className="text-blue-600 hover:text-blue-800 flex items-center text-sm"
        >
          数据分析
          <ChevronRight size={16} className="ml-1" />
        </Link>
      )}
    </div>
  );

  if (isLoading) {
    return (
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          {renderHeaderLinks()}
          <div className="h-8 bg-gray-200 rounded w-1/2 animate-pulse mt-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/3 animate-pulse mt-4"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse mt-3"></div>
        </div>
      </header>
    );
  }

  if (error) {
    return (
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          {renderHeaderLinks()}
          <div className="text-red-500 mt-2">加载设备信息失败: {error.message}</div>
        </div>
      </header>
    );
  }

  if (!basicInfo) return null;

  return (
    <header className="bg-white shadow-sm sticky top-0 z-20">
      <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
        {renderHeaderLinks()}
        <div className="flex items-center">
          <h1 className="text-3xl font-bold text-gray-900">{basicInfo.device_name}</h1>
          {basicInfo.current_status && (
            <span className={`ml-4 px-2 py-1 text-xs font-semibold text-green-800 bg-green-100 rounded-full`}>
              {basicInfo.current_status}
            </span>
          )}
          {headerActions && <div className="ml-4">{headerActions}</div>}
        </div>
        <div className="text-sm text-gray-500 mt-2">
          device_id: <span className="font-mono text-gray-700">{basicInfo.device_id}</span>
        </div>
        <div className="mt-4 flex flex-wrap gap-x-6 gap-y-2 text-base text-gray-700">
          <span>企业: <span className="font-medium">{basicInfo.enterprise_name}</span></span>
          <span>集群: <span className="font-medium">{basicInfo.d_base_cate_cluster_name}</span></span>
          <span>产品: <span className="font-medium">{basicInfo.d_base_pcate_name}</span></span>
          <span>型号: <span className="font-medium">{basicInfo.model}</span></span>
        </div>
      </div>
    </header>
  );
};

export default DevicePageHeader; 