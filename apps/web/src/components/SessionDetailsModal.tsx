import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getSessionsByIds, Session, Message } from '@/api/queries';
import { Loader2, X, MessageSquare, Clock, ChevronsUpDown, User, Bot } from 'lucide-react';

const MessageItem: React.FC<{ message: Message }> = ({ message }) => {
  const isUser = message.role === 'user';
  return (
    <div className={`flex items-start gap-4 my-4 ${isUser ? '' : 'flex-row-reverse'}`}>
      <div className={`p-2 rounded-full ${isUser ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'}`}>
        {isUser ? <User size={18} /> : <Bot size={18} />}
      </div>
      <div className={`p-4 rounded-lg max-w-[70%] ${isUser ? 'bg-blue-50' : 'bg-green-50'}`}>
        <p className="text-gray-800">{message.content}</p>
        <div className="text-xs text-gray-400 mt-2 text-right">{new Date(message.message_timestamp).toLocaleString()}</div>
      </div>
    </div>
  );
};

const SessionItem: React.FC<{ session: Session }> = ({ session }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border border-gray-200 rounded-lg mb-4">
      <button
        className="w-full p-4 flex justify-between items-center bg-gray-50 hover:bg-gray-100 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center gap-4">
          <MessageSquare size={16} className="text-gray-600" />
          <span className="font-medium">{session.session_id}</span>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-1 text-sm text-gray-500">
            <Clock size={14} />
            <span>{session.conversation_turns} turns</span>
          </div>
          <ChevronsUpDown size={16} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>
      </button>
      {isOpen && (
        <div className="p-4 border-t border-gray-200">
          {session.messages.map((msg, index) => (
            <MessageItem key={index} message={msg} />
          ))}
        </div>
      )}
    </div>
  );
};

interface SessionDetailsModalProps {
  sessionIds: string[];
  onClose: () => void;
  isOpen: boolean;
}

const SessionDetailsModal: React.FC<SessionDetailsModalProps> = ({ sessionIds, onClose, isOpen }) => {
  const { data: sessions, isLoading, isError } = useQuery({
    queryKey: ['sessions', sessionIds],
    queryFn: () => getSessionsByIds(sessionIds),
    enabled: isOpen && sessionIds.length > 0,
  });

  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    window.addEventListener('keydown', handleEsc);
    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, [onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        <header className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-800">会话详情 ({sessionIds.length})</h2>
          <button onClick={onClose} className="p-2 rounded-full hover:bg-gray-200 transition-colors">
            <X size={24} />
          </button>
        </header>
        <main className="p-6 overflow-y-auto">
          {isLoading && (
            <div className="flex items-center justify-center h-64">
              <Loader2 size={40} className="animate-spin text-blue-500" />
              <p className="ml-4 text-gray-600">正在加载会话数据...</p>
            </div>
          )}
          {isError && (
            <div className="text-center h-64 text-red-500">加载失败，请稍后重试。</div>
          )}
          {!isLoading && !isError && sessions && sessions.length === 0 && (
            <div className="text-center h-64 text-gray-500">未找到任何会话。</div>
          )}
          {!isLoading && !isError && sessions && sessions.length > 0 && (
            <div>
              {sessions.map(session => (
                <SessionItem key={session.session_id} session={session} />
              ))}
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default SessionDetailsModal; 