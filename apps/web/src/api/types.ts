export interface Device {
  id: number
  device_id: string
  name: string
  location: string
  latitude: number
  longitude: number
  status: 'active_ok' | 'usage_warn' | 'usage_crit'
  last_query_time: string
  created_at: string
  updated_at: string
  queries_per_day?: number
}

export interface SessionBehaviorSummary {
  total_sessions: number;
  valid_sessions: number;
  no_response_sessions: number;
  assistant_first_sessions: number;
  multiple_greeting_sessions: number;
  assistant_repeat_sessions: number;
  avg_conversation_turns: number;
  avg_session_duration: number;
  session_ids_total?: string[];
  session_ids_valid?: string[];
  session_ids_no_response?: string[];
}

export interface MessageBehaviorSummary {
  user_messages: number;
  assistant_messages: number;
  avg_user_message_length: number;
  avg_assistant_message_length: number;
  action_trigger_count: number;
  event_trigger_count: number;
  session_ids_action_triggered: string[];
  session_ids_event_triggered: string[];
  session_ids_user: string[];
  session_ids_assistant: string[];
}

export interface ActionBehaviorItem {
  name: string;
  display_name: string;
  count: number;
  session_ids: string[];
}

export interface UserQuestionItem {
  question: string;
  count: number;
  session_ids: string[];
}

export interface DurationDistribution {
  bucket_lt_30s: number;
  bucket_30_60s: number;
  bucket_1_3min: number;
  bucket_3_5min: number;
  bucket_5_10min: number;
  bucket_10_20min: number;
  bucket_gt_20min: number;
  session_ids?: Record<string, string[]>;
}

export interface IntervalDistribution {
  bucket_lt_10s: number;
  bucket_10_20s: number;
  bucket_1_3min: number;
  bucket_3_5min: number;
  bucket_5_10min: number;
  bucket_10_20min: number;
  bucket_gt_20min: number;
  session_ids?: Record<string, string[]>;
}

export interface ActiveHoursSummary {
  avg_consecutive_active_hours: number;
  avg_assistant_first_ratio: number;
  avg_greeting_ratio: number;
  session_ids_active?: string;
}

export interface UserPreferenceItem {
  field_name: string;
  field_value: string;
  count: number;
  session_ids: string[];
}

export interface EventBehaviorSummary {
  total_events: number;
  path_frequency: Record<string, number>;
  target_scenario_frequency: Record<string, number>;
  session_ids?: string[];
}

export interface StatisticsDataItem {
  time_bucket?: string;
  session_behavior_summary: SessionBehaviorSummary;
  message_behavior_summary: MessageBehaviorSummary;
  action_behavior_summary: ActionBehaviorItem[];
  user_question_summary: UserQuestionItem[];
  duration_distribution: DurationDistribution;
  interval_distribution: IntervalDistribution;
  active_hours_summary: ActiveHoursSummary;
  user_preference_summary: UserPreferenceItem[];
  event_behavior_summary: EventBehaviorSummary;
}

export interface HumanMetricsOverallSummary {
  total_records: number;
  unique_devices: number;
}

export interface HumanMetricsRecognitionPerformance {
  recognized_records_count: number;
  unrecognized_records_count: number;
  recognition_success_rate_percent: number;
}

export interface HumanMetricsGenderDistribution {
  male_count: number;
  female_count: number;
  unknown_gender_count: number;
}

export interface HumanMetricsAgeDistribution {
  average_age: number;
  minimum_age: number;
  maximum_age: number;
  age_group_distribution: Record<string, number>;
}

export interface HumanMetricsTopVisitor {
  visitor_name: string;
  visit_count: number;
}

export interface HumanMetricsHourlyRecord {
  hour_of_day: string;
  records_count: number;
}

export interface HumanMetricsDataItem {
  time_bucket: string | null;
  overall_summary: HumanMetricsOverallSummary;
  recognition_performance_metrics: HumanMetricsRecognitionPerformance;
  gender_distribution_metrics: HumanMetricsGenderDistribution;
  age_distribution_metrics: HumanMetricsAgeDistribution;
  recognized_visitors_summary: HumanMetricsTopVisitor[];
  records_by_hour_of_day: HumanMetricsHourlyRecord[];
}

export interface HumanMetricsAPIResponse {
  query_summary: {
    enterprise_id: string;
    device_id?: string;
    start_time: string;
    end_time: string;
    group_by: 'total' | 'day' | 'hour';
  };
  data: HumanMetricsDataItem[];
}

export interface DeviceStats {
  online_count: number
  warn_count: number
  offline_count: number
  today_queries: number
}

export interface DeviceQuery {
  id: number
  device_id: number
  query_time: string
  query_type: string
  query_result: string
  response_time: number
} 