import axios from 'axios';

// 统一的API前缀，将由Vite代理
const API_PREFIX = '/api/v1';

// --- Data Structures for the new Conversation API ---

export interface Message {
  role: 'user' | 'assistant';
  content: string;
  message_timestamp: string;
  images_path: string[] | null;
  audio_path: string | null;
  action_data: Record<string, any> | null;
  event_data: Record<string, any> | null;
}

export interface Session {
  session_id: string;
  client_id: string;
  group_id: string;
  enterprise_id: string;
  device_id: string;
  product_id: number;
  product_model: string;
  user_preferences: Record<string, any>;
  session_start_time: string;
  session_end_time: string;
  conversation_turns: number;
  messages: Message[];
}

export interface ConversationResponse {
  total: number;
  page: number;
  page_size: number;
  valid_query_count?: number;
  items: Session[];
}

/**
 * Fetches conversation sessions for a specific device.
 */
export const getDeviceConversations = async (
  enterpriseId: string,
  deviceId: string,
  page: number = 1,
  pageSize: number = 40,
  startTime: string = '2023-01-01T00:00:00',
  endTime: string = '2025-12-31T23:59:59'
): Promise<ConversationResponse> => {
  const url = `${API_PREFIX}/device-queries/${enterpriseId}/${deviceId}/conversations`;

  try {
    const response = await axios.get<ConversationResponse>(url, {
      params: {
        start_time: startTime,
        end_time: endTime,
        page,
        page_size: pageSize,
      }
    });
    return response.data;
  } catch (error) {
    console.error(`Failed to fetch conversations for device ${deviceId}:`, error);
    // Fallback to mock data on error for development purposes
    console.warn('API call failed. Falling back to mock data.');
    return {
        "total": 0,
        "page": 1,
        "page_size": pageSize,
        "items": []
    };
  }
};

/**
 * Fetches a list of sessions by their IDs.
 */
export const getSessionsByIds = async (session_ids: string[]): Promise<Session[]> => {
  try {
    const response = await axios.post(`${API_PREFIX}/device-queries/sessions-by-ids`, {
      session_ids: session_ids,
    });
    return response.data;
  } catch (error) {
    console.error('Failed to fetch sessions by IDs:', error);
    return [];
  }
}; 