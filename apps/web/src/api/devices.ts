/// <reference types="vite/client" />

import axios from 'axios';

const API_PREFIX = '/api/v1';

export interface PositionInfo {
  device_id: string;
  enterprise_id: string;
  rt_province: string;
  rt_city: string;
  rt_distinct: string;
  rt_street: string;
  lat: string;
  lng: string;
  count_time: string;
  enterprise_name: string;
  device_name: string;
  d_base_agency_id: string;
  d_base_agency_name: string;
  model: string;
  version: string;
}

export interface Operator {
  avatar_url: string;
  email: string;
  en_name: string;
  id: string;
  name: string;
}

export interface Device {
  device_id: string;
  name: string;
  device_type: string;
  version_type: string;
  current_status: string;
  asr_status: string;
  position_info: PositionInfo;
  agentos_status: string;
  environment: string[];
  customer_type: string;
  upgrade_time: number | null;
  rollback_time: number;
  apply_time: number;
  operator1: Operator[];
  operator2: Operator[];
  applicant: Operator;
  created_time: number;
  last_modified_time: number;
  created_by: Operator;
  last_modified_by: Operator;
  version_info?: {
    display_version?: string;
    version_id?: string;
  };
}

export interface DevicesResponse {
  total: number;
  has_more: boolean;
  page_token: string;
  devices: Device[];
}

export const getDevices = async (scope: string = 'cn'): Promise<DevicesResponse> => {
  const response = await axios.get(`${API_PREFIX}/devices/?scope=${scope}`);
  return response.data;
};

export async function getDeviceStats(scope: 'cn' | 'global'): Promise<{
  online_count: number
  offline_count: number
  today_queries: number
}> {
  const response = await axios.get(`${API_PREFIX}/stats?scope=${scope}`)
  return response.data;
}

export interface DeviceDetails {
  device_id: string;
  name: string;
  device_type: string;
  version_type: string;
  current_status: string;
  customer_type: string;
}

export const getDeviceDetails = async (enterpriseId: string, deviceId: string): Promise<DeviceDetails> => {
  console.warn("API for getDeviceDetails doesn't exist. Using mock data.");
  return Promise.resolve({
    device_id: deviceId,
    name: `机器人-${deviceId.slice(-6)}`,
    device_type: "智能对话型",
    version_type: "V3.2.1",
    current_status: "Online",
    customer_type: "外部客户"
  });
};

export interface DeviceBasicInfo {
  enterprise_id: string;
  device_id: string;
  version: string;
  model: string;
  rt_province: string;
  rt_city: string;
  rt_distinct: string;
  rt_street: string;
  lat: string;
  lng: string;
  os_type: string;
  device_name: string;
  corp_class_id: number;
  init_bind_time: string;
  bind_time: string;
  d_base_cumulative_active_days: number;
  enterprise_name: string;
  d_base_agency_id: string;
  d_base_agency_name: string;
  d_base_pcate_name: string;
  d_base_cate_name: string;
  d_base_new_cate_name: string;
  d_base_cate_cluster_name: string;
  is_test: number;
  platform: null | string;
  is_agentOs: number;
  current_status?: string;
}

export const getDeviceBasicInfo = async (enterpriseId: string, deviceId: string): Promise<DeviceBasicInfo> => {
  const response = await axios.get<DeviceBasicInfo>(`${API_PREFIX}/devices/basic_info`, {
    params: {
      enterprise_id: enterpriseId,
      device_id: deviceId,
    },
  });
  return response.data;
} 