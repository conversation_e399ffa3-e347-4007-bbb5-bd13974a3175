/// <reference types="vite/client" />

import axios from 'axios'
import { Device } from './devices'
import { StatisticsDataItem, HumanMetricsAPIResponse } from './types'

const API_PREFIX = '/api/v1';

export interface Stat {
    enterprise_id: string;
    enterprise_name: string;
    device_id: string;
    count_time: string; // e.g. "2025-06-20"
    query_nums: number;
}

export interface StatsResponse {
    success: boolean;
    data: Stat[];
    total: number;
    scope: string;
    device_count: number;
}

export const getStats = async (scope: 'cn' | 'global', devices: Device[]): Promise<StatsResponse> => {
    if (devices.length === 0) {
        return {
            success: true,
            data: [],
            total: 0,
            scope,
            device_count: 0,
        };
    }

    const device_ids = devices.map(d => d.device_id);
    const response = await axios.post(`${API_PREFIX}/stats`, {
        scope,
        device_ids,
    });
    return response.data;
};

export interface StatisticsResponse {
  query_summary: {
    enterprise_id: string;
    device_id: string;
    start_time: string;
    end_time: string;
    total_sessions: number;
    total_messages: number;
  };
  data: StatisticsDataItem[];
}

export const getDeviceStatistics = async (
  enterpriseId: string,
  deviceId: string,
  startTime?: string,
  endTime?: string,
  groupBy: 'total' | 'day' | 'hour' = 'total'
): Promise<StatisticsResponse> => {
  const url = `${API_PREFIX}/stats/summary`;
  
  try {
    const params: Record<string, string> = {
      enterprise_id: enterpriseId,
      device_id: deviceId,
      group_by: groupBy,
      scope: 'cn',
    };
    
    if (startTime) params.start_time = startTime;
    if (endTime) params.end_time = endTime;
    
    const response = await axios.get<StatisticsResponse>(url, { params });
    return response.data;
  } catch (error) {
    console.error(`Failed to fetch statistics for device ${deviceId}:`, error);
    // Mock data fallback
    return {
      query_summary: { enterprise_id: enterpriseId, device_id: deviceId, start_time: startTime || '', end_time: endTime || '', total_sessions: 0, total_messages: 0 },
      data: [],
    };
  }
};

export const getHumanMetricsStats = async (
    enterprise_id: string,
    device_id: string | null,
    start_time: string,
    end_time: string,
    group_by: 'total' | 'day' | 'hour'
): Promise<HumanMetricsAPIResponse> => {
    const params: Record<string, any> = {
        enterprise_id,
        start_time,
        end_time,
        group_by,
    };
    if (device_id) {
        params.device_id = device_id;
    }
    const response = await axios.get<HumanMetricsAPIResponse>(`${API_PREFIX}/stats/human-metrics`, { params });
    return response.data;
}; 