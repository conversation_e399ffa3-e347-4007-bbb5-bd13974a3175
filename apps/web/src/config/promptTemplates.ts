export interface PromptTemplate {
  id: string;
  name: string;
  content: string;
  description: string;
  type: 'single' | 'multiple';
  hidden?: boolean; // 新增字段，用于控制是否在编辑器中显示
}

// 默认prompt模板
export const DEFAULT_PROMPTS: PromptTemplate[] = [
  {
    id: 'single-v1',
    name: '单天报告 V1',
    content: `这是我公司前台的机器人的当日使用数据报告。

【机制】
人靠近机器人时，人员会被动人脸识别（认识、不认识/男、女/年龄）。

【目标】
请你帮我生成一个每日工作报告，目标观看者为老板和运营人员.

我希望需要体现出来
- 基础使用数据、当天数据趋势、action数据统计
- 当日亮点事件和预警事件
- 当日满意服务案例和不满意的案例
- 一些新视角的观察
    - 如：最积极的用户，每日高峰，最早到达公司的员工，最晚离开的员工，大家最感兴趣的话题等等。
- 提出一些经营建议（短一点，讲具体的，比如优化哪些配置等等）


`,
    description: '基础的单天设备使用分析报告模板',
    type: 'single'
  },
  {
    id: 'single-v2',
    name: '单天报告 V2 (推荐)',
    content: `我想生成一个前台机器人的每日使用数据报告。

【机制】
人靠近机器人时，人员会被动人脸识别（认识、不认识/男、女/年龄）。

【目标】
请你帮我生成一个每日工作报告，目标观看者为老板和运营人员.

我希望需要体现出来
- 基础使用数据、数据趋势（和昨日比）
- 亮点事件
- 预警事件
- 满意服务案例
- 不满意的案例
- 一些新视角的观察
    - 如：最积极的用户，每日高峰，最早到达公司的员工，最晚离开的员工，大家最感兴趣的话题等等。
- 提出一些经营建议（短一点，讲具体的，比如优化哪些配置等等）

`,
    description: '增强版的单天设备使用分析报告模板，包含更详细的分析维度',
    type: 'single',
    hidden: true // 在编辑器中隐藏此模板
  },
  {
    id: 'multiple',
    name: '多天报告',
    content: `请基于提供的多天机器人使用数据，生成一份完整的设备使用趋势分析报告。报告应包含以下十个核心部分，请按顺序生成：

## 1. 多天数据概览

【分析内容】
- 分析期间总体使用情况
- 各action功能模块使用频率汇总
- 用户访问时间分布趋势
- 使用量变化趋势（日环比）

【输出要求】
- 突出期间整体表现
- 使用饼图展示功能使用分布
- 简洁明了，适合管理层快速浏览
- 标注数据覆盖天数

## 2. 趋势变化分析

【分析内容】
- 使用量变化趋势（日环比、周环比）
- 用户行为模式演变
- 高峰时段变化规律
- 功能受欢迎度变化趋势
- 异常数据识别和解释

【输出要求】
- 使用折线图展示趋势变化
- 重点关注拐点和异常
- 提供趋势解读和预测
- 标注关键变化节点


## 3. 期间亮点事件汇总

【分析内容】
- 期间最成功的服务案例
- 用户满意度最高的交互
- 机器人表现最出色的场景
- 有趣的用户互动记录
- 值得表扬的服务时刻

【输出要求】
- 按时间顺序排列
- 详细描述事件场景和影响
- 分析成功因素和可复制性

## 4. 期间预警事件分析

【分析内容】
- 服务失败案例汇总
- 用户不满意交互统计
- 系统性能问题记录
- 功能异常情况分析
- 需要人工介入的场景统计

【输出要求】
- 按严重程度和频率分类
- 提供问题根因分析
- 给出系统性改进建议
- 标注紧急程度和影响范围

## 5. 一些新视角的观察

【分析内容】
- 最积极的用户
- 高峰时段变化规律
- 最早到达公司的员工
- 最晚离开公司的员工 
- 大家最感兴趣的话题等等


【多天分析特殊要求】
- 重点关注数据趋势和变化规律
- 识别周期性模式和异常
- 提供基于历史数据的预测
- 分析长期运营效果
- 避免单日数据的简单累加
- 突出多天数据的独特价值
- 使用时间序列图表展示趋势
- 提供深度洞察而非表面统计
`,
    description: '专门针对多天数据分析的报告模板，包含趋势分析和对比',
    type: 'multiple'
  }
];

// 获取默认模板的辅助函数
export const getDefaultPrompt = (mode: 'single' | 'multiple'): string => {
  const defaultTemplate = mode === 'single' 
    ? DEFAULT_PROMPTS.find(t => t.id === 'single-v1') // 单天模式默认使用V1版本
    : DEFAULT_PROMPTS.find(t => t.id === 'multiple');
  
  return defaultTemplate?.content || '';
};

// 根据模式获取可用模板
export const getTemplatesByMode = (mode: 'single' | 'multiple'): PromptTemplate[] => {
  return DEFAULT_PROMPTS.filter(template => template.type === mode && !template.hidden);
};

// 根据ID获取模板
export const getTemplateById = (id: string): PromptTemplate | undefined => {
  return DEFAULT_PROMPTS.find(template => template.id === id);
}; 