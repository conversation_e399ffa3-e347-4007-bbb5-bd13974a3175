import React, { useState, useCallback, useEffect, useMemo } from 'react'
import { Ta<PERSON>, TabsList, TabsTrigger } from './components/ui/tabs'
import { Stats } from './features/stats'
import DeviceList from './features/devices/DeviceList'
import { getDevices, Device } from './api/devices'

interface AppStats {
  enterpriseCount: number;
  deviceCount: number;
}

const tabOptions = [
  { value: 'cn', label: '国内设备' },
  { value: 'global', label: '海外设备' },
]

function App() {
  const [scope, setScope] = useState<'cn' | 'global'>('cn')
  const [devices, setDevices] = useState<Device[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [uiNonce, setUiNonce] = useState(0);

  const handleDeviceSelect = useCallback((device: Device) => {
    setSelectedDevice(device);
  }, []);

  const onSidebarInteraction = useCallback(() => {
    setUiNonce(n => n + 1);
  }, []);

  useEffect(() => {
    const fetchDevices = async () => {
      setLoading(true)
      try {
        const response = await getDevices(scope)
        
        // 更可靠的去重逻辑
        const deviceMap = new Map<string, Device>();
        response.devices.forEach(device => {
          if (!device.device_id) return; // 忽略没有device_id的记录

          const existingDevice = deviceMap.get(device.device_id);
          // 如果设备不存在，或者新设备的更新时间更晚，则更新map
          if (!existingDevice || device.last_modified_time > existingDevice.last_modified_time) {
            deviceMap.set(device.device_id, device);
          }
        });
        const uniqueDevices = Array.from(deviceMap.values());
        
        setDevices(uniqueDevices)
      } catch (error) {
        console.error('Failed to fetch devices:', error)
        setDevices([]) // 清空设备列表以防出错
      } finally {
        setLoading(false)
      }
    }
    fetchDevices()
  }, [scope])

  const stats = useMemo(() => {
    const enterpriseIds = new Set(devices.map((d) => d.position_info?.enterprise_id).filter(Boolean))
    return {
      enterpriseCount: enterpriseIds.size,
      deviceCount: devices.length,
    }
  }, [devices])

  return (
    <div className="h-screen bg-background flex flex-col">
      {/* 顶部标题 */}
      <header className="border-b bg-white flex-shrink-0">
        <div className="w-full mx-auto flex flex-col items-center py-6 px-4 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">CEM 客户体验监控系统【Customer Experience Monitor 】</h1>
          {/* 切换按钮和设备总数 */}
          <div className="flex items-center gap-4 justify-center">
            <button
              className={`px-4 py-2 rounded ${scope === 'cn' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
              onClick={() => setScope('cn')}
            >
              国内设备
            </button>
            <button
              className={`px-4 py-2 rounded ${scope === 'global' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
              /* onClick={() => setScope('global')} */
            >
              海外设备[敬请期待]
            </button>
            <span className="ml-4 font-bold">企业总数: {stats.enterpriseCount}</span>
            <span className="ml-4 font-bold">设备总数: {stats.deviceCount}</span>
          </div>
        </div>
      </header>
      <main className="w-full mx-auto flex flex-col lg:flex-row gap-6 py-8 flex-grow px-4 sm:px-6 lg:px-8 min-h-0">
        {/* 左侧地图区 */}
        <section className="w-full lg:w-3/4 min-w-0 flex flex-col">
          {/* <div className="flex-shrink-0">
            <h2 className="text-2xl font-bold mb-1">{scope === 'cn' ? '国内机器人分布图' : '全球机器人分布图'}</h2>
            <p className="text-muted-foreground mb-4">实时监控{scope === 'cn' ? '国内' : '全球'}机器人设备状态</p>
          </div> */}
          <div className="rounded-lg border bg-white p-2 shadow-sm flex-grow">
            <DeviceList scope={scope} devices={devices} loading={loading} selectedDevice={selectedDevice} uiNonce={uiNonce} />
          </div>
        </section>
        {/* 右侧统计 */}
        <aside className="w-full lg:w-1/4 flex-shrink-0 space-y-4">
          <Stats scope={scope} devices={devices} onDeviceSelect={handleDeviceSelect} onInteraction={onSidebarInteraction} />
        </aside>
      </main>
    </div>
  )
}

export default App 