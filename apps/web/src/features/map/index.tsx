import { useEffect, useRef } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import { useQuery } from '@tanstack/react-query'
import { getDevices } from '../../api/devices'

// 自定义状态图标
const iconMap = {
  active_ok: new L.Icon({
    iconUrl: 'https://cdn.jsdelivr.net/gh/pointhi/leaflet-color-markers@master/img/marker-icon-green.png',
    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41],
  }),
  usage_warn: new L.Icon({
    iconUrl: 'https://cdn.jsdelivr.net/gh/pointhi/leaflet-color-markers@master/img/marker-icon-yellow.png',
    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41],
  }),
  usage_crit: new L.Icon({
    iconUrl: 'https://cdn.jsdelivr.net/gh/pointhi/leaflet-color-markers@master/img/marker-icon-red.png',
    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41],
  }),
}

interface MapProps {
  scope: 'cn' | 'global'
}

export function Map({ scope }: MapProps) {
  const mapRef = useRef<L.Map>(null)
  
  const { data: devices, isLoading } = useQuery({
    queryKey: ['devices', scope],
    queryFn: () => getDevices(scope),
  })

  useEffect(() => {
    if (mapRef.current && devices?.length) {
      const bounds = L.latLngBounds(devices.map(d => [d.latitude, d.longitude]))
      mapRef.current.fitBounds(bounds)
    }
  }, [devices])

  if (isLoading) {
    return <div>地图加载中...</div>
  }

  return (
    <div className="h-[480px] rounded-lg border overflow-hidden">
      <MapContainer
        ref={mapRef}
        center={[35.8617, 104.1954]}
        zoom={4}
        className="h-full w-full"
        style={{ minHeight: 400 }}
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />
        {devices?.map(device => (
          <Marker
            key={device.id}
            position={[device.latitude, device.longitude]}
            icon={iconMap[device.status]}
          >
            <Popup>
              <div className="min-w-[180px]">
                <div className="font-bold text-base mb-1">{device.name}</div>
                <div className="text-xs text-muted-foreground mb-1">{device.location}</div>
                <div className="text-xs mb-1">设备ID: {device.device_id}</div>
                <div className="text-xs">状态: {device.status === 'active_ok' ? '在线' : device.status === 'usage_warn' ? '警告' : '离线'}</div>
                <div className="text-xs">今日查询: {device.queries_per_day ?? 0}</div>
              </div>
            </Popup>
          </Marker>
        ))}
      </MapContainer>
    </div>
  )
} 