import React, { useEffect, useState, useRef, useMemo } from 'react';
import { Amap } from '@amap/amap-react';
import { GoogleMap, LoadScript, Marker as GoogleMarker } from '@react-google-maps/api';
import { Device } from '../../api/devices';
import { getModelInfo } from '../../mapping';
import { getStats } from '../../api/stats';
import { Link, useNavigate } from 'react-router-dom';

interface DeviceListProps {
  scope: 'cn' | 'global';
  devices: Device[];
  loading: boolean;
  selectedDevice: Device | null;
  uiNonce: number;
}

interface PopupPosition {
  left: number;
  top: number;
}

interface AdminClusterInfo {
  name: string;
  count: number;
  lat: number;
  lng: number;
}

interface EnterpriseInfo {
  enterprise_id: string;
  enterprise_name: string;
  device_count: number;
  lat: number;
  lng: number;
  devices: Device[];
}

interface DeviceWithQuery extends Device {
  query_count: number;
  bug_records?: any[];
  version_info?: { display_version?: string };
}

const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k';
  }
  return num.toString();
};

// 设备状态样式配置
const getCurrentStatusInfo = (status: string): { style: string; displayName: string } => {
  if (!status) {
    return { style: 'bg-gray-100 text-gray-800', displayName: '未知' };
  }
  
  const s = status.toLowerCase();
  let style = 'bg-purple-100 text-purple-800'; // default style
  
  if (s.includes('online') || s.includes('running') || s.includes('ok')) {
      style = 'bg-green-100 text-green-800';
  } else if (s.includes('offline') || s.includes('stopped')) {
      style = 'bg-gray-200 text-gray-600';
  } else if (s.includes('error') || s.includes('fail')) {
      style = 'bg-red-100 text-red-800';
  } else if (s.includes('warn')) {
      style = 'bg-yellow-100 text-yellow-800';
  } else if (s.includes('upgrad') || s.includes('updating')) {
      style = 'bg-blue-100 text-blue-800';
  }

  let displayName = status;
  if (status === '已推送并开通菜单') {
      displayName = '已开通菜单';
  } else if (s.startsWith('upgrading to')) {
      displayName = '升级中...';
  }
  
  return { style, displayName };
};

const DeviceList: React.FC<DeviceListProps> = ({ scope, devices, loading, selectedDevice, uiNonce }) => {
  const [mode, setMode] = useState<'enterprise' | 'device'>('enterprise'); // 'enterprise' or 'device'
  const [selectedItem, setSelectedItem] = useState<EnterpriseInfo | DeviceWithQuery | null>(null);
  const [popupPos, setPopupPos] = useState<PopupPosition | null>(null);
  const [mapReady, setMapReady] = useState(false);
  const [zoom, setZoom] = useState(4);
  const [center, setCenter] = useState<[number, number]>([116.397428, 39.90923]);
  const [bounds, setBounds] = useState<any>(null);
  const [isNavigating, setIsNavigating] = useState(false);
  const popupRef = useRef<HTMLDivElement>(null);
  const mapRef = useRef<any>(null);
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const infoWindowRef = useRef<any>(null);
  const markerClustererRef = useRef<any>(null);
  const [showAllBugs, setShowAllBugs] = useState(false);
  const [deviceStats, setDeviceStats] = useState<{ [device_id: string]: number }>({});

  const navigate = useNavigate();

  const handleCloseInfo = () => {
    setSelectedItem(null);
    setPopupPos(null);
  };

  useEffect(() => {
    handleCloseInfo();
    if (selectedDevice && mapRef.current) {
      const { lat, lng } = selectedDevice.position_info;
      if (lat && lng) {
        const position = new (window as any).AMap.LngLat(parseFloat(lng), parseFloat(lat));
        mapRef.current.setZoomAndCenter(14, position, false, 1000);
      }
    }
  }, [selectedDevice]);

  useEffect(() => {
    handleCloseInfo();
  }, [uiNonce]);

  // The devices and loading state are now passed as props.
  // The useEffect for fetching devices has been moved to the parent component (App.tsx).

  // 拉取 stats 数据
  useEffect(() => {
    if (!devices || devices.length === 0) return;
    getStats(scope, devices).then(res => {
      // 按 device_id 聚合（如只统计最近一天/三天，可在这里处理）
      const statsMap: { [device_id: string]: number } = {};
      res.data.forEach(stat => {
        // 这里只统计所有返回的 query_nums 总和（如需限制天数可加判断）
        statsMap[stat.device_id] = (statsMap[stat.device_id] || 0) + stat.query_nums;
      });
      setDeviceStats(statsMap);
    });
  }, [devices, scope]);

  // 创建一个干净的、保证坐标有效的设备列表
  const cleanedDevices = useMemo(() => {
    if (!devices) return [];
    return devices.filter(d => 
      d.position_info &&
      d.position_info.lat &&
      d.position_info.lng &&
      !isNaN(parseFloat(d.position_info.lat)) &&
      !isNaN(parseFloat(d.position_info.lng))
    );
  }, [devices]);

  // 用真实 stats 数据合成 query_count
  const allDevicesWithQuery = useMemo<DeviceWithQuery[]>(() => {
    return cleanedDevices.map(d => ({
      ...d,
      query_count: deviceStats[d.device_id] || 0,
    }));
  }, [cleanedDevices, deviceStats]);

  // 按省份聚合
  const provinceData = useMemo<AdminClusterInfo[]>(() => {
    if (!cleanedDevices || cleanedDevices.length === 0) return [];
    const provinceMap = new Map<string, { totalLat: number; totalLng: number; count: number }>();
    cleanedDevices.forEach(d => {
      const provinceName = d.position_info.rt_province;
      if (!provinceName) return;
      const lat = parseFloat(d.position_info.lat);
      const lng = parseFloat(d.position_info.lng);
      if (!provinceMap.has(provinceName)) {
        provinceMap.set(provinceName, { totalLat: 0, totalLng: 0, count: 0 });
      }
      const group = provinceMap.get(provinceName)!;
      group.totalLat += lat;
      group.totalLng += lng;
      group.count++;
    });
    const result: AdminClusterInfo[] = [];
    provinceMap.forEach((group, name) => {
      result.push({ name, count: group.count, lat: group.totalLat / group.count, lng: group.totalLng / group.count });
    });
    return result;
  }, [cleanedDevices]);

  // 按城市聚合
  const cityData = useMemo<AdminClusterInfo[]>(() => {
    if (!cleanedDevices || cleanedDevices.length === 0) return [];
    const cityMap = new Map<string, { totalLat: number; totalLng: number; count: number }>();
    cleanedDevices.forEach(d => {
      const cityName = d.position_info.rt_city;
      if (!cityName) return;
      const lat = parseFloat(d.position_info.lat);
      const lng = parseFloat(d.position_info.lng);
      if (!cityMap.has(cityName)) {
        cityMap.set(cityName, { totalLat: 0, totalLng: 0, count: 0 });
      }
      const group = cityMap.get(cityName)!;
      group.totalLat += lat;
      group.totalLng += lng;
      group.count++;
    });
    const result: AdminClusterInfo[] = [];
    cityMap.forEach((group, name) => {
      result.push({ name, count: group.count, lat: group.totalLat / group.count, lng: group.totalLng / group.count });
    });
    return result;
  }, [cleanedDevices]);

  // 按省份聚合查询数 (设备视图)
  const provinceQueryData = useMemo<AdminClusterInfo[]>(() => {
    if (!allDevicesWithQuery) return [];
    const provinceMap = new Map<string, { totalLat: number; totalLng: number; deviceCount: number; totalQueries: number }>();
    allDevicesWithQuery.forEach(d => {
      const provinceName = d.position_info.rt_province;
      if (!provinceName) return;
      const lat = parseFloat(d.position_info.lat);
      const lng = parseFloat(d.position_info.lng);
      if (!provinceMap.has(provinceName)) {
        provinceMap.set(provinceName, { totalLat: 0, totalLng: 0, deviceCount: 0, totalQueries: 0 });
      }
      const group = provinceMap.get(provinceName)!;
      group.totalLat += lat;
      group.totalLng += lng;
      group.deviceCount++;
      group.totalQueries += d.query_count;
    });
    return Array.from(provinceMap.entries()).map(([name, group]) => ({
      name,
      count: group.totalQueries,
      lat: group.totalLat / group.deviceCount,
      lng: group.totalLng / group.deviceCount,
    }));
  }, [allDevicesWithQuery]);

  // 按城市聚合查询数 (设备视图)
  const cityQueryData = useMemo<AdminClusterInfo[]>(() => {
    if (!allDevicesWithQuery) return [];
    const cityMap = new Map<string, { totalLat: number; totalLng: number; deviceCount: number; totalQueries: number }>();
    allDevicesWithQuery.forEach(d => {
      const cityName = d.position_info.rt_city;
      if (!cityName) return;
      const lat = parseFloat(d.position_info.lat);
      const lng = parseFloat(d.position_info.lng);
      if (!cityMap.has(cityName)) {
        cityMap.set(cityName, { totalLat: 0, totalLng: 0, deviceCount: 0, totalQueries: 0 });
      }
      const group = cityMap.get(cityName)!;
      group.totalLat += lat;
      group.totalLng += lng;
      group.deviceCount++;
      group.totalQueries += d.query_count;
    });
    return Array.from(cityMap.entries()).map(([name, group]) => ({
      name,
      count: group.totalQueries,
      lat: group.totalLat / group.deviceCount,
      lng: group.totalLng / group.deviceCount,
    }));
  }, [allDevicesWithQuery]);

  // 按企业ID聚合数据 (用于企业详情视图)
  const enterpriseData = useMemo<EnterpriseInfo[]>(() => {
    if (mode !== 'enterprise') return [];
    console.log('Processing raw device list for enterprise view...');

    // 步骤1：基于完整的74台设备，按企业ID进行分组
    const enterpriseMap: { [key: string]: { name: string; devices: Device[] } } = {};
    devices.forEach(d => {
      if (!d.position_info?.enterprise_id) return;
      const id = d.position_info.enterprise_id;
      if (!enterpriseMap[id]) {
        enterpriseMap[id] = { name: d.position_info.enterprise_name || '未知企业', devices: [] };
      }
      enterpriseMap[id].devices.push(d);
    });
    console.log(`Aggregated into ${Object.keys(enterpriseMap).length} total enterprises.`);

    // 步骤2：为每个聚合后的企业寻找一个有效的GPS坐标用于地图展示
    const result = Object.entries(enterpriseMap).map(([id, data]) => {
      // 从该企业的所有设备中，寻找第一个带有有效坐标的设备
      const firstMappableDevice = data.devices.find(d =>
        d.position_info?.lat &&
        d.position_info?.lng &&
        !isNaN(parseFloat(d.position_info.lat)) &&
        !isNaN(parseFloat(d.position_info.lng))
      );

      // 如果企业下的所有设备都没有有效坐标，则该企业无法在地图上显示
      if (!firstMappableDevice) {
        console.warn(`企业 "${data.name}" (${id}) 拥有 ${data.devices.length} 台设备，但没有任何一台有有效坐标，因此无法在地图上显示。`);
        return null;
      }

      const lat = parseFloat(firstMappableDevice.position_info.lat!);
      const lng = parseFloat(firstMappableDevice.position_info.lng!);

      return {
        enterprise_id: id,
        enterprise_name: data.name,
        device_count: data.devices.length, // 这是该企业下完整的设备数量
        lat,
        lng,
        devices: data.devices,
      };
    }).filter((item): item is EnterpriseInfo => item !== null);

    console.log(`创建了 ${result.length} 个可在地图上显示的企业.`, result);
    return result;
  }, [devices, mode]);

  const enterpriseCount = useMemo(() => {
    if (!cleanedDevices) return 0;
    const enterpriseIds = new Set(cleanedDevices.map(d => d.position_info.enterprise_id));
    return enterpriseIds.size;
  }, [cleanedDevices]);

  // 为设备数据添加假的query数
  const deviceData = useMemo<DeviceWithQuery[]>(() => {
    if (mode !== 'device') return [];
    console.log('Processing data for device view...');
    console.log('allDevicesWithQuery:', allDevicesWithQuery);
    // 直接复用已经计算好的带查询数的设备列表
    return allDevicesWithQuery;
  }, [mode, allDevicesWithQuery]);

  // 自动调整弹窗位置，防止溢出
  useEffect(() => {
    if (popupRef.current && popupPos && mapContainerRef.current) {
      const popup = popupRef.current;
      const container = mapContainerRef.current;
      const containerRect = container.getBoundingClientRect();
      const rect = popup.getBoundingClientRect();
      let left = popupPos.left;
      let top = popupPos.top;
      // 右侧溢出
      if (left + rect.width > containerRect.width) {
        left = containerRect.width - rect.width - 8;
      }
      // 底部溢出
      if (top + rect.height > containerRect.height) {
        top = containerRect.height - rect.height - 8;
      }
      // 顶部溢出
      if (top < 8) top = 8;
      // 左侧溢出
      if (left < 8) left = 8;
      if (left !== popupPos.left || top !== popupPos.top) {
        setPopupPos({ left, top });
      }
    }
  }, [popupPos, selectedItem]);

  // 设备信息卡片
  const renderInfoPopup = () => {
    if (!selectedItem || !popupPos) return null;

    const isEnterprise = 'enterprise_id' in selectedItem;

    return (
      <div
        ref={popupRef}
        className="absolute z-10 p-4 bg-white rounded-lg shadow-lg border w-96 text-sm"
        style={{ left: popupPos.left, top: popupPos.top }}
      >
        <button onClick={handleCloseInfo} className="absolute top-2 right-2 text-gray-500 hover:text-gray-800">
          ✕
        </button>

        {isEnterprise ? (() => {
          const enterprise = selectedItem as EnterpriseInfo;
          
          const statusCounts = enterprise.devices.reduce((acc, device) => {
              const status = device.current_status || '未知';
              acc[status] = (acc[status] || 0) + 1;
              return acc;
          }, {} as Record<string, number>);

          const deviceTypeCounts = enterprise.devices.reduce((acc, device) => {
              const type = device.device_type || '未知类型';
              acc[type] = (acc[type] || 0) + 1;
              return acc;
          }, {} as Record<string, number>);

          const creators = [...new Map(enterprise.devices.map(d => [d.created_by.id, d.created_by])).values()];

          return (
            <>
              <h3 className="font-bold text-xl mb-2 text-gray-900">{enterprise.enterprise_name}</h3>
              <div className="text-sm text-gray-600 mb-2">企业ID: <span className="font-mono text-gray-800 text-xs">{enterprise.enterprise_id}</span></div>
              
              <div className="border-t border-b border-gray-200 my-4 py-3 space-y-3">
                  <div className="font-semibold text-gray-800">设备总览 (共 {enterprise.device_count} 台)</div>
                  
                  <div className="flex gap-4">
                    <div>
                        <div className="text-sm font-medium text-gray-700">状态分布:</div>
                        <div className="flex flex-wrap gap-2 mt-1">
                            {Object.entries(statusCounts).map(([status, count]) => {
                                const { style, displayName } = getCurrentStatusInfo(status);
                                return (
                                    <span key={status} className={`text-xs px-2 py-1 rounded-full font-semibold ${style}`}>
                                        {displayName}: {count}
                                    </span>
                                )
                            })}
                        </div>
                    </div>
                    
                    <div>
                        <div className="text-sm font-medium text-gray-700">设备类型:</div>
                         <div className="flex flex-wrap gap-2 mt-1">
                            {Object.entries(deviceTypeCounts).map(([type, count]) => (
                                 <span key={type} className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-800 font-medium">
                                     {getModelInfo(type).name || type}: {count}
                                 </span>
                            ))}
                        </div>
                    </div>
                  </div>
              </div>

              <div className="border-b border-gray-200 mb-4 pb-3">
                <div className="font-semibold text-gray-800 mb-2">设备列表</div>
                <div className="max-h-48 overflow-y-auto pr-2 space-y-2">
                  {(enterprise).devices.map(device => (
                    <div key={device.device_id} className="p-2 rounded-md bg-gray-50 border">
                      <p className="font-semibold text-gray-700 text-sm">{device.position_info.device_name || '未知设备'}</p>
                      <p className="text-xs text-gray-500">{device.device_id}</p>
                      <div className="mt-2 flex gap-2">
                        <button
                          className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600"
                          onClick={() => navigate(`/enterprise/${device.position_info.enterprise_id}/device/${device.device_id}/queries`)}
                        >
                          查询Query
                        </button>
                        <button
                          className="text-xs bg-green-500 text-white px-2 py-1 rounded hover:bg-green-600"
                          onClick={() => navigate(`/enterprise/${device.position_info.enterprise_id}/device/${device.device_id}/analysis`)}
                        >
                          数据分析
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="border-b border-gray-200 mb-4 pb-3">
                  <div className="text-sm font-medium text-gray-700 mb-2">主要创建人:</div>
                  <div className="flex flex-wrap gap-4">
                      {creators.slice(0, 5).map(creator => (
                          <div key={creator.id} className="flex items-center gap-2">
                              <img src={creator.avatar_url} alt={creator.name} className="w-8 h-8 rounded-full object-cover border"/>
                              <div>
                                  <p className="text-sm font-semibold text-gray-800">{creator.name}</p>
                                  <p className="text-xs text-gray-500">{creator.email}</p>
                              </div>
                          </div>
                      ))}
                      {creators.length > 5 && <div className="text-sm text-gray-500 self-center">...等{creators.length}人</div>}
                  </div>
              </div>
            </>
          )
        })() : (
          // Device View
          <>
            <h3 className="font-bold text-xl mb-2 text-gray-900">{(selectedItem as DeviceWithQuery).position_info.device_name}</h3>
            
            {/* 业务信息展示区域 */}
            <div className="bg-gray-50 rounded-lg p-3 mb-3">
              <div className="text-sm font-semibold text-gray-700 mb-2">业务信息</div>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex flex-col">
                  <span className="text-gray-500">企业:</span>
                  <span className="text-gray-800 font-medium">{(selectedItem as DeviceWithQuery).position_info.enterprise_name || '未知企业'}</span>
                  <span className="text-gray-400 text-[10px] truncate">{(selectedItem as DeviceWithQuery).position_info.enterprise_id}</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-gray-500">型号:</span>
                  <span className="text-gray-800 font-medium">{getModelInfo((selectedItem as DeviceWithQuery).position_info.model).name}</span>
                  <span className="text-gray-400 text-[10px] truncate">{(selectedItem as DeviceWithQuery).position_info.model}</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-gray-500">版本:</span>
                  <span className="text-gray-800 font-medium">{(selectedItem as DeviceWithQuery).position_info.version || '未知版本'}</span>
                  <span className="text-gray-400 text-[10px] truncate">{(selectedItem as DeviceWithQuery).version_type}</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-gray-500">环境:</span>
                  <span className="text-gray-800 font-medium">{(selectedItem as DeviceWithQuery).environment?.join(', ') || '未知环境'}</span>
                  <span className="text-gray-400 text-[10px] truncate">{(selectedItem as DeviceWithQuery).customer_type}</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-gray-500">当前状态:</span>
                  <span className="text-gray-800 font-medium">{(selectedItem as DeviceWithQuery).current_status}</span>
                  <span className="text-gray-400 text-[10px] truncate">{(selectedItem as DeviceWithQuery).position_info.device_id}</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-gray-500">位置:</span>
                  <span className="text-gray-800 font-medium">{(selectedItem as DeviceWithQuery).position_info.rt_province} {(selectedItem as DeviceWithQuery).position_info.rt_city} {(selectedItem as DeviceWithQuery).position_info.rt_distinct} </span>
                </div>
              </div>
              <div className="mt-2 pt-2 border-t border-gray-200">
                <div className="flex items-center gap-2">
                  <span className="text-gray-500 text-xs">查询总数:</span>
                  <span className="text-blue-600 text-xs font-semibold bg-blue-50 px-2 py-1 rounded-full">
                    {(selectedItem as DeviceWithQuery).query_count}
                  </span>
                  <button
                    className="ml-2 px-2 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 rounded transition-all border border-blue-200"
                    onClick={() => {
                      const device = selectedItem as DeviceWithQuery;
                      if (device.position_info?.enterprise_id) {
                        window.open(`/enterprise/${device.position_info.enterprise_id}/device/${device.device_id}/queries`);
                      } else {
                        alert('无法确定企业ID，无法查看查询记录。');
                      }
                    }}
                  >
                    查看Query
                  </button>
                  <button
                    className="ml-2 px-2 py-1 text-xs bg-green-100 hover:bg-green-200 text-green-700 rounded transition-all border border-green-200"
                    onClick={() => {
                      const device = selectedItem as DeviceWithQuery;
                      if (device.position_info?.enterprise_id) {
                        window.open(`/enterprise/${device.position_info.enterprise_id}/device/${device.device_id}/analysis`);
                      } else {
                        alert('无法确定企业ID，无法查看数据分析。');
                      }
                    }}
                  >
                    数据分析
                  </button>
                </div>
              </div>
            </div>
            <hr className="my-3 border-gray-200" />
            <div className="flex items-center gap-3 mb-4">
              <img
                src={(selectedItem as DeviceWithQuery).created_by.avatar_url}
                alt={(selectedItem as DeviceWithQuery).created_by.name}
                className="w-12 h-12 rounded-full border border-gray-200 shadow-sm object-cover"
              />
              <div className="flex flex-col">
                <span className="font-semibold text-gray-900">{(selectedItem as DeviceWithQuery).created_by.name}</span>
                <span className="text-xs text-gray-500">{(selectedItem as DeviceWithQuery).created_by.email}</span>
              </div>
            </div>
            {/* Bug 信息展示区块 */}
            {(() => {
              const bugRecords = (selectedItem as DeviceWithQuery).bug_records || [];
              const openBugs = bugRecords.filter((bug: any) => bug.解决进度 !== '已验证并关闭');
              return (
                <div className="mt-4">
                  <div className="font-bold text-sm mb-1">
                    Bug记录 <span className="text-red-500">({openBugs.length}/{bugRecords.length})</span>
                  </div>
                  {bugRecords.length === 0 ? (
                    <div className="text-xs text-gray-400">暂无Bug</div>
                  ) : (
                    <div>
                      {(showAllBugs ? bugRecords : bugRecords.slice(0, 2)).map((bug: any) => (
                        <div key={bug.问题ID} className="mb-2 p-2 bg-gray-50 rounded">
                          <div className="text-xs text-gray-700 font-semibold">{bug.问题类型} | {bug.解决进度}</div>
                          <div className="text-xs text-gray-600 truncate">描述：{bug.功能问题描述?.[0]?.text}</div>
                          <div className="text-xs text-gray-500">处理人：{bug.处理人?.map((p: any)=>p.name).join(',')}</div>
                          <div className="text-xs text-gray-400">提交人：{bug.提交人?.name}</div>
                          <div className="text-xs text-gray-400">创建：{bug.创建时间 ? new Date(bug.创建时间).toLocaleDateString() : ''}</div>
                        </div>
                      ))}
                      {bugRecords.length > 2 && !showAllBugs && (
                        <div className="text-xs text-blue-500 cursor-pointer" onClick={() => setShowAllBugs(true)}>
                          查看更多...
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })()}
          </>
        )}
      </div>
    );
  };

  // 地图渲染
  const handleMarkerClick = (item: EnterpriseInfo | DeviceWithQuery, e: any) => {
    console.log('Marker clicked:', item, e);
    if (!mapRef.current || !mapContainerRef.current) return;
    let left = 0, top = 0;
    if (scope === 'cn') {
      const lng = 'lng' in item ? item.lng : parseFloat(item.position_info.lng);
      const lat = 'lat' in item ? item.lat : parseFloat(item.position_info.lat);
      const pixel = mapRef.current.lngLatToContainer([lng, lat]);
      left = pixel.x;
      top = pixel.y;
    } else {
      // Google Map click handling logic (simplified)
      const lat = 'lat' in item ? item.lat : parseFloat(item.position_info.lat);
      const lng = 'lng' in item ? item.lng : parseFloat(item.position_info.lng);
      // This logic for Google Maps needs to be verified as it's complex
      left = e.domEvent.clientX;
      top = e.domEvent.clientY;
    }
    setSelectedItem(item);
    setPopupPos({ left, top });
  };

  // Effect for AMap clustering
  useEffect(() => {
    if (scope !== 'cn' || !mapRef.current || !mapReady || !bounds) return;

    if (isNavigating) {
      console.log('Skipping render while navigating...');
      return;
    }

    // 先清理旧的图层
    if (markerClustererRef.current) {
      markerClustererRef.current.setMap(null);
      markerClustererRef.current = null;
    }
    // 同时清理地图上所有覆盖物, 以防设备视图的残留
    mapRef.current.clearMap();

    const PROVINCE_ZOOM = 7;
    const CITY_ZOOM = 10;

    if (mode === 'enterprise') {
      if (zoom < PROVINCE_ZOOM) {
        console.log(`Rendering province clusters at zoom ${zoom}`);
        provinceData.forEach(p => {
          const contentDiv = document.createElement('div');
          contentDiv.className = 'bg-blue-500 text-white rounded-full w-12 h-12 flex flex-col items-center justify-center text-xs font-bold shadow-lg cursor-pointer';
          contentDiv.innerHTML = `<div>${p.name.replace('省','').replace('市','')}</div><div>${p.count}</div>`;
          contentDiv.onclick = () => {
            console.log(`Province clicked: ${p.name}`);
            const devicesInProvince = cleanedDevices.filter(d => d.position_info.rt_province === p.name);
            console.log(`Found ${devicesInProvince.length} devices in this province.`);

            if (devicesInProvince.length > 0) {
              setIsNavigating(true);
              const lats = devicesInProvince.map(d => parseFloat(d.position_info.lat));
              const lngs = devicesInProvince.map(d => parseFloat(d.position_info.lng));
              console.log('Device latitudes for this province:', lats);
              console.log('Device longitudes for this province:', lngs);
              
              const minLng = Math.min(...lngs);
              const minLat = Math.min(...lats);
              const maxLng = Math.max(...lngs);
              const maxLat = Math.max(...lats);

              if (minLng === maxLng && minLat === maxLat) {
                console.log('All devices in province are at the same location. Using setZoomAndCenter.');
                mapRef.current.setZoomAndCenter(CITY_ZOOM + 1, [minLng, minLat]);
              } else {
                console.log('Setting bounds to fit all devices in province.');
                const southWest = new window.AMap.LngLat(minLng, minLat);
                const northEast = new window.AMap.LngLat(maxLng, maxLat);
                const bounds = new window.AMap.Bounds(southWest, northEast);
                mapRef.current.setBounds(bounds, false, [80, 80, 80, 80]);
              }
            }
          };
          const marker = new window.AMap.Marker({
            position: [p.lng, p.lat],
            content: contentDiv,
            offset: new window.AMap.Pixel(-24, -24),
          });
          mapRef.current.add(marker);
        });
        return;
      }

      if (zoom < CITY_ZOOM) {
        console.log(`Rendering city clusters at zoom ${zoom}`);
        const visibleCities = cityData.filter(c => {
          if (!bounds) return false;
          const point = new window.AMap.LngLat(c.lng, c.lat);
          return bounds.contains(point);
        });
        console.log(`Found ${cityData.length} total cities, ${visibleCities.length} are visible.`);
        visibleCities.forEach(c => {
          const contentDiv = document.createElement('div');
          contentDiv.className = 'bg-sky-500 text-white rounded-full w-12 h-12 flex flex-col items-center justify-center text-xs font-bold shadow-lg cursor-pointer';
          contentDiv.innerHTML = `<div>${c.name.replace('市','')}</div><div>${c.count}</div>`;
          contentDiv.onclick = () => {
            console.log(`City clicked: ${c.name}`);
            const devicesInCity = cleanedDevices.filter(d => d.position_info.rt_city === c.name);
            console.log(`Found ${devicesInCity.length} devices in this city.`);

            if (devicesInCity.length > 0) {
              setIsNavigating(true);
              const lats = devicesInCity.map(d => parseFloat(d.position_info.lat));
              const lngs = devicesInCity.map(d => parseFloat(d.position_info.lng));
              console.log('Device latitudes for this city:', lats);
              console.log('Device longitudes for this city:', lngs);

              const minLng = Math.min(...lngs);
              const minLat = Math.min(...lats);
              const maxLng = Math.max(...lngs);
              const maxLat = Math.max(...lats);
              
              if (minLng === maxLng && minLat === maxLat) {
                console.log('All devices in city are at the same location. Using setZoomAndCenter.');
                mapRef.current.setZoomAndCenter(CITY_ZOOM + 4, [minLng, minLat]);
              } else {
                console.log('Setting bounds to fit all devices in city.');
                const southWest = new window.AMap.LngLat(minLng, minLat);
                const northEast = new window.AMap.LngLat(maxLng, maxLat);
                const bounds = new window.AMap.Bounds(southWest, northEast);
                mapRef.current.setBounds(bounds, false, [80, 80, 80, 80]);
              }
            }
          };
          const marker = new window.AMap.Marker({
            position: [c.lng, c.lat],
            content: contentDiv,
            offset: new window.AMap.Pixel(-24, -24),
          });
          mapRef.current.add(marker);
        });
        return;
      }
      
      console.log(`Rendering enterprise clusters at zoom ${zoom}`);
      const visibleEnterprises = enterpriseData.filter(e => {
        if (!bounds) return false;
        const point = new window.AMap.LngLat(e.lng, e.lat);
        return bounds.contains(point);
      });
      console.log(`Found ${enterpriseData.length} total enterprises, ${visibleEnterprises.length} are visible.`);

      if (visibleEnterprises.length === 0) {
        console.log('No enterprises to render in current view.');
        return;
      }

      // 1. 准备经纬度数据
      const points = visibleEnterprises.map(enterprise => ({
        lnglat: [enterprise.lng, enterprise.lat],
        extData: enterprise, // 把业务数据存起来
      }));

      // 2. 正确创建聚合器
      markerClustererRef.current = new (window as any).AMap.MarkerCluster(
        mapRef.current, 
        points, 
        {
          gridSize: 80,
          minClusterSize: 2,
          // 完全自定义聚合点样式
          renderClusterMarker: (context: any) => {
            const count = context.count;
            const div = document.createElement('div');
            div.className = 'flex items-center justify-center w-12 h-12 bg-blue-500 text-white font-bold rounded-full shadow-lg border-2 border-white';
            div.innerHTML = count;
            
            const clusterPoints = context.clusterData;
            
            div.onclick = () => {
              console.log('Cluster clicked, data:', clusterPoints.map((p: any) => p.extData));
              // 点击聚合点, 放大至包含所有内部元素的范围
              if (clusterPoints && clusterPoints.length > 0) {
                setIsNavigating(true);
                const firstLng = clusterPoints[0].lnglat[0];
                const firstLat = clusterPoints[0].lnglat[1];
                const allSame = clusterPoints.every((p: any) => p.lnglat[0] === firstLng && p.lnglat[1] === firstLat);

                if (allSame) {
                  console.log('All points in cluster are at the same location. Using setZoomAndCenter.');
                  mapRef.current.setZoomAndCenter(mapRef.current.getZoom() + 2, [firstLng, firstLat]);
                } else {
                  console.log('Points in cluster are at different locations. Using setFitView.');
                  const lngLats = clusterPoints.map((p: any) => new window.AMap.LngLat(p.lnglat[0], p.lnglat[1]));
                  mapRef.current.setFitView(lngLats, false, [80, 80, 80, 80]);
                }
              }
            };
            context.marker.setContent(div);
          },
          // 自定义非聚合点样式
          renderMarker: (context: any) => {
            const enterprise = context.data[0].extData;
            
            // 直接设置内容和样式
            context.marker.setContent(
              `<div class="bg-white px-2 py-1 rounded-md shadow-md text-sm whitespace-nowrap">${enterprise.enterprise_name}</div>`
            );
            // 设置正确的偏移, 使标签在锚点上方居中
            context.marker.setOffset(new window.AMap.Pixel(-50, -30));

            // 清理旧监听器并添加新监听器
            context.marker.off('click');
            context.marker.on('click', (e: any) => {
              if (e.domEvent) e.domEvent.stopPropagation();
              handleMarkerClick(enterprise, e);
            });
          },
        }
      );

    } else { // mode === 'device'
      // 设备视图下使用高德地图MarkerClusterer进行真实地理聚合
      const points = deviceData.map(device => ({
        lnglat: [parseFloat(device.position_info.lng), parseFloat(device.position_info.lat)],
        extData: device,
      }));
      if (points.length === 0) return;
      markerClustererRef.current = new (window as any).AMap.MarkerCluster(
        mapRef.current,
        points,
        {
          gridSize: 80,
          minClusterSize: 2,
          renderClusterMarker: (context: any) => {
            const count = context.count;
            const div = document.createElement('div');
            div.className = 'flex flex-col items-center justify-center w-14 h-14 bg-green-500 text-white font-bold rounded-full shadow-lg border-2 border-white';
            div.innerHTML = `<div class=\"text-2xl\"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bot w-8 h-8 text-blue-500 flex-shrink-0" aria-hidden="true"><path d="M12 8V4H8"></path><rect width="16" height="12" x="4" y="8" rx="2"></rect><path d="M2 14h2"></path><path d="M20 14h2"></path><path d="M15 13v2"></path><path d="M9 13v2"></path></svg></div><div class=\"text-lg\">${count}</div><div class=\"text-[10px] -mt-1 opacity-80\">台</div>`;
            div.onclick = () => {
              const clusterPoints = context.clusterData;
              if (clusterPoints && clusterPoints.length > 0) {
                setIsNavigating(true);
                const firstLng = clusterPoints[0].lnglat[0];
                const firstLat = clusterPoints[0].lnglat[1];
                const allSame = clusterPoints.every((p: any) => p.lnglat[0] === firstLng && p.lnglat[1] === firstLat);
                if (allSame) {
                  mapRef.current.setZoomAndCenter(mapRef.current.getZoom() + 2, [firstLng, firstLat]);
                } else {
                  const lngLats = clusterPoints.map((p: any) => new window.AMap.LngLat(p.lnglat[0], p.lnglat[1]));
                  mapRef.current.setFitView(lngLats, false, [80, 80, 80, 80]);
                }
              }
            };
            context.marker.setContent(div);
          },
          renderMarker: (context: any) => {
            const device = context.data[0].extData;
            context.marker.setContent(
              `<div class=\"bg-white px-2 py-1 rounded-md shadow-md text-sm whitespace-nowrap\">${device.name}</div>`
            );
            context.marker.setOffset(new window.AMap.Pixel(-50, -30));
            context.marker.off('click');
            context.marker.on('click', (e: any) => {
              if (e.domEvent) e.domEvent.stopPropagation();
              handleMarkerClick(device, e);
            });
          },
        }
      );
      return;
    }
  }, [mode, enterpriseData, deviceData, scope, mapReady, zoom, provinceData, cityData, cleanedDevices, bounds, isNavigating]);

  const renderMap = () => {
    if (loading) {
      return <div className="flex items-center justify-center h-full">加载中...</div>;
    }

    if (scope === 'cn') {
      return (
        <div ref={mapContainerRef} className="w-full h-full relative">
          <Amap
            zoom={zoom}
            center={center}
            onComplete={(map) => {
              console.log('AMap component complete');
              mapRef.current = map;

              const updateViewState = () => {
                if (!mapRef.current) return;
                console.log('Updating map view state (zoom/bounds/center)');
                const newZoom = mapRef.current.getZoom();
                const newBounds = mapRef.current.getBounds();
                const newCenter = mapRef.current.getCenter();
                setZoom(newZoom);
                setBounds(newBounds);
                setCenter([newCenter.getLng(), newCenter.getLat()]);
                setIsNavigating(false);
              };

              map.on('zoomend', updateViewState);
              map.on('moveend', updateViewState);

              setMapReady(true);
              updateViewState(); // Initial set
            }}
            onClick={handleCloseInfo}
          >
            {/* Markers are now added programmatically */}
          </Amap>
          {renderInfoPopup()}
        </div>
      );
    } else {
      // Google Map implementation needs similar refactoring for clustering
      return (
        <div ref={mapContainerRef} className="w-full h-full relative">
          <LoadScript googleMapsApiKey="AIzaSyDqKlhVdahKnT4YCyUV-OPG6ESd9IYLzZ8">
            <GoogleMap
              mapContainerStyle={{ width: '100%', height: '500px' }}
              center={{ lat: 0, lng: 0 }}
              zoom={2}
              onLoad={map => { mapRef.current = map; }}
              onClick={handleCloseInfo}
            >
              {deviceData.map((device) => (
                <GoogleMarker
                  key={device.device_id}
                  position={{
                    lat: parseFloat(device.position_info.lat),
                    lng: parseFloat(device.position_info.lng)
                  }}
                  title={device.name}
                  label={device.name}
                  onClick={(e) => handleMarkerClick(device, e)}
                />
              ))}
            </GoogleMap>
          </LoadScript>
          {renderInfoPopup()}
        </div>
      );
    }
  };

  if (loading) {
    return <div className="flex items-center justify-center h-full">加载中...</div>;
  }

  return (
    <div className="relative h-full">
      {/* 模式切换按钮 */}
      <div className="absolute top-4 left-4 z-10 bg-white shadow-lg rounded-lg p-1 flex gap-1">
        <button
          className={`px-3 py-1 text-sm font-semibold rounded-md transition-colors ${mode === 'enterprise' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'}`}
          onClick={() => setMode('enterprise')}
        >
          企业视图
        </button>
        <button
          className={`px-3 py-1 text-sm font-semibold rounded-md transition-colors ${mode === 'device' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'}`}
          onClick={() => setMode('device')}
        >
          设备视图
        </button>
      </div>

      {/* 地图区域 */}
      {renderMap()}
    </div>
  );
};

export default DeviceList; 