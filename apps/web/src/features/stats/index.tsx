import { useQuery } from '@tanstack/react-query'
import { getStats, Stat } from '../../api/stats'
import { Device } from '../../api/devices'
import { CheckCircle2, AlertTriangle, XCircle, TrendingUp, TrendingDown, MapPin, BarChart2, ChevronDown, ChevronUp } from 'lucide-react'
import { useMemo, useState } from 'react'
import { subDays, format, parseISO, differenceInCalendarDays } from 'date-fns';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { cn } from '../../lib/utils';
import { KEY_FOCUSED_DEVICE_IDS } from '../../config';
import { useNavigate } from 'react-router-dom';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, ChartTooltip, Legend, ChartDataLabels);

interface StatsProps {
  scope: 'cn' | 'global'
  devices: Device[]
  onDeviceSelect: (device: Device) => void;
  onInteraction: () => void;
}

// --- Helper Functions ---
const today = new Date();
const yesterday = subDays(today, 1);
const dayBeforeYesterday = subDays(today, 2);

const formatDate = (date: Date): string => format(date, 'yyyy-MM-dd');

const YESTERDAY_STR = formatDate(yesterday);
const DAY_BEFORE_YESTERDAY_STR = formatDate(dayBeforeYesterday);

enum DeviceStatus {
  Online = 'Online',
  Warning = 'Warning',
  Offline = 'Offline',
}

interface ProcessedDevice extends Device {
  status: DeviceStatus;
  lastQueryCount: number;
  lastQueryDate: string | null;
  location: string;
  version: string;
  recentQueries: { time: string, queries: number }[];
}

// 客户类型样式配置
const getCustomerTypeInfo = (customerType: string): { style: string; displayName: string } => {
  switch (customerType) {
    case '内部客户':
    case 'INTERNAL':
      return { style: 'bg-blue-100 text-blue-800', displayName: '内部' };
    case '外部客户':
    case 'EXTERNAL':
      return { style: 'bg-green-100 text-green-800', displayName: '外部' };
    case '合作伙伴':
    case 'PARTNER':
      return { style: 'bg-purple-100 text-purple-800', displayName: '合作' };
    case '试用客户':
    case 'TRIAL':
      return { style: 'bg-orange-100 text-orange-800', displayName: '试用' };
    default:
      return { style: 'bg-gray-100 text-gray-800', displayName: '其他' };
  }
};

// 设备状态样式配置
const getCurrentStatusInfo = (status: string): { style: string; displayName: string } => {
  if (!status) {
    return { style: 'bg-gray-100 text-gray-800', displayName: '未知' };
  }
  
  const s = status.toLowerCase();
  let style = 'bg-purple-100 text-purple-800'; // default style
  
  if (s.includes('online') || s.includes('running') || s.includes('ok')) {
      style = 'bg-green-100 text-green-800';
  } else if (s.includes('offline') || s.includes('stopped')) {
      style = 'bg-gray-200 text-gray-600';
  } else if (s.includes('error') || s.includes('fail')) {
      style = 'bg-red-100 text-red-800';
  } else if (s.includes('warn')) {
      style = 'bg-yellow-100 text-yellow-800';
  } else if (s.includes('upgrad') || s.includes('updating')) {
      style = 'bg-blue-100 text-blue-800';
  }

  let displayName = status;
  if (status === '已推送并开通菜单') {
      displayName = '已开通菜单';
  } else if (s.startsWith('upgrading to')) {
      displayName = '升级中...';
  }
  
  return { style, displayName };
};

// --- Main Component ---
export function Stats({ scope, devices, onDeviceSelect, onInteraction }: StatsProps) {
  const [view, setView] = useState<'enterprise' | 'device'>('device');
  const [filterStatus, setFilterStatus] = useState<DeviceStatus | 'All'>('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [customerTypeFilter, setCustomerTypeFilter] = useState('All');
  const [sortKey, setSortKey] = useState<'query' | 'name' | 'status'>('query');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showQueryModal, setShowQueryModal] = useState(false);
  const [isFocusListCollapsed, setIsFocusListCollapsed] = useState(false);

  const navigate = useNavigate();

  const { data: statsData, isLoading: isLoadingStats } = useQuery({
    queryKey: ['stats', scope, devices.map(d => d.device_id)],
    queryFn: () => getStats(scope, devices),
    enabled: devices.length > 0, // Only run query if there are devices
  });

  const processedDevices = useMemo((): ProcessedDevice[] => {
    if (!statsData || !statsData.data) return [];

    const statsByDevice = new Map<string, Stat[]>();
    statsData.data.forEach(stat => {
      if (!statsByDevice.has(stat.device_id)) {
        statsByDevice.set(stat.device_id, []);
      }
      statsByDevice.get(stat.device_id)!.push(stat);
    });

    return devices.map(d => {
      const deviceStats = statsByDevice.get(d.device_id) || [];
      deviceStats.sort((a, b) => b.count_time.localeCompare(a.count_time)); // Sort by date descending

      const deviceDailyQueries = deviceStats.map(stat => ({
        time: stat.count_time,
        queries: stat.query_nums,
      }));

      let status = DeviceStatus.Offline;
      const lastQuery = deviceDailyQueries[0];
      const lastQueryDate = lastQuery ? lastQuery.time : null;
      const lastQueryCount = lastQuery ? lastQuery.queries : 0;

      if (lastQueryDate) {
        const lastUsageDaysAgo = differenceInCalendarDays(today, parseISO(lastQueryDate));
        if (lastUsageDaysAgo <= 1) { // Online if active today or yesterday
            status = DeviceStatus.Online;
        } else if (lastUsageDaysAgo <= 3) { // Warning if active 2-3 days ago
            status = DeviceStatus.Warning;
        }
      }

      return {
        ...d,
        status,
        lastQueryCount,
        lastQueryDate,
        location: `${d.position_info?.rt_city || ''} ${d.position_info?.rt_distinct || ''}`.trim(),
        version: d.version_info?.display_version || 'N/A',
        customerType: d.customer_type || 'UNKNOWN',
        name: `${d.position_info?.enterprise_name || '未知企业'}:${d.position_info?.device_name || '未知设备'}`,
        recentQueries: deviceDailyQueries.slice(1, 4),
      };
    });
  }, [devices, statsData]);

  const focusDevices = useMemo(() => {
    return processedDevices.filter(d => KEY_FOCUSED_DEVICE_IDS.includes(d.device_id));
  }, [processedDevices]);

  const overviewStats = useMemo(() => {
    const online_count = processedDevices.filter(d => d.status === DeviceStatus.Online).length;
    const warn_count = processedDevices.filter(d => d.status === DeviceStatus.Warning).length;
    const offline_count = processedDevices.filter(d => d.status === DeviceStatus.Offline).length;

    const yesterdayQueries = statsData?.data
      .filter(s => s.count_time === YESTERDAY_STR)
      .reduce((sum, s) => sum + s.query_nums, 0) ?? 0;
      
    const dayBeforeYesterdayQueries = statsData?.data
      .filter(s => s.count_time === DAY_BEFORE_YESTERDAY_STR)
      .reduce((sum, s) => sum + s.query_nums, 0) ?? 0;
      
    const trend = yesterdayQueries - dayBeforeYesterdayQueries;

    return { online_count, warn_count, offline_count, yesterdayQueries, trend };
  }, [processedDevices, statsData]);

  const displayedDevices = useMemo(() => {
    let devicesToSort = [...processedDevices];

    if (filterStatus !== 'All') {
      devicesToSort = devicesToSort.filter(device => device.status === filterStatus);
    }

    devicesToSort.sort((a, b) => {
      if (filterStatus === 'All') {
        const statusOrder = {
          [DeviceStatus.Online]: 1,
          [DeviceStatus.Warning]: 2,
          [DeviceStatus.Offline]: 3,
        };
        if (a.status !== b.status) {
          return statusOrder[a.status] - statusOrder[b.status];
        }
      }
      
      if (a.status === DeviceStatus.Online) {
        return b.lastQueryCount - a.lastQueryCount;
      }

      if (a.status === DeviceStatus.Warning || a.status === DeviceStatus.Offline) {
        if (!a.lastQueryDate) return 1;
        if (!b.lastQueryDate) return -1;
        return b.lastQueryDate.localeCompare(a.lastQueryDate);
      }
      
      return 0;
    });

    return devicesToSort;
  }, [processedDevices, filterStatus]);

  const enterpriseData = useMemo(() => {
    if (view !== 'enterprise') return [];

    const enterpriseMap = new Map<string, { name: string; devices: ProcessedDevice[]; online: number; warning: number; offline: number; }>();
    processedDevices.forEach(device => {
      const id = device.position_info?.enterprise_id || 'unknown';
      const name = device.position_info?.enterprise_name || '未知企业';
      if (!enterpriseMap.has(id)) {
        enterpriseMap.set(id, { name, devices: [], online: 0, warning: 0, offline: 0 });
      }
      const enterprise = enterpriseMap.get(id)!;
      enterprise.devices.push(device);
      if (device.status === DeviceStatus.Online) enterprise.online++;
      else if (device.status === DeviceStatus.Warning) enterprise.warning++;
      else enterprise.offline++;
    });

    let enterpriseList = Array.from(enterpriseMap.values()).map(e => {
        const totalQueries = e.devices.reduce((sum, d) => sum + d.lastQueryCount, 0);
        
        const locations = e.devices.map(d => d.position_info?.rt_city).filter(Boolean);
        const primaryLocation = locations.length > 0 
            ? Object.entries(locations.reduce((acc, loc) => { acc[loc] = (acc[loc] || 0) + 1; return acc; }, {} as Record<string, number>)).sort((a, b) => b[1] - a[1])[0][0]
            : '未知位置';

        const customerType = e.devices.length > 0 ? e.devices[0].customer_type : '未知';

        return { ...e, totalQueries, primaryLocation, customerType };
    });

    if (filterStatus !== 'All') {
        enterpriseList = enterpriseList.filter(e => {
            if (filterStatus === DeviceStatus.Online) return e.online > 0;
            if (filterStatus === DeviceStatus.Warning) return e.warning > 0;
            if (filterStatus === DeviceStatus.Offline) return e.offline > 0;
            return false;
        })
    }
    
    // Sort enterprises: online > warning > offline > totalQueries
    enterpriseList.sort((a, b) => {
        if (a.online !== b.online) return b.online - a.online;
        if (a.warning !== b.warning) return b.warning - a.warning;
        if (a.offline !== b.offline) return b.offline - a.offline;
        if (a.totalQueries !== b.totalQueries) return b.totalQueries - a.totalQueries;
        return a.name.localeCompare(b.name);
    });

    return enterpriseList;
  }, [processedDevices, filterStatus, view]);

  // 计算最近半个月的查询数据（从昨天起往前推14天）
  const last15Days = useMemo(() => {
    const days = [];
    for (let i = 14; i >= 0; i--) {
      const d = subDays(yesterday, i);
      days.push(formatDate(d));
    }
    return days;
  }, []);
  const last15DaysData = useMemo(() => {
    if (!statsData?.data) return [];
    return last15Days.map(day => {
      const sum = statsData.data.filter(s => s.count_time === day).reduce((acc, s) => acc + s.query_nums, 0);
      return { day, sum };
    });
  }, [statsData, last15Days]);

  // 统计多条线数据
  const last15DaysMultiData = useMemo(() => {
    if (!statsData?.data) return [];
    return last15Days.map(day => {
      const dayStats = statsData.data.filter(s => s.count_time === day);
      const querySum = dayStats.reduce((acc, s) => acc + s.query_nums, 0);
      const activeDeviceSet = new Set(dayStats.map(s => s.device_id));
      const activeEnterpriseSet = new Set(dayStats.map(s => s.enterprise_id));
      const maxDeviceQuery = dayStats.length > 0 ? Math.max(...dayStats.map(s => s.query_nums)) : 0;
      return {
        day,
        querySum,
        activeDeviceCount: activeDeviceSet.size,
        activeEnterpriseCount: activeEnterpriseSet.size,
        maxDeviceQuery,
      };
    });
  }, [statsData, last15Days]);

  // 搜索、筛选、排序后的设备/企业
  const filteredDevices = useMemo(() => {
    let list = [...displayedDevices];
    if (searchTerm.trim()) {
      const term = searchTerm.trim().toLowerCase();
      list = list.filter(d =>
        (d.position_info?.device_name?.toLowerCase().includes(term) ||
         d.device_id?.toLowerCase().includes(term) ||
         d.position_info?.enterprise_name?.toLowerCase().includes(term))
      );
    }
    if (customerTypeFilter !== 'All') {
      list = list.filter(d => d.customer_type === customerTypeFilter);
    }
    // 排序
    list.sort((a, b) => {
      if (sortKey === 'query') {
        return sortOrder === 'desc' ? b.lastQueryCount - a.lastQueryCount : a.lastQueryCount - b.lastQueryCount;
      } else if (sortKey === 'name') {
        const nameA = a.position_info?.device_name || '';
        const nameB = b.position_info?.device_name || '';
        return sortOrder === 'desc'
          ? nameB.localeCompare(nameA)
          : nameA.localeCompare(nameB);
      } else if (sortKey === 'status') {
        const statusA = a.status || '';
        const statusB = b.status || '';
        return sortOrder === 'desc'
          ? statusB.localeCompare(statusA)
          : statusA.localeCompare(statusB);
      }
      return 0;
    });
    return list;
  }, [displayedDevices, searchTerm, customerTypeFilter, sortKey, sortOrder]);

  const filteredEnterprises = useMemo(() => {
    let list = [...enterpriseData];
    if (searchTerm.trim()) {
      const term = searchTerm.trim().toLowerCase();
      list = list.filter(e =>
        (e.name || '').toLowerCase().includes(term)
      );
    }
    if (customerTypeFilter !== 'All') {
      list = list.filter(e => e.customerType === customerTypeFilter);
    }
    // 排序
    list.sort((a, b) => {
      if (sortKey === 'query') {
        return sortOrder === 'desc' ? b.totalQueries - a.totalQueries : a.totalQueries - b.totalQueries;
      } else if (sortKey === 'name') {
        const nameA = a.name || '';
        const nameB = b.name || '';
        return sortOrder === 'desc'
          ? nameB.localeCompare(nameA)
          : nameA.localeCompare(nameB);
      } else if (sortKey === 'status') {
        return sortOrder === 'desc'
          ? b.online - a.online
          : a.online - b.online;
      }
      return 0;
    });
    return list;
  }, [enterpriseData, searchTerm, customerTypeFilter, sortKey, sortOrder]);

  if (isLoadingStats && devices.length > 0) {
    return (
      <div className="space-y-4">
        <p>正在加载设备统计...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      {/* 重点关注设备 */}
      <div className="rounded-lg border bg-yellow-50 border-yellow-200 p-4 shadow-sm">
        <div className="flex justify-between items-center mb-3 cursor-pointer" onClick={() => setIsFocusListCollapsed(!isFocusListCollapsed)}>
          <div className="flex items-center gap-2">
            <AlertTriangle className="text-yellow-600 w-5 h-5" />
            <h2 className="text-xl font-bold text-yellow-800">重点关注设备</h2>
          </div>
          {isFocusListCollapsed ? <ChevronDown className="text-yellow-600" /> : <ChevronUp className="text-yellow-600" />}
        </div>
        {!isFocusListCollapsed && (
          <div className="space-y-3">
            {focusDevices.length > 0 ? (
              focusDevices.map(device => (
                <div
                  key={device.device_id}
                  className="bg-white/60 p-3 rounded-lg cursor-pointer hover:bg-white/90 transition-all"
                  onClick={() => onDeviceSelect(device)}
                >
                  <div className="flex justify-between items-center">
                    <div className="flex-1 min-w-0">
                      <p className="font-bold text-gray-800 leading-tight truncate" title={device.name}>{device.name}</p>
                      <p className="text-xs text-gray-500 mt-1 truncate">{device.location}</p>
                    </div>

                    <div className="flex items-center gap-3 ml-4 flex-shrink-0">
                      <div className="text-right">
                        <div className="flex items-center gap-1 justify-end">
                          <BarChart2 className="w-3.5 h-3.5 text-gray-400" />
                          <p className="font-bold text-base text-gray-800">{device.lastQueryCount}</p>
                        </div>
                        <p className="text-xs text-gray-500">{device.lastQueryDate}</p>
                      </div>

                      <div className="flex gap-2">
                        <button
                          className="text-xs bg-blue-100 text-blue-800 font-semibold px-2 py-1 rounded hover:bg-blue-200 transition-colors"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(`/enterprise/${device.position_info?.enterprise_id}/device/${device.device_id}/queries`);
                          }}
                        >
                          Query
                        </button>
                        <button
                          className="text-xs bg-green-100 text-green-800 font-semibold px-2 py-1 rounded hover:bg-green-200 transition-colors"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(`/enterprise/${device.position_info?.enterprise_id}/device/${device.device_id}/analysis`);
                          }}
                        >
                          分析
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-sm text-gray-500 text-center py-4">
                配置的设备未在当前设备列表中找到。
              </div>
            )}
          </div>
        )}
      </div>

      {/* 设备总览 */}
      <div>
        <h2 className="text-xl font-bold mb-2">设备总览</h2>
        <div className="grid grid-cols-4 gap-2">
          <div
            className="rounded-lg border bg-white p-2 flex flex-col items-center gap-1 shadow-sm cursor-pointer hover:bg-green-50"
            onClick={() => { setView('device'); setFilterStatus(DeviceStatus.Online); onInteraction(); }}
          >
            <div className="flex items-center gap-2">
              <CheckCircle2 className="text-green-500 w-5 h-5" />
              <div className="text-xl font-bold leading-tight">{overviewStats.online_count}</div>
            </div>
            <div className="text-sm text-muted-foreground">在线设备</div>
          </div>
          <div
            className="rounded-lg border bg-white p-2 flex flex-col items-center gap-1 shadow-sm cursor-pointer hover:bg-yellow-50"
            onClick={() => { setView('device'); setFilterStatus(DeviceStatus.Warning); onInteraction(); }}
          >
            <div className="flex items-center gap-2">
              <AlertTriangle className="text-yellow-500 w-5 h-5" />
              <div className="text-xl font-bold leading-tight">{overviewStats.warn_count}</div>
            </div>
            <div className="text-sm text-muted-foreground">警告设备</div>
          </div>
          <div
            className="rounded-lg border bg-white p-2 flex flex-col items-center gap-1 shadow-sm cursor-pointer hover:bg-red-50"
            onClick={() => { setView('device'); setFilterStatus(DeviceStatus.Offline); onInteraction(); }}
          >
            <div className="flex items-center gap-2">
              <XCircle className="text-red-500 w-5 h-5" />
              <div className="text-xl font-bold leading-tight">{overviewStats.offline_count}</div>
            </div>
            <div className="text-sm text-muted-foreground">离线设备</div>
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="rounded-lg border bg-white p-2 flex flex-col items-center justify-center gap-1 shadow-sm cursor-pointer hover:bg-blue-50"
                  onClick={() => setShowQueryModal(true)}
                >
                  <div className="flex items-center gap-2">
                    {overviewStats.trend >= 0 ? <TrendingUp className="text-blue-500 w-5 h-5" /> : <TrendingDown className="text-red-500 w-5 h-5" />}
                    <div className="text-xl font-bold leading-tight">{overviewStats.yesterdayQueries.toLocaleString()}</div>
                  </div>
                  <div className="text-sm text-muted-foreground">昨日查询</div>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p className={`text-xs flex items-center ${overviewStats.trend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {overviewStats.trend >= 0 ? '▲' : '▼'} {Math.abs(overviewStats.trend).toLocaleString()} vs 前日
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* 设备状态列表 */}
      <div>
        <div className="flex justify-between items-center mb-3">
            <h2 className="text-xl font-bold">状态汇总</h2>
            <div className="flex items-center gap-1 bg-gray-200 p-1 rounded-lg">
                <button 
                    onClick={() => setView('enterprise')}
                    className={`px-3 py-1 text-sm font-semibold rounded-md transition-colors ${view === 'enterprise' ? 'bg-white shadow' : 'text-gray-600'}`}
                >
                    企业状态
                </button>
                <button 
                    onClick={() => setView('device')}
                    className={`px-3 py-1 text-sm font-semibold rounded-md transition-colors ${view === 'device' ? 'bg-white shadow' : 'text-gray-600'}`}
                >
                    设备状态
                </button>
            </div>
        </div>

        <div className="rounded-lg border bg-white shadow-sm flex flex-col">
           {/* Filter Buttons */}
          <div className="p-3 border-b grid grid-cols-4 gap-2">
             <button
              onClick={() => { setFilterStatus('All'); onInteraction(); }}
              className={cn(
                'px-3 py-2 text-sm rounded-lg text-center font-semibold transition-all',
                filterStatus === 'All' ? 'bg-gray-800 text-white shadow' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              )}
            >
              全部 {processedDevices.length}
            </button>
            <button
              onClick={() => { setFilterStatus(DeviceStatus.Online); onInteraction(); }}
              className={cn(
                'px-3 py-2 text-sm rounded-lg font-semibold flex items-center justify-center gap-2 transition-all',
                filterStatus === DeviceStatus.Online ? 'bg-green-500 text-white shadow-lg' : 'bg-green-100 text-green-800 hover:bg-green-200'
              )}
            >
              在线 {overviewStats.online_count}
            </button>
            <button
              onClick={() => { setFilterStatus(DeviceStatus.Warning); onInteraction(); }}
               className={cn(
                'px-3 py-2 text-sm rounded-lg font-semibold flex items-center justify-center gap-2 transition-all',
                filterStatus === DeviceStatus.Warning ? 'bg-yellow-500 text-white shadow-lg' : 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
              )}
            >
              警告 {overviewStats.warn_count}
            </button>
            <button
              onClick={() => { setFilterStatus(DeviceStatus.Offline); onInteraction(); }}
              className={cn(
                'px-3 py-2 text-sm rounded-lg font-semibold flex items-center justify-center gap-2 transition-all',
                filterStatus === DeviceStatus.Offline ? 'bg-red-500 text-white shadow-lg' : 'bg-red-100 text-red-800 hover:bg-red-200'
              )}
            >
              离线 {overviewStats.offline_count}
            </button>
          </div>
          
          {/* Search, Filter, Sort */}
          <div className="flex flex-wrap gap-2 mb-3 items-center">
            <input
              type="text"
              className="border rounded px-3 py-1 text-sm w-56"
              placeholder="搜索设备/企业/ID..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
            />
            <select
              className="border rounded px-2 py-1 text-sm"
              value={customerTypeFilter}
              onChange={e => setCustomerTypeFilter(e.target.value)}
            >
              <option value="All">全部客户类型</option>
              <option value="INTERNAL">内部</option>
              <option value="EXTERNAL">外部</option>
              {/* <option value="PARTNER">合作伙伴</option>
              <option value="TRIAL">试用</option> */}
            </select>
            <select
              className="border rounded px-2 py-1 text-sm"
              value={sortKey}
              onChange={e => setSortKey(e.target.value as any)}
            >
              <option value="query">按查询量排序</option>
              <option value="name">按名称排序</option>
              <option value="status">按状态排序</option>
            </select>
            <button
              className="border rounded px-2 py-1 text-sm"
              onClick={() => setSortOrder(o => o === 'asc' ? 'desc' : 'asc')}
            >
              {sortOrder === 'asc' ? '升序' : '降序'}
            </button>
          </div>
          
          {/* List Content */}
          <div className="divide-y divide-gray-100 overflow-y-auto" style={{ maxHeight: 'calc(100vh - 450px)' }}>
            {view === 'enterprise' ? (
              filteredEnterprises.map(enterprise => (
                <div key={enterprise.name} className="p-4 cursor-pointer hover:bg-gray-50 transition-colors" onClick={() => {
                  console.log('Enterprise clicked:', enterprise);
                  onDeviceSelect(enterprise.devices[0]);
                }}>
                    <div className="flex justify-between items-start gap-4">
                        <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                                <p className="font-bold text-gray-800 truncate" title={enterprise.name}>{enterprise.name}</p>
                                <span className={`flex-shrink-0 text-xs px-2 py-0.5 rounded-full ${getCustomerTypeInfo(enterprise.customerType).style}`}>
                                    {getCustomerTypeInfo(enterprise.customerType).displayName}
                                </span>
                            </div>
                            <p className="text-xs text-gray-500 mt-1.5 flex items-center gap-1 truncate" title={enterprise.primaryLocation}>
                                <MapPin className="w-3 h-3 flex-shrink-0" />
                                {enterprise.primaryLocation}
                            </p>
                            <p className="text-xs text-gray-500 mt-1.5">
                                <span className={cn('font-semibold', enterprise.online > 0 ? 'text-green-600' : 'text-gray-400')}>在线 {enterprise.online}</span> | 
                                <span className={cn('font-semibold', enterprise.warning > 0 ? 'text-yellow-600' : 'text-gray-400')}> 警告 {enterprise.warning}</span> | 
                                <span className={cn('font-semibold', enterprise.offline > 0 ? 'text-red-600' : 'text-gray-400')}> 离线 {enterprise.offline}</span>
                            </p>
                        </div>
                        <div className="text-right flex-shrink-0">
                            <div className="flex items-center gap-1 justify-end">
                               <BarChart2 className="w-4 h-4 text-gray-400" />
                               <p className="font-bold text-lg text-gray-800">{enterprise.totalQueries.toLocaleString()}</p>
                            </div>
                            <p className="text-xs text-gray-500">总查询</p>
                        </div>
                    </div>
                </div>
              ))
            ) : (
              filteredDevices.map((device, index) => (
                <div key={`${device.device_id}-${index}`} className="p-4 cursor-pointer hover:bg-gray-50 transition-colors" onClick={() => {
                  console.log('Device clicked:', device);
                  onDeviceSelect(device);
                }}>
                  <div className="flex gap-3">
                    {/* Status Icon */}
                    <div className="flex-shrink-0">
                      {device.status === DeviceStatus.Online && <CheckCircle2 className="w-5 h-5 text-green-500" />}
                      {device.status === DeviceStatus.Warning && <AlertTriangle className="w-5 h-5 text-yellow-500" />}
                      {device.status === DeviceStatus.Offline && <XCircle className="w-5 h-5 text-red-500" />}
                    </div>

                    {/* Info Block */}
                    <div className="flex-1 min-w-0">
                      {/* Top Row */}
                      <div className="flex justify-between items-start">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 flex-wrap">
                            <p className="font-bold text-gray-800 truncate" title={`${device.position_info?.device_name || '未知设备'}[${device.device_id}]`}>
                               {device.position_info?.enterprise_name || '未知企业'}:{device.position_info?.device_name || '未知设备'}
                            </p>
                            <span className={`flex-shrink-0 text-xs px-2 py-0.5 rounded-full ${getCustomerTypeInfo(device.customer_type).style}`}>
                              {getCustomerTypeInfo(device.customer_type).displayName} 
                            </span>
                          </div>
                        </div>

                        <div className="flex-shrink-0 flex gap-2">
                            <button
                                className="text-xs bg-blue-100 text-blue-800 font-semibold px-2 py-1 rounded hover:bg-blue-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled={!device.position_info?.enterprise_id}
                                onClick={(e) => {
                                e.stopPropagation();
                                if (device.position_info?.enterprise_id) {
                                  navigate(`/enterprise/${device.position_info.enterprise_id}/device/${device.device_id}/queries`);
                                }
                                }}
                            >
                                Query
                            </button>
                            <button
                                className="text-xs bg-green-100 text-green-800 font-semibold px-2 py-1 rounded hover:bg-green-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled={!device.position_info?.enterprise_id}
                                onClick={(e) => {
                                e.stopPropagation();
                                if (device.position_info?.enterprise_id) {
                                  navigate(`/enterprise/${device.position_info.enterprise_id}/device/${device.device_id}/analysis`);
                                }
                                }}
                            >
                                分析
                            </button>
                        </div>

                        {/* Right side: query count */}
                        <div className="text-right flex-shrink-0 w-24">
                          <div className="flex items-center gap-1 justify-end">
                            <BarChart2 className="w-4 h-4 text-gray-400" />
                            <p className="font-bold text-lg text-gray-800">{device.lastQueryCount.toLocaleString()}</p>
                          </div>
                          <p className="text-xs text-gray-500">{device.lastQueryDate}</p>
                        </div>
                      </div>
                      {/* Bottom row: status, location, recent queries */}
                      <div className="mt-2 text-xs text-gray-500 space-y-1">
                          <div className="flex items-center gap-2">
                            <span className={`flex-shrink-0 text-xs px-1.5 py-0.5 rounded-full font-semibold ${getCurrentStatusInfo(device.current_status).style}`}>
                                {getCurrentStatusInfo(device.current_status).displayName}
                            </span>
                            <span className="truncate">
                                <MapPin className="w-3 h-3 inline-block mr-1" />
                                {device.location}
                            </span>
                          </div>
                          {device.recentQueries.length > 0 && (
                            <div className="pl-2 border-l-2 ml-1">
                              <div className="font-semibold">最近查询:</div>
                              {device.recentQueries.map(q => (
                                <div key={q.time} className="flex justify-between">
                                  <span>{q.time}:</span>
                                  <span>{q.queries.toLocaleString()}次</span>
                                </div>
                              ))}
                            </div>
                          )}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* 弹窗 */}
      {showQueryModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40" onClick={e => { if (e.target === e.currentTarget) setShowQueryModal(false); }}>
          <div className="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-6xl relative flex flex-col items-center border border-blue-200 h-[700px]" onClick={e => e.stopPropagation()}>
            <button className="absolute right-6 top-6 text-gray-400 hover:text-blue-600 text-3xl font-bold rounded-full w-10 h-10 flex items-center justify-center transition-colors hover:bg-blue-50" onClick={() => setShowQueryModal(false)}>×</button>
            <h3 className="text-2xl font-bold mb-6 text-blue-700 tracking-wide">最近两周查询趋势</h3>
            <div className="w-full flex justify-center items-center h-[530px]">
              <Line
                data={{
                  labels: last15DaysMultiData.map(d => d.day.slice(5)),
                  datasets: [
                    {
                      label: '查询总数',
                      data: last15DaysMultiData.map(d => d.querySum),
                      borderColor: '#2563eb',
                      backgroundColor: '#2563eb',
                      pointBackgroundColor: '#2563eb',
                      pointBorderColor: '#fff',
                      pointRadius: 5,
                      pointHoverRadius: 7,
                      tension: 0.4,
                      cubicInterpolationMode: 'monotone',
                      datalabels: { color: '#2563eb' },
                      yAxisID: 'y',
                    },
                    {
                      label: '活跃设备数',
                      data: last15DaysMultiData.map(d => d.activeDeviceCount),
                      borderColor: '#06b6d4',
                      backgroundColor: '#06b6d4',
                      pointBackgroundColor: '#06b6d4',
                      pointBorderColor: '#fff',
                      pointRadius: 5,
                      pointHoverRadius: 7,
                      tension: 0.4,
                      cubicInterpolationMode: 'monotone',
                      datalabels: { color: '#06b6d4' },
                      yAxisID: 'y1',
                    },
                    {
                      label: '活跃企业数',
                      data: last15DaysMultiData.map(d => d.activeEnterpriseCount),
                      borderColor: '#f59e42',
                      backgroundColor: '#f59e42',
                      pointBackgroundColor: '#f59e42',
                      pointBorderColor: '#fff',
                      pointRadius: 5,
                      pointHoverRadius: 7,
                      tension: 0.4,
                      cubicInterpolationMode: 'monotone',
                      datalabels: { color: '#f59e42' },
                      yAxisID: 'y1',
                    },
                    {
                      label: '单设备最大查询量',
                      data: last15DaysMultiData.map(d => d.maxDeviceQuery),
                      borderColor: '#fb7185',
                      backgroundColor: '#fb7185',
                      pointBackgroundColor: '#fb7185',
                      pointBorderColor: '#fff',
                      pointRadius: 5,
                      pointHoverRadius: 7,
                      tension: 0.4,
                      cubicInterpolationMode: 'monotone',
                      datalabels: { color: '#fb7185' },
                      yAxisID: 'y',
                    },
                  ],
                }}
                options={{
                  responsive: true,
                  plugins: {
                    legend: {
                      display: true,
                      position: 'top',
                      labels: {
                        usePointStyle: true,
                        boxWidth: 10,
                        font: { size: 16, weight: 'bold' },
                        padding: 24,
                      },
                      title: {
                        display: false,
                      },
                    },
                    tooltip: {
                      mode: 'index',
                      intersect: false,
                      backgroundColor: '#fff',
                      titleColor: '#222',
                      bodyColor: '#222',
                      borderColor: '#e5e7eb',
                      borderWidth: 1,
                      padding: 16,
                      caretSize: 8,
                      cornerRadius: 10,
                      titleFont: { size: 18, weight: 'bold' },
                      bodyFont: { size: 16 },
                      boxPadding: 8,
                      displayColors: true,
                      boxWidth: 16,
                      boxHeight: 16,
                      callbacks: {
                        label: ctx => `${ctx.dataset.label}: ${ctx.parsed.y}`,
                      },
                    },
                    datalabels: {
                      anchor: 'end',
                      align: 'top',
                      font: { weight: 'bold', size: 12 },
                      formatter: v => v,
                      display: true,
                    },
                  },
                  layout: {
                    padding: {
                      top: 16,
                      bottom: 0,
                      left: 0,
                      right: 0,
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                      grid: { color: '#e0e7ef' },
                      title: { display: true, text: '查询量', color: '#2563eb', font: { size: 14, weight: 'bold' } },
                      ticks: { color: '#2563eb', font: { size: 13 } },
                    },
                    y1: {
                      beginAtZero: true,
                      position: 'right',
                      grid: { drawOnChartArea: false },
                      title: { display: true, text: '活跃数', color: '#06b6d4', font: { size: 14, weight: 'bold' } },
                      ticks: { color: '#06b6d4', font: { size: 13 } },
                    },
                    x: { grid: { color: '#f3f4f6' } },
                  },
                  interaction: { mode: 'nearest', axis: 'x', intersect: false },
                }}
              />
            </div>
            <div className="mt-6 text-sm text-gray-500 text-center w-full">
              数据区间：{last15Days[0]} ~ {last15Days[14]}
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 