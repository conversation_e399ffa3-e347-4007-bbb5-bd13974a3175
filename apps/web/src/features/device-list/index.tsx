import { useQuery } from '@tanstack/react-query'
import { getDevices } from '../../api/devices'
import { BadgeCheck, AlertTriangle, XCircle } from 'lucide-react'

interface DeviceListProps {
  scope: 'cn' | 'global'
}

const statusMap = {
  active_ok: {
    label: '在线',
    color: 'bg-green-100 text-green-700',
    icon: <BadgeCheck className="w-4 h-4 mr-1 text-green-500" />,
  },
  usage_warn: {
    label: '警告',
    color: 'bg-yellow-100 text-yellow-700',
    icon: <AlertTriangle className="w-4 h-4 mr-1 text-yellow-500" />,
  },
  usage_crit: {
    label: '离线',
    color: 'bg-red-100 text-red-700',
    icon: <XCircle className="w-4 h-4 mr-1 text-red-500" />,
  },
}

export function DeviceList({ scope }: DeviceListProps) {
  const { data: devices, isLoading } = useQuery({
    queryKey: ['devices', scope],
    queryFn: () => getDevices(scope),
  })

  if (isLoading) {
    return <div>加载中...</div>
  }

  return (
    <div className="rounded-lg border bg-white shadow-sm">
      <div className="p-4 border-b">
        <h2 className="text-lg font-semibold">设备状态</h2>
      </div>
      <div className="divide-y">
        {devices?.map(device => {
          const status = statusMap[device.status]
          return (
            <div key={device.id} className="p-4 flex items-center gap-3 hover:bg-muted/50">
              <div className="flex-shrink-0">
                {status.icon}
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium truncate">{device.name}</div>
                <div className="text-xs text-muted-foreground truncate">{device.location}</div>
                <div className="text-xs text-muted-foreground mt-1">{device.queries_per_day ? `${device.queries_per_day} 次查询/天` : ''}</div>
              </div>
              <div className={`px-2 py-0.5 rounded-full text-xs font-semibold ${status.color}`}>{status.label}</div>
            </div>
          )
        })}
      </div>
    </div>
  )
} 