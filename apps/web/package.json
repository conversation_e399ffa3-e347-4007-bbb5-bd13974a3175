{"name": "ce-monitor-web", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "dev:test": "vite --mode test", "build": "vite build", "build:test": "vite build --mode test", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@amap/amap-react": "^0.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.2.7", "@react-google-maps/api": "^2.20.6", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.18.1", "@tanstack/react-table": "^8.21.3", "@types/dompurify": "^3.0.5", "@types/marked": "^5.0.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "dompurify": "^3.2.6", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "echarts-wordcloud": "^2.1.0", "leaflet": "^1.9.4", "lucide-react": "^0.516.0", "marked": "^16.0.0", "mermaid": "^11.9.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-leaflet": "^4.2.1", "react-markdown": "^10.1.0", "react-mermaid2": "^0.1.4", "react-plotly.js": "^2.6.0", "react-router-dom": "^6.22.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "zustand": "^4.5.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.1.0", "vitest": "^1.2.2"}}