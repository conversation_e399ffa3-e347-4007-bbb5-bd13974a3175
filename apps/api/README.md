# 启动服务

## 0. 安装 pyenv
git clone https://gitee.com/mirrors/pyenv.git ~/.pyenv

## 0. 配置 pyenv
echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.bashrc && echo 'command -v pyenv >/dev/null || export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.bashrc && echo 'eval "$(pyenv init -)"' >> ~/.bashrc

source ~/.bashrc

## 1. 安装 python
pyenv install 3.12.11

## 2. 设置 python 版本
pyenv global 3.12.11 # 设置全局 python 版本

pyenv local 3.12.11 # 设置本地 python 版本

## 3. 安装 pip
pip install --upgrade pip

## 1. 创建并激活虚拟环境（如果还没有）
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
.\venv\Scripts\activate  # Windows

## 2. 安装依赖
pip install -r requirements.txt

## 3. 启动服务
  uvicorn app.main:app --reload
dev环境
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
test测试环境
uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload


nohup uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload > /data/app/CEMonitor_api/logs/uvicorn.log 2>&1 &

## 4. 访问服务
http://localhost:8000

## 5. 停止服务
Ctrl + C


## 执行 task 任务
celery -A celery_app.celery_app worker --loglevel=info


celery -A celery_app call tasks.sync_robot_positions

## 执行定时任务
celery -A celery_app.celery_app beat -l info

celery -A celery_app worker -l info
watchgod celery.bin.worker.main -A celery_app worker -l info

celery -A celery_app beat -l info



## 测试脚本运行

```
# 首先，进入正确的目录
cd /data/app/CEMonitor/apps/api/

# 然后，运行pytest来执行我们的测试文件
pytest app/tests/test_impala_service.py -v -s


```