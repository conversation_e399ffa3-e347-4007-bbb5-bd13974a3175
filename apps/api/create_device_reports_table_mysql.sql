-- 创建设备报告生成记录表 (MySQL版本)
-- 用于记录每次生成报告的请求参数和LLM生成结果

-- 创建设备报告生成记录表
CREATE TABLE aos_device_reports (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    enterprise_id VARCHAR(100) NOT NULL COMMENT '企业ID',
    device_id VARCHAR(100) NOT NULL COMMENT '设备ID',
    report_type ENUM('SINGLE_DAY', 'MULTI_DAY') NOT NULL COMMENT '报告类型',
    `status` ENUM('GENERATING', 'COMPLETED', 'FAILED', 'CANCELLED') DEFAULT 'GENERATING' COMMENT '报告状态',
    
    -- 请求参数
    target_date VARCHAR(10) NOT NULL COMMENT '目标日期(YYYY-MM-DD)',
    start_date VARCHAR(10) COMMENT '开始日期(多天报告)',
    end_date VARCHAR(10) COMMENT '结束日期(多天报告)',
    prompt_version VARCHAR(50) COMMENT 'Prompt版本',
    custom_system_prompt TEXT COMMENT '自定义系统Prompt',
    custom_user_prompt TEXT COMMENT '自定义用户Prompt',
    prompt_type ENUM('SYSTEM', 'USER', 'CUSTOM') DEFAULT 'SYSTEM' COMMENT 'Prompt类型',
    
    -- LLM生成结果
    system_prompt_used TEXT COMMENT '实际使用的系统Prompt',
    user_prompt_used TEXT COMMENT '实际使用的用户Prompt',
    generated_content LONGTEXT COMMENT '生成的报告内容',
    model_name VARCHAR(50) COMMENT '使用的模型名称',
    
    -- 性能指标
    content_length INT DEFAULT 0 COMMENT '内容长度',
    chunk_count INT DEFAULT 0 COMMENT '内容块数量',
    tokens_used INT COMMENT '使用的Token数量',
    generation_start_time DATETIME COMMENT '生成开始时间',
    generation_end_time DATETIME COMMENT '生成结束时间',
    generation_duration INT COMMENT '生成耗时(秒)',
    
    -- 错误信息
    error_message TEXT COMMENT '错误信息',
    
    -- 元数据
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备报告生成记录表 - 记录请求参数和LLM生成结果';

-- 创建索引
CREATE INDEX idx_enterprise_device_date ON aos_device_reports (enterprise_id, device_id, target_date);
CREATE INDEX idx_report_type_status ON aos_device_reports (report_type, status);
CREATE INDEX idx_prompt_type ON aos_device_reports (prompt_type);
CREATE INDEX idx_prompt_version ON aos_device_reports (prompt_version);
CREATE INDEX idx_created_at ON aos_device_reports (created_at); 