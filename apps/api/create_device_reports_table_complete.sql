-- 完整的设备报告表创建SQL语句
-- 数据库: speech_ai_robot
-- 表名: aos_device_reports

-- 如果表已存在，先删除
DROP TABLE IF EXISTS `speech_ai_robot`.`aos_device_reports`;

-- 创建设备报告生成记录表
CREATE TABLE `speech_ai_robot`.`aos_device_reports` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `enterprise_id` VARCHAR(100) NOT NULL COMMENT '企业ID',
    `device_id` VARCHAR(100) NOT NULL COMMENT '设备ID',
    `report_type` VARCHAR(20) NOT NULL COMMENT '报告类型',
    `status` VARCHAR(20) DEFAULT 'generating' COMMENT '报告状态',
    
    -- 请求参数
    `target_date` VARCHAR(10) NOT NULL COMMENT '目标日期(YYYY-MM-DD)',
    `start_date` VARCHAR(10) NULL COMMENT '开始日期(多天报告)',
    `end_date` VARCHAR(10) NULL COMMENT '结束日期(多天报告)',
    `prompt_type` VARCHAR(20) DEFAULT 'system' COMMENT 'Prompt类型',
    `prompt_version` VARCHAR(50) NULL COMMENT 'Prompt版本',
    `custom_system_prompt` TEXT NULL COMMENT '自定义系统Prompt',
    `custom_user_prompt` TEXT NULL COMMENT '自定义用户Prompt',
    
    
    -- LLM生成结果
    `system_prompt_used` TEXT NULL COMMENT '实际使用的系统Prompt',
    `user_prompt_used` TEXT NULL COMMENT '实际使用的用户Prompt',
    `generated_content` TEXT NULL COMMENT '生成的报告内容',
    `model_name` VARCHAR(50) NULL COMMENT '使用的模型名称',
    
    -- 性能指标
    `content_length` INT DEFAULT 0 COMMENT '内容长度',
    `chunk_count` INT DEFAULT 0 COMMENT '内容块数量',
    `tokens_used` INT NULL COMMENT '使用的Token数量',
    `generation_start_time` DATETIME NULL COMMENT '生成开始时间',
    `generation_end_time` DATETIME NULL COMMENT '生成结束时间',
    `generation_duration` INT NULL COMMENT '生成耗时(秒)',
    
    -- 错误信息
    `error_message` TEXT NULL COMMENT '错误信息',
    
    -- 元数据
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_enterprise_device_date` (`enterprise_id`, `device_id`, `target_date`),
    INDEX `idx_report_type_status` (`report_type`, `status`),
    INDEX `idx_prompt_type` (`prompt_type`),
    INDEX `idx_prompt_version` (`prompt_version`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备报告生成记录表';

-- 验证表创建成功
DESCRIBE `speech_ai_robot`.`aos_device_reports`;

-- 查看表结构
SHOW CREATE TABLE `speech_ai_robot`.`aos_device_reports`; 