# Mermaid格式优化API接入指南

## 概述

Mermaid格式优化API是一个专门用于优化和修复Mermaid图表代码格式的服务。当您的Mermaid图表无法正确渲染时，可以使用此API自动修复格式问题。

## API接口信息

### 基本信息
- **接口地址**: `POST /api/v1/device-reports/optimize-mermaid`
- **Content-Type**: `application/json`
- **返回格式**: JSON
- **请求方式**: 一次性返回（非流式）

### 请求参数

#### 请求体结构
```json
{
  "mermaid_code": "需要优化的Mermaid代码字符串",
  "prompt_version": "MERMAID_OPTIMIZE"
}
```

#### 参数说明
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| mermaid_code | string | 是 | - | 需要优化的Mermaid代码 |
| prompt_version | string | 否 | MERMAID_OPTIMIZE | Prompt版本，目前只支持MERMAID_OPTIMIZE |

### 响应格式

#### 成功响应
```json
{
  "status": "success",
  "original_code": "原始Mermaid代码",
  "optimized_code": "优化后的Mermaid代码",
  "optimization_summary": {
    "changes_detected": ["检测到的变化类型"],
    "statistics": {
      "lines_changed": 0,
      "chars_changed": 0,
      "original_lines": 10,
      "optimized_lines": 10,
      "original_chars": 200,
      "optimized_chars": 200
    },
    "optimization_needed": false
  },
  "timestamp": "2024-01-15T10:30:00.000Z",
  "message": "Mermaid格式优化完成"
}
```

#### 错误响应
```json
{
  "status": "error",
  "error": "错误信息",
  "original_code": "原始Mermaid代码",
  "optimized_code": "",
  "optimization_summary": "优化失败",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "message": "Mermaid格式优化失败: 具体错误信息"
}
```

## 使用示例

### JavaScript/TypeScript示例

#### 使用fetch API
```javascript
async function optimizeMermaid(mermaidCode) {
  try {
    const response = await fetch('/api/v1/device-reports/optimize-mermaid', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        mermaid_code: mermaidCode,
        prompt_version: 'MERMAID_OPTIMIZE'
      })
    });

    const result = await response.json();
    
    if (result.status === 'success') {
      console.log('优化成功:', result.optimized_code);
      return result.optimized_code;
    } else {
      console.error('优化失败:', result.message);
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('请求失败:', error);
    throw error;
  }
}

// 使用示例
const originalMermaid = `
timeline
    title 2025年7月18日机器人关键工作时间线
    06:24 : 最早迎宾，机器人主动打招呼
    09:40 : 上班高峰，访客登记&找人业务量最大
    10:30 : 讲解服务、带路引领多次触发
    12:00 : 午间，员工互动较多
    14:00 : 下午访客高峰，带路/导航&趣味互动频繁
    18:30 : 晚间持续有访客/员工互动
    19:00 : 晚班用户"我是谁"趣味互动多
    21:00 : 偶有访客，机器人持续在岗
    23:33 : 最晚服务，机器人全天无间断
`;

optimizeMermaid(originalMermaid)
  .then(optimizedCode => {
    // 使用优化后的代码渲染图表
    mermaid.render('mermaid-chart', optimizedCode);
  })
  .catch(error => {
    console.error('Mermaid优化失败:', error);
  });
```

#### 使用axios
```javascript
import axios from 'axios';

async function optimizeMermaidWithAxios(mermaidCode) {
  try {
    const response = await axios.post('/api/v1/device-reports/optimize-mermaid', {
      mermaid_code: mermaidCode,
      prompt_version: 'MERMAID_OPTIMIZE'
    });

    const result = response.data;
    
    if (result.status === 'success') {
      return result.optimized_code;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('Mermaid优化请求失败:', error);
    throw error;
  }
}
```

### React组件示例

```jsx
import React, { useState } from 'react';

const MermaidOptimizer = () => {
  const [originalCode, setOriginalCode] = useState('');
  const [optimizedCode, setOptimizedCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleOptimize = async () => {
    if (!originalCode.trim()) {
      setError('请输入Mermaid代码');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/v1/device-reports/optimize-mermaid', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mermaid_code: originalCode,
          prompt_version: 'MERMAID_OPTIMIZE'
        })
      });

      const result = await response.json();
      
      if (result.status === 'success') {
        setOptimizedCode(result.optimized_code);
      } else {
        setError(result.message);
      }
    } catch (error) {
      setError('请求失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mermaid-optimizer">
      <h2>Mermaid格式优化器</h2>
      
      <div className="input-section">
        <label>原始Mermaid代码:</label>
        <textarea
          value={originalCode}
          onChange={(e) => setOriginalCode(e.target.value)}
          placeholder="请输入需要优化的Mermaid代码..."
          rows={10}
        />
      </div>

      <button 
        onClick={handleOptimize} 
        disabled={loading}
        className="optimize-btn"
      >
        {loading ? '优化中...' : '开始优化'}
      </button>

      {error && (
        <div className="error-message">
          错误: {error}
        </div>
      )}

      {optimizedCode && (
        <div className="output-section">
          <label>优化后的代码:</label>
          <textarea
            value={optimizedCode}
            readOnly
            rows={10}
          />
          <button 
            onClick={() => navigator.clipboard.writeText(optimizedCode)}
            className="copy-btn"
          >
            复制代码
          </button>
        </div>
      )}
    </div>
  );
};

export default MermaidOptimizer;
```

### Vue.js示例

```vue
<template>
  <div class="mermaid-optimizer">
    <h2>Mermaid格式优化器</h2>
    
    <div class="input-section">
      <label>原始Mermaid代码:</label>
      <textarea
        v-model="originalCode"
        placeholder="请输入需要优化的Mermaid代码..."
        rows="10"
      />
    </div>

    <button 
      @click="optimizeMermaid" 
      :disabled="loading"
      class="optimize-btn"
    >
      {{ loading ? '优化中...' : '开始优化' }}
    </button>

    <div v-if="error" class="error-message">
      错误: {{ error }}
    </div>

    <div v-if="optimizedCode" class="output-section">
      <label>优化后的代码:</label>
      <textarea
        v-model="optimizedCode"
        readonly
        rows="10"
      />
      <button @click="copyCode" class="copy-btn">
        复制代码
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MermaidOptimizer',
  data() {
    return {
      originalCode: '',
      optimizedCode: '',
      loading: false,
      error: ''
    };
  },
  methods: {
    async optimizeMermaid() {
      if (!this.originalCode.trim()) {
        this.error = '请输入Mermaid代码';
        return;
      }

      this.loading = true;
      this.error = '';

      try {
        const response = await fetch('/api/v1/device-reports/optimize-mermaid', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            mermaid_code: this.originalCode,
            prompt_version: 'MERMAID_OPTIMIZE'
          })
        });

        const result = await response.json();
        
        if (result.status === 'success') {
          this.optimizedCode = result.optimized_code;
        } else {
          this.error = result.message;
        }
      } catch (error) {
        this.error = '请求失败: ' + error.message;
      } finally {
        this.loading = false;
      }
    },
    
    async copyCode() {
      try {
        await navigator.clipboard.writeText(this.optimizedCode);
        alert('代码已复制到剪贴板');
      } catch (error) {
        console.error('复制失败:', error);
      }
    }
  }
};
</script>
```

## 支持的Mermaid图表类型

该API支持优化以下类型的Mermaid图表：

1. **饼图 (pie)**
2. **流程图 (graph)**
3. **序列图 (sequenceDiagram)**
4. **折线图 (xychart-beta)**
5. **流程图 (journey)**
6. **甘特图 (gantt)**
7. **时间线 (timeline)**
8. **架构图 (architecture-beta)**
9. **Git图 (gitGraph)**

## 常见优化场景

### 1. 缺少代码块标记
**问题**: 代码没有 ```mermaid 标记
**优化**: 自动添加正确的代码块标记

### 2. 语法错误
**问题**: 节点格式、连接符使用错误
**优化**: 修正语法错误，确保符合Mermaid规范

### 3. 特殊字符问题
**问题**: 特殊字符未正确转义
**优化**: 自动转义特殊字符

### 4. 格式不规范
**问题**: 缩进、换行等格式问题
**优化**: 统一格式，提高可读性

## 错误处理

### 常见错误码
- `400`: 请求参数错误
- `422`: 数据验证失败
- `500`: 服务器内部错误

### 错误处理建议
```javascript
// 错误处理示例
try {
  const result = await optimizeMermaid(code);
  // 处理成功结果
} catch (error) {
  if (error.response) {
    // 服务器返回错误
    console.error('服务器错误:', error.response.data);
  } else if (error.request) {
    // 网络错误
    console.error('网络错误:', error.request);
  } else {
    // 其他错误
    console.error('其他错误:', error.message);
  }
}
```

## 性能考虑

1. **请求大小**: 建议单个Mermaid代码不超过10KB
2. **并发请求**: 建议控制并发请求数量，避免服务器压力过大
3. **缓存策略**: 对于相同的代码，可以考虑客户端缓存优化结果

## 安全注意事项

1. **输入验证**: 服务端会对输入进行验证，但仍建议客户端也进行基本验证
2. **敏感信息**: 避免在Mermaid代码中包含敏感信息
3. **请求频率**: 建议控制请求频率，避免滥用

## 更新日志

- **v1.0.0**: 初始版本，支持基本的Mermaid格式优化
- 支持所有主要Mermaid图表类型
- 提供完整的错误处理和优化摘要 