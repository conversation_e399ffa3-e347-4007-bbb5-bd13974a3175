from pymongo import MongoClient
from pymongo.collection import Collection
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging
from ..config.mongodb_config import MONGODB_CONFIG, COLLECTIONS

logger = logging.getLogger(__name__)

class MongoDBService:
    def __init__(self):
        self.client = None
        self.db = None
        self._connect()

    def _connect(self):
        """建立MongoDB连接"""
        try:
            self.client = MongoClient(
                host=MONGODB_CONFIG["host"],
                port=MONGODB_CONFIG["port"],
                username=MONGODB_CONFIG["username"],
                password=MONGODB_CONFIG["password"],
                authSource=MONGODB_CONFIG["auth_source"]
            )
            self.db = self.client[MONGODB_CONFIG["database"]]
            logger.info("MongoDB连接成功")
        except Exception as e:
            logger.error(f"MongoDB连接失败: {str(e)}")
            raise

    def get_collection(self, collection_name: str) -> Collection:
        """获取集合对象"""
        return self.db[COLLECTIONS[collection_name]]

    def insert_device_query(self, device_id: str, query_data: Dict[str, Any]) -> str:
        """
        插入设备查询记录
        :param device_id: 设备ID
        :param query_data: 查询数据
        :return: 插入记录的ID
        """
        collection = self.get_collection("device_queries")
        document = {
            "device_id": device_id,
            "query_time": datetime.utcnow(),
            **query_data
        }
        result = collection.insert_one(document)
        return str(result.inserted_id)

    def get_device_queries(
        self,
        device_id: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        获取设备查询历史
        :param device_id: 设备ID
        :param start_time: 开始时间
        :param end_time: 结束时间
        :param limit: 返回记录数量限制
        :return: 查询记录列表
        """
        collection = self.get_collection("device_queries")
        query = {"device_id": device_id}
        
        if start_time or end_time:
            query["query_time"] = {}
            if start_time:
                query["query_time"]["$gte"] = start_time
            if end_time:
                query["query_time"]["$lte"] = end_time

        cursor = collection.find(query).sort("query_time", -1).limit(limit)
        return list(cursor)

    def get_device_metrics(
        self,
        device_id: str,
        metric_type: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        获取设备指标数据
        :param device_id: 设备ID
        :param metric_type: 指标类型
        :param start_time: 开始时间
        :param end_time: 结束时间
        :return: 指标数据列表
        """
        collection = self.get_collection("device_metrics")
        query = {"device_id": device_id}
        
        if metric_type:
            query["metric_type"] = metric_type
            
        if start_time or end_time:
            query["timestamp"] = {}
            if start_time:
                query["timestamp"]["$gte"] = start_time
            if end_time:
                query["timestamp"]["$lte"] = end_time

        cursor = collection.find(query).sort("timestamp", -1)
        return list(cursor)

    def close(self):
        """关闭MongoDB连接"""
        if self.client:
            self.client.close()
            logger.info("MongoDB连接已关闭")

# 创建全局MongoDB服务实例
mongodb_service = MongoDBService() 