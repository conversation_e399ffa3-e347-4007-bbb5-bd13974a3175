import logging
import asyncio
from datetime import datetime
from app.services.impala.connection import get_impala_connection
from app.services.impala import repository as impala_repository
from app.services.feishu import repository as feishu_repository
from app.services.mysql.position_service import (
    check_if_position_exists_in_mysql, 
    insert_location_to_mysql
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def _get_all_sns_from_feishu() -> list[str]:
    """
    并发地从飞书获取所有区域的设备SN，并合并返回。
    """
    logger.info("并发获取所有 scopes 的设备SN...")
    tasks = [
        feishu_repository.get_device_sns_by_scope(scope="cn"),
        #feishu_repository.get_device_sns_by_scope(scope="global")
    ]
    # 使用 return_exceptions=True 来确保一个任务的失败不会影响另一个
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    all_sns = set()
    for res in results:
        if isinstance(res, Exception):
            logger.error(f"获取某个scope的飞书数据时失败: {res}", exc_info=res)
        elif isinstance(res, list):
            all_sns.update(res)
            
    return list(all_sns)

def sync_robot_positions():
    """
    协调机器人位置数据的同步流程。
    数据源为飞书多维表格，目标为Impala表 orion_gb_rt_position_by_day。
    """
    logger.info("开始机器人位置数据同步流程...")

    # 1. 从飞书并发获取所有需要监控的设备SN列表
    try:
        target_rdids = asyncio.run(_get_all_sns_from_feishu())
    except Exception as e:
        logger.error(f"运行飞书数据获取任务时发生严重错误，任务终止: {e}", exc_info=True)
        return
    
    if not target_rdids:
        logger.warning("从飞书获取的设备SN列表为空，任务结束。")
        return
        
    logger.info(f"从飞书获取到 {len(target_rdids)} 个设备SN，开始与Impala同步...")

    # 2. 使用 with 语句正确管理Impala连接
    try:
        with get_impala_connection() as impala_conn:
            today_str = datetime.now().strftime('%Y-%m-%d')
            synced_count = 0

            for rdid in target_rdids:
                try:
                    # 检查当天数据是否已在MySQL中存在
                    if check_if_position_exists_in_mysql(rdid):
                        logger.debug(f"RDID: {rdid} 今天的数据已在MySQL中存在，跳过。")
                        continue

                    # 数据不存在，从Impala查询最新位置
                    logger.info(f"RDID: {rdid} 今日数据不存在，正在查询最新位置...")
                    location_data = impala_repository.get_latest_location(impala_conn, rdid, today_str)
                    
                    if not location_data:
                        logger.warning(f"在 bi_gb.data_gb_location 中未找到 rdid: {rdid} 的任何位置数据。")
                        continue
                    
                    logger.info(f"location_data: {location_data}")
                    # 将数据写入到MySQL
                    insert_location_to_mysql(location_data)
                    synced_count += 1
                    logger.info(f"成功同步 rdid: {rdid} 的位置数据到MySQL。")

                except Exception as e:
                    logger.error(f"处理 rdid: {rdid} 时出错: {e}", exc_info=True)
                    continue
            
            logger.info(f"同步任务完成。共检查 {len(target_rdids)} 个设备，新同步 {synced_count} 条数据。")
    
    except Exception as e:
        logger.error(f"获取Impala连接或执行同步时发生严重错误，任务终止: {e}", exc_info=True) 