from sqlalchemy.orm import Session as DbSession
from sqlalchemy import func
from typing import Optional, List
from datetime import datetime

from ..models.conversation import AosConversationSession, AosConversationMessage
from ..schemas.conversation import Session as SessionSchema, Message as MessageSchema, PaginatedSessionResponse

class ConversationService:
    def get_conversations(
        self,
        db: DbSession,
        enterprise_id: str,
        project_id: str,
        device_id: str,
        start_time: Optional[datetime],
        end_time: Optional[datetime],
        page: int,
        page_size: int,
    ) -> PaginatedSessionResponse:
        
        query = db.query(AosConversationSession).filter(
            AosConversationSession.enterprise_id == enterprise_id,
            AosConversationSession.project_id == project_id,
            AosConversationSession.device_id == device_id
        )

        if start_time:
            query = query.filter(AosConversationSession.session_start_time >= start_time)
        if end_time:
            query = query.filter(AosConversationSession.session_start_time <= end_time)

        total = query.count()

        sessions_db = query.order_by(AosConversationSession.session_start_time.desc()).offset((page - 1) * page_size).limit(page_size).all()

        if not sessions_db:
            return PaginatedSessionResponse(total=0, page=page, page_size=page_size, items=[])

        session_ids = [s.session_id for s in sessions_db]
        
        messages_db = db.query(AosConversationMessage).filter(
            AosConversationMessage.session_id.in_(session_ids)
        ).order_by(AosConversationMessage.message_timestamp.asc()).all()

        messages_by_session = {}
        for msg in messages_db:
            if msg.session_id not in messages_by_session:
                messages_by_session[msg.session_id] = []
            messages_by_session[msg.session_id].append(MessageSchema.from_orm(msg))

        result_sessions = []
        for session_db in sessions_db:
            session_schema = SessionSchema.from_orm(session_db)
            session_schema.messages = messages_by_session.get(session_db.session_id, [])
            result_sessions.append(session_schema)
            
        return PaginatedSessionResponse(
            total=total,
            page=page,
            page_size=page_size,
            items=result_sessions
        )

conversation_service = ConversationService() 