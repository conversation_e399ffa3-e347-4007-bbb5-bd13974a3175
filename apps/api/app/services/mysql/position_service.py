from .base import get_connection
import pymysql
import logging
import time

"""
-- 数据库表结构
CREATE TABLE `orion_gb_rt_position_by_day` (
  `enterprise_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `device_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `version` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `model` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `rt_province` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `rt_city` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `rt_distinct` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `rt_street` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `lat` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `lng` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `is_test` bigint DEFAULT NULL,
  `is_delete` bigint DEFAULT NULL,
  `count_time` date DEFAULT NULL,
  `gmt_create` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `kid` int NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`kid`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4747024 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `orion_device_profile_gb` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `model` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备model',
  `version` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '版本',
  `os_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '豹小秘型号',
  `device_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备id',
  `device_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备名称',
  `corp_class_id` int DEFAULT NULL COMMENT '类型',
  `d_base_city` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所在城市',
  `d_base_provice` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所在省份',
  `d_base_county` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所在区县',
  `init_bind_time` datetime DEFAULT NULL COMMENT '机器首次激活时间',
  `bind_time` datetime DEFAULT NULL COMMENT '机器最近激活时间',
  `d_base_cumulative_active_days` int DEFAULT NULL COMMENT '机器最近激活累计开机天数',
  `d_base_active7` int DEFAULT NULL COMMENT '机器近7日开机天数',
  `d_base_active_score` float(11,2) DEFAULT NULL COMMENT '机器活跃分数',
  `d_base_work_time` float(11,2) DEFAULT NULL COMMENT '机器工作时长',
  `boot_days_30` int DEFAULT NULL COMMENT '近30天的开机天数',
  `total_sid_click` int DEFAULT NULL COMMENT '语音点击数',
  `enterprise_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所属企业',
  `enterprise_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所属企业名称',
  `d_base_agency_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '代理商id',
  `d_base_agency_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '代理商名称',
  `d_base_pcate_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '一级行业',
  `d_base_cate_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '二级行业',
  `d_base_new_cate_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '新二级行业',
  `d_base_cate_cluster_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '聚类名称',
  `is_test` tinyint(1) DEFAULT NULL COMMENT '是否是测试设备 1代表测试设备',
  `platform` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'platform',
  `rt_city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '城市',
  `rt_distinct` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地区',
  `rt_street` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '街道',
  `count_time` date DEFAULT NULL COMMENT '日期',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint(1) DEFAULT NULL COMMENT '是否删除 1代表删除',
  `is_agentOs` tinyint(1) DEFAULT '0' COMMENT '是否是agent_os设备',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_epid_did_ctime` (`enterprise_id`,`device_id`,`count_time`),
  KEY `idx_ctime` (`count_time`)
) ENGINE=InnoDB AUTO_INCREMENT=76462810 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='豹小秘设备画像表';
"""

def check_if_position_exists_in_mysql(device_id: str, db_key="bigdata_cn") -> bool:
    """
    检查在MySQL的 orion_gb_rt_position_by_day 表中是否存在指定rdid和日期的记录。
    """
    sql = """
    SELECT 1 
    FROM orion_gb_rt_position_by_day
    WHERE device_id = %s 
    LIMIT 1
    """
    with get_connection(db_key) as conn:
        with conn.cursor() as cursor:
            cursor.execute(sql, (device_id))
            return cursor.fetchone() is not None

def get_device_position(device_id, db_key="bigdata_cn"):
    sql = """
    SELECT * FROM orion_gb_rt_position_by_day
    WHERE device_id=%s
    ORDER BY count_time DESC, kid DESC
    LIMIT 1
    """
    with get_connection(db_key) as conn:
        with conn.cursor(pymysql.cursors.DictCursor) as cursor:
            cursor.execute(sql, (device_id,))
            return cursor.fetchone()

def get_devices_info(device_ids, db_key="bigdata_cn"):
    logger = logging.getLogger(__name__)
    start_time = time.time()
    logger.info(f"开始查询设备信息，设备数量: {len(device_ids)}")
    
    sql = """
    SELECT 
        p.enterprise_id,
        p.device_id,
        p.version,
        p.model,
        p.rt_province,
        p.rt_city,
        p.rt_distinct,
        p.rt_street,
        p.lat,
        p.lng,
        d.os_type,
        d.device_name,
        d.corp_class_id,
        d.init_bind_time,
        d.bind_time,
        d.d_base_cumulative_active_days,
        d.enterprise_name,
        d.d_base_agency_id,
        d.d_base_agency_name,
        d.d_base_pcate_name,
        d.d_base_cate_name,
        d.d_base_new_cate_name,
        d.d_base_cate_cluster_name,
        d.is_test,
        d.platform,
        d.is_agentOs
    FROM (
        SELECT device_id, MAX(count_time) as max_count_time
        FROM orion_gb_rt_position_by_day
        WHERE device_id IN %s
        AND is_delete != 1
        GROUP BY device_id
    ) latest_pos
    INNER JOIN orion_gb_rt_position_by_day p 
        ON p.device_id = latest_pos.device_id 
        AND p.count_time = latest_pos.max_count_time
        AND p.is_delete != 1
    LEFT JOIN (
        SELECT d.*
        FROM orion_device_profile_gb d
        INNER JOIN (
            SELECT device_id, MAX(count_time) as max_count_time
            FROM orion_device_profile_gb
            WHERE device_id IN %s
            AND is_delete != 1
            GROUP BY device_id
        ) latest_dev ON d.device_id = latest_dev.device_id 
            AND d.count_time = latest_dev.max_count_time
            AND d.is_delete != 1
    ) d ON p.device_id = d.device_id
    """
    try:
        with get_connection(db_key) as conn:
            with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, (device_ids, device_ids))
                rows = cursor.fetchall()
        
        query_time = time.time() - start_time
        logger.info(f"MySQL查询完成，耗时: {query_time:.2f}秒")
        logger.info(f"MySQL返回行数: {len(rows)}")
        
        if rows:
            logger.info(f"样例数据: {rows[0]}")
        
        # 只保留每个device_id最新一条
        latest = {}
        for row in rows:
            did = row['device_id']
            if did not in latest:
                latest[did] = row
        
        logger.info(f"最终合并后设备数: {len(latest)}")
        total_time = time.time() - start_time
        logger.info(f"总处理时间: {total_time:.2f}秒")
        
        return list(latest.values())
    except Exception as e:
        error_time = time.time() - start_time
        logger.error(f"MySQL连接或查询失败，耗时: {error_time:.2f}秒，错误信息: {e}")
        raise


def get_device_position_info(device_ids ,  db_key="bigdata_cn"):
    logger = logging.getLogger(__name__)
    start_time = time.time()
    logger.info(f"开始查询设备位置信息，设备数量: {len(device_ids)}")
    
    # 第一步：查询orion_gb_rt_position_by_day表的数据
    # 使用窗口函数ROW_NUMBER()来为每个device_id分组，并按时间倒序排名，然后只取排名第一的记录
    position_sql = """
    SELECT 
        enterprise_id, device_id, version, model, 
        rt_province, rt_city, rt_distinct, rt_street, 
        lat, lng
    FROM (
        SELECT 
            *,
            ROW_NUMBER() OVER(PARTITION BY device_id ORDER BY count_time DESC, kid DESC) as rn
        FROM orion_gb_rt_position_by_day
        WHERE device_id IN %s
        AND is_delete != 1
    ) as ranked_positions
    WHERE rn = 1
    """
    
    # 第二步：查询orion_device_profile_gb表的最新数据
    profile_sql = """
    SELECT 
        device_id,
        os_type,
        device_name,
        corp_class_id,
        init_bind_time,
        bind_time,
        d_base_cumulative_active_days,
        enterprise_name,
        d_base_agency_id,
        d_base_agency_name,
        d_base_pcate_name,
        d_base_cate_name,
        d_base_new_cate_name,
        d_base_cate_cluster_name,
        is_test,
        platform,
        is_agentOs
    FROM orion_device_profile_gb
    WHERE device_id = %s
    AND enterprise_id = %s
    AND is_delete != 1
    AND count_time = (
        SELECT MAX(count_time)
        FROM orion_device_profile_gb
        WHERE device_id = %s
        AND enterprise_id = %s
        AND is_delete != 1
    )
    LIMIT 1
    """
    
    try:
        with get_connection(db_key) as conn:
            with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                # 执行位置信息查询
                cursor.execute(position_sql, (device_ids,))
                position_data = cursor.fetchall()
                
                # 对每个位置信息查询对应的profile信息
                result = []
                for pos in position_data:
                    device_id = pos['device_id']
                    enterprise_id = pos['enterprise_id']
                    
                    # 查询对应的profile信息
                    cursor.execute(profile_sql, (device_id, enterprise_id, device_id, enterprise_id))
                    profile_data = cursor.fetchone()
                    
                    if profile_data:
                        # 合并位置信息和profile信息
                        merged_data = {**pos, **profile_data}
                        result.append(merged_data)
                    else:
                        # 如果没有找到profile信息，只添加位置信息
                        logger.info(f"没有找到profile信息，只添加位置信息: {pos}")
                        result.append(pos)
        
        query_time = time.time() - start_time
        logger.info(f"MySQL查询完成，耗时: {query_time:.2f}秒")
        logger.info(f"返回数据条数: {len(result)}")
        
        if result:
            #logger.info(f"查询数据结果: {result}")
            pass
        
        return result
        
    except Exception as e:
        error_time = time.time() - start_time
        logger.error(f"MySQL连接或查询失败，耗时: {error_time:.2f}秒，错误信息: {e}")
        raise


def get_device_position_info_by_enterprise(enterprise_id: str, device_id: str, db_key="bigdata_cn"):
    """
    根据企业ID和设备ID查询设备位置信息
    
    Args:
        enterprise_id: 企业ID
        device_id: 设备ID
        db_key: 数据库连接键
    
    Returns:
        设备信息字典，如果未找到则返回None
    """
    logger = logging.getLogger(__name__)
    start_time = time.time()
    logger.info(f"开始查询设备位置信息，enterprise_id: {enterprise_id}, device_id: {device_id}")
    
    # 查询位置信息
    position_sql = """
    SELECT 
        enterprise_id, device_id, version, model, 
        rt_province, rt_city, rt_distinct, rt_street, 
        lat, lng
    FROM (
        SELECT 
            *,
            ROW_NUMBER() OVER(PARTITION BY device_id ORDER BY count_time DESC, kid DESC) as rn
        FROM orion_gb_rt_position_by_day
        WHERE device_id = %s
        AND enterprise_id = %s
        AND is_delete != 1
    ) as ranked_positions
    WHERE rn = 1
    """
    
    # 查询profile信息
    profile_sql = """
    SELECT 
        device_id,
        os_type,
        device_name,
        corp_class_id,
        init_bind_time,
        bind_time,
        d_base_cumulative_active_days,
        enterprise_name,
        d_base_agency_id,
        d_base_agency_name,
        d_base_pcate_name,
        d_base_cate_name,
        d_base_new_cate_name,
        d_base_cate_cluster_name,
        is_test,
        platform,
        is_agentOs
    FROM orion_device_profile_gb
    WHERE device_id = %s
    AND enterprise_id = %s
    AND is_delete != 1
    AND count_time = (
        SELECT MAX(count_time)
        FROM orion_device_profile_gb
        WHERE device_id = %s
        AND enterprise_id = %s
        AND is_delete != 1
    )
    LIMIT 1
    """
    
    try:
        with get_connection(db_key) as conn:
            with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                # 执行位置信息查询
                cursor.execute(position_sql, (device_id, enterprise_id))
                position_data = cursor.fetchone()
                
                if not position_data:
                    logger.warning(f"未找到位置信息: enterprise_id={enterprise_id}, device_id={device_id}")
                    return None
                
                # 查询对应的profile信息
                cursor.execute(profile_sql, (device_id, enterprise_id, device_id, enterprise_id))
                profile_data = cursor.fetchone()
                
                if profile_data:
                    # 合并位置信息和profile信息
                    result = {**position_data, **profile_data}
                else:
                    # 如果没有找到profile信息，只返回位置信息
                    logger.info(f"没有找到profile信息，只返回位置信息: {position_data}")
                    result = position_data
        
        query_time = time.time() - start_time
        logger.info(f"MySQL查询完成，耗时: {query_time:.2f}秒")
        logger.info(f"返回数据: {result}")
        
        return result
        
    except Exception as e:
        error_time = time.time() - start_time
        logger.error(f"MySQL连接或查询失败，耗时: {error_time:.2f}秒，错误信息: {e}")
        raise

def insert_location_to_mysql(location_data: dict, db_key="bigdata_cn"):
    """
    将从Impala获取的位置数据插入到MySQL的 orion_gb_rt_position_by_day 表中。
    """
    logger = logging.getLogger(__name__)
    # 打印将要插入的数据
    logger.info(f"准备插入到MySQL的数据: {location_data}")

    sql = """
    INSERT INTO orion_gb_rt_position_by_day (
        enterprise_id, device_id, version, model, 
        rt_province, rt_city, rt_distinct, rt_street,
        lat, lng, is_test, is_delete, count_time, gmt_create
    ) VALUES (
        %(enterprise_id)s, %(device_id)s, %(version)s, %(model)s,
        %(rt_province)s, %(rt_city)s, %(rt_distinct)s, %(rt_street)s,
        %(lat)s, %(lng)s, %(is_test)s, %(is_delete)s, %(count_time)s, %(gmt_create)s
    )
    """
    
    params = {
        "enterprise_id": location_data.get("p_corpid"),
        "device_id": location_data.get("p_rdid"),
        "version": location_data.get("p_version"),
        "model": location_data.get("p_model"),
        "rt_province": location_data.get("b_province"),
        "rt_city": location_data.get("b_city"),
        "rt_distinct": location_data.get("b_district"),
        "rt_street": location_data.get("b_street"),
        "lat": location_data.get("b_latitude"),
        "lng": location_data.get("b_longitude"),
        "is_test": 2, # 默认为非测试
        "is_delete": 0, # 默认为未删除
        "count_time": location_data.get("dates"),
        "gmt_create": location_data.get("insert_time")
    }

    # 打印将要执行的SQL语句和参数
    logger.info(f"将要执行的SQL: {sql.strip()}")
    logger.info(f"SQL参数: {params}")

    with get_connection(db_key) as conn:
        with conn.cursor() as cursor:
            try:
                cursor.execute(sql, params)
                conn.commit()
                logger.info(f"成功为 device_id: {params['device_id']} 插入位置数据。")
            except Exception as e:
                logger.error(f"为 device_id: {params['device_id']} 插入数据时失败: {e}")
                conn.rollback()
                raise

def get_device_query_stats_by_day(device_ids: list, start_date: str, db_key="bigdata_cn"):
    """
    查询设备按天的query数据统计
    
    Args:
        device_ids: 设备ID列表
        start_date: 开始日期，格式为 'YYYY-MM-DD'
        db_key: 数据库连接键
    
    Returns:
        查询结果列表
    """
    logger = logging.getLogger(__name__)
    start_time = time.time()
    logger.info(f"开始查询设备query统计，设备数量: {len(device_ids)}，开始日期: {start_date}")
    
    # 构建device_ids的占位符
    placeholders = ','.join(['%s'] * len(device_ids))
    
    sql = f"""
    SELECT  
        `enterprise_id`,
        `enterprise_name`, 
        device_id,
        count_time,
        SUM(`d_b_interaction_times`) AS query_nums 
    FROM `orion_device_profile_gb` 
    WHERE count_time >= %s 
    AND device_id IN ({placeholders})
    AND `is_agentOs` = 1 
    GROUP BY `enterprise_id`, `enterprise_name`, count_time, device_id
    ORDER BY `enterprise_id`, count_time
    """
    
    try:
        with get_connection(db_key) as conn:
            with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                # 构建参数列表：start_date + device_ids
                params = [start_date] + device_ids
                cursor.execute(sql, params)
                rows = cursor.fetchall()
        
        query_time = time.time() - start_time
        logger.info(f"MySQL查询完成，耗时: {query_time:.2f}秒")
        logger.info(f"MySQL返回行数: {len(rows)}")
        
        if rows:
            logger.info(f"样例数据: {rows[0]}")
        
        total_time = time.time() - start_time
        logger.info(f"总处理时间: {total_time:.2f}秒")
        
        return rows
    except Exception as e:
        error_time = time.time() - start_time
        logger.error(f"MySQL连接或查询失败，耗时: {error_time:.2f}秒，错误信息: {e}")
        raise