from sqlalchemy.orm import Session as DbSession
from sqlalchemy import func
from typing import Optional, List
from datetime import datetime

from app.models.conversation import AosConversationSession, AosConversationMessage
from app.schemas import PaginatedSessionResponse, Session as SessionSchema, Message as MessageSchema

class ConversationService:
    def get_sessions_by_ids(self, db: DbSession, session_ids: List[str]) -> List[SessionSchema]:
        if not session_ids:
            return []

        sessions_db = db.query(AosConversationSession).filter(
            AosConversationSession.session_id.in_(session_ids)
        ).all()

        if not sessions_db:
            return []

        # Get all messages for the found sessions
        messages_db = db.query(AosConversationMessage).filter(
            AosConversationMessage.session_id.in_(session_ids)
        ).order_by(AosConversationMessage.message_timestamp.asc()).all()

        messages_by_session = {}
        for msg in messages_db:
            if msg.session_id not in messages_by_session:
                messages_by_session[msg.session_id] = []
            messages_by_session[msg.session_id].append(MessageSchema.from_orm(msg))

        result_sessions = []
        # session_db in sessions_db sort by session_ids
        session_db_map = {s.session_id: s for s in sessions_db}
        for session_id in session_ids:
            if session_id in session_db_map:
                session_db = session_db_map[session_id]
                session_schema = SessionSchema.from_orm(session_db)
                session_schema.messages = messages_by_session.get(session_db.session_id, [])
                result_sessions.append(session_schema)

        return result_sessions

    def get_conversations(
        self,
        db: DbSession,
        enterprise_id: str,
        device_id: str,
        start_time: Optional[datetime],
        end_time: Optional[datetime],
        page: int,
        page_size: int,
        project_id: Optional[str] = None,
    ) -> PaginatedSessionResponse:
        
        query = db.query(AosConversationSession).filter(
            AosConversationSession.enterprise_id == enterprise_id,
            AosConversationSession.device_id == device_id
        )

        if project_id:
            query = query.filter(AosConversationSession.project_id == project_id)

        if start_time:
            query = query.filter(AosConversationSession.session_start_time >= start_time)
        if end_time:
            query = query.filter(AosConversationSession.session_start_time <= end_time)

        total = query.count()
        valid_query_count = query.filter(AosConversationSession.conversation_turns > 0).count()

        sessions_db = query.order_by(AosConversationSession.session_start_time.desc()).offset((page - 1) * page_size).limit(page_size).all()

        if not sessions_db:
            return PaginatedSessionResponse(total=0, page=page, page_size=page_size, items=[])

        session_ids = [s.session_id for s in sessions_db]
        
        messages_db = db.query(AosConversationMessage).filter(
            AosConversationMessage.session_id.in_(session_ids)
        ).order_by(AosConversationMessage.message_timestamp.asc()).all()

        messages_by_session = {}
        for msg in messages_db:
            if msg.session_id not in messages_by_session:
                messages_by_session[msg.session_id] = []
            messages_by_session[msg.session_id].append(MessageSchema.from_orm(msg))

        result_sessions = []
        for session_db in sessions_db:
            session_schema = SessionSchema.from_orm(session_db)
            session_schema.messages = messages_by_session.get(session_db.session_id, [])
            result_sessions.append(session_schema)
            
        return PaginatedSessionResponse(
            total=total,
            valid_query_count=valid_query_count,
            page=page,
            page_size=page_size,
            items=result_sessions
        )

conversation_service = ConversationService() 