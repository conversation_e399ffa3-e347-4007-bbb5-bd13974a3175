# -*- coding: utf-8 -*-
"""
报告数据服务 - 处理报告生成记录的数据库操作（简化版）
"""

import logging
from typing import Optional, List
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from app.models.report_models import (
    DeviceReport, ReportStatus, ReportType, PromptType
)

logger = logging.getLogger(__name__)


class ReportDataService:
    """报告数据服务类"""
    
    def __init__(self, db: Session):
        """
        初始化报告数据服务
        
        Args:
            db: 数据库会话
        """
        self.db = db
    
    def create_report_record(
        self,
        enterprise_id: str,
        device_id: str,
        report_type: ReportType,
        target_date: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        prompt_version: Optional[str] = None,
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        prompt_type: PromptType = PromptType.SYSTEM,
        model_name: Optional[str] = None
    ) -> DeviceReport:
        """
        创建报告生成记录
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            report_type: 报告类型
            target_date: 目标日期
            start_date: 开始日期(多天报告)
            end_date: 结束日期(多天报告)
            prompt_version: Prompt版本
            custom_system_prompt: 自定义系统Prompt
            custom_user_prompt: 自定义用户Prompt
            prompt_type: Prompt类型
            
        Returns:
            创建的报告记录
        """
        try:
            report = DeviceReport(
                enterprise_id=enterprise_id,
                device_id=device_id,
                report_type=report_type.value,
                status=ReportStatus.GENERATING.value,
                target_date=target_date,
                start_date=start_date,
                end_date=end_date,
                prompt_version=prompt_version,
                custom_system_prompt=custom_system_prompt,
                custom_user_prompt=custom_user_prompt,
                prompt_type=prompt_type.value,
                model_name=model_name,
                generation_start_time=datetime.utcnow()
            )
            
            self.db.add(report)
            self.db.commit()
            self.db.refresh(report)
            
            logger.info(f"创建报告记录成功: ID={report.id}, 企业={enterprise_id}, 设备={device_id}")
            return report
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建报告记录失败: {str(e)}")
            raise
    
    def update_report_completion(
        self,
        report_id: int,
        system_prompt_used: str,
        user_prompt_used: str,
        generated_content: str,
        model_name: str,
        tokens_used: Optional[int] = None,
        chunk_count: int = 0,
        prompt_type: Optional[PromptType] = None
    ) -> DeviceReport:
        """
        更新报告完成信息
        
        Args:
            report_id: 报告ID
            system_prompt_used: 实际使用的系统Prompt
            user_prompt_used: 实际使用的用户Prompt
            generated_content: 生成的报告内容
            model_name: 使用的模型名称
            tokens_used: 使用的Token数量
            chunk_count: 内容块数量
            
        Returns:
            更新后的报告记录
        """
        try:
            report = self.db.query(DeviceReport).filter(DeviceReport.id == report_id).first()
            if not report:
                raise ValueError(f"报告记录不存在: ID={report_id}")
            
            report.status = ReportStatus.COMPLETED.value
            report.generation_end_time = datetime.utcnow()
            report.system_prompt_used = system_prompt_used
            report.user_prompt_used = user_prompt_used
            report.generated_content = generated_content
            report.model_name = model_name
            report.content_length = len(generated_content)
            report.chunk_count = chunk_count
            report.tokens_used = tokens_used
            
            # 更新Prompt类型（如果提供）
            if prompt_type is not None:
                report.prompt_type = prompt_type.value
                logger.info(f"更新Prompt类型: {prompt_type.value}")
            
            # 计算生成耗时
            if report.generation_start_time and report.generation_end_time:
                duration = (report.generation_end_time - report.generation_start_time).total_seconds()
                report.generation_duration = int(duration)
            
            self.db.commit()
            self.db.refresh(report)
            
            logger.info(f"更新报告完成信息成功: ID={report_id}")
            return report
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新报告完成信息失败: {str(e)}")
            raise
    
    def update_report_failure(
        self,
        report_id: int,
        error_message: str
    ) -> DeviceReport:
        """
        更新报告失败信息
        
        Args:
            report_id: 报告ID
            error_message: 错误信息
            
        Returns:
            更新后的报告记录
        """
        try:
            report = self.db.query(DeviceReport).filter(DeviceReport.id == report_id).first()
            if not report:
                raise ValueError(f"报告记录不存在: ID={report_id}")
            
            report.status = ReportStatus.FAILED.value
            report.generation_end_time = datetime.utcnow()
            report.error_message = error_message
            
            # 计算生成耗时
            if report.generation_start_time and report.generation_end_time:
                duration = (report.generation_end_time - report.generation_start_time).total_seconds()
                report.generation_duration = int(duration)
            
            self.db.commit()
            self.db.refresh(report)
            
            logger.info(f"更新报告失败信息成功: ID={report_id}")
            return report
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新报告失败信息失败: {str(e)}")
            raise
    
    def get_report_by_id(self, report_id: int) -> Optional[DeviceReport]:
        """
        根据ID获取报告记录
        
        Args:
            report_id: 报告ID
            
        Returns:
            报告记录
        """
        return self.db.query(DeviceReport).filter(DeviceReport.id == report_id).first()
    
    def get_reports_by_device(
        self,
        enterprise_id: str,
        device_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[DeviceReport]:
        """
        获取设备的报告记录列表
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            报告记录列表
        """
        return self.db.query(DeviceReport).filter(
            and_(
                DeviceReport.enterprise_id == enterprise_id,
                DeviceReport.device_id == device_id
            )
        ).order_by(desc(DeviceReport.created_at)).offset(offset).limit(limit).all()
    
    def get_reports_by_date_range(
        self,
        enterprise_id: str,
        device_id: str,
        start_date: str,
        end_date: str
    ) -> List[DeviceReport]:
        """
        根据日期范围获取报告记录
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            报告记录列表
        """
        return self.db.query(DeviceReport).filter(
            and_(
                DeviceReport.enterprise_id == enterprise_id,
                DeviceReport.device_id == device_id,
                DeviceReport.target_date >= start_date,
                DeviceReport.target_date <= end_date
            )
        ).order_by(desc(DeviceReport.created_at)).all()
    
    def get_reports_by_prompt_version(
        self,
        prompt_version: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[DeviceReport]:
        """
        根据Prompt版本获取报告记录
        
        Args:
            prompt_version: Prompt版本
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            报告记录列表
        """
        return self.db.query(DeviceReport).filter(
            DeviceReport.prompt_version == prompt_version
        ).order_by(desc(DeviceReport.created_at)).offset(offset).limit(limit).all()
    
    def get_reports_by_prompt_type(
        self,
        prompt_type: PromptType,
        limit: int = 50,
        offset: int = 0
    ) -> List[DeviceReport]:
        """
        根据Prompt类型获取报告记录
        
        Args:
            prompt_type: Prompt类型
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            报告记录列表
        """
        return self.db.query(DeviceReport).filter(
            DeviceReport.prompt_type == prompt_type
        ).order_by(desc(DeviceReport.created_at)).offset(offset).limit(limit).all()
    
    def update_chunk_count(self, report_id: int, chunk_count: int) -> DeviceReport:
        """
        更新内容块数量
        
        Args:
            report_id: 报告ID
            chunk_count: 内容块数量
            
        Returns:
            更新后的报告记录
        """
        try:
            report = self.db.query(DeviceReport).filter(DeviceReport.id == report_id).first()
            if not report:
                raise ValueError(f"报告记录不存在: ID={report_id}")
            
            report.chunk_count = chunk_count
            
            self.db.commit()
            self.db.refresh(report)
            
            return report
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新内容块数量失败: {str(e)}")
            raise 