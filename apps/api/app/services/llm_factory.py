# -*- coding: utf-8 -*-
"""
LLM服务工厂 - 支持多种LLM提供商
"""

import logging
from typing import Optional, Dict, Any
import openai
import httpx
import json

from app.config.llm_models import LLMModelConfig, ModelProvider, ModelType

logger = logging.getLogger(__name__)


class LLMServiceFactory:
    """LLM服务工厂类"""
    
    @staticmethod
    def create_llm_service(model_name: str) -> Optional['BaseLLMService']:
        """
        创建LLM服务实例
        
        Args:
            model_name: 模型名称
            
        Returns:
            LLM服务实例，如果创建失败则返回None
        """
        try:
            config = LLMModelConfig.get_model_config(model_name)
            if not config:
                logger.error(f"未找到模型配置: {model_name}")
                return None
            
            provider = config["provider"]
            
            if provider == ModelProvider.OPENAI.value:
                return OpenAIService(model_name, config)
            elif provider == ModelProvider.GOOGLE.value:
                return GoogleGeminiService(model_name, config)
            elif provider == ModelProvider.ANTHROPIC.value:
                return AnthropicService(model_name, config)
            elif provider == ModelProvider.MOCK.value:
                return MockLLMService(model_name, config)
            else:
                logger.error(f"不支持的LLM提供商: {provider}")
                return None
                
        except Exception as e:
            logger.error(f"创建LLM服务失败: {str(e)}")
            return None


class BaseLLMService:
    """LLM服务基类"""
    
    def __init__(self, model_name: str, config: Dict[str, Any]):
        self.model_name = model_name
        self.config = config
        self.provider = config["provider"]
        self.api_key = LLMModelConfig.get_model_api_key(model_name)
        self.base_url = config["base_url"]
        self.max_tokens = config["max_tokens"]
        self.temperature = config["temperature"]
        
    async def generate_report_stage(self, system_prompt: str, user_prompt: str, stage_info: Dict[str, Any]):
        """生成报告阶段 - 子类必须实现"""
        raise NotImplementedError
    
    async def generate_simple_response(self, system_prompt: str, user_prompt: str) -> str:
        """生成简单响应 - 子类必须实现"""
        raise NotImplementedError


class OpenAIService(BaseLLMService):
    """OpenAI服务实现"""
    
    def __init__(self, model_name: str, config: Dict[str, Any]):
        super().__init__(model_name, config)
        
        try:
            self.client = openai.AsyncOpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
            logger.info(f"OpenAI客户端初始化成功: {model_name}")
        except Exception as e:
            logger.error(f"OpenAI客户端初始化失败: {str(e)}")
            self.client = None
    
    async def generate_report_stage(self, system_prompt: str, user_prompt: str, stage_info: Dict[str, Any]):
        """生成报告阶段"""
        try:
            stage_name = stage_info.get('name', 'Unknown')
            logger.info(f"开始生成报告阶段: {stage_name} (OpenAI: {self.model_name})")
            
            if self.client is None:
                error_msg = f"❌ OpenAI客户端未初始化，无法生成 {stage_name}\n\n"
                logger.error("OpenAI客户端未初始化")
                yield (error_msg, None)
                return
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            try:
                response = await self.client.chat.completions.create(
                    model=self.config["model_name"],
                    messages=messages,
                    stream=True,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                    #max_tokens=4096
                )
                logger.info(f"🔄 开始调用 {self.model_name} 生成内容...")
            except Exception as api_error:
                logger.error(f"❌ OpenAI API请求失败: {str(api_error)}")
                raise api_error
            
            chunk_count = 0
            content_chunks = 0
            tokens_used = 0
            
            try:
                async for chunk in response:
                    chunk_count += 1
                    
                    if hasattr(chunk, 'choices') and chunk.choices:
                        choice = chunk.choices[0]
                        if hasattr(choice, 'delta') and hasattr(choice.delta, 'content'):
                            if choice.delta.content:
                                content_chunks += 1
                                if content_chunks == 1:
                                    logger.info("📝 开始接收OpenAI内容...")
                                yield (choice.delta.content, None)
                
                # 获取token使用量
                if hasattr(response, 'usage'):
                    usage = response.usage
                    if hasattr(usage, 'total_tokens'):
                        tokens_used = usage.total_tokens
                    elif hasattr(usage, 'prompt_tokens') and hasattr(usage, 'completion_tokens'):
                        tokens_used = usage.prompt_tokens + usage.completion_tokens
                
                logger.info(f"✅ OpenAI内容生成完成 (总块数: {chunk_count}, 内容块: {content_chunks}, tokens: {tokens_used})")
                yield (None, {"tokens_used": tokens_used, "chunk_count": chunk_count, "content_chunks": content_chunks})
                
            except Exception as stream_error:
                logger.error(f"❌ OpenAI流式响应处理失败: {str(stream_error)}")
                raise stream_error
                    
        except Exception as e:
            stage_name = stage_info.get('name', 'Unknown')
            logger.error(f"OpenAI生成报告失败 - {stage_name}: {str(e)}")
            error_msg = f"❌ 生成 {stage_name} 时发生错误: {str(e)}\n\n"
            yield (error_msg, None)
    
    async def generate_simple_response(self, system_prompt: str, user_prompt: str) -> str:
        """生成简单响应"""
        try:
            if self.client is None:
                logger.error("OpenAI客户端未初始化")
                return "❌ OpenAI客户端未初始化，无法生成响应"
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.client.chat.completions.create(
                model=self.config["model_name"],
                messages=messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens
                #max_tokens=4096
            )
            
            if hasattr(response, 'choices') and response.choices:
                choice = response.choices[0]
                if hasattr(choice, 'message') and hasattr(choice.message, 'content'):
                    return choice.message.content
            
            return "❌ 无法解析OpenAI API响应"
            
        except Exception as e:
            logger.error(f"OpenAI生成响应失败: {str(e)}")
            return f"生成响应时发生错误: {str(e)}"


class GoogleGeminiService(BaseLLMService):
    """Google Gemini服务实现"""
    
    def __init__(self, model_name: str, config: Dict[str, Any]):
        super().__init__(model_name, config)
        # 增加超时时间，特别是读取超时
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(
                connect=30.0,  # 连接超时
                read=300.0,    # 读取超时 - 增加到5分钟
                write=30.0,    # 写入超时
                pool=30.0      # 连接池超时
            )
        )
    
    async def generate_report_stage(self, system_prompt: str, user_prompt: str, stage_info: Dict[str, Any]):
        """生成报告阶段"""
        try:
            stage_name = stage_info.get('name', 'Unknown')
            logger.info(f"开始生成报告阶段: {stage_name} (Gemini: {self.model_name})")
            
            # 构建Gemini流式API请求
            url = f"{self.base_url}/models/{self.config['model_name']}:streamGenerateContent?alt=sse"
            headers = {
                "Content-Type": "application/json",
                "x-goog-api-key": self.api_key
            }
            
            # Gemini的消息格式 - 合并系统提示词和用户提示词，并添加标记
            if system_prompt:
                combined_prompt = f"[SYSTEM]\n{system_prompt}\n\n[USER]\n{user_prompt}"
            else:
                combined_prompt = f"[USER]\n{user_prompt}"
            
            data = {
                "contents": [
                    {
                        "parts": [
                            {"text": combined_prompt}
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": self.temperature,
                    "maxOutputTokens": min(self.max_tokens, 8192)  # 限制最大输出token
                }
            }
            
            try:
                logger.info(f"🔄 开始调用 {self.model_name} 生成内容...")
                logger.info(f"请求URL: {url}")
                logger.info(f"请求头: {headers}")
                #logger.info(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                
                async with self.client.stream("POST", url, headers=headers, json=data) as response:
                    logger.info(f"响应状态码: {response.status_code}")
                    response.raise_for_status()
                    
                    chunk_count = 0
                    content_chunks = 0
                    tokens_used = 0
                    
                    async for line in response.aiter_lines():
                        chunk_count += 1
                        
                        if line.startswith("data: "):
                            data_str = line[6:]
                            if data_str == "[DONE]":
                                break
                            
                            try:
                                chunk_data = json.loads(data_str)
                                
                                # 解析Gemini流式响应
                                if "candidates" in chunk_data and chunk_data["candidates"]:
                                    candidate = chunk_data["candidates"][0]
                                    if "content" in candidate and "parts" in candidate["content"]:
                                        content_parts = candidate["content"]["parts"]
                                        for part in content_parts:
                                            if "text" in part:
                                                text = part["text"]
                                                if text:
                                                    content_chunks += 1
                                                    if content_chunks == 1:
                                                        logger.info("📝 开始接收Gemini内容...")
                                                    yield (text, None)
                                
                                # 获取token使用量
                                if "usageMetadata" in chunk_data:
                                    usage = chunk_data["usageMetadata"]
                                    tokens_used = usage.get("totalTokenCount", 0)
                                    
                            except json.JSONDecodeError:
                                continue
                    
                    logger.info(f"✅ Gemini内容生成完成 (总块数: {chunk_count}, 内容块: {content_chunks}, tokens: {tokens_used})")
                    yield (None, {"tokens_used": tokens_used, "chunk_count": chunk_count, "content_chunks": content_chunks})
                
            except Exception as api_error:
                logger.error(f"❌ Gemini API请求失败: {str(api_error)}")
                logger.error(f"错误类型: {type(api_error).__name__}")
                if hasattr(api_error, 'response'):
                    logger.error(f"响应状态码: {api_error.response.status_code}")
                    try:
                        error_text = api_error.response.text
                        logger.error(f"响应内容: {error_text}")
                        # 尝试解析错误详情
                        try:
                            error_json = api_error.response.json()
                            logger.error(f"错误详情: {json.dumps(error_json, ensure_ascii=False, indent=2)}")
                        except:
                            pass
                    except:
                        logger.error("无法读取响应内容")
                raise api_error
                    
        except Exception as e:
            stage_name = stage_info.get('name', 'Unknown')
            logger.error(f"Gemini生成报告失败 - {stage_name}: {str(e)}")
            error_msg = f"❌ 生成 {stage_name} 时发生错误: {str(e)}\n\n"
            yield (error_msg, None)
    
    async def generate_simple_response(self, system_prompt: str, user_prompt: str) -> str:
        """生成简单响应"""
        try:
            url = f"{self.base_url}/models/{self.config['model_name']}:generateContent"
            headers = {
                "Content-Type": "application/json",
                "x-goog-api-key": self.api_key
            }
            
            # Gemini的消息格式 - 合并系统提示词和用户提示词，并添加标记
            if system_prompt:
                combined_prompt = f"[SYSTEM]\n{system_prompt}\n\n[USER]\n{user_prompt}"
            else:
                combined_prompt = f"[USER]\n{user_prompt}"
            
            data = {
                "contents": [
                    {
                        "parts": [
                            {"text": combined_prompt}
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": self.temperature,
                    "maxOutputTokens": min(self.max_tokens, 8192)  # 限制最大输出token
                }
            }
            
            response = await self.client.post(url, headers=headers, json=data)
            response.raise_for_status()
            result = response.json()
            
            if "candidates" in result and result["candidates"]:
                candidate = result["candidates"][0]
                if "content" in candidate and "parts" in candidate["content"]:
                    content_parts = candidate["content"]["parts"]
                    for part in content_parts:
                        if "text" in part:
                            return part["text"]
            
            return "❌ 无法解析Gemini API响应"
            
        except Exception as e:
            logger.error(f"Gemini生成响应失败: {str(e)}")
            return f"生成响应时发生错误: {str(e)}"


class AnthropicService(BaseLLMService):
    """Anthropic Claude服务实现"""
    
    def __init__(self, model_name: str, config: Dict[str, Any]):
        super().__init__(model_name, config)
        # 增加超时时间，特别是读取超时
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(
                connect=30.0,  # 连接超时
                read=300.0,    # 读取超时 - 增加到5分钟
                write=30.0,    # 写入超时
                pool=30.0      # 连接池超时
            )
        )
    
    async def generate_report_stage(self, system_prompt: str, user_prompt: str, stage_info: Dict[str, Any]):
        """生成报告阶段"""
        try:
            stage_name = stage_info.get('name', 'Unknown')
            logger.info(f"开始生成报告阶段: {stage_name} (Claude: {self.model_name})")
            
            url = f"{self.base_url}/v1/messages"
            headers = {
                "Content-Type": "application/json",
                "x-api-key": self.api_key,
                "anthropic-version": "2023-06-01"
            }
            
            data = {
                "model": self.config["model_name"],
                "max_tokens": self.max_tokens,
                "temperature": self.temperature,
                "system": system_prompt,
                "messages": [{"role": "user", "content": user_prompt}],
                "stream": True
            }
            
            try:
                async with self.client.stream("POST", url, headers=headers, json=data) as response:
                    response.raise_for_status()
                    
                    chunk_count = 0
                    content_chunks = 0
                    tokens_used = 0
                    
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data_str = line[6:]
                            if data_str == "[DONE]":
                                break
                            
                            try:
                                chunk_data = json.loads(data_str)
                                if "delta" in chunk_data and "text" in chunk_data["delta"]:
                                    text = chunk_data["delta"]["text"]
                                    if text:
                                        content_chunks += 1
                                        if content_chunks == 1:
                                            logger.info("📝 开始接收Claude内容...")
                                        yield (text, None)
                                
                                if "usage" in chunk_data:
                                    usage = chunk_data["usage"]
                                    tokens_used = usage.get("output_tokens", 0)
                                    
                            except json.JSONDecodeError:
                                continue
                    
                    logger.info(f"✅ Claude内容生成完成 (总块数: {chunk_count}, 内容块: {content_chunks}, tokens: {tokens_used})")
                    yield (None, {"tokens_used": tokens_used, "chunk_count": chunk_count, "content_chunks": content_chunks})
                
            except Exception as api_error:
                logger.error(f"❌ Claude API请求失败: {str(api_error)}")
                raise api_error
                    
        except Exception as e:
            stage_name = stage_info.get('name', 'Unknown')
            logger.error(f"Claude生成报告失败 - {stage_name}: {str(e)}")
            error_msg = f"❌ 生成 {stage_name} 时发生错误: {str(e)}\n\n"
            yield (error_msg, None)
    
    async def generate_simple_response(self, system_prompt: str, user_prompt: str) -> str:
        """生成简单响应"""
        try:
            url = f"{self.base_url}/v1/messages"
            headers = {
                "Content-Type": "application/json",
                "x-api-key": self.api_key,
                "anthropic-version": "2023-06-01"
            }
            
            data = {
                "model": self.config["model_name"],
                "max_tokens": self.max_tokens,
                "temperature": self.temperature,
                "system": system_prompt,
                "messages": [{"role": "user", "content": user_prompt}]
            }
            
            response = await self.client.post(url, headers=headers, json=data)
            response.raise_for_status()
            result = response.json()
            
            if "content" in result and result["content"]:
                for content_item in result["content"]:
                    if content_item["type"] == "text":
                        return content_item["text"]
            
            return "❌ 无法解析Claude API响应"
            
        except Exception as e:
            logger.error(f"Claude生成响应失败: {str(e)}")
            return f"生成响应时发生错误: {str(e)}"


class MockLLMService(BaseLLMService):
    """模拟LLM服务 - 用于测试"""
    
    def __init__(self, model_name: str, config: Dict[str, Any]):
        super().__init__(model_name, config)
        logger.info(f"初始化模拟LLM服务: {model_name}")
    
    async def generate_report_stage(self, system_prompt: str, user_prompt: str, stage_info: Dict[str, Any]):
        """生成报告阶段 - 模拟实现"""
        try:
            stage_name = stage_info.get('name', 'Unknown')
            logger.info(f"开始生成报告阶段: {stage_name} (Mock: {self.model_name})")
            
            # 模拟生成内容
            mock_content = f"""
# {stage_name} 分析报告

## 概述
这是使用 {self.model_name} 模型生成的 {stage_name} 分析内容。

## 主要发现
- 基于提供的聊天数据进行了深入分析
- 识别了关键的业务模式和趋势
- 提供了具体的改进建议

## 数据洞察
根据分析，发现了以下重要信息：
1. 用户交互模式分析
2. 服务效率评估
3. 用户体验优化建议

## 建议
- 持续监控关键指标
- 优化服务流程
- 提升用户体验

---
*此报告由 {self.model_name} 模型生成*
"""
            
            # 模拟流式输出
            import asyncio
            for i, line in enumerate(mock_content.split('\n')):
                if line.strip():
                    yield (line + '\n', None)
                    await asyncio.sleep(0.1)  # 模拟网络延迟
            
            # 模拟token使用量
            tokens_used = len(mock_content.split()) * 1.3  # 粗略估算
            logger.info(f"✅ Mock内容生成完成 (tokens: {tokens_used:.0f})")
            yield (None, {"tokens_used": int(tokens_used), "chunk_count": 1, "content_chunks": 1})
                    
        except Exception as e:
            stage_name = stage_info.get('name', 'Unknown')
            logger.error(f"Mock生成报告失败 - {stage_name}: {str(e)}")
            error_msg = f"❌ 生成 {stage_name} 时发生错误: {str(e)}\n\n"
            yield (error_msg, None)
    
    async def generate_simple_response(self, system_prompt: str, user_prompt: str) -> str:
        """生成简单响应 - 模拟实现"""
        return f"这是使用 {self.model_name} 模型生成的模拟响应。\n\n系统提示词: {system_prompt[:100]}...\n用户提示词: {user_prompt[:100]}..." 