# -*- coding: utf-8 -*-
"""
设备报告生成服务 - 简化版
专注于核心的报告生成功能
"""

import logging
import os
from typing import AsyncGenerator, Optional
import asyncio
import time

from app.config.report_prompts import ReportPrompts
from app.services.llm_service import LLMService
from app.models.report_models import ReportType, PromptType

logger = logging.getLogger(__name__)


class DeviceReportService:
    """设备报告生成服务"""
    
    def __init__(self, report_data_service=None):
        """
        初始化设备报告服务
        
        Args:
            report_data_service: 报告数据服务实例，用于数据库操作
        """
        self.llm_service = None
        self.report_data_service = report_data_service
    
    def _get_llm_service(self, model_name: str = None):
        """延迟初始化LLM服务"""
        if self.llm_service is None or (model_name and self.llm_service.model_name != model_name):
            # 使用指定的模型名称或默认模型
            self.llm_service = LLMService(model_name=model_name)
        return self.llm_service
    
    def _load_chat_data(self, enterprise_id: str, device_id: str, date: str) -> str:
        """
        从data目录加载指定日期的聊天记录
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID  
            date: 日期 (YYYY-MM-DD格式)
            
        Returns:
            聊天记录文本
        """
        try:
            # 构建文件路径
            data_dir = os.path.join("app", "data", enterprise_id, device_id)
            
            # 查找匹配的文件
            if os.path.exists(data_dir):
                for filename in os.listdir(data_dir):
                    if filename.endswith(f"_chats数据_{date}.txt"):
                        file_path = os.path.join(data_dir, filename)
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        logger.info(f"成功加载聊天数据: {file_path}")
                        return content
            
            # 如果找不到文件，返回空数据提示
            logger.warning(f"未找到日期 {date} 的聊天数据文件")
            return f"未找到企业 {enterprise_id} 设备 {device_id} 在 {date} 的聊天记录数据。"
            
        except Exception as e:
            logger.error(f"加载聊天数据失败: {str(e)}")
            return f"加载聊天数据时发生错误: {str(e)}"
    
    async def generate_single_day_report(
        self,
        enterprise_id: str,
        device_id: str, 
        date: str,
        prompt_version: str = "UNIFIED_PROMPT_V2",
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        model_name: Optional[str] = None,
        report_id: Optional[int] = None
    ) -> AsyncGenerator[tuple, None]:
        """
        生成单天设备使用报告
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            date: 日期 (YYYY-MM-DD格式)
            prompt_version: Prompt版本 ("UNIFIED_PROMPT", "UNIFIED_PROMPT_V2" 或 "MULTI_DAY_PROMPT")
            
        Yields:
            报告内容流
        """
        try:
            logger.info(f"开始生成单天设备报告: 企业={enterprise_id}, 设备={device_id}, 日期={date}, Prompt版本={prompt_version}")
            
            # 记录开始时间
            start_time = time.time()
            
            # 加载聊天数据
            chat_data = self._load_chat_data(enterprise_id, device_id, date)
            
            # 检查数据是否有效
            is_valid_data = (
                chat_data and 
                len(chat_data.strip()) > 0 and
                "未找到企业" not in chat_data and 
                "加载聊天数据时发生错误" not in chat_data
            )
            
            # 发送报告开始信息
            yield (f"# 🚀 设备使用报告（单天）\n\n", None)
            yield (f"**企业ID**: {enterprise_id}\n", None)
            yield (f"**设备ID**: {device_id}\n", None)
            yield (f"**日期**: {date}\n", None)
            yield (f"**Prompt版本**: {prompt_version}\n", None)
            yield (f"**数据状态**: {'✅ 有效' if is_valid_data else '❌ 无效'}\n\n", None)
            yield ("---\n\n", None)
            
            # 如果数据无效，直接返回提示信息
            if not is_valid_data:
                yield (f"⚠️ **数据检查结果**\n\n", None)
                yield (f"在 {date} 未找到有效的聊天记录数据。\n\n", None)
                yield (f"**原始数据内容**:\n", None)
                yield (f"```\n{chat_data}\n```\n\n", None)
                yield (f"**建议操作**:\n", None)
                yield (f"- 检查设备ID和企业ID是否正确\n", None)
                yield (f"- 尝试选择其他日期\n", None)
                yield (f"- 联系技术支持确认数据状态\n\n", None)
                return
            
            # 确定使用的Prompt
            if custom_system_prompt:
                # 使用统一的SYSTEM_PROMPT作为基础，结合自定义内容
                base_system_prompt = ReportPrompts.get_system_prompt()
                system_prompt = f"{base_system_prompt}\n\n【自定义补充要求】\n{custom_system_prompt}"
                prompt_type = PromptType.CUSTOM
                logger.info(f"🔧 单天报告: 使用自定义系统Prompt，长度: {len(system_prompt)}")
            else:
                system_prompt = ReportPrompts.get_system_prompt()
                prompt_type = PromptType.SYSTEM
                logger.info(f"🔧 单天报告: 使用默认系统Prompt，长度: {len(system_prompt)}")
            
            # 创建数据信息
            data_info = ReportPrompts.create_single_day_data_info(
                date=date,
                chat_data=chat_data,
                description=f"以下是 {date} 的设备使用原始数据："
            )
            
            # 确定用户提示词
            if custom_user_prompt:
                # 使用自定义用户提示词，需要替换数据占位符
                user_prompt = custom_user_prompt.replace("{data_description}", data_info.get('description', ''))
                user_prompt = user_prompt.replace("{analysis_data}", data_info.get('data_sources', [{}])[0].get('content', ''))
                prompt_type = PromptType.CUSTOM
            else:
                # 根据版本获取用户提示词
                user_prompt = ReportPrompts.get_unified_prompt(data_info, prompt_version)
                prompt_type = PromptType.SYSTEM
            
            # 打印关键统计信息
            logger.info(f"📊 单天报告生成准备完成:")
            logger.info(f"   - 聊天数据长度: {len(chat_data)} 字符")
            logger.info(f"   - 提示词长度: {len(user_prompt)} 字符")
            logger.info(f"   - 使用的Prompt版本: {prompt_version}")
            logger.info(f"   - 使用的模型: {model_name or '默认模型'}")
            
            # 发送LLM开始标记
            yield ("\n\n🚀 **开始生成AI报告内容**\n\n", None)
            yield ("---\n\n", None)
            
            # 调用LLM生成完整内容
            llm_service = self._get_llm_service(model_name)
            response_content = ""
            chunk_count = 0
            tokens_used = 0
            
            async for result in llm_service.generate_report_stage(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                stage_info={"name": "单天设备使用报告", "prompt_version": prompt_version}
            ):
                content_chunk, token_info = result
                
                if content_chunk is not None:
                    # 这是内容块
                    response_content += content_chunk
                    chunk_count += 1
                    yield (content_chunk, None)
                elif token_info is not None:
                    # 这是token信息
                    tokens_used = token_info.get('tokens_used', 0)
                    logger.info(f"📊 获取到Token使用量: {tokens_used}")
            
            # 更新报告完成信息（如果启用了数据库记录）
            if self.report_data_service and report_id:
                logger.info(f"开始更新报告完成信息: report_id={report_id}, chunk_count={chunk_count}")
                try:
                    self.report_data_service.update_report_completion(
                        report_id=report_id,
                        system_prompt_used=system_prompt,
                        user_prompt_used=user_prompt,
                        generated_content=response_content,
                        model_name=llm_service.model_name,
                        chunk_count=chunk_count,
                        tokens_used=tokens_used,
                        prompt_type=prompt_type
                    )
                    logger.info(f"报告完成信息更新成功: report_id={report_id}, tokens_used={tokens_used}, prompt_type={prompt_type.value}")
                except Exception as e:
                    logger.error(f"更新报告完成信息失败: {str(e)}")
                    raise
            else:
                logger.warning(f"跳过报告完成信息更新: report_data_service={self.report_data_service is not None}, report_id={report_id}")
            
            # 发送LLM结束标记
            yield ("\n\n---\n\n", None)
            yield ("✅ **AI报告内容生成完成**\n\n", None)
            
        except Exception as e:
            logger.error(f"生成单天报告失败: {str(e)}")
            yield (f"\n\n❌ 生成单天报告时发生错误: {str(e)}\n\n", None)

    async def generate_multi_day_report(
        self,
        enterprise_id: str,
        device_id: str,
        start_date: str,
        end_date: str,
        prompt_version: str = "UNIFIED_PROMPT_V2",
        custom_system_prompt: Optional[str] = None,
        custom_user_prompt: Optional[str] = None,
        model_name: Optional[str] = None,
        report_id: Optional[int] = None
    ) -> AsyncGenerator[tuple, None]:
        """
        生成多天设备使用报告
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            start_date: 开始日期 (YYYY-MM-DD格式)
            end_date: 结束日期 (YYYY-MM-DD格式)
            prompt_version: Prompt版本 ("UNIFIED_PROMPT", "UNIFIED_PROMPT_V2" 或 "MULTI_DAY_PROMPT")
            
        Yields:
            报告内容流
        """
        try:
            logger.info(f"开始生成多天设备报告: 企业={enterprise_id}, 设备={device_id}, 日期范围={start_date}至{end_date}, Prompt版本={prompt_version}")
            
            # 记录开始时间
            start_time = time.time()
            
            # 加载多天聊天数据
            data_sources = []
            from datetime import datetime, timedelta
            
            # 生成日期范围
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            current_dt = start_dt
            
            logger.info(f"📅 开始扫描日期范围: {start_date} 至 {end_date}")
            
            while current_dt <= end_dt:
                current_date = current_dt.strftime("%Y-%m-%d")
                logger.info(f"🔍 检查日期: {current_date}")
                
                chat_data = self._load_chat_data(enterprise_id, device_id, current_date)
                
                # 检查数据是否有效（不是错误信息且不为空）
                is_valid_data = (
                    chat_data and 
                    len(chat_data.strip()) > 0 and
                    "未找到企业" not in chat_data and 
                    "加载聊天数据时发生错误" not in chat_data
                )
                
                if is_valid_data:
                    logger.info(f"✅ 找到有效数据: {current_date}, 长度: {len(chat_data)} 字符")
                    data_sources.append({
                        'date': current_date,
                        'content': chat_data
                    })
                else:
                    logger.warning(f"❌ 日期 {current_date} 无有效数据: {chat_data[:100]}...")
                
                current_dt += timedelta(days=1)
            
            logger.info(f"📊 数据扫描完成: 找到 {len(data_sources)} 天的有效数据")
            
            if not data_sources:
                yield (f"# ❌ 多天报告生成失败\n\n", None)
                yield (f"**企业ID**: {enterprise_id}\n", None)
                yield (f"**设备ID**: {device_id}\n", None)
                yield (f"**日期范围**: {start_date} 至 {end_date}\n", None)
                yield (f"**数据状态**: ❌ 无效\n\n", None)
                yield (f"在指定日期范围内未找到任何有效的聊天数据。\n\n", None)
                yield (f"**扫描结果**:\n", None)
                yield (f"- 扫描的日期范围: {start_date} 至 {end_date}\n", None)
                yield (f"- 有效数据天数: 0 天\n", None)
                yield (f"- 所有日期均无有效数据\n\n", None)
                yield (f"**可能的原因**:\n", None)
                yield (f"- 该设备在指定日期范围内没有产生聊天记录\n", None)
                yield (f"- 数据文件不存在或路径错误\n", None)
                yield (f"- 数据文件格式不正确\n\n", None)
                yield (f"**建议操作**:\n", None)
                yield (f"- 检查设备ID和企业ID是否正确\n", None)
                yield (f"- 尝试选择其他日期范围\n", None)
                yield (f"- 联系技术支持确认数据状态\n\n", None)
                yield (f"**调试信息**:\n", None)
                yield (f"- 数据文件路径: app/data/{enterprise_id}/{device_id}/\n", None)
                yield (f"- 文件命名格式: *_chats数据_YYYY-MM-DD.txt\n\n", None)
                return
            
                        # 发送报告开始信息
            yield (f"# 🚀 设备使用报告（多天）\n\n", None)
            yield (f"**企业ID**: {enterprise_id}\n", None)
            yield (f"**设备ID**: {device_id}\n", None)
            yield (f"**日期范围**: {start_date} 至 {end_date}\n", None)
            yield (f"**数据天数**: {len(data_sources)} 天\n", None)
            yield (f"**有效日期**: {', '.join([source['date'] for source in data_sources])}\n", None)
            yield (f"**Prompt版本**: {prompt_version}\n", None)
            yield (f"**数据状态**: ✅ 有效\n\n", None)
            yield ("---\n\n", None)
            
            # 确定使用的Prompt
            if custom_system_prompt:
                # 使用统一的SYSTEM_PROMPT作为基础，结合自定义内容
                base_system_prompt = ReportPrompts.get_system_prompt()
                system_prompt = f"{base_system_prompt}\n\n【自定义补充要求】\n{custom_system_prompt}"
                prompt_type = PromptType.CUSTOM
                logger.info(f"🔧 多天报告: 使用自定义系统Prompt，长度: {len(system_prompt)}")
            else:
                system_prompt = ReportPrompts.get_system_prompt()
                prompt_type = PromptType.SYSTEM
                logger.info(f"🔧 多天报告: 使用默认系统Prompt，长度: {len(system_prompt)}")
            
            # 创建多天数据信息
            data_info = ReportPrompts.create_multi_day_data_info(
                data_sources=data_sources,
                description=f"以下是 {start_date} 至 {end_date} 期间的设备使用原始数据（共{len(data_sources)}天）："
            )
            
            # 确定用户提示词
            if custom_user_prompt:
                # 使用自定义用户提示词，需要替换数据占位符
                user_prompt = custom_user_prompt.replace("{data_description}", data_info.get('description', ''))
                prompt_type = PromptType.CUSTOM
                # 格式化多天数据
                formatted_data = ""
                for source in data_info.get('data_sources', []):
                    formatted_data += f"【{source['date']} 数据】\n{source['content']}\n\n"
                user_prompt = user_prompt.replace("{analysis_data}", formatted_data.strip())
            else:
                # 根据版本获取用户提示词
                user_prompt = ReportPrompts.get_unified_prompt(data_info, prompt_version)
                prompt_type = PromptType.SYSTEM
            
            # 打印关键统计信息
            total_chars = sum(len(source['content']) for source in data_sources)
            logger.info(f"📊 多天报告生成准备完成:")
            logger.info(f"   - 数据天数: {len(data_sources)} 天")
            logger.info(f"   - 有效日期: {[source['date'] for source in data_sources]}")
            logger.info(f"   - 总聊天数据长度: {total_chars} 字符")
            logger.info(f"   - 提示词长度: {len(user_prompt)} 字符")
            logger.info(f"   - 使用的Prompt版本: {prompt_version}")
            logger.info(f"   - 使用的模型: {model_name or '默认模型'}")
            
            # 发送LLM开始标记
            yield ("\n\n🚀 **开始生成AI报告内容**\n\n", None)
            yield ("---\n\n", None)
            
            # 调用LLM生成完整内容
            llm_service = self._get_llm_service(model_name)
            response_content = ""
            chunk_count = 0
            tokens_used = 0
            
            async for result in llm_service.generate_report_stage(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                stage_info={"name": "多天设备使用报告", "prompt_version": prompt_version}
            ):
                content_chunk, token_info = result
                
                if content_chunk is not None:
                    # 这是内容块
                    response_content += content_chunk
                    chunk_count += 1
                    yield (content_chunk, None)
                elif token_info is not None:
                    # 这是token信息
                    tokens_used = token_info.get('tokens_used', 0)
                    logger.info(f"📊 获取到多天报告Token使用量: {tokens_used}")
            
            # 更新报告完成信息（如果启用了数据库记录）
            if self.report_data_service and report_id:
                logger.info(f"开始更新多天报告完成信息: report_id={report_id}, chunk_count={chunk_count}")
                try:
                    self.report_data_service.update_report_completion(
                        report_id=report_id,
                        system_prompt_used=system_prompt,
                        user_prompt_used=user_prompt,
                        generated_content=response_content,
                        model_name=llm_service.model_name,
                        chunk_count=chunk_count,
                        tokens_used=tokens_used,
                        prompt_type=prompt_type
                    )
                    logger.info(f"多天报告完成信息更新成功: report_id={report_id}, tokens_used={tokens_used}, prompt_type={prompt_type.value}")
                except Exception as e:
                    logger.error(f"更新多天报告完成信息失败: {str(e)}")
                    raise
            else:
                logger.warning(f"跳过多天报告完成信息更新: report_data_service={self.report_data_service is not None}, report_id={report_id}")
            
            # 发送LLM结束标记
            yield ("\n\n---\n\n", None)
            yield ("✅ **AI报告内容生成完成**\n\n", None)
            
        except Exception as e:
            logger.error(f"生成多天报告失败: {str(e)}")
            yield (f"\n\n❌ 生成多天报告时发生错误: {str(e)}\n\n", None)

    async def generate_report(
        self,
        enterprise_id: str,
        device_id: str, 
        date: str,
        prompt_version: str = "UNIFIED_PROMPT_V2"
    ) -> AsyncGenerator[tuple, None]:
        """
        生成设备使用报告（兼容方法，默认单天）
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            date: 日期 (YYYY-MM-DD格式)
            prompt_version: Prompt版本 ("UNIFIED_PROMPT", "UNIFIED_PROMPT_V2" 或 "MULTI_DAY_PROMPT")
            
        Yields:
            报告内容流
        """
        # 调用单天报告生成方法
        async for chunk in self.generate_single_day_report(
            enterprise_id=enterprise_id,
            device_id=device_id,
            date=date,
            prompt_version=prompt_version
        ):
            yield chunk


# 创建单例实例
device_report_service = DeviceReportService() 