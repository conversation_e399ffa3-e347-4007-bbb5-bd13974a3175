import logging
from datetime import datetime
from sqlalchemy import text

logger = logging.getLogger(__name__)

def get_latest_location(conn, rdid: str, today_str: str) -> dict | None:
    """
    从Impala的data_gb_location获取指定RDID的最新位置数据。
    """
    query = text(f"""
        SELECT * FROM bi_gb.data_gb_location 
        WHERE p_rdid = :rdid AND dates <= :today_str
        ORDER BY insert_time DESC 
        LIMIT 1
    """)
    cursor = conn.execute(query, {"rdid": rdid, "today_str": today_str})
    data = cursor.fetchone()
    if not data:
        return None
    
    # fetchone() for SQLAlchemy 2.0 returns a Row object which can be accessed like a named tuple or dict
    return data._asdict()
