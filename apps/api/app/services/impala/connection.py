import logging
from sqlalchemy import create_engine
from contextlib import contextmanager
from ...config.impala_config import IMPALA_CONNECTIONS

logger = logging.getLogger(__name__)

def get_impala_engine(db_key="default"):
    """
    根据db_key获取一个SQLAlchemy引擎。
    """
    if db_key not in IMPALA_CONNECTIONS:
        raise ValueError(f"在 impala_config.py 中未找到 '{db_key}' 的Impala连接配置")
    
    config = IMPALA_CONNECTIONS[db_key]
    
    # 准备连接参数 - 只使用Impala驱动支持的参数
    connect_args = {
        'auth_mechanism': config.get('auth_mechanism', 'NOSASL'),
        'user': config.get('user'),
        'password': config.get('password'),
        'timeout': config.get('timeout', 60),
        'use_ssl': config.get('use_ssl', False),
        'ca_cert': config.get('ca_cert'),
        'ldap_base_dn': config.get('ldap_base_dn'),
        'ldap_bind_pattern': config.get('ldap_bind_pattern')
    }

    # 只传递非None的参数给驱动
    filtered_connect_args = {k: v for k, v in connect_args.items() if v is not None}

    try:
        logger.info(f"尝试连接到Impala: {config['host']}:{config['port']}")
        logger.info(f"连接参数: {filtered_connect_args}")
        
        # 创建连接字符串
        connection_string = f"impala://{config['host']}:{config['port']}/{config.get('database', '')}"
        
        # 创建引擎时添加连接池配置
        engine = create_engine(
            connection_string,
            connect_args=filtered_connect_args,
            # 连接池配置
            pool_size=5,  # 连接池大小
            max_overflow=10,  # 最大溢出连接数
            pool_timeout=30,  # 连接池超时时间
            pool_recycle=3600,  # 连接回收时间（1小时）
            pool_pre_ping=True,  # 连接前ping测试
            # 其他重要参数
            echo=False,  # 不显示SQL语句
            future=True  # 使用SQLAlchemy 2.0风格
        )
        return engine
    except Exception as e:
        logger.error(f"创建Impala引擎失败 ({db_key}): {e}", exc_info=True)
        raise

@contextmanager
def get_impala_connection(db_key="default"):
    """
    提供一个Impala数据库连接的上下文管理器。
    """
    engine = get_impala_engine(db_key)
    connection = None
    try:
        connection = engine.connect()
        logger.info(f"成功获取到Impala数据库连接 ({db_key})。")
        yield connection
    except Exception as e:
        logger.error(f"无法连接到Impala数据库 ({db_key}): {e}", exc_info=True)
        # 提供更详细的错误信息
        if "TSocket read 0 bytes" in str(e):
            logger.error("这通常表示网络连接问题，请检查：")
            logger.error("1. Impala服务器是否正在运行")
            logger.error("2. 网络连接是否正常")
            logger.error("3. 防火墙是否阻止了连接")
            logger.error("4. 端口21050是否可访问")
        raise
    finally:
        if connection:
            try:
                connection.close()
                logger.info(f"Impala数据库连接已关闭 ({db_key})。")
            except Exception as e:
                logger.warning(f"关闭Impala连接时出现警告: {e}") 