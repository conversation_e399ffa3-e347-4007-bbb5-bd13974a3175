import logging
from datetime import datetime, timedelta
from typing import Dict, List, <PERSON>ple
from collections import defaultdict, Counter
from sqlalchemy import and_, UniqueConstraint
import jieba
from app.database import get_agentos_db, get_speech_ai_robot_db
from app.models.conversation import AosConversationSession, AosConversationMessage
from app.models.statistics import (
    AosStatSessionBehaviorHourly,
    AosStatMessageBehaviorHourly,
    AosStatActionBehaviorHourly,
    AosStatEventBehaviorHourly,
    AosStatUserPreferenceDetailHourly,
    AosStatSessionDurationDistributionHourly,
    AosStatSessionIntervalDistributionHourly,
    AosStatActiveHoursHourly,
    AosStatUserQuestionTopNHourly
)

logger = logging.getLogger(__name__)

class StatisticsService:
    """机器人对话统计服务"""
    
    def __init__(self):
        self.greeting_keywords = ["你好", "您好", "欢迎", "hello", "hi", "welcome"]
    
    def _get_hour_range(self, target_hour: datetime) -> <PERSON><PERSON>[datetime, datetime]:
        """获取指定小时的时间范围"""
        start_time = target_hour.replace(minute=0, second=0, microsecond=0)
        end_time = start_time + timedelta(hours=1)
        return start_time, end_time
    
    def _get_session_data(self, start_time: datetime, end_time: datetime, enterprise_id: str = None, device_id: str = None) -> List[Dict]:
        """获取指定时间范围内的会话数据"""
        with next(get_agentos_db()) as db:
            query = db.query(AosConversationSession).filter(
                and_(
                    AosConversationSession.session_start_time >= start_time,
                    AosConversationSession.session_start_time < end_time
                )
            )
            
            # 添加企业ID和设备ID过滤
            if enterprise_id:
                query = query.filter(AosConversationSession.enterprise_id == enterprise_id)
            if device_id:
                query = query.filter(AosConversationSession.device_id == device_id)
            
            sessions = query.all()
            
            return [
                {
                    'session_id': session.session_id,
                    'enterprise_id': session.enterprise_id,
                    'device_id': session.device_id,
                    'project_id': session.project_id,
                    'conversation_turns': session.conversation_turns,
                    'session_duration_seconds': session.session_duration_seconds,
                    'user_preferences': session.user_preferences,
                    'session_start_time': session.session_start_time,
                    'session_end_time': session.session_end_time
                }
                for session in sessions
            ]
    
    def _get_message_data(self, session_ids: List[str]) -> List[Dict]:
        """获取指定会话的消息数据"""
        if not session_ids:
            return []
            
        with next(get_agentos_db()) as db:
            messages = db.query(AosConversationMessage).filter(
                AosConversationMessage.session_id.in_(session_ids)
            ).order_by(AosConversationMessage.session_id, AosConversationMessage.message_index).all()
            
            return [
                {
                    'session_id': msg.session_id,
                    'message_index': msg.message_index,
                    'role': msg.role,
                    'content': msg.content,
                    'action_data': msg.action_data,
                    'event_data': msg.event_data,
                    'message_timestamp': msg.message_timestamp
                }
                for msg in messages
            ]
    
    def _group_sessions_by_device(self, sessions: List[Dict]) -> Dict[Tuple[str, str], List[Dict]]:
        """按企业ID和设备ID分组会话"""
        grouped = defaultdict(list)
        for session in sessions:
            key = (session['enterprise_id'], session['device_id'])
            grouped[key].append(session)
        return dict(grouped)
    
    def _group_messages_by_session(self, messages: List[Dict]) -> Dict[str, List[Dict]]:
        """按会话ID分组消息"""
        grouped = defaultdict(list)
        for message in messages:
            grouped[message['session_id']].append(message)
        return dict(grouped)
    
    def _calculate_basic_stats(self, sessions: List[Dict], messages_by_session: Dict[str, List[Dict]]) -> Dict:
        """计算基础统计"""
        stats = {
            'session_total': len(sessions),
            'session_ids_session_total': [s['session_id'] for s in sessions],
            'valid_sessions': 0,
            'session_ids_valid_sessions': [],
            'no_response_sessions': 0,
            'session_ids_no_response_sessions': [],
            'first_assistant_sessions': 0,
            'session_ids_first_assistant_sessions': [],
            'first_user_sessions': 0,
            'session_ids_first_user_sessions': [],
            'avg_conversation_turns': 0,
            'avg_session_duration': 0
        }
        
        if not sessions:
            return stats
        
        # 计算平均值
        total_turns = sum(s['conversation_turns'] for s in sessions)
        total_duration = sum(s['session_duration_seconds'] for s in sessions)
        stats['avg_conversation_turns'] = total_turns / len(sessions)
        stats['avg_session_duration'] = total_duration / len(sessions)
        
        for session in sessions:
            session_id = session['session_id']
            session_messages = messages_by_session.get(session_id, [])
            
            if not session_messages:
                continue
            
            # 检查有效会话（有user和assistant消息）
            user_messages = [m for m in session_messages if m['role'] == 'user']
            assistant_messages = [m for m in session_messages if m['role'] == 'assistant']
            
            if user_messages and assistant_messages:
                stats['valid_sessions'] += 1
                stats['session_ids_valid_sessions'].append(session_id)
            elif user_messages and not assistant_messages:
                stats['no_response_sessions'] += 1
                stats['session_ids_no_response_sessions'].append(session_id)
            
            # 检查首条消息类型
            first_message = session_messages[0]
            if first_message['role'] == 'assistant':
                stats['first_assistant_sessions'] += 1
                stats['session_ids_first_assistant_sessions'].append(session_id)
            else:
                stats['first_user_sessions'] += 1
                stats['session_ids_first_user_sessions'].append(session_id)
        
        return stats
    
    def _calculate_message_stats(self, messages: List[Dict]) -> Dict:
        """计算消息统计"""
        stats = {
            'user_msg_count': 0,
            'session_ids_user_msg_count': set(),
            'assistant_msg_count': 0,
            'session_ids_assistant_msg_count': set(),
            'action_trigger_count': 0,
            'session_ids_action_trigger_count': set(),
            'event_trigger_count': 0,
            'session_ids_event_trigger_count': set(),
            'user_msg_lengths': [],
            'assistant_msg_lengths': []
        }
        
        for message in messages:
            if message['role'] == 'user':
                stats['user_msg_count'] += 1
                stats['session_ids_user_msg_count'].add(message['session_id'])
                stats['user_msg_lengths'].append(len(message['content']))
            elif message['role'] == 'assistant':
                stats['assistant_msg_count'] += 1
                stats['session_ids_assistant_msg_count'].add(message['session_id'])
                stats['assistant_msg_lengths'].append(len(message['content']))
            
            if message['action_data']:
                stats['action_trigger_count'] += 1
                stats['session_ids_action_trigger_count'].add(message['session_id'])
            
            if message['event_data']:
                stats['event_trigger_count'] += 1
                stats['session_ids_event_trigger_count'].add(message['session_id'])
        
        # 转换为列表并计算平均长度
        stats['session_ids_user_msg_count'] = list(stats['session_ids_user_msg_count'])
        stats['session_ids_assistant_msg_count'] = list(stats['session_ids_assistant_msg_count'])
        stats['session_ids_action_trigger_count'] = list(stats['session_ids_action_trigger_count'])
        stats['session_ids_event_trigger_count'] = list(stats['session_ids_event_trigger_count'])
        
        stats['avg_user_msg_length'] = sum(stats['user_msg_lengths']) / len(stats['user_msg_lengths']) if stats['user_msg_lengths'] else 0
        stats['avg_assistant_msg_length'] = sum(stats['assistant_msg_lengths']) / len(stats['assistant_msg_lengths']) if stats['assistant_msg_lengths'] else 0
        
        # 移除临时字段，只保留模型需要的字段
        stats.pop('user_msg_lengths', None)
        stats.pop('assistant_msg_lengths', None)
        
        return stats
    
    def _calculate_user_question_stats(self, messages: List[Dict]) -> List[Dict]:
        """计算用户问题TopN明细"""
        user_messages = [m for m in messages if m['role'] == 'user' and m.get('content')]
        
        if not user_messages:
            return []

        # 1. Group questions by content to count occurrences and session ids
        questions = defaultdict(lambda: {'count': 0, 'session_ids': set()})
        for msg in user_messages:
            content = msg['content'].strip()
            if not content: continue
            questions[content]['count'] += 1
            questions[content]['session_ids'].add(msg['session_id'])
            
        # 2. Format results with individual word clouds using jieba
        results = []
        for content, stats in questions.items():
            # Use jieba for word segmentation on the specific question content
            words = jieba.lcut(content)
            # Filter out single-character words to get more meaningful keywords
            meaningful_words = [word for word in words if len(word) > 1]
            wordcloud = dict(Counter(meaningful_words))

            results.append({
                'question_content': content,
                'question_count': stats['count'],
                'session_ids': list(stats['session_ids']),
                'wordcloud_keywords': wordcloud
            })
            
        return results
    
    def _calculate_user_preference_details(self, sessions: List[Dict]) -> List[Dict]:
        """计算用户偏好详情统计"""
        preference_details = []
        
        for session in sessions:
            user_preferences = session.get('user_preferences')
            if not user_preferences:
                continue
            
            # 处理JSON格式的user_preferences
            if isinstance(user_preferences, str):
                try:
                    import json
                    user_preferences = json.loads(user_preferences)
                except:
                    continue
            
            if not isinstance(user_preferences, dict):
                continue
            
            # 提取每个偏好字段
            for field_name, field_value in user_preferences.items():
                if field_value:  # 确保字段值不为空
                    preference_detail = {
                        'field_name': str(field_name),
                        'field_value': str(field_value),
                        'session_id': session['session_id'],
                        'project_id': session.get('project_id', '')
                    }
                    preference_details.append(preference_detail)
        
        return preference_details
    
    def _calculate_action_stats(self, messages: List[Dict]) -> List[Dict]:
        """计算每个动作的统计数据"""
        action_stats = defaultdict(lambda: {'count': 0, 'session_ids': set()})
        
        for msg in messages:
            if not msg.get('action_data'):
                continue
            
            action_data = msg['action_data']
            if isinstance(action_data, str):
                import json
                try:
                    action_data = json.loads(action_data)
                except json.JSONDecodeError:
                    continue
            
            if not isinstance(action_data, dict):
                continue

            action_name = action_data.get('name') or action_data.get('type')
            if not action_name:
                continue

            action_display_name = action_data.get('display_name', action_name)
            key = (action_name, action_display_name)
            
            action_stats[key]['count'] += 1
            action_stats[key]['session_ids'].add(msg['session_id'])

        results = []
        for (name, display_name), stats in action_stats.items():
            results.append({
                'action_name': name,
                'action_display_name': display_name,
                'action_count': stats['count'],
                'session_ids': list(stats['session_ids'])
            })
        
        return results
    
    def _calculate_duration_distribution(self, sessions: List[Dict]) -> Dict:
        """计算会话时长分布"""
        buckets = defaultdict(list)

        for session in sessions:
            duration = session['session_duration_seconds']
            session_id = session['session_id']
            if duration < 30:
                buckets['lt_30s'].append(session_id)
            elif 30 <= duration < 60:
                buckets['30_60s'].append(session_id)
            elif 60 <= duration < 180:
                buckets['1_3min'].append(session_id)
            elif 180 <= duration < 300:
                buckets['3_5min'].append(session_id)
            elif 300 <= duration < 600:
                buckets['5_10min'].append(session_id)
            elif 600 <= duration < 1200:
                buckets['10_20min'].append(session_id)
            else:
                buckets['gt_20min'].append(session_id)

        stats = {
            'bucket_lt_30s': len(buckets['lt_30s']),
            'bucket_30_60s': len(buckets['30_60s']),
            'bucket_1_3min': len(buckets['1_3min']),
            'bucket_3_5min': len(buckets['3_5min']),
            'bucket_5_10min': len(buckets['5_10min']),
            'bucket_10_20min': len(buckets['10_20min']),
            'bucket_gt_20min': len(buckets['gt_20min']),
            'session_ids_duration_bucket': buckets,
        }
        return stats

    def _calculate_interval_distribution(self, sessions: List[Dict], messages_by_session: Dict[str, List[Dict]]) -> Dict:
        """计算发言间隔分布"""
        interval_counts = defaultdict(int)
        interval_session_ids = defaultdict(set)

        for session in sessions:
            session_id = session['session_id']
            # message_timestamp is a datetime object
            session_messages = sorted(messages_by_session.get(session_id, []), key=lambda m: m['message_timestamp'])
            
            for i in range(len(session_messages) - 1):
                msg1 = session_messages[i]
                msg2 = session_messages[i+1]
                interval = (msg2['message_timestamp'] - msg1['message_timestamp']).total_seconds()

                bucket_name = None
                if interval < 10:
                    bucket_name = 'lt_10s'
                elif 10 <= interval < 20:
                    bucket_name = '10_20s'
                elif 60 <= interval < 180:
                    bucket_name = '1_3min'
                elif 180 <= interval < 300:
                    bucket_name = '3_5min'
                elif 300 <= interval < 600:
                    bucket_name = '5_10min'
                elif 600 <= interval < 1200:
                    bucket_name = '10_20min'
                elif interval >= 1200:
                    bucket_name = 'gt_20min'
                
                if bucket_name:
                    interval_counts[bucket_name] += 1
                    interval_session_ids[bucket_name].add(session_id)

        stats = {
            'bucket_lt_10s': interval_counts['lt_10s'],
            'bucket_10_20s': interval_counts['10_20s'],
            'bucket_1_3min': interval_counts['1_3min'],
            'bucket_3_5min': interval_counts['3_5min'],
            'bucket_5_10min': interval_counts['5_10min'],
            'bucket_10_20min': interval_counts['10_20min'],
            'bucket_gt_20min': interval_counts['gt_20min'],
            'session_ids_interval_bucket': {k: list(v) for k, v in interval_session_ids.items()},
        }
        return stats

    def _calculate_event_stats(self, messages: List[Dict]) -> Dict:
        """计算事件行为统计"""
        import re
        event_messages = [m for m in messages if m.get('event_data')]
        
        if not event_messages:
            return {}

        # 1. 按 session_id 分组所有 event 消息
        events_by_session = defaultdict(list)
        for msg in event_messages:
            events_by_session[msg['session_id']].append(msg)

        # 2. 只取每个 session 的最后一条 event
        last_events = []
        for session_id, events in events_by_session.items():
            # 按 message_index 排序，取最后一条
            events_sorted = sorted(events, key=lambda x: x.get('message_index', 0))
            last_events.append(events_sorted[-1])

        path_freq = defaultdict(int)
        target_freq = defaultdict(int)
        session_ids = set()

        for msg in last_events:
            session_ids.add(msg['session_id'])
            event_data = msg['event_data']
            if isinstance(event_data, str):
                import json
                try:
                    event_data = json.loads(event_data)
                except json.JSONDecodeError:
                    print(f"[event_stats] event_data JSONDecodeError: {msg['event_data']}")
                    continue
            if not isinstance(event_data, dict):
                print(f"[event_stats] event_data not dict: {event_data}")
                continue

            desc = event_data.get('desc', '')
            from_scene = None
            to_scene = None
            if desc and desc.startswith('The scene has switched:'):
                match = re.search(r'The scene has switched:\s*(.+?)\s*→\s*(.+)', desc)
                if match:
                    from_scene = match.group(1).strip()
                    to_scene = match.group(2).strip()
            if not from_scene:
                from_scene = event_data.get('from', 'N/A')
            if not to_scene:
                to_scene = event_data.get('to')

            # 临时打印原始数据和适配结果
            print(f"[event_stats] 原始event_data: {event_data}")
            print(f"[event_stats] 解析结果 from_scene: {from_scene}, to_scene: {to_scene}")

            if from_scene and to_scene:
                # 按新逻辑：path_freq 统计 from_scene，target_freq 统计 to_scene
                print(f"[event_stats] 统计 event_path_freq: {from_scene}, target_scene_freq: {to_scene}")
                path_freq[from_scene] += 1
                target_freq[to_scene] += 1
        
        path_freq_str = ", ".join([f"{k}:{v}" for k, v in path_freq.items()])
        target_freq_str = ", ".join([f"{k}:{v}" for k, v in target_freq.items()])

        return {
            'event_total_count': len(event_messages),
            'session_ids_event_total_count': list(session_ids),
            'event_path_freq': path_freq_str,
            'target_scene_freq': target_freq_str
        }

    def _save_statistics(self, hour_bucket: datetime, enterprise_id: str, device_id: str, stats_data: Dict):
        """保存统计数据到数据库"""
        with next(get_speech_ai_robot_db()) as db:
            try:
                query_params = {'hour_bucket': hour_bucket, 'enterprise_id': enterprise_id, 'device_id': device_id}

                # --- 1. 保存或更新会话行为统计 ---
                session_behavior_data = stats_data.get('session_behavior', {})
                session_behavior_obj = db.query(AosStatSessionBehaviorHourly).filter_by(**query_params).first()
                if session_behavior_obj:
                    for key, value in session_behavior_data.items():
                        setattr(session_behavior_obj, key, value)
                else:
                    session_behavior_obj = AosStatSessionBehaviorHourly(**query_params, **session_behavior_data)
                    db.add(session_behavior_obj)

                # --- 2. 保存或更新消息行为统计 ---
                message_behavior_data = stats_data.get('message_behavior', {})
                message_behavior_obj = db.query(AosStatMessageBehaviorHourly).filter_by(**query_params).first()
                if message_behavior_obj:
                    for key, value in message_behavior_data.items():
                        setattr(message_behavior_obj, key, value)
                else:
                    message_behavior_obj = AosStatMessageBehaviorHourly(**query_params, **message_behavior_data)
                    db.add(message_behavior_obj)
                
                # --- 3. 保存动作行为统计 (先删后插) ---
                db.query(AosStatActionBehaviorHourly).filter_by(**query_params).delete(synchronize_session=False)
                for action_stat in stats_data.get('action_stats', []):
                    action_behavior = AosStatActionBehaviorHourly(
                        **query_params,
                        action_name=action_stat['action_name'],
                        action_display_name=action_stat.get('action_display_name'),
                        action_count=action_stat['action_count'],
                        session_ids=action_stat['session_ids']
                    )
                    db.add(action_behavior)

                # --- 4. 保存或更新事件行为统计 (无事件则不保存) ---
                event_behavior_data = stats_data.get('event_behavior', {})
                if event_behavior_data and event_behavior_data.get('event_total_count', 0) > 0:
                    event_behavior_obj = db.query(AosStatEventBehaviorHourly).filter_by(**query_params).first()
                    if event_behavior_obj:
                        for key, value in event_behavior_data.items():
                            setattr(event_behavior_obj, key, value)
                    else:
                        db.add(AosStatEventBehaviorHourly(**query_params, **event_behavior_data))
                else:
                    db.query(AosStatEventBehaviorHourly).filter_by(**query_params).delete(synchronize_session=False)

                # --- 5. 保存用户问题TopN (先删后插) ---
                db.query(AosStatUserQuestionTopNHourly).filter_by(**query_params).delete(synchronize_session=False)
                user_question_data_list = stats_data.get('user_question_stats', [])
                for question_data in user_question_data_list:
                    question_obj = AosStatUserQuestionTopNHourly(
                        hour_bucket=hour_bucket,
                        enterprise_id=enterprise_id,
                        device_id=device_id,
                        question_content=question_data['question_content'],
                        question_count=question_data['question_count'],
                        session_ids=question_data['session_ids'],
                        wordcloud_keywords=question_data['wordcloud_keywords']
                    )
                    db.add(question_obj)
                
                # --- 6. 保存用户偏好详情 (总是新增) ---
                db.query(AosStatUserPreferenceDetailHourly).filter_by(**query_params).delete(synchronize_session=False)
                for preference_detail in stats_data.get('preference_details', []):
                    user_preference = AosStatUserPreferenceDetailHourly(
                        **query_params,
                        project_id=preference_detail.get('project_id', ''),
                        field_name=preference_detail['field_name'],
                        field_value=preference_detail['field_value'],
                        session_id=preference_detail['session_id']
                    )
                    db.add(user_preference)
                
                # --- 7. 保存或更新会话时长分布 ---
                duration_data = stats_data.get('duration_dist', {})
                duration_dist_obj = db.query(AosStatSessionDurationDistributionHourly).filter_by(**query_params).first()
                if duration_dist_obj:
                    for key, value in duration_data.items():
                        setattr(duration_dist_obj, key, value)
                else:
                    duration_dist_obj = AosStatSessionDurationDistributionHourly(**query_params, **duration_data)
                    db.add(duration_dist_obj)
                
                # --- 8. 保存或更新会话间隔分布 ---
                interval_data = stats_data.get('interval_dist', {})
                interval_dist_obj = db.query(AosStatSessionIntervalDistributionHourly).filter_by(**query_params).first()
                if interval_dist_obj:
                    for key, value in interval_data.items():
                        setattr(interval_dist_obj, key, value)
                else:
                    interval_dist_obj = AosStatSessionIntervalDistributionHourly(**query_params, **interval_data)
                    db.add(interval_dist_obj)
                
                # --- 9. 保存或更新活跃小时统计 ---
                active_hours_data = stats_data.get('active_hours_stats', {}) # Needs to be calculated and passed
                active_hours_obj = db.query(AosStatActiveHoursHourly).filter_by(**query_params).first()
                if active_hours_obj:
                     for key, value in active_hours_data.items():
                        setattr(active_hours_obj, key, value)
                else:
                    # Needs a calculation function
                    if active_hours_data:
                        db.add(AosStatActiveHoursHourly(**query_params, **active_hours_data))

                db.commit()
                logger.info(f"✅ 成功保存或更新统计数据: {enterprise_id}-{device_id} @ {hour_bucket}")
                
            except Exception as e:
                db.rollback()
                logger.error(f"❌ 保存统计数据时发生严重错误: {e}", exc_info=True)
                raise
    
    def calculate_statistics_for_hour(self, target_hour: datetime, enterprise_id: str = None, device_id: str = None):
        """计算指定小时的统计数据"""
        start_time, end_time = self._get_hour_range(target_hour)
        
        # 1. 获取会话数据
        sessions = self._get_session_data(start_time, end_time, enterprise_id, device_id)
        if not sessions:
            logger.info(f"时间段 {start_time} - {end_time} 内没有会话数据")
            return {}
        
        # 2. 获取消息数据
        session_ids = [s['session_id'] for s in sessions]
        messages = self._get_message_data(session_ids)
        
        # 3. 按设备分组
        sessions_by_device = self._group_sessions_by_device(sessions)
        messages_by_session = self._group_messages_by_session(messages)
        
        all_device_stats = {}

        # 4. 为每个设备计算统计
        for (current_enterprise_id, current_device_id), device_sessions in sessions_by_device.items():
            try:
                logger.info(f"正在处理设备: {current_enterprise_id}-{current_device_id}, 会话数: {len(device_sessions)}")
                
                # 获取该设备的消息
                device_session_ids = [s['session_id'] for s in device_sessions]
                device_messages = [m for m in messages if m['session_id'] in device_session_ids]
                
                # 计算各项统计
                session_behavior = self._calculate_basic_stats(device_sessions, messages_by_session)
                message_behavior = self._calculate_message_stats(device_messages)
                user_question_stats = self._calculate_user_question_stats(device_messages)
                action_stats = self._calculate_action_stats(device_messages)
                duration_dist = self._calculate_duration_distribution(device_sessions)
                interval_dist = self._calculate_interval_distribution(device_sessions, messages_by_session)
                event_stats = self._calculate_event_stats(device_messages)
                
                # 计算用户偏好详情
                preference_details = self._calculate_user_preference_details(device_sessions)
                
                # 准备所有统计数据
                stats_data = {
                    'session_behavior': session_behavior,
                    'message_behavior': message_behavior,
                    'user_question_stats': user_question_stats,
                    'action_stats': action_stats,
                    'duration_dist': duration_dist,
                    'interval_dist': interval_dist,
                    'preference_details': preference_details,
                    'event_behavior': event_stats, 
                    'active_hours_stats': {
                        'consecutive_active_hours': 1, 
                        'assistant_first_ratio': session_behavior.get('first_assistant_sessions', 0) / session_behavior.get('session_total', 1) if session_behavior.get('session_total', 0) > 0 else 0,
                        'greeting_ratio': 0,
                        'session_ids_active': session_behavior.get('session_ids_session_total', [])
                    }
                }
                
                self._save_statistics(target_hour, current_enterprise_id, current_device_id, stats_data)
                
                all_device_stats[f"{current_enterprise_id}-{current_device_id}"] = stats_data
                
            except Exception as e:
                logger.error(f"处理设备 {current_enterprise_id}-{current_device_id} 时出错: {e}", exc_info=True)
                continue
        
        return all_device_stats

    def calculate_statistics_for_hours(self, hours: List[datetime], enterprise_id: str = None, device_id: str = None):
        """计算多个小时的统计数据"""
        for hour in hours:
            try:
                self.calculate_statistics_for_hour(hour, enterprise_id, device_id)
            except Exception as e:
                logger.error(f"计算 {hour} 统计数据时出错: {e}", exc_info=True)
                continue
 