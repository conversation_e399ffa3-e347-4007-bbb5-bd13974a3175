# -*- coding: utf-8 -*-
"""
LLM服务 - 用于调用AI模型生成报告
支持多种LLM提供商（OpenAI、Google Gemini、Anthropic Claude等）
"""

import logging
from typing import Dict, Any, Optional, AsyncGenerator
import json
import asyncio
from datetime import datetime

from app.services.llm_factory import LLMServiceFactory
from app.config.llm_models import LLMModelConfig, DEFAULT_MODEL

logger = logging.getLogger(__name__)


class LLMService:
    """LLM服务类，负责与AI模型交互"""
    
    def __init__(self, model_name: str = None):
        """
        初始化LLM服务
        
        Args:
            model_name: 模型名称，如果为None则使用默认模型
        """
        self.model_name = model_name or DEFAULT_MODEL
        self.llm_service = None
        self._initialize_service()
    
    def _initialize_service(self):
        """初始化LLM服务实例"""
        try:
            self.llm_service = LLMServiceFactory.create_llm_service(self.model_name)
            if self.llm_service:
                logger.info(f"LLM服务初始化成功: {self.model_name}")
            else:
                logger.error(f"LLM服务初始化失败: {self.model_name}")
        except Exception as e:
            logger.error(f"LLM服务初始化异常: {str(e)}")
            self.llm_service = None
        
    async def generate_report_stage(
        self, 
        system_prompt: str,
        user_prompt: str,
        stage_info: Dict[str, Any]
    ) -> AsyncGenerator[tuple, None]:
        """
        生成报告的指定阶段
        
        Args:
            system_prompt: 系统提示词
            user_prompt: 用户提示词  
            stage_info: 阶段信息
            
        Yields:
            生成的文本流
        """
        try:
            stage_name = stage_info.get('name', 'Unknown')
            logger.info(f"开始生成报告阶段: {stage_name} (模型: {self.model_name})")
            
            # 检查LLM服务是否初始化成功
            if self.llm_service is None:
                error_msg = f"❌ LLM服务未初始化，无法生成 {stage_name}\n\n"
                logger.error(f"LLM服务未初始化: {self.model_name}")
                yield (error_msg, None)
                return
            
            # 调用具体的LLM服务
            async for result in self.llm_service.generate_report_stage(system_prompt, user_prompt, stage_info):
                yield result
                    
        except Exception as e:
            stage_name = stage_info.get('name', 'Unknown')
            logger.error(f"LLM生成报告失败 - {stage_name}: {str(e)}")
            error_msg = f"❌ 生成 {stage_name} 时发生错误: {str(e)}\n\n"
            yield (error_msg, None)
    
    async def generate_simple_response(
        self,
        system_prompt: str, 
        user_prompt: str
    ) -> str:
        """
        生成简单的非流式响应
        
        Args:
            system_prompt: 系统提示词
            user_prompt: 用户提示词
            
        Returns:
            生成的文本
        """
        try:
            # 检查LLM服务是否初始化成功
            if self.llm_service is None:
                logger.error(f"LLM服务未初始化: {self.model_name}")
                return f"❌ LLM服务未初始化，无法生成响应"
            
            # 调用具体的LLM服务
            return await self.llm_service.generate_simple_response(system_prompt, user_prompt)
            
        except Exception as e:
            logger.error(f"LLM生成响应失败: {str(e)}")
            return f"生成响应时发生错误: {str(e)}"


 