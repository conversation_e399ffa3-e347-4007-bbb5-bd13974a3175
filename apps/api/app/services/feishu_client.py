from typing import List, Dict, Any, Optional
import lark_oapi as lark
from lark_oapi.api.bitable.v1 import *
from lark_oapi.api.wiki.v2 import GetNodeSpaceRequest, GetNodeSpaceResponse
from copy import deepcopy
import logging
from ..config.feishu_config import (
    FEISHU_CONFIG, 
    FEISHU_BITABLE_CONFIG_AOS_UPGRADE_CN,
    FEISHU_BITABLE_CONFIG_AOS_UPGRADE_OS,
    FEISHU_BITABLE_CONFIG_HUMAN_STATS,
    FEISHU_FIELD_MAPPING
)

logger = logging.getLogger(__name__)

class FeishuService:
    def __init__(self):
        self.app_id = FEISHU_CONFIG["app_id"]
        self.app_secret = FEISHU_CONFIG["app_secret"]
        self.verification_token = FEISHU_CONFIG["verification_token"]
        self.encrypt_key = FEISHU_CONFIG["encrypt_key"]
        
        # 配置飞书 SDK
        lark.APP_ID = self.app_id
        lark.APP_SECRET = self.app_secret
        lark.VERIFICATION_TOKEN = self.verification_token
        lark.ENCRYPT_KEY = self.encrypt_key
        
        # 创建客户端
        self.client = lark.Client.builder() \
            .app_id(self.app_id) \
            .app_secret(self.app_secret) \
            .log_level(lark.LogLevel.DEBUG) \
            .build()

    def is_configured(self) -> bool:
        """检查飞书配置是否完整"""
        return bool(self.app_id and self.app_secret)

    def _get_bitable_config(self, scope: str) -> Dict[str, str]:
        """根据scope获取对应的多维表格配置"""
        if scope == "cn":
            return FEISHU_BITABLE_CONFIG_AOS_UPGRADE_CN
        elif scope == "global":
            return FEISHU_BITABLE_CONFIG_AOS_UPGRADE_OS
        elif scope == "human_stats":
            return FEISHU_BITABLE_CONFIG_HUMAN_STATS
        else:
            raise ValueError(f"不支持的scope: {scope}")

    async def get_all_records_by_scope(
        self,
        scope: str = "cn",
        filter: str = "" # 过滤条件，如：CurrentValue.[设备sn]=\"M03SCN2B17025101K2E1\"
    ) -> List[Dict[str, Any]]:
        """通过循环分页获取指定 scope 的所有记录"""
        all_records = []
        page_token = None
        has_more = True

        while has_more:
            response_data = await self.get_table_records(
                scope=scope,
                filter=filter,
                page_size=500,
                page_token=page_token
            )
            
            records = response_data.get("items", [])
            if records:
                all_records.extend(records)
            
            has_more = response_data.get("has_more", False)
            page_token = response_data.get("page_token")

            # Safety break: if has_more is true but no page_token is provided, exit to prevent infinite loop.
            if has_more and not page_token:
                logger.warning(f"Feishu API reported has_more=True but returned no page_token. Breaking loop to prevent infinite repetition. Scope: {scope}")
                break

        return all_records

    async def get_table_records(
        self,
        scope: str = "cn",
        filter: str = "",
        page_size: int = 500,
        page_token: Optional[str] = None,
        override_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """获取多维表格记录"""
        if not self.is_configured():
            raise ValueError("飞书配置不完整，请设置 app_id 和 app_secret")

        # 如果提供了覆盖配置，则优先使用；否则，按scope获取
        bitable_config = override_config if override_config is not None else self._get_bitable_config(scope)
        #filter = "CurrentValue.[设备sn]=\"M03SCN2B17025101K2E1\""
        # 构造请求对象
        request_builder = ListAppTableRecordRequest.builder() \
            .app_token(bitable_config["app_token"]) \
            .table_id(bitable_config["table_id"]) \
            .view_id(bitable_config["view_id"]) \
            .filter(filter) \
            .sort("[\"提交时间 DESC\"]") \
            .field_names("") \
            .text_field_as_array(True) \
            .user_id_type("user_id") \
            .display_formula_ref(True) \
            .automatic_fields(True) \
            .page_size(page_size)

        if page_token:
            request_builder.page_token(page_token)
        
        print("request_builder", request_builder)
        request = request_builder.build()

        # 发起请求
        response = self.client.bitable.v1.app_table_record.list(request)

        # 处理失败返回
        if not response.success():
            raise Exception(
                f"获取表格数据失败: code={response.code}, msg={response.msg}, log_id={response.get_log_id()}"
            )
        #if is_dump:
        # 处理业务结果
        #lark.logger.info(lark.JSON.marshal(response.data, indent=4))
        # 直接返回 response.data，它应该包含 items 属性
        return {
            "items": response.data.items if hasattr(response.data, 'items') else [],
            "page_token": response.data.page_token if hasattr(response.data, 'page_token') else None,
            "has_more": response.data.has_more if hasattr(response.data, 'has_more') else False,
            "total": response.data.total if hasattr(response.data, 'total') else 0
        }

    async def add_table_record(
        self,
        scope: str = "cn",
        fields: Dict[str, Any] = None,
        user_id_type: str = "user_id",
        client_token: str = ""
    ) -> Dict[str, Any]:
        """添加多维表格记录"""
        if not self.is_configured():
            raise ValueError("飞书配置不完整，请设置 app_id 和 app_secret")

        # 获取对应scope的配置
        bitable_config = self._get_bitable_config(scope)

        # 构造请求对象
        request = CreateAppTableRecordRequest.builder() \
            .app_token(bitable_config["app_token"]) \
            .table_id(bitable_config["table_id"]) \
            .user_id_type(user_id_type) \
            .client_token(client_token) \
            .request_body(AppTableRecord.builder()
                         .fields(fields)
                         .build()) \
            .build()

        # 发起请求
        response = self.client.bitable.v1.app_table_record.create(request)

        # 处理失败返回
        if not response.success():
            raise Exception(
                f"添加表格记录失败: code={response.code}, msg={response.msg}, log_id={response.get_log_id()}"
            )

        return response.data

    async def update_table_record(
        self,
        scope: str = "cn",
        record_id: str = None,
        fields: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """更新多维表格记录"""
        if not self.is_configured():
            raise ValueError("飞书配置不完整，请设置 app_id 和 app_secret")

        # 获取对应scope的配置
        bitable_config = self._get_bitable_config(scope)

        # 构造请求对象
        request = UpdateAppTableRecordRequest.builder() \
            .app_token(bitable_config["app_token"]) \
            .table_id(bitable_config["table_id"]) \
            .record_id(record_id) \
            .user_id_type("user_id") \
            .request_body(AppTableRecord.builder()
                         .fields(fields)
                         .build()) \
            .build()

        # 发起请求
        response = self.client.bitable.v1.app_table_record.update(request)

        # 处理失败返回
        if not response.success():
            raise Exception(
                f"更新表格记录失败: code={response.code}, msg={response.msg}, log_id={response.get_log_id()}"
            )

        return response.data

    def get_real_bitable_token_from_wiki_node(self, node_token: str) -> str:
        """
        通过 wiki 节点 token 获取真实的多维表格 token
        参考文档：https://open.feishu.cn/document/server-docs/docs/wiki-v2/space-node/get_node?appId=cli_a7ce94ec90b09013
        如何获取参考文档：https://open.feishu.cn/document/server-docs/docs/faq
        """
        request: GetNodeSpaceRequest = GetNodeSpaceRequest.builder() \
            .token(node_token) \
            .build()
        response: GetNodeSpaceResponse = self.client.wiki.v2.space.get_node(request)
        if not response.success():
            print(response)
            raise Exception(f"获取wiki节点信息失败: code={response.code}, msg={response.msg}, log_id={response.get_log_id()}")
        # 返回真实的表格token
        return response.data.node.obj_token

feishu_service = FeishuService() 