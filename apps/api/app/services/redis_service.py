import json
import redis
from typing import Any, Optional
from ..config.redis_config import REDIS_CONFIG, CACHE_EXPIRE

class CustomJSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理无法序列化的对象"""
    def default(self, obj):
        # 处理Person对象和其他自定义对象
        if hasattr(obj, '__dict__'):
            # 尝试将对象转换为字典
            try:
                return obj.__dict__
            except:
                # 如果转换失败，返回字符串表示
                return str(obj)
        # 处理其他无法序列化的对象
        try:
            return str(obj)
        except:
            return None

class RedisService:
    def __init__(self):
        self.redis_client = redis.Redis(**REDIS_CONFIG)
    
    def set_cache(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """
        设置缓存
        :param key: 缓存键
        :param value: 缓存值
        :param expire: 过期时间（秒），如果为 None 则使用默认配置
        :return: 是否设置成功
        """
        try:
            # 将值转换为 JSON 字符串，使用自定义编码器
            json_value = json.dumps(value, ensure_ascii=False, cls=CustomJSONEncoder)
            # 设置缓存
            self.redis_client.set(key, json_value)
            # 设置过期时间
            if expire is not None:
                self.redis_client.expire(key, expire)
            return True
        except Exception as e:
            print(f"设置缓存失败: {str(e)}")
            return False
    
    def get_cache(self, key: str) -> Optional[Any]:
        """
        获取缓存
        :param key: 缓存键
        :return: 缓存值，如果不存在则返回 None
        """
        try:
            # 获取缓存
            value = self.redis_client.get(key)
            if value is None:
                return None
            # 解析 JSON 字符串
            return json.loads(value)
        except Exception as e:
            print(f"获取缓存失败: {str(e)}")
            return None
    
    def delete_cache(self, key: str) -> bool:
        """
        删除缓存
        :param key: 缓存键
        :return: 是否删除成功
        """
        try:
            return bool(self.redis_client.delete(key))
        except Exception as e:
            print(f"删除缓存失败: {str(e)}")
            return False
    
    def clear_all_cache(self) -> bool:
        """
        清除所有缓存
        :return: 是否清除成功
        """
        try:
            self.redis_client.flushdb()
            return True
        except Exception as e:
            print(f"清除缓存失败: {str(e)}")
            return False

# 创建 Redis 服务实例
redis_service = RedisService() 