import logging
from datetime import datetime
from typing import Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy import func, case, Date, cast
import json

from app.models.statistics import (
    AosStatSessionBehaviorHourly, 
    AosStatMessageBehaviorHourly,
    AosStatActionBehaviorHourly,
    AosStatEventBehaviorHourly,
    AosStatUserQuestionTopNHourly,
    AosStatSessionDurationDistributionHourly,
    AosStatSessionIntervalDistributionHourly,
    AosStatActiveHoursHourly,
    AosStatUserPreferenceDetailHourly
)

logger = logging.getLogger(__name__)

class StatsQueryService:
    """机器人对话统计数据查询服务"""
    def _flatten_json_array_of_arrays(self, json_string: str) -> List[Any]:
        """
        解析一个代表"数组的数组"的JSON字符串，并将其压平为一个单一的数组。
        例如: '[[1, 2], null, [3]]' -> [1, 2, 3]
        """
        if not json_string:
            return []
        try:
            list_of_lists = json.loads(json_string)
            if not isinstance(list_of_lists, list):
                return []
            
            flat_list = []
            for sublist in list_of_lists:
                if isinstance(sublist, list):
                    flat_list.extend(sublist)
            return flat_list
        except (json.JSONDecodeError, TypeError):
            return []
            
    def __init__(self, db: Session):
        self.db = db

    def get_summary(
        self,
        enterprise_id: str,
        device_id: str,
        start_time: datetime,
        end_time: datetime,
        group_by: str
    ) -> Dict[str, Any]:
        
        # --- 1. 根据 group_by 参数决定聚合方式 ---
        if group_by == 'total':
            data = self._get_total_summary(enterprise_id, device_id, start_time, end_time)
        elif group_by == 'day':
            data = self._get_daily_summary(enterprise_id, device_id, start_time, end_time)
        else: # default to 'hour'
            data = self._get_hourly_summary(enterprise_id, device_id, start_time, end_time)

        # --- 2. 构建最终响应 ---
        response = {
            "query_summary": {
                "enterprise_id": enterprise_id,
                "device_id": device_id,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "group_by": group_by
            },
            "data": data
        }
        return response

    def _get_total_summary(self, enterprise_id: str, device_id: str, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """获取总览数据（只返回一个元素）"""
        # 构建基础查询条件
        query_params = {
            'enterprise_id': enterprise_id,
            'device_id': device_id,
            'hour_bucket_start': start_time,
            'hour_bucket_end': end_time
        }

        # 1. 会话行为汇总
        session_summary = self.db.query(AosStatSessionBehaviorHourly).filter(
            AosStatSessionBehaviorHourly.enterprise_id == enterprise_id,
            AosStatSessionBehaviorHourly.device_id == device_id,
            AosStatSessionBehaviorHourly.hour_bucket >= start_time,
            AosStatSessionBehaviorHourly.hour_bucket < end_time
        ).with_entities(
            func.sum(AosStatSessionBehaviorHourly.session_total).label("total_sessions"),
            func.sum(AosStatSessionBehaviorHourly.valid_sessions).label("valid_sessions"),
            func.sum(AosStatSessionBehaviorHourly.no_response_sessions).label("no_response_sessions"),
            func.avg(AosStatSessionBehaviorHourly.avg_conversation_turns).label("avg_conversation_turns"),
            func.avg(AosStatSessionBehaviorHourly.avg_session_duration).label("avg_session_duration"),
        ).first()

        # 2. 消息行为汇总
        message_summary = self.db.query(AosStatMessageBehaviorHourly).filter(
            AosStatMessageBehaviorHourly.enterprise_id == enterprise_id,
            AosStatMessageBehaviorHourly.device_id == device_id,
            AosStatMessageBehaviorHourly.hour_bucket >= start_time,
            AosStatMessageBehaviorHourly.hour_bucket < end_time
        ).with_entities(
            func.sum(AosStatMessageBehaviorHourly.user_msg_count).label("user_messages"),
            func.sum(AosStatMessageBehaviorHourly.assistant_msg_count).label("assistant_messages"),
            func.sum(AosStatMessageBehaviorHourly.action_trigger_count).label("action_trigger_count"),
            func.sum(AosStatMessageBehaviorHourly.event_trigger_count).label("event_trigger_count"),
            func.avg(AosStatMessageBehaviorHourly.avg_user_msg_length).label("avg_user_msg_length"),
            func.avg(AosStatMessageBehaviorHourly.avg_assistant_msg_length).label("avg_assistant_msg_length"),
        ).first()

        # 3. 动作行为汇总
        action_summary = self.db.query(AosStatActionBehaviorHourly).filter(
            AosStatActionBehaviorHourly.enterprise_id == enterprise_id,
            AosStatActionBehaviorHourly.device_id == device_id,
            AosStatActionBehaviorHourly.hour_bucket >= start_time,
            AosStatActionBehaviorHourly.hour_bucket < end_time
        ).with_entities(
            AosStatActionBehaviorHourly.action_name,
            AosStatActionBehaviorHourly.action_display_name,
            func.sum(AosStatActionBehaviorHourly.action_count).label("total_count"),
            func.json_arrayagg(AosStatActionBehaviorHourly.session_ids).label("all_session_ids")
        ).group_by(
            AosStatActionBehaviorHourly.action_name,
            AosStatActionBehaviorHourly.action_display_name
        ).order_by(func.sum(AosStatActionBehaviorHourly.action_count).desc()).all()

        # 4. 事件行为汇总
        event_summary = self.db.query(AosStatEventBehaviorHourly).filter(
            AosStatEventBehaviorHourly.enterprise_id == enterprise_id,
            AosStatEventBehaviorHourly.device_id == device_id,
            AosStatEventBehaviorHourly.hour_bucket >= start_time,
            AosStatEventBehaviorHourly.hour_bucket < end_time
        ).with_entities(
            func.sum(AosStatEventBehaviorHourly.event_total_count).label("total_events"),
            func.max(AosStatEventBehaviorHourly.event_path_freq).label("event_path_freq"),
            func.max(AosStatEventBehaviorHourly.target_scene_freq).label("target_scene_freq"),
            func.json_arrayagg(AosStatEventBehaviorHourly.session_ids_event_total_count).label("all_session_ids")
        ).first()

        # 5. 用户问题汇总
        question_summary = self.db.query(AosStatUserQuestionTopNHourly).filter(
            AosStatUserQuestionTopNHourly.enterprise_id == enterprise_id,
            AosStatUserQuestionTopNHourly.device_id == device_id,
            AosStatUserQuestionTopNHourly.hour_bucket >= start_time,
            AosStatUserQuestionTopNHourly.hour_bucket < end_time
        ).with_entities(
            AosStatUserQuestionTopNHourly.question_content,
            func.sum(AosStatUserQuestionTopNHourly.question_count).label("total_count"),
            func.json_arrayagg(AosStatUserQuestionTopNHourly.session_ids).label("all_session_ids")
        ).group_by(
            AosStatUserQuestionTopNHourly.question_content
        ).order_by(func.sum(AosStatUserQuestionTopNHourly.question_count).desc()).limit(10).all()

        # 6. 会话时长分布汇总
        duration_summary = self.db.query(AosStatSessionDurationDistributionHourly).filter(
            AosStatSessionDurationDistributionHourly.enterprise_id == enterprise_id,
            AosStatSessionDurationDistributionHourly.device_id == device_id,
            AosStatSessionDurationDistributionHourly.hour_bucket >= start_time,
            AosStatSessionDurationDistributionHourly.hour_bucket < end_time
        ).with_entities(
            func.sum(AosStatSessionDurationDistributionHourly.bucket_lt_30s).label("bucket_lt_30s"),
            func.sum(AosStatSessionDurationDistributionHourly.bucket_30_60s).label("bucket_30_60s"),
            func.sum(AosStatSessionDurationDistributionHourly.bucket_1_3min).label("bucket_1_3min"),
            func.sum(AosStatSessionDurationDistributionHourly.bucket_3_5min).label("bucket_3_5min"),
            func.sum(AosStatSessionDurationDistributionHourly.bucket_5_10min).label("bucket_5_10min"),
            func.sum(AosStatSessionDurationDistributionHourly.bucket_10_20min).label("bucket_10_20min"),
            func.sum(AosStatSessionDurationDistributionHourly.bucket_gt_20min).label("bucket_gt_20min"),
            func.json_arrayagg(AosStatSessionDurationDistributionHourly.session_ids_duration_bucket).label("all_session_ids")
        ).first()

        # 7. 会话间隔分布汇总
        interval_summary = self.db.query(AosStatSessionIntervalDistributionHourly).filter(
            AosStatSessionIntervalDistributionHourly.enterprise_id == enterprise_id,
            AosStatSessionIntervalDistributionHourly.device_id == device_id,
            AosStatSessionIntervalDistributionHourly.hour_bucket >= start_time,
            AosStatSessionIntervalDistributionHourly.hour_bucket < end_time
        ).with_entities(
            func.sum(AosStatSessionIntervalDistributionHourly.bucket_lt_10s).label("bucket_lt_10s"),
            func.sum(AosStatSessionIntervalDistributionHourly.bucket_10_20s).label("bucket_10_20s"),
            func.sum(AosStatSessionIntervalDistributionHourly.bucket_1_3min).label("bucket_1_3min"),
            func.sum(AosStatSessionIntervalDistributionHourly.bucket_3_5min).label("bucket_3_5min"),
            func.sum(AosStatSessionIntervalDistributionHourly.bucket_5_10min).label("bucket_5_10min"),
            func.sum(AosStatSessionIntervalDistributionHourly.bucket_10_20min).label("bucket_10_20min"),
            func.sum(AosStatSessionIntervalDistributionHourly.bucket_gt_20min).label("bucket_gt_20min"),
            func.json_arrayagg(AosStatSessionIntervalDistributionHourly.session_ids_interval_bucket).label("all_session_ids")
        ).first()

        # 8. 活跃小时汇总
        active_hours_summary = self.db.query(AosStatActiveHoursHourly).filter(
            AosStatActiveHoursHourly.enterprise_id == enterprise_id,
            AosStatActiveHoursHourly.device_id == device_id,
            AosStatActiveHoursHourly.hour_bucket >= start_time,
            AosStatActiveHoursHourly.hour_bucket < end_time
        ).with_entities(
            func.avg(AosStatActiveHoursHourly.consecutive_active_hours).label("avg_consecutive_active_hours"),
            func.avg(AosStatActiveHoursHourly.assistant_first_ratio).label("avg_assistant_first_ratio"),
            func.avg(AosStatActiveHoursHourly.greeting_ratio).label("avg_greeting_ratio"),
            func.json_arrayagg(AosStatActiveHoursHourly.session_ids_active).label("all_session_ids")
        ).first()

        # 9. 用户偏好汇总
        preference_summary = self.db.query(AosStatUserPreferenceDetailHourly).filter(
            AosStatUserPreferenceDetailHourly.enterprise_id == enterprise_id,
            AosStatUserPreferenceDetailHourly.device_id == device_id,
            AosStatUserPreferenceDetailHourly.hour_bucket >= start_time,
            AosStatUserPreferenceDetailHourly.hour_bucket < end_time
        ).with_entities(
            AosStatUserPreferenceDetailHourly.field_name,
            AosStatUserPreferenceDetailHourly.field_value,
            func.count(AosStatUserPreferenceDetailHourly.field_value).label("count"),
            func.json_arrayagg(AosStatUserPreferenceDetailHourly.session_id).label("all_session_ids")
        ).group_by(
            AosStatUserPreferenceDetailHourly.field_name,
            AosStatUserPreferenceDetailHourly.field_value
        ).order_by(func.count(AosStatUserPreferenceDetailHourly.field_value).desc()).limit(20).all()

        # 构建响应数据
        total_data = {
            "session_behavior_summary": {
                "total_sessions": session_summary.total_sessions or 0,
                "valid_sessions": session_summary.valid_sessions or 0,
                "no_response_sessions": session_summary.no_response_sessions or 0,
                "avg_conversation_turns": round(float(session_summary.avg_conversation_turns or 0), 2),
                "avg_session_duration": round(float(session_summary.avg_session_duration or 0), 2),
                "session_ids_total": self._flatten_json_array_of_arrays(session_summary.session_ids_total),
                "session_ids_valid": self._flatten_json_array_of_arrays(session_summary.session_ids_valid),
            },
            "message_behavior_summary": {
                "user_messages": message_summary.user_messages or 0,
                "assistant_messages": message_summary.assistant_messages or 0,
                "action_trigger_count": message_summary.action_trigger_count or 0,
                "event_trigger_count": message_summary.event_trigger_count or 0,
                "avg_user_msg_length": float(message_summary.avg_user_msg_length or 0),
                "avg_assistant_msg_length": float(message_summary.avg_assistant_msg_length or 0),
            },
            "action_behavior_summary": [
                {
                    "name": action.action_name,
                    "display_name": action.action_display_name,
                    "count": action.total_count,
                    "session_ids": self._flatten_json_array_of_arrays(action.all_session_ids)
                }
                for action in action_summary
            ],
            "event_behavior_summary": {
                "total_events": event_summary.total_events or 0,
                "event_path_freq": event_summary.event_path_freq or "",
                "target_scene_freq": event_summary.target_scene_freq or "",
                "session_ids": self._flatten_json_array_of_arrays(event_summary.all_session_ids)
            },
            "user_question_summary": [
                {
                    "question": question.question_content,
                    "count": question.total_count,
                    "session_ids": self._flatten_json_array_of_arrays(question.all_session_ids)
                }
                for question in question_summary
            ],
            "duration_distribution": {
                "bucket_lt_30s": duration_summary.bucket_lt_30s or 0,
                "bucket_30_60s": duration_summary.bucket_30_60s or 0,
                "bucket_1_3min": duration_summary.bucket_1_3min or 0,
                "bucket_3_5min": duration_summary.bucket_3_5min or 0,
                "bucket_5_10min": duration_summary.bucket_5_10min or 0,
                "bucket_10_20min": duration_summary.bucket_10_20min or 0,
                "bucket_gt_20min": duration_summary.bucket_gt_20min or 0,
                "session_ids": self._flatten_json_array_of_arrays(duration_summary.all_session_ids)
            },
            "interval_distribution": {
                "bucket_lt_10s": interval_summary.bucket_lt_10s or 0,
                "bucket_10_20s": interval_summary.bucket_10_20s or 0,
                "bucket_1_3min": interval_summary.bucket_1_3min or 0,
                "bucket_3_5min": interval_summary.bucket_3_5min or 0,
                "bucket_5_10min": interval_summary.bucket_5_10min or 0,
                "bucket_10_20min": interval_summary.bucket_10_20min or 0,
                "bucket_gt_20min": interval_summary.bucket_gt_20min or 0,
                "session_ids": self._flatten_json_array_of_arrays(interval_summary.all_session_ids)
            },
            "active_hours_summary": {
                "avg_consecutive_active_hours": float(active_hours_summary.avg_consecutive_active_hours or 0),
                "avg_assistant_first_ratio": float(active_hours_summary.avg_assistant_first_ratio or 0),
                "avg_greeting_ratio": float(active_hours_summary.avg_greeting_ratio or 0),
                "session_ids": self._flatten_json_array_of_arrays(active_hours_summary.all_session_ids)
            },
            "user_preference_summary": [
                {
                    "field_name": pref.field_name,
                    "field_value": pref.field_value,
                    "count": pref.count,
                    "session_ids": self._flatten_json_array_of_arrays(pref.all_session_ids)
                }
                for pref in preference_summary
            ]
        }

        return [total_data]

    def _get_daily_summary(self, enterprise_id: str, device_id: str, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """按天聚合数据"""
        # 会话行为聚合
        session_summary = self.db.query(AosStatSessionBehaviorHourly).filter(
            AosStatSessionBehaviorHourly.enterprise_id == enterprise_id,
            AosStatSessionBehaviorHourly.device_id == device_id,
            AosStatSessionBehaviorHourly.hour_bucket >= start_time,
            AosStatSessionBehaviorHourly.hour_bucket < end_time
        ).group_by(
            cast(AosStatSessionBehaviorHourly.hour_bucket, Date)
        ).with_entities(
            cast(AosStatSessionBehaviorHourly.hour_bucket, Date).label("time_bucket"),
            func.sum(AosStatSessionBehaviorHourly.session_total).label("total_sessions"),
            func.sum(AosStatSessionBehaviorHourly.valid_sessions).label("valid_sessions"),
            func.sum(AosStatSessionBehaviorHourly.no_response_sessions).label("no_response_sessions"),
            func.avg(AosStatSessionBehaviorHourly.avg_conversation_turns).label("avg_conversation_turns"),
            func.avg(AosStatSessionBehaviorHourly.avg_session_duration).label("avg_session_duration"),
            func.json_arrayagg(AosStatSessionBehaviorHourly.session_ids_session_total).label("session_ids_total"),
            func.json_arrayagg(AosStatSessionBehaviorHourly.session_ids_valid_sessions).label("session_ids_valid"),
        ).all()

        # 消息行为聚合
        message_summary = self.db.query(AosStatMessageBehaviorHourly).filter(
            AosStatMessageBehaviorHourly.enterprise_id == enterprise_id,
            AosStatMessageBehaviorHourly.device_id == device_id,
            AosStatMessageBehaviorHourly.hour_bucket >= start_time,
            AosStatMessageBehaviorHourly.hour_bucket < end_time
        ).group_by(
            cast(AosStatMessageBehaviorHourly.hour_bucket, Date)
        ).with_entities(
            cast(AosStatMessageBehaviorHourly.hour_bucket, Date).label("time_bucket"),
            func.sum(AosStatMessageBehaviorHourly.user_msg_count).label("user_messages"),
            func.sum(AosStatMessageBehaviorHourly.assistant_msg_count).label("assistant_messages"),
            func.sum(AosStatMessageBehaviorHourly.action_trigger_count).label("action_trigger_count"),
            func.sum(AosStatMessageBehaviorHourly.event_trigger_count).label("event_trigger_count"),
            func.json_arrayagg(AosStatMessageBehaviorHourly.session_ids_user_msg_count).label("session_ids_user"),
            func.json_arrayagg(AosStatMessageBehaviorHourly.session_ids_assistant_msg_count).label("session_ids_assistant"),
        ).all()

        # 事件行为聚合
        event_summary = self.db.query(AosStatEventBehaviorHourly).filter(
            AosStatEventBehaviorHourly.enterprise_id == enterprise_id,
            AosStatEventBehaviorHourly.device_id == device_id,
            AosStatEventBehaviorHourly.hour_bucket >= start_time,
            AosStatEventBehaviorHourly.hour_bucket < end_time
        ).group_by(
            cast(AosStatEventBehaviorHourly.hour_bucket, Date)
        ).with_entities(
            cast(AosStatEventBehaviorHourly.hour_bucket, Date).label("time_bucket"),
            func.sum(AosStatEventBehaviorHourly.event_total_count).label("total_events"),
            func.max(AosStatEventBehaviorHourly.event_path_freq).label("event_path_freq"),
            func.max(AosStatEventBehaviorHourly.target_scene_freq).label("target_scene_freq"),
            func.json_arrayagg(AosStatEventBehaviorHourly.session_ids_event_total_count).label("session_ids_events"),
        ).all()

        # 会话时长分布聚合
        duration_summary = self.db.query(AosStatSessionDurationDistributionHourly).filter(
            AosStatSessionDurationDistributionHourly.enterprise_id == enterprise_id,
            AosStatSessionDurationDistributionHourly.device_id == device_id,
            AosStatSessionDurationDistributionHourly.hour_bucket >= start_time,
            AosStatSessionDurationDistributionHourly.hour_bucket < end_time
        ).group_by(
            cast(AosStatSessionDurationDistributionHourly.hour_bucket, Date)
        ).with_entities(
            cast(AosStatSessionDurationDistributionHourly.hour_bucket, Date).label("time_bucket"),
            func.sum(AosStatSessionDurationDistributionHourly.bucket_lt_30s).label("bucket_lt_30s"),
            func.sum(AosStatSessionDurationDistributionHourly.bucket_30_60s).label("bucket_30_60s"),
            func.sum(AosStatSessionDurationDistributionHourly.bucket_1_3min).label("bucket_1_3min"),
            func.sum(AosStatSessionDurationDistributionHourly.bucket_3_5min).label("bucket_3_5min"),
            func.sum(AosStatSessionDurationDistributionHourly.bucket_5_10min).label("bucket_5_10min"),
            func.sum(AosStatSessionDurationDistributionHourly.bucket_10_20min).label("bucket_10_20min"),
            func.sum(AosStatSessionDurationDistributionHourly.bucket_gt_20min).label("bucket_gt_20min"),
            func.json_arrayagg(AosStatSessionDurationDistributionHourly.session_ids_duration_bucket).label("session_ids_duration"),
        ).all()

        # 会话间隔分布聚合
        interval_summary = self.db.query(AosStatSessionIntervalDistributionHourly).filter(
            AosStatSessionIntervalDistributionHourly.enterprise_id == enterprise_id,
            AosStatSessionIntervalDistributionHourly.device_id == device_id,
            AosStatSessionIntervalDistributionHourly.hour_bucket >= start_time,
            AosStatSessionIntervalDistributionHourly.hour_bucket < end_time
        ).group_by(
            cast(AosStatSessionIntervalDistributionHourly.hour_bucket, Date)
        ).with_entities(
            cast(AosStatSessionIntervalDistributionHourly.hour_bucket, Date).label("time_bucket"),
            func.sum(AosStatSessionIntervalDistributionHourly.bucket_lt_10s).label("bucket_lt_10s"),
            func.sum(AosStatSessionIntervalDistributionHourly.bucket_10_20s).label("bucket_10_20s"),
            func.sum(AosStatSessionIntervalDistributionHourly.bucket_1_3min).label("bucket_1_3min"),
            func.sum(AosStatSessionIntervalDistributionHourly.bucket_3_5min).label("bucket_3_5min"),
            func.sum(AosStatSessionIntervalDistributionHourly.bucket_5_10min).label("bucket_5_10min"),
            func.sum(AosStatSessionIntervalDistributionHourly.bucket_10_20min).label("bucket_10_20min"),
            func.sum(AosStatSessionIntervalDistributionHourly.bucket_gt_20min).label("bucket_gt_20min"),
            func.json_arrayagg(AosStatSessionIntervalDistributionHourly.session_ids_interval_bucket).label("session_ids_interval"),
        ).all()

        # 活跃小时聚合
        active_hours_summary = self.db.query(AosStatActiveHoursHourly).filter(
            AosStatActiveHoursHourly.enterprise_id == enterprise_id,
            AosStatActiveHoursHourly.device_id == device_id,
            AosStatActiveHoursHourly.hour_bucket >= start_time,
            AosStatActiveHoursHourly.hour_bucket < end_time
        ).group_by(
            cast(AosStatActiveHoursHourly.hour_bucket, Date)
        ).with_entities(
            cast(AosStatActiveHoursHourly.hour_bucket, Date).label("time_bucket"),
            func.avg(AosStatActiveHoursHourly.consecutive_active_hours).label("avg_consecutive_active_hours"),
            func.avg(AosStatActiveHoursHourly.assistant_first_ratio).label("avg_assistant_first_ratio"),
            func.avg(AosStatActiveHoursHourly.greeting_ratio).label("avg_greeting_ratio"),
            func.json_arrayagg(AosStatActiveHoursHourly.session_ids_active).label("session_ids_active"),
        ).all()
        
        # 用户问题聚合
        question_summary = self.db.query(
            cast(AosStatUserQuestionTopNHourly.hour_bucket, Date).label("time_bucket"),
            AosStatUserQuestionTopNHourly.question_content,
            func.sum(AosStatUserQuestionTopNHourly.question_count).label("count"),
            func.json_arrayagg(AosStatUserQuestionTopNHourly.session_ids).label("session_ids")
        ).filter(
            AosStatUserQuestionTopNHourly.enterprise_id == enterprise_id,
            AosStatUserQuestionTopNHourly.device_id == device_id,
            AosStatUserQuestionTopNHourly.hour_bucket >= start_time,
            AosStatUserQuestionTopNHourly.hour_bucket < end_time
        ).group_by(
            cast(AosStatUserQuestionTopNHourly.hour_bucket, Date),
            AosStatUserQuestionTopNHourly.question_content
        ).all()
        
        # 动作行为聚合
        action_summary = self.db.query(
            cast(AosStatActionBehaviorHourly.hour_bucket, Date).label("time_bucket"),
            AosStatActionBehaviorHourly.action_name,
            AosStatActionBehaviorHourly.action_display_name,
            func.sum(AosStatActionBehaviorHourly.action_count).label("count"),
            func.json_arrayagg(AosStatActionBehaviorHourly.session_ids).label("session_ids")
        ).filter(
            AosStatActionBehaviorHourly.enterprise_id == enterprise_id,
            AosStatActionBehaviorHourly.device_id == device_id,
            AosStatActionBehaviorHourly.hour_bucket >= start_time,
            AosStatActionBehaviorHourly.hour_bucket < end_time
        ).group_by(
            cast(AosStatActionBehaviorHourly.hour_bucket, Date),
            AosStatActionBehaviorHourly.action_name,
            AosStatActionBehaviorHourly.action_display_name
        ).all()
        
        # 用户偏好聚合
        preference_summary = self.db.query(
            cast(AosStatUserPreferenceDetailHourly.hour_bucket, Date).label("time_bucket"),
            AosStatUserPreferenceDetailHourly.field_name,
            AosStatUserPreferenceDetailHourly.field_value,
            func.count(AosStatUserPreferenceDetailHourly.field_value).label("count"),
            func.json_arrayagg(AosStatUserPreferenceDetailHourly.session_id).label("session_ids")
        ).filter(
            AosStatUserPreferenceDetailHourly.enterprise_id == enterprise_id,
            AosStatUserPreferenceDetailHourly.device_id == device_id,
            AosStatUserPreferenceDetailHourly.hour_bucket >= start_time,
            AosStatUserPreferenceDetailHourly.hour_bucket < end_time
        ).group_by(
            cast(AosStatUserPreferenceDetailHourly.hour_bucket, Date),
            AosStatUserPreferenceDetailHourly.field_name,
            AosStatUserPreferenceDetailHourly.field_value
        ).all()
        
        # 合并所有出现过的time_bucket
        all_buckets = set()
        for rows in [session_summary, message_summary, event_summary, duration_summary, interval_summary, active_hours_summary, question_summary, action_summary, preference_summary]:
            all_buckets.update([row.time_bucket for row in rows])
        all_buckets = sorted(list(all_buckets))
        
        # 统一补全所有维度
        merged_data = {}
        for bucket in all_buckets:
            merged_data[bucket] = {
                "time_bucket": bucket.isoformat(),
                "session_behavior_summary": {},
                "message_behavior_summary": {},
                "event_behavior_summary": {},
                "duration_distribution": {},
                "interval_distribution": {},
                "active_hours_summary": {},
                "action_behavior_summary": [],
                "user_question_summary": [],
                "user_preference_summary": []
            }
        
        # 处理会话行为数据
        for row in session_summary:
            merged_data[row.time_bucket]["session_behavior_summary"] = {
                "total_sessions": row.total_sessions or 0,
                "valid_sessions": row.valid_sessions or 0,
                "no_response_sessions": row.no_response_sessions or 0,
                "avg_conversation_turns": round(float(row.avg_conversation_turns or 0), 2),
                "avg_session_duration": round(float(row.avg_session_duration or 0), 2),
                "session_ids_total": self._flatten_json_array_of_arrays(row.session_ids_total),
                "session_ids_valid": self._flatten_json_array_of_arrays(row.session_ids_valid),
            }
        
        # 处理消息行为数据
        for row in message_summary:
            if row.time_bucket in merged_data:
                merged_data[row.time_bucket]["message_behavior_summary"] = {
                    "user_messages": row.user_messages or 0,
                    "assistant_messages": row.assistant_messages or 0,
                    "action_trigger_count": row.action_trigger_count or 0,
                    "event_trigger_count": row.event_trigger_count or 0,
                    "session_ids_user": self._flatten_json_array_of_arrays(row.session_ids_user),
                    "session_ids_assistant": self._flatten_json_array_of_arrays(row.session_ids_assistant),
                }
        
        # 处理事件行为数据
        for row in event_summary:
            if row.time_bucket in merged_data:
                merged_data[row.time_bucket]["event_behavior_summary"] = {
                    "total_events": row.total_events or 0,
                    "event_path_freq": row.event_path_freq or "",
                    "target_scene_freq": row.target_scene_freq or "",
                    "session_ids_events": self._flatten_json_array_of_arrays(row.session_ids_events),
                }
        
        # 处理会话时长分布数据
        for row in duration_summary:
            if row.time_bucket in merged_data:
                merged_data[row.time_bucket]["duration_distribution"] = {
                    "bucket_lt_30s": row.bucket_lt_30s or 0,
                    "bucket_30_60s": row.bucket_30_60s or 0,
                    "bucket_1_3min": row.bucket_1_3min or 0,
                    "bucket_3_5min": row.bucket_3_5min or 0,
                    "bucket_5_10min": row.bucket_5_10min or 0,
                    "bucket_10_20min": row.bucket_10_20min or 0,
                    "bucket_gt_20min": row.bucket_gt_20min or 0,
                    "session_ids_duration": self._flatten_json_array_of_arrays(row.session_ids_duration),
                }
        
        # 处理会话间隔分布数据
        for row in interval_summary:
            if row.time_bucket in merged_data:
                merged_data[row.time_bucket]["interval_distribution"] = {
                    "bucket_lt_10s": row.bucket_lt_10s or 0,
                    "bucket_10_20s": row.bucket_10_20s or 0,
                    "bucket_1_3min": row.bucket_1_3min or 0,
                    "bucket_3_5min": row.bucket_3_5min or 0,
                    "bucket_5_10min": row.bucket_5_10min or 0,
                    "bucket_10_20min": row.bucket_10_20min or 0,
                    "bucket_gt_20min": row.bucket_gt_20min or 0,
                    "session_ids_interval": self._flatten_json_array_of_arrays(row.session_ids_interval),
                }
        
        # 处理活跃小时数据
        for row in active_hours_summary:
            if row.time_bucket in merged_data:
                merged_data[row.time_bucket]["active_hours_summary"] = {
                    "avg_consecutive_active_hours": float(row.avg_consecutive_active_hours or 0),
                    "avg_assistant_first_ratio": float(row.avg_assistant_first_ratio or 0),
                    "avg_greeting_ratio": float(row.avg_greeting_ratio or 0),
                    "session_ids_active": self._flatten_json_array_of_arrays(row.session_ids_active),
                }
        
        # 处理user_question_summary
        for row in question_summary:
            merged_data[row.time_bucket]["user_question_summary"].append({
                "question": row.question_content,
                "count": row.count or 0,
                "session_ids": self._flatten_json_array_of_arrays(row.session_ids)
            })
        
        # 处理action_behavior_summary
        for row in action_summary:
            merged_data[row.time_bucket]["action_behavior_summary"].append({
                "name": row.action_name,
                "display_name": row.action_display_name,
                "count": row.count or 0,
                "session_ids": self._flatten_json_array_of_arrays(row.session_ids)
            })
        
        # 处理user_preference_summary
        for row in preference_summary:
            merged_data[row.time_bucket]["user_preference_summary"].append({
                "field_name": row.field_name,
                "field_value": row.field_value,
                "count": row.count or 0,
                "session_ids": self._flatten_json_array_of_arrays(row.session_ids)
            })
        
        return [merged_data[b] for b in all_buckets]
        
    def _get_hourly_summary(self, enterprise_id: str, device_id: str, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """按小时聚合数据"""
        # 会话行为聚合
        session_summary = self.db.query(AosStatSessionBehaviorHourly).filter(
            AosStatSessionBehaviorHourly.enterprise_id == enterprise_id,
            AosStatSessionBehaviorHourly.device_id == device_id,
            AosStatSessionBehaviorHourly.hour_bucket >= start_time,
            AosStatSessionBehaviorHourly.hour_bucket < end_time
        ).with_entities(
            AosStatSessionBehaviorHourly.hour_bucket.label("time_bucket"),
            AosStatSessionBehaviorHourly.session_total.label("total_sessions"),
            AosStatSessionBehaviorHourly.valid_sessions.label("valid_sessions"),
            AosStatSessionBehaviorHourly.no_response_sessions.label("no_response_sessions"),
            AosStatSessionBehaviorHourly.avg_conversation_turns.label("avg_conversation_turns"),
            AosStatSessionBehaviorHourly.avg_session_duration.label("avg_session_duration"),
            AosStatSessionBehaviorHourly.session_ids_session_total.label("session_ids_total"),
            AosStatSessionBehaviorHourly.session_ids_valid_sessions.label("session_ids_valid"),
        ).order_by(AosStatSessionBehaviorHourly.hour_bucket).all()
        
        # 消息行为聚合
        message_summary = self.db.query(AosStatMessageBehaviorHourly).filter(
            AosStatMessageBehaviorHourly.enterprise_id == enterprise_id,
            AosStatMessageBehaviorHourly.device_id == device_id,
            AosStatMessageBehaviorHourly.hour_bucket >= start_time,
            AosStatMessageBehaviorHourly.hour_bucket < end_time
        ).with_entities(
            AosStatMessageBehaviorHourly.hour_bucket.label("time_bucket"),
            AosStatMessageBehaviorHourly.user_msg_count.label("user_messages"),
            AosStatMessageBehaviorHourly.assistant_msg_count.label("assistant_messages"),
            AosStatMessageBehaviorHourly.action_trigger_count.label("action_trigger_count"),
            AosStatMessageBehaviorHourly.event_trigger_count.label("event_trigger_count"),
            AosStatMessageBehaviorHourly.session_ids_user_msg_count.label("session_ids_user"),
            AosStatMessageBehaviorHourly.session_ids_assistant_msg_count.label("session_ids_assistant"),
        ).order_by(AosStatMessageBehaviorHourly.hour_bucket).all()

        # 事件行为聚合
        event_summary = self.db.query(AosStatEventBehaviorHourly).filter(
            AosStatEventBehaviorHourly.enterprise_id == enterprise_id,
            AosStatEventBehaviorHourly.device_id == device_id,
            AosStatEventBehaviorHourly.hour_bucket >= start_time,
            AosStatEventBehaviorHourly.hour_bucket < end_time
        ).with_entities(
            AosStatEventBehaviorHourly.hour_bucket.label("time_bucket"),
            AosStatEventBehaviorHourly.event_total_count.label("total_events"),
            AosStatEventBehaviorHourly.event_path_freq.label("event_path_freq"),
            AosStatEventBehaviorHourly.target_scene_freq.label("target_scene_freq"),
            AosStatEventBehaviorHourly.session_ids_event_total_count.label("session_ids_events"),
        ).order_by(AosStatEventBehaviorHourly.hour_bucket).all()

        # 会话时长分布聚合
        duration_summary = self.db.query(AosStatSessionDurationDistributionHourly).filter(
            AosStatSessionDurationDistributionHourly.enterprise_id == enterprise_id,
            AosStatSessionDurationDistributionHourly.device_id == device_id,
            AosStatSessionDurationDistributionHourly.hour_bucket >= start_time,
            AosStatSessionDurationDistributionHourly.hour_bucket < end_time
        ).with_entities(
            AosStatSessionDurationDistributionHourly.hour_bucket.label("time_bucket"),
            AosStatSessionDurationDistributionHourly.bucket_lt_30s.label("bucket_lt_30s"),
            AosStatSessionDurationDistributionHourly.bucket_30_60s.label("bucket_30_60s"),
            AosStatSessionDurationDistributionHourly.bucket_1_3min.label("bucket_1_3min"),
            AosStatSessionDurationDistributionHourly.bucket_3_5min.label("bucket_3_5min"),
            AosStatSessionDurationDistributionHourly.bucket_5_10min.label("bucket_5_10min"),
            AosStatSessionDurationDistributionHourly.bucket_10_20min.label("bucket_10_20min"),
            AosStatSessionDurationDistributionHourly.bucket_gt_20min.label("bucket_gt_20min"),
            AosStatSessionDurationDistributionHourly.session_ids_duration_bucket.label("session_ids_duration"),
        ).order_by(AosStatSessionDurationDistributionHourly.hour_bucket).all()

        # 会话间隔分布聚合
        interval_summary = self.db.query(AosStatSessionIntervalDistributionHourly).filter(
            AosStatSessionIntervalDistributionHourly.enterprise_id == enterprise_id,
            AosStatSessionIntervalDistributionHourly.device_id == device_id,
            AosStatSessionIntervalDistributionHourly.hour_bucket >= start_time,
            AosStatSessionIntervalDistributionHourly.hour_bucket < end_time
        ).with_entities(
            AosStatSessionIntervalDistributionHourly.hour_bucket.label("time_bucket"),
            AosStatSessionIntervalDistributionHourly.bucket_lt_10s.label("bucket_lt_10s"),
            AosStatSessionIntervalDistributionHourly.bucket_10_20s.label("bucket_10_20s"),
            AosStatSessionIntervalDistributionHourly.bucket_1_3min.label("bucket_1_3min"),
            AosStatSessionIntervalDistributionHourly.bucket_3_5min.label("bucket_3_5min"),
            AosStatSessionIntervalDistributionHourly.bucket_5_10min.label("bucket_5_10min"),
            AosStatSessionIntervalDistributionHourly.bucket_10_20min.label("bucket_10_20min"),
            AosStatSessionIntervalDistributionHourly.bucket_gt_20min.label("bucket_gt_20min"),
            AosStatSessionIntervalDistributionHourly.session_ids_interval_bucket.label("session_ids_interval"),
        ).order_by(AosStatSessionIntervalDistributionHourly.hour_bucket).all()

        # 活跃小时聚合
        active_hours_summary = self.db.query(AosStatActiveHoursHourly).filter(
            AosStatActiveHoursHourly.enterprise_id == enterprise_id,
            AosStatActiveHoursHourly.device_id == device_id,
            AosStatActiveHoursHourly.hour_bucket >= start_time,
            AosStatActiveHoursHourly.hour_bucket < end_time
        ).with_entities(
            AosStatActiveHoursHourly.hour_bucket.label("time_bucket"),
            AosStatActiveHoursHourly.consecutive_active_hours.label("consecutive_active_hours"),
            AosStatActiveHoursHourly.assistant_first_ratio.label("assistant_first_ratio"),
            AosStatActiveHoursHourly.greeting_ratio.label("greeting_ratio"),
            AosStatActiveHoursHourly.session_ids_active.label("session_ids_active"),
        ).order_by(AosStatActiveHoursHourly.hour_bucket).all()

        # 用户问题聚合
        question_summary = self.db.query(
            AosStatUserQuestionTopNHourly.hour_bucket.label("time_bucket"),
            AosStatUserQuestionTopNHourly.question_content,
            func.sum(AosStatUserQuestionTopNHourly.question_count).label("count"),
            func.json_arrayagg(AosStatUserQuestionTopNHourly.session_ids).label("session_ids")
        ).filter(
            AosStatUserQuestionTopNHourly.enterprise_id == enterprise_id,
            AosStatUserQuestionTopNHourly.device_id == device_id,
            AosStatUserQuestionTopNHourly.hour_bucket >= start_time,
            AosStatUserQuestionTopNHourly.hour_bucket < end_time
        ).group_by(
            AosStatUserQuestionTopNHourly.hour_bucket,
            AosStatUserQuestionTopNHourly.question_content
        ).all()
        
        # 动作行为聚合
        action_summary = self.db.query(
            AosStatActionBehaviorHourly.hour_bucket.label("time_bucket"),
            AosStatActionBehaviorHourly.action_name,
            AosStatActionBehaviorHourly.action_display_name,
            func.sum(AosStatActionBehaviorHourly.action_count).label("count"),
            func.json_arrayagg(AosStatActionBehaviorHourly.session_ids).label("session_ids")
        ).filter(
            AosStatActionBehaviorHourly.enterprise_id == enterprise_id,
            AosStatActionBehaviorHourly.device_id == device_id,
            AosStatActionBehaviorHourly.hour_bucket >= start_time,
            AosStatActionBehaviorHourly.hour_bucket < end_time
        ).group_by(
            AosStatActionBehaviorHourly.hour_bucket,
            AosStatActionBehaviorHourly.action_name,
            AosStatActionBehaviorHourly.action_display_name
        ).all()
        
        # 用户偏好聚合
        preference_summary = self.db.query(
            AosStatUserPreferenceDetailHourly.hour_bucket.label("time_bucket"),
            AosStatUserPreferenceDetailHourly.field_name,
            AosStatUserPreferenceDetailHourly.field_value,
            func.count(AosStatUserPreferenceDetailHourly.field_value).label("count"),
            func.json_arrayagg(AosStatUserPreferenceDetailHourly.session_id).label("session_ids")
        ).filter(
            AosStatUserPreferenceDetailHourly.enterprise_id == enterprise_id,
            AosStatUserPreferenceDetailHourly.device_id == device_id,
            AosStatUserPreferenceDetailHourly.hour_bucket >= start_time,
            AosStatUserPreferenceDetailHourly.hour_bucket < end_time
        ).group_by(
            AosStatUserPreferenceDetailHourly.hour_bucket,
            AosStatUserPreferenceDetailHourly.field_name,
            AosStatUserPreferenceDetailHourly.field_value
        ).all()
        
        # 合并所有出现过的time_bucket
        all_buckets = set()
        for rows in [session_summary, message_summary, event_summary, duration_summary, interval_summary, active_hours_summary, question_summary, action_summary, preference_summary]:
            all_buckets.update([row.time_bucket for row in rows])
        all_buckets = sorted(list(all_buckets))
        
        # 统一补全所有维度
        merged_data = {}
        for bucket in all_buckets:
            merged_data[bucket] = {
                "time_bucket": bucket.isoformat() if hasattr(bucket, 'isoformat') else str(bucket),
                "session_behavior_summary": {},
                "message_behavior_summary": {},
                "event_behavior_summary": {},
                "duration_distribution": {},
                "interval_distribution": {},
                "active_hours_summary": {},
                "action_behavior_summary": [],
                "user_question_summary": [],
                "user_preference_summary": []
            }
        
        # 处理会话行为数据
        for row in session_summary:
            merged_data[row.time_bucket]["session_behavior_summary"] = {
                "total_sessions": row.total_sessions or 0,
                "valid_sessions": row.valid_sessions or 0,
                "no_response_sessions": row.no_response_sessions or 0,
                "avg_conversation_turns": round(float(row.avg_conversation_turns or 0), 2),
                "avg_session_duration": round(float(row.avg_session_duration or 0), 2),
                "session_ids_total": self._flatten_json_array_of_arrays(row.session_ids_total),
                "session_ids_valid": self._flatten_json_array_of_arrays(row.session_ids_valid),
            }
        
        # 处理消息行为数据
        for row in message_summary:
            if row.time_bucket in merged_data:
                merged_data[row.time_bucket]["message_behavior_summary"] = {
                    "user_messages": row.user_messages or 0,
                    "assistant_messages": row.assistant_messages or 0,
                    "action_trigger_count": row.action_trigger_count or 0,
                    "event_trigger_count": row.event_trigger_count or 0,
                    "session_ids_user": self._flatten_json_array_of_arrays(row.session_ids_user),
                    "session_ids_assistant": self._flatten_json_array_of_arrays(row.session_ids_assistant),
                }
        
        # 处理事件行为数据
        for row in event_summary:
            if row.time_bucket in merged_data:
                merged_data[row.time_bucket]["event_behavior_summary"] = {
                    "total_events": row.total_events or 0,
                    "event_path_freq": row.event_path_freq or "",
                    "target_scene_freq": row.target_scene_freq or "",
                    "session_ids_events": self._flatten_json_array_of_arrays(row.session_ids_events),
                }
        
        # 处理会话时长分布数据
        for row in duration_summary:
            if row.time_bucket in merged_data:
                merged_data[row.time_bucket]["duration_distribution"] = {
                    "bucket_lt_30s": row.bucket_lt_30s or 0,
                    "bucket_30_60s": row.bucket_30_60s or 0,
                    "bucket_1_3min": row.bucket_1_3min or 0,
                    "bucket_3_5min": row.bucket_3_5min or 0,
                    "bucket_5_10min": row.bucket_5_10min or 0,
                    "bucket_10_20min": row.bucket_10_20min or 0,
                    "bucket_gt_20min": row.bucket_gt_20min or 0,
                    "session_ids_duration": self._flatten_json_array_of_arrays(row.session_ids_duration),
                }
        
        # 处理会话间隔分布数据
        for row in interval_summary:
            if row.time_bucket in merged_data:
                merged_data[row.time_bucket]["interval_distribution"] = {
                    "bucket_lt_10s": row.bucket_lt_10s or 0,
                    "bucket_10_20s": row.bucket_10_20s or 0,
                    "bucket_1_3min": row.bucket_1_3min or 0,
                    "bucket_3_5min": row.bucket_3_5min or 0,
                    "bucket_5_10min": row.bucket_5_10min or 0,
                    "bucket_10_20min": row.bucket_10_20min or 0,
                    "bucket_gt_20min": row.bucket_gt_20min or 0,
                    "session_ids_interval": self._flatten_json_array_of_arrays(row.session_ids_interval),
                }
        
        # 处理活跃小时数据
        for row in active_hours_summary:
            if row.time_bucket in merged_data:
                merged_data[row.time_bucket]["active_hours_summary"] = {
                    "consecutive_active_hours": row.consecutive_active_hours or 0,
                    "assistant_first_ratio": float(row.assistant_first_ratio or 0),
                    "greeting_ratio": float(row.greeting_ratio or 0),
                    "session_ids_active": self._flatten_json_array_of_arrays(row.session_ids_active),
                }
        
        # 处理user_question_summary
        for row in question_summary:
            merged_data[row.time_bucket]["user_question_summary"].append({
                "question": row.question_content,
                "count": row.count or 0,
                "session_ids": self._flatten_json_array_of_arrays(row.session_ids)
            })
        
        # 处理action_behavior_summary
        for row in action_summary:
            merged_data[row.time_bucket]["action_behavior_summary"].append({
                "name": row.action_name,
                "display_name": row.action_display_name,
                "count": row.count or 0,
                "session_ids": self._flatten_json_array_of_arrays(row.session_ids)
            })
        
        # 处理user_preference_summary
        for row in preference_summary:
            merged_data[row.time_bucket]["user_preference_summary"].append({
                "field_name": row.field_name,
                "field_value": row.field_value,
                "count": row.count or 0,
                "session_ids": self._flatten_json_array_of_arrays(row.session_ids)
            })
        
        return [merged_data[b] for b in all_buckets] 