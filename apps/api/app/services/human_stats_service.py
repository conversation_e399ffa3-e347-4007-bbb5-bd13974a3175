import pandas as pd
import re
import asyncio
from datetime import datetime, timedelta, timezone
from typing import List, Optional, Dict, Any
from collections import Counter
import logging
from collections import defaultdict

from ..services.feishu_client import feishu_service
from ..models.human_stats import HumanStatsGroupedResponse
from ..services.redis_service import redis_service, CACHE_EXPIRE

logger = logging.getLogger(__name__)

BEIJING_TZ = timezone(timedelta(hours=8))

class HumanStatsService:
    def _get_text_from_field(self, field_value: Any) -> str:
        """Safely extracts text from a Feishu field, which can be a list or a string."""
        if isinstance(field_value, list) and field_value:
            # Feishu text fields can be a list of dicts
            return field_value[0].get("text", "")
        elif isinstance(field_value, str):
            return field_value
        return ""

    def _parse_extended_field(self, text_field: Any) -> Dict[str, Any]:
        text = self._get_text_from_field(text_field)
        if not text:
            return {"gender": "unknown", "age": None}
        gender_match = re.search(r"性别: (\w+)", text)
        age_match = re.search(r"年龄: (\d+)", text)
        gender = gender_match.group(1).lower() if gender_match else "unknown"
        age = int(age_match.group(1)) if age_match else None
        return {"gender": gender, "age": age}

    def _get_age_bucket(self, age: Optional[int]) -> str:
        if age is None or pd.isna(age):
            return "unknown"
        if age <= 18: return "0-18"
        if age <= 30: return "19-30"
        if age <= 45: return "31-45"
        if age <= 60: return "46-60"
        return "60+"

    def _calculate_stats_from_records(self, processed_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        if not processed_data:
            return {
                "overall_summary": {"total_records": 0, "unique_devices": 0},
                "recognition_performance_metrics": {"recognized_records_count": 0, "unrecognized_records_count": 0, "recognition_success_rate_percent": 0.0},
                "gender_distribution_metrics": {"male_count": 0, "female_count": 0, "unknown_gender_count": 0},
                "age_distribution_metrics": {"average_age": 0.0, "minimum_age": 0, "maximum_age": 0, "age_group_distribution": {}},
                "recognized_visitors_summary": [], 
                "records_by_hour_of_day": []
            }
        
        total = len(processed_data)
        all_devices = set(d["device_id"] for d in processed_data)
        ages = [d["age"] for d in processed_data if d["age"] is not None and pd.notna(d["age"])]
        
        success_count = sum(1 for d in processed_data if d["recognition_status"] == "识别成功")
        gender_counts = Counter(d["gender"] for d in processed_data)
        age_buckets = Counter(self._get_age_bucket(d["age"]) for d in processed_data)
        visitor_counts = Counter(d["visitor_name"] for d in processed_data if d["recognition_status"] == "识别成功" and d["visitor_name"] not in ["未知访客", "陌生人"])
        hourly_activity = Counter(t.hour for t in [d["trigger_time"] for d in processed_data if d["trigger_time"]])

        return {
            "overall_summary": {"total_records": total, "unique_devices": len(all_devices)},
            "recognition_performance_metrics": {
                "recognized_records_count": success_count, "unrecognized_records_count": total - success_count,
                "recognition_success_rate_percent": round((success_count / total) * 100, 2) if total > 0 else 0.0,
            },
            "gender_distribution_metrics": {"male_count": gender_counts["male"], "female_count": gender_counts["female"], "unknown_gender_count": gender_counts["unknown"]},
            "age_distribution_metrics": {
                "average_age": round(sum(ages) / len(ages), 1) if ages else 0.0,
                "minimum_age": min(ages) if ages else 0, "maximum_age": max(ages) if ages else 0,
                "age_group_distribution": dict(sorted(age_buckets.items())),
            },
            "recognized_visitors_summary": [{"visitor_name": name, "visit_count": count} for name, count in visitor_counts.most_common()],
            "records_by_hour_of_day": [{"hour_of_day": f"{h:02d}:00", "records_count": count} for h, count in sorted(hourly_activity.items())],
        }

    async def _get_and_process_data(self, start_time: Optional[datetime], end_time: Optional[datetime], device_id: Optional[str] = None, enterprise_id: Optional[str] = None) -> List[Dict[str, Any]]:
        raw_records = await feishu_service.get_all_records_by_scope(scope="human_stats")
        
        # 1. 按天、设备ID分组，原始数据直接存入Redis（不做字段调整）
        raw_grouped = defaultdict(list)
        for record in raw_records:
            fields = record.fields if hasattr(record, 'fields') else {}
            # 获取设备ID，如果没有则用'all'
            record_device_id = fields.get("设备ID", "all")
            if device_id and device_id != "all" and record_device_id != device_id:
                continue
            
            # 获取触发时间
            trigger_time_str = fields.get("触发时间")
            if not trigger_time_str:
                continue
                
            try:
                # 支持整数时间戳和字符串两种格式
                if isinstance(trigger_time_str, (int, float)):
                    # 时间戳格式（毫秒）
                    trigger_time = datetime.fromtimestamp(trigger_time_str / 1000, tz=timezone.utc)
                    trigger_time = trigger_time.astimezone(BEIJING_TZ)
                elif isinstance(trigger_time_str, str):
                    # 字符串格式
                    trigger_time = datetime.fromisoformat(trigger_time_str.replace('Z', '+00:00'))
                else:
                    logging.warning(f"未知的触发时间格式: {type(trigger_time_str)} - {trigger_time_str}")
                    continue
                    
                day_str = trigger_time.strftime('%Y-%m-%d')
                # 直接存原始记录，不做字段调整
                raw_grouped[f"{enterprise_id}:{record_device_id}:{day_str}"].append({
                    "record_id": getattr(record, 'record_id', ''),
                    "fields": fields  # 直接存原始fields
                })
            except Exception as e:
                logging.warning(f"解析时间失败: {trigger_time_str}, 错误: {e}")
                continue
        
        # 2. 原始数据存入Redis
        for key_suffix, records in raw_grouped.items():
            cache_key = f"human_stats_raw:{key_suffix}"
            redis_service.set_cache(cache_key, records, expire=7*24*3600)  # 7天TTL
            logging.info(f"[HUMAN_STATS] 原始数据已缓存: {cache_key}, 记录数: {len(records)}")
        
        # 3. 处理数据用于分析（这里做字段调整）
        processed_data = []
        for record in raw_records:
            fields = record.fields if hasattr(record, 'fields') else {}
            record_device_id = fields.get("设备ID", "all")
            if device_id and device_id != "all" and record_device_id != device_id:
                continue
            
            trigger_time_str = fields.get("触发时间")
            if not trigger_time_str:
                continue
                
            try:
                # 支持整数时间戳和字符串两种格式
                if isinstance(trigger_time_str, (int, float)):
                    # 时间戳格式（毫秒）
                    trigger_time = datetime.fromtimestamp(trigger_time_str / 1000, tz=timezone.utc)
                    trigger_time = trigger_time.astimezone(BEIJING_TZ)
                elif isinstance(trigger_time_str, str):
                    # 字符串格式
                    trigger_time = datetime.fromisoformat(trigger_time_str.replace('Z', '+00:00'))
                else:
                    logging.warning(f"未知的触发时间格式: {type(trigger_time_str)} - {trigger_time_str}")
                    continue
                    
                # 字段处理用于分析
                # 处理飞书字段格式转换
                def extract_field_value(field_value):
                    """从飞书字段中提取实际值"""
                    if isinstance(field_value, dict):
                        # 多选项格式
                        if 'value' in field_value and isinstance(field_value['value'], list):
                            return field_value['value'][0] if field_value['value'] else ''
                        # 单选项格式
                        elif 'value' in field_value:
                            return field_value['value']
                        # 其他字典格式
                        else:
                            return str(field_value)
                    elif isinstance(field_value, list):
                        return ','.join(map(str, field_value))
                    else:
                        return str(field_value) if field_value is not None else ''
                
                processed_data.append({
                    "trigger_time": trigger_time,
                    "device_id": record_device_id,
                    "enterprise_id": enterprise_id,
                    "name": extract_field_value(fields.get("姓名", "")),
                    "gender": extract_field_value(fields.get("性别", "")),
                    "age": extract_field_value(fields.get("年龄", "")),
                    "tag": extract_field_value(fields.get("标签信息", "")),
                    "module": extract_field_value(fields.get("功能模块", "")),
                    "raw_record": fields  # 保留原始数据引用
                })
            except Exception as e:
                logging.warning(f"处理记录失败: {e}")
                continue
        
        return processed_data

    async def get_human_stats(self, enterprise_id: str, device_id: Optional[str], start_time: Optional[datetime], end_time: Optional[datetime], group_by: str) -> Dict[str, Any]:
        from datetime import timedelta
        # --- 新增缓存逻辑 ---
        # 只对 group_by==day 时做分日缓存，否则按原逻辑
        if group_by == 'day' and start_time and end_time:
            # 放宽条件：支持单天或跨天查询都缓存
            # 计算查询的日期范围
            start_date = start_time.replace(tzinfo=BEIJING_TZ) if start_time else None
            end_date = end_time.replace(tzinfo=BEIJING_TZ) if end_time else None
            
            if start_date and end_date:
                # 生成日期范围内的所有日期
                current_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
                end_date_only = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
                
                # 先尝试从缓存获取所有日期的数据
                all_cached_data = []
                all_dates_have_cache = True
                
                while current_date <= end_date_only:
                    day_str = current_date.strftime('%Y-%m-%d')
                    cache_key = f"human_stats:{enterprise_id}:{device_id or 'all'}:{group_by}:{day_str}"
                    
                    # 先查当天
                    cached = redis_service.get_cache(cache_key)
                    if cached:
                        logging.info(f"[HUMAN_STATS] 命中缓存: {cache_key}")
                        all_cached_data.extend(cached.get('data', []))
                    else:
                        # 没有当天缓存，尝试前一天
                        prev_day = current_date - timedelta(days=1)
                        prev_day_str = prev_day.strftime('%Y-%m-%d')
                        prev_cache_key = f"human_stats:{enterprise_id}:{device_id or 'all'}:{group_by}:{prev_day_str}"
                        prev_cached = redis_service.get_cache(prev_cache_key)
                        if prev_cached:
                            logging.info(f"[HUMAN_STATS] 未命中当天缓存，回退前一天: {prev_cache_key}")
                            all_cached_data.extend(prev_cached.get('data', []))
                        else:
                            all_dates_have_cache = False
                            break
                    
                    current_date += timedelta(days=1)
                
                # 如果所有日期都有缓存，直接返回
                if all_dates_have_cache and all_cached_data:
                    result = {
                        "query_summary": {
                            "enterprise_id": enterprise_id, "device_id": device_id,
                            "start_time": start_date.isoformat(),
                            "end_time": end_date.isoformat(),
                            "group_by": group_by,
                        },
                        "data": all_cached_data
                    }
                    logging.info(f"[HUMAN_STATS] 全部命中缓存，返回 {len(all_cached_data)} 条数据")
                    return result
                
                # 部分或全部没有缓存，需要拉取数据
                logging.info(f"[HUMAN_STATS] 部分或全部未命中缓存，拉取飞书数据")
                # 拉取并处理数据（带enterprise_id参数）
                processed_data = await self._get_and_process_data(start_time, end_time, device_id, enterprise_id)
                
                # 组装data_list
                df = pd.DataFrame(processed_data)
                df['trigger_time'] = pd.to_datetime(df['trigger_time'], errors='coerce')
                df = df.dropna(subset=['trigger_time'])
                df['period'] = df['trigger_time'].dt.strftime('%Y-%m-%d')
                grouped = df.groupby('period')
                data_list = []
                
                for period, group_df in grouped:
                    group_records = group_df.to_dict('records')
                    stats = self._calculate_stats_from_records(group_records)
                    stats['time_bucket'] = period
                    data_list.append(stats)
                    
                    # 为每个日期单独缓存
                    day_cache_key = f"human_stats:{enterprise_id}:{device_id or 'all'}:{group_by}:{period}"
                    day_result = {
                        "query_summary": {
                            "enterprise_id": enterprise_id, "device_id": device_id,
                            "start_time": start_date.isoformat(),
                            "end_time": end_date.isoformat(),
                            "group_by": group_by,
                        },
                        "data": [stats]
                    }
                    redis_service.set_cache(day_cache_key, day_result, expire=600)
                    logging.info(f"[HUMAN_STATS] 缓存日期数据: {day_cache_key}")
                
                data_list.sort(key=lambda x: x['time_bucket'])
                result = {
                    "query_summary": {
                        "enterprise_id": enterprise_id, "device_id": device_id,
                        "start_time": start_date.isoformat(),
                        "end_time": end_date.isoformat(),
                        "group_by": group_by,
                    },
                    "data": data_list
                }
                return result
        # 其他情况，走原有逻辑
        # 拉取并处理数据（带enterprise_id参数）
        processed_data = await self._get_and_process_data(start_time, end_time, device_id, enterprise_id)
        if not processed_data:
            return {"query_summary": {"enterprise_id": enterprise_id, "device_id": device_id, "start_time": str(start_time), "end_time": str(end_time), "group_by": group_by}, "data": []}
        df = pd.DataFrame(processed_data)
        if 'trigger_time' not in df.columns:
            return {"query_summary": {"enterprise_id": enterprise_id, "device_id": device_id, "start_time": str(start_time), "end_time": str(end_time), "group_by": group_by}, "data": []}
        df['trigger_time'] = pd.to_datetime(df['trigger_time'], errors='coerce')
        df = df.dropna(subset=['trigger_time'])
        # 修复tag字段为list时报错的问题
        if 'tag' in df.columns:
            df['tag'] = df['tag'].apply(lambda x: ','.join(map(str, x)) if isinstance(x, list) else (str(x) if x is not None else ''))
        # 按小时分组
        df['hour_bucket'] = df['trigger_time'].dt.strftime('%Y-%m-%d %H:00')
        # 1. 分小时+分功能模块聚合
        data = []
        for hour, hour_df in df.groupby('hour_bucket'):
            module_stats = {}
            for module, mod_df in hour_df.groupby('module'):
                if mod_df.empty:
                    continue
                try:
                    # 安全地获取各个字段的统计
                    gender_dist = {}
                    if 'gender' in mod_df.columns:
                        gender_series = mod_df['gender']
                        if not gender_series.empty:
                            # 确保gender_series是Series而不是dict
                            if hasattr(gender_series, 'value_counts'):
                                gender_dist = dict(gender_series.value_counts())
                    
                    age_dist = {}
                    if 'age' in mod_df.columns:
                        age_series = mod_df['age']
                        if not age_series.empty:
                            if hasattr(age_series, 'value_counts'):
                                age_dist = dict(age_series.value_counts())
                    
                    tag_dist = {}
                    if 'tag' in mod_df.columns:
                        tag_series = mod_df['tag']
                        if not tag_series.empty:
                            if hasattr(tag_series, 'value_counts'):
                                tag_dist = dict(tag_series.value_counts())
                    
                    top_visitors = {}
                    if 'name' in mod_df.columns:
                        name_series = mod_df['name']
                        if not name_series.empty:
                            if hasattr(name_series, 'value_counts'):
                                top_visitors = name_series.value_counts().head(5).to_dict()
                    
                    module_stats[module] = {
                        "total_count": len(mod_df),
                        "unique_visitors": mod_df['name'].nunique() if 'name' in mod_df.columns else 0,
                        "tag_distribution": tag_dist,
                        "gender_distribution": gender_dist,
                        "age_distribution": age_dist,
                        "top_visitors": top_visitors,
                        "raw_records": mod_df['raw_record'].tolist() if 'raw_record' in mod_df.columns else []
                    }
                except Exception as e:
                    logging.error(f"处理模块 {module} 统计时出错: {e}")
                    # 提供默认值
                    module_stats[module] = {
                        "total_count": len(mod_df),
                        "unique_visitors": 0,
                        "tag_distribution": {},
                        "gender_distribution": {},
                        "age_distribution": {},
                        "top_visitors": {},
                        "raw_records": []
                    }
            # 2. 人的档案聚合（该小时内）
            person_stats = {}
            for visitor, vdf in hour_df.groupby('name'):
                if vdf.empty:
                    continue
                try:
                    # 安全地获取各个字段
                    gender = ''
                    if 'gender' in vdf.columns and not vdf.empty:
                        gender_series = vdf['gender']
                        if not gender_series.empty and hasattr(gender_series, 'iloc'):
                            gender = gender_series.iloc[0] if not gender_series.empty else ''
                    
                    age = ''
                    if 'age' in vdf.columns and not vdf.empty:
                        age_series = vdf['age']
                        if not age_series.empty and hasattr(age_series, 'iloc'):
                            age = age_series.iloc[0] if not age_series.empty else ''
                    
                    first_seen = ''
                    last_seen = ''
                    if not vdf.empty and 'trigger_time' in vdf.columns:
                        time_series = vdf['trigger_time']
                        if hasattr(time_series, 'min') and hasattr(time_series, 'max'):
                            first_seen = time_series.min().strftime('%Y-%m-%d %H:%M:%S')
                            last_seen = time_series.max().strftime('%Y-%m-%d %H:%M:%S')
                    
                    module_usage = {}
                    if 'module' in vdf.columns and not vdf['module'].empty:
                        module_series = vdf['module']
                        if hasattr(module_series, 'value_counts'):
                            module_usage = dict(module_series.value_counts())
                    
                    hourly_activity = {}
                    if not vdf.empty and 'trigger_time' in vdf.columns:
                        time_series = vdf['trigger_time']
                        if hasattr(time_series, 'dt') and hasattr(time_series.dt, 'strftime'):
                            hourly_series = time_series.dt.strftime('%H:00')
                            if hasattr(hourly_series, 'value_counts'):
                                hourly_activity = dict(hourly_series.value_counts())
                    
                    tags = []
                    if 'tag' in vdf.columns:
                        tag_series = vdf['tag']
                        if hasattr(tag_series, 'unique'):
                            tags = list(tag_series.unique())
                    
                    raw_records = []
                    if 'raw_record' in vdf.columns:
                        raw_series = vdf['raw_record']
                        if hasattr(raw_series, 'tolist'):
                            raw_records = raw_series.tolist()
                    
                    person_stats[visitor] = {
                        "gender": gender,
                        "age": age,
                        "first_seen": first_seen,
                        "last_seen": last_seen,
                        "total_visits": len(vdf),
                        "module_usage": module_usage,
                        "hourly_activity": hourly_activity,
                        "tags": tags,
                        "raw_records": raw_records
                    }
                except Exception as e:
                    logging.error(f"处理访客 {visitor} 统计时出错: {e}")
                    # 提供默认值
                    person_stats[visitor] = {
                        "gender": '',
                        "age": '',
                        "first_seen": '',
                        "last_seen": '',
                        "total_visits": len(vdf),
                        "module_usage": {},
                        "hourly_activity": {},
                        "tags": [],
                        "raw_records": []
                    }
            data.append({
                "time_bucket": hour,
                "module_stats": module_stats,
                "person_stats": person_stats
            })
        # 构建响应
        response = {
            "query_summary": {
                "enterprise_id": enterprise_id,
                "device_id": device_id,
                "start_time": str(start_time),
                "end_time": str(end_time),
                "group_by": group_by
            },
            "data": data
        }
        
        # 4. 聚合数据单独存入Redis
        if group_by == 'day' and start_time and end_time:
            if (end_time - start_time).days == 0:
                day_str = start_time.astimezone(BEIJING_TZ).strftime('%Y-%m-%d')
                agg_cache_key = f"human_stats_agg:{enterprise_id}:{device_id or 'all'}:{group_by}:{day_str}"
                redis_service.set_cache(agg_cache_key, response, expire=3600)  # 1小时TTL
                logging.info(f"[HUMAN_STATS] 聚合数据已缓存: {agg_cache_key}")
        
        return response

human_stats_service = HumanStatsService() 