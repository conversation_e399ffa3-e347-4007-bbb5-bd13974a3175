import logging
from typing import List
from copy import deepcopy
from ...services.feishu_client import feishu_service
from ...config.feishu_config import (
    FEISHU_BITABLE_CONFIG_AOS_UPGRADE_CN,
    FEISHU_BITABLE_CONFIG_AOS_UPGRADE_OS,
    FEISHU_BITABLE_CONFIG_BUGS_CN,
    FEISHU_BITABLE_CONFIG_BUGS_OS,
)

logger = logging.getLogger(__name__)

async def get_device_sns_by_scope(scope: str) -> List[str]:
    """
    根据指定的scope从飞书多维表格获取设备SN列表。
    该实现严格参考 routers/devices.py 中的数据获取逻辑。
    """
    sns = set()
    logger.info(f"开始从飞书多维表格获取 scope: {scope} 的设备SN...")

    try:
        # 1. 根据scope选择配置
        if scope == "cn":
            base_config = FEISHU_BITABLE_CONFIG_AOS_UPGRADE_CN
        elif scope == "global":
            base_config = FEISHU_BITABLE_CONFIG_AOS_UPGRADE_OS
        else:
            raise ValueError(f"不支持的scope: {scope}")

        # 创建配置的深拷贝以避免修改全局配置
        bitable_config = deepcopy(base_config)

        # 2. 如果是wiki下的多维表格，需要先获取真实token
        if bitable_config.get("is_wiki"):
            logger.info(f"检测到 scope: {scope} 为 is_wiki 类型，正在获取真实token...")
            real_token = feishu_service.get_real_bitable_token_from_wiki_node(
                bitable_config["wiki_node_token"]
            )
            bitable_config["app_token"] = real_token
            logger.info(f"已获取 scope: {scope} 的真实token。")

        # 3. 使用最终确定的配置调用核心服务
        table_data = await feishu_service.get_table_records(scope=scope, override_config=bitable_config)
        
        if not table_data or not table_data.get("items"):
            logger.warning(f"Scope '{scope}' 没有返回任何设备记录。")
            return []
        print(table_data)
        # 4. 从记录中解析SN
        for record in table_data["items"]:
            #fields = getattr(record, 'fields', record.get("fields", {}))
            # 检查记录类型
            if hasattr(record, 'fields'):
                # 如果是 AppTableRecord 对象
                fields = record.fields
                created_time = record.created_time
                last_modified_time = record.last_modified_time
                created_by = record.created_by
                last_modified_by = record.last_modified_by
            else:
                # 如果是字典
                fields = record["fields"]
                created_time = record["created_time"]
                last_modified_time = record["last_modified_time"]
                created_by = record["created_by"]
                last_modified_by = record["last_modified_by"]
            
            if "设备sn" in fields and fields["设备sn"]:
                device_sn = fields["设备sn"][0].get("text")
                if device_sn:
                    sns.add(device_sn)
        
        logger.info(f"从 scope: {scope} 共获取到 {len(sns)} 个唯一的设备SN。")

    except Exception as e:
        logger.error(f"从飞书获取 scope '{scope}' 的数据时出错: {e}", exc_info=True)
        return [] # 出错时返回空列表
            
    return list(sns)

async def get_bug_records_by_device_id(device_id: str, scope: str = "cn") -> List[dict]:
    """
    根据设备SN和scope从飞书多维表格获取关联的bug记录。
    scope: 'cn' - 国内, 'global' - 海外
    """
    if not device_id:
        logger.warning("请求查询bug记录，但未提供device_id。")
        return []

    logger.info(f"开始从飞书多维表格获取 device_id: {device_id}, scope: {scope} 的bug记录...")
    
    try:
        # 1. 根据scope选择 Bug 跟踪表的配置
        if scope == "cn":
            bitable_config = FEISHU_BITABLE_CONFIG_BUGS_CN
        elif scope == "global":
            bitable_config = FEISHU_BITABLE_CONFIG_BUGS_OS
        else:
            logger.warning(f"不支持的 scope: {scope}，无法查询bug记录。")
            return []

        # 2. 构建过滤器，注意字段名需要与多维表格中的完全一致
        # 根据截图，字段名为"机器SN"
        filter_str = f"CurrentValue.[机器SN]=\"{device_id}\""

        # 3. 调用核心服务获取记录
        table_data = await feishu_service.get_table_records(
            override_config=bitable_config,
            filter=filter_str
        )
        
        if not table_data or not table_data.get("items"):
            logger.info(f"设备SN '{device_id}' 没有查询到任何bug记录。")
            return []

        # 4. 从记录中解析所需字段
        bug_records = []
        for record in table_data["items"]:
            if hasattr(record, 'fields'):
                fields = record.fields
            else:
                fields = record.get("fields", {})
            bug_records.append(fields)
        
        logger.info(f"为 device_id: {device_id} 共获取到 {len(bug_records)} 条bug记录。")
        return bug_records

    except Exception as e:
        logger.error(f"为 device_id '{device_id}' 获取bug记录时出错: {e}", exc_info=True)
        return [] # 出错时返回空列表 