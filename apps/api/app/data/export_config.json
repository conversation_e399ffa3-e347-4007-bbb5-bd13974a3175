[{"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}, {"export_timestamp": "2025-07-15T13:23:00.308615", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-15", "end_date": "2025-07-15", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-15T15:11:20.156392", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-15", "end_date": "2025-07-15", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-15T15:12:44.175840", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-15", "end_date": "2025-07-15", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-15T15:13:26.450790", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-15", "end_date": "2025-07-15", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-15T15:15:27.797092", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-15", "end_date": "2025-07-15", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-15T15:20:55.669794", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-15", "end_date": "2025-07-15", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-15T15:28:28.085415", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-15", "end_date": "2025-07-15", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-15T15:30:50.680260", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-15", "end_date": "2025-07-15", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-15T15:34:07.335715", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-15", "end_date": "2025-07-15", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-15T15:37:04.876248", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-15", "end_date": "2025-07-15", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-15T15:37:23.726445", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-15", "end_date": "2025-07-15", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-15T15:37:55.887418", "enterprise_id": "orion.ovs.entprise.2808534718", "device_id": "M03SCN2A23025122K86C", "start_date": "2025-07-15", "end_date": "2025-07-15", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-15T15:38:29.404634", "enterprise_id": "orion.ovs.entprise.2808534718", "device_id": "M03SCN2A23025122K86C", "start_date": "2025-07-15", "end_date": "2025-07-15", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-15T15:43:51.421042", "enterprise_id": "orion.ovs.entprise.2808534718", "device_id": "M03SCN2A23025122K86C", "start_date": "2025-07-15", "end_date": "2025-07-15", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-18T23:19:06.089142", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-17", "end_date": "2025-07-18", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-18T23:19:11.256652", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-17", "end_date": "2025-07-18", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-18T23:19:15.860218", "enterprise_id": "orion.ovs.entprise.2808534718", "device_id": "M03SCN2A23025122K86C", "start_date": "2025-07-17", "end_date": "2025-07-18", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-18T23:19:21.684801", "enterprise_id": "orion.ovs.entprise.2808534718", "device_id": "M03SCN2A23025122HB6A", "start_date": "2025-07-17", "end_date": "2025-07-18", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-19T00:58:57.460416", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-10", "end_date": "2025-07-18", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-19T06:15:57.000728", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-08", "end_date": "2025-07-18", "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-21T02:35:44.059931", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "M03SCN2A170252017BB1", "start_date": "2025-07-15", "end_date": "2025-07-21", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-21T02:35:50.533460", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-15", "end_date": "2025-07-21", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-21T02:35:58.337423", "enterprise_id": "orion.ovs.entprise.2808534718", "device_id": "M03SCN2A23025122K86C", "start_date": "2025-07-15", "end_date": "2025-07-21", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-21T02:36:04.435573", "enterprise_id": "orion.ovs.entprise.2808534718", "device_id": "M03SCN2A23025122HB6A", "start_date": "2025-07-15", "end_date": "2025-07-21", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-22T02:28:43.889771", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "M03SCN2A170252017BB1", "start_date": "2025-07-16", "end_date": "2025-07-22", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-22T02:28:50.749581", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-16", "end_date": "2025-07-22", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-22T02:28:57.550910", "enterprise_id": "orion.ovs.entprise.2808534718", "device_id": "M03SCN2A23025122K86C", "start_date": "2025-07-16", "end_date": "2025-07-22", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-22T02:29:04.479335", "enterprise_id": "orion.ovs.entprise.2808534718", "device_id": "M03SCN2A23025122HB6A", "start_date": "2025-07-16", "end_date": "2025-07-22", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-28T11:08:12.919276", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "M03SCN2A170252017BB1", "start_date": "2025-07-22", "end_date": "2025-07-28", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-28T11:08:18.575147", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-22", "end_date": "2025-07-28", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-28T11:08:26.570836", "enterprise_id": "orion.ovs.entprise.2808534718", "device_id": "M03SCN2A23025122K86C", "start_date": "2025-07-22", "end_date": "2025-07-28", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-28T11:08:35.239633", "enterprise_id": "orion.ovs.entprise.2808534718", "device_id": "M03SCN2A23025122HB6A", "start_date": "2025-07-22", "end_date": "2025-07-28", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-29T03:04:51.184350", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "M03SCN2A170252017BB1", "start_date": "2025-07-23", "end_date": "2025-07-29", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-29T03:04:59.743327", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-23", "end_date": "2025-07-29", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-29T03:05:07.529589", "enterprise_id": "orion.ovs.entprise.2808534718", "device_id": "M03SCN2A23025122K86C", "start_date": "2025-07-23", "end_date": "2025-07-29", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-29T03:05:14.853609", "enterprise_id": "orion.ovs.entprise.2808534718", "device_id": "M03SCN2A23025122HB6A", "start_date": "2025-07-23", "end_date": "2025-07-29", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-29T13:30:16.403306", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "M03SCN2A170252017BB1", "start_date": "2025-07-23", "end_date": "2025-07-29", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-29T13:30:21.897360", "enterprise_id": "orion.ovs.entprise.4498860269", "device_id": "MC1BCNC016021348B693", "start_date": "2025-07-23", "end_date": "2025-07-29", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-29T13:30:30.146634", "enterprise_id": "orion.ovs.entprise.2808534718", "device_id": "M03SCN2A23025122K86C", "start_date": "2025-07-23", "end_date": "2025-07-29", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}, {"export_timestamp": "2025-07-29T13:30:38.799924", "enterprise_id": "orion.ovs.entprise.2808534718", "device_id": "M03SCN2A23025122HB6A", "start_date": "2025-07-23", "end_date": "2025-07-29", "days_back": 7, "merge_interval": 30, "user_content_format": "merge", "config": {"merge_interval_seconds": 30, "output_format": "txt", "include_sensitive_data": false, "max_message_length": 1000, "compress_output": false, "output_encoding": "utf-8", "include_statistics": true, "include_user_preferences": true, "include_action_event_data": true, "file_separator": "================================================================================", "log_level": "INFO", "batch_size": 1000, "show_progress": true, "filename_pattern": "{enterprise_id}/{device_id}/{date}.txt", "create_index_file": true, "index_filename": "export_index.json", "validate_output": true, "max_concurrent_processes": 1, "temp_dir": "/tmp/enterprise_export", "keep_temp_files": false, "file_permissions": 420, "add_file_header": true, "file_header_template": "# 企业数据导出文件\n# 企业ID: {enterprise_id}\n# 设备ID: {device_id}\n# 导出日期: {export_date}\n# 数据日期: {data_date}\n# 合并间隔: {merge_interval}秒\n# 导出时间: {export_timestamp}\n# 数据条数: {chat_count}\n# 会话数: {session_count}\n# 消息数: {message_count}\n# 分隔符: {separator}\n", "include_data_summary": true, "summary_fields": ["chat_id", "chat_start_time", "chat_end_time", "interaction_rounds", "user_message_count", "assistant_message_count", "action_count", "event_count"], "include_detailed_messages": true, "message_content_filters": {"min_length": 1, "max_length": 10000, "exclude_patterns": [], "include_patterns": []}, "include_session_metadata": true, "session_metadata_fields": ["session_id", "project_id", "app_id", "group_id", "client_id", "product_id", "product_model", "timezone"], "include_message_metadata": true, "message_metadata_fields": ["message_index", "role", "message_timestamp", "app_id", "page_id", "agent_id", "face_id"], "data_quality_checks": {"check_timestamp_order": true, "check_message_sequence": true, "check_session_integrity": true, "check_data_completeness": true}, "error_handling": {"continue_on_error": true, "log_errors": true, "save_error_log": true, "max_retries": 3}, "performance_options": {"use_bulk_queries": true, "cache_session_data": true, "cache_message_data": true, "optimize_memory_usage": true}, "use_dialogue_format": true, "user_content_format": "merge"}}]