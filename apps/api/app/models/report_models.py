# -*- coding: utf-8 -*-
"""
报告相关数据模型 - 简化版
只保留一张表记录请求参数和LLM生成结果
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Enum, Index
from datetime import datetime
import enum
from ..database import Base_speech_ai_robot as Base


class ReportStatus(enum.Enum):
    """报告状态枚举"""
    GENERATING = "generating"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ReportType(enum.Enum):
    """报告类型枚举"""
    SINGLE_DAY = "single_day"
    MULTI_DAY = "multi_day"


class PromptType(enum.Enum):
    """Prompt类型枚举"""
    SYSTEM = "system"
    USER = "user"
    CUSTOM = "custom"


class DeviceReport(Base):
    """设备报告生成记录表 - 记录请求参数和生成结果"""
    __tablename__ = "aos_device_reports"
    __table_args__ = {'comment': '设备报告生成记录表'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    enterprise_id = Column(String(100), nullable=False, comment='企业ID')
    device_id = Column(String(100), nullable=False, comment='设备ID')
    report_type = Column(String(20), nullable=False, comment='报告类型')
    status = Column(String(20), default=ReportStatus.GENERATING.value, comment='报告状态')
    
    # 请求参数
    target_date = Column(String(10), nullable=False, comment='目标日期(YYYY-MM-DD)')
    start_date = Column(String(10), comment='开始日期(多天报告)')
    end_date = Column(String(10), comment='结束日期(多天报告)')
    prompt_version = Column(String(50), comment='Prompt版本')
    custom_system_prompt = Column(Text, comment='自定义系统Prompt')
    custom_user_prompt = Column(Text, comment='自定义用户Prompt')
    prompt_type = Column(String(20), default=PromptType.SYSTEM.value, comment='Prompt类型')
    
    # LLM生成结果
    system_prompt_used = Column(Text, comment='实际使用的系统Prompt')
    user_prompt_used = Column(Text, comment='实际使用的用户Prompt')
    generated_content = Column(Text, comment='生成的报告内容')
    model_name = Column(String(50), comment='使用的模型名称')
    
    # 性能指标
    content_length = Column(Integer, default=0, comment='内容长度')
    chunk_count = Column(Integer, default=0, comment='内容块数量')
    tokens_used = Column(Integer, comment='使用的Token数量')
    generation_start_time = Column(DateTime, comment='生成开始时间')
    generation_end_time = Column(DateTime, comment='生成结束时间')
    generation_duration = Column(Integer, comment='生成耗时(秒)')
    
    # 错误信息
    error_message = Column(Text, comment='错误信息')
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 索引
    __table_args__ = (
        Index('idx_enterprise_device_date', 'enterprise_id', 'device_id', 'target_date'),
        Index('idx_report_type_status', 'report_type', 'status'),
        Index('idx_prompt_type', 'prompt_type'),
        Index('idx_prompt_version', 'prompt_version'),
        Index('idx_created_at', 'created_at'),
        {'comment': '设备报告生成记录表'}
    ) 