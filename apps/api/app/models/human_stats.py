from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional

# --- Pydantic Models for Response ---

class RecognitionPerformanceMetrics(BaseModel):
    recognized_records_count: int = Field(..., description="识别成功的记录总数")
    unrecognized_records_count: int = Field(..., description="识别为'陌生人'的记录总数")
    recognition_success_rate_percent: float = Field(..., description="识别成功率（百分比）")

class GenderDistributionMetrics(BaseModel):
    male_count: int = Field(..., description="识别为男性的总数")
    female_count: int = Field(..., description="识别为女性的总数")
    unknown_gender_count: int = Field(..., description="性别未知的总数")

class AgeDistributionMetrics(BaseModel):
    average_age: float = Field(..., description="所有识别出年龄的访客的平均年龄")
    minimum_age: int = Field(..., description="最小年龄")
    maximum_age: int = Field(..., description="最大年龄")
    age_group_distribution: Dict[str, int] = Field(..., description="按年龄段划分的访客数量分布")

class TopVisitor(BaseModel):
    visitor_name: str = Field(..., description="访客姓名")
    visit_count: int = Field(..., description="来访次数")

class HourlyActivity(BaseModel):
    hour_of_day: str = Field(..., description="小时（24小时制，例如 '09:00'）")
    records_count: int = Field(..., description="该小时内的记录总数")

class HumanStatsData(BaseModel):
    time_bucket: Optional[str] = Field(None, description="数据聚合的时间段 (例如, '2023-10-27' 或 '2023-10-27 15:00')")
    overall_summary: Dict[str, Any] = Field(..., description="关于记录总数和独立设备数的摘要信息")
    recognition_performance_metrics: RecognitionPerformanceMetrics
    gender_distribution_metrics: GenderDistributionMetrics
    age_distribution_metrics: AgeDistributionMetrics
    recognized_visitors_summary: List[TopVisitor] = Field(..., description="所有识别出的访客及其来访次数的完整列表")
    records_by_hour_of_day: List[HourlyActivity]

class QuerySummary(BaseModel):
    enterprise_id: str
    device_id: Optional[str]
    start_time: str
    end_time: str
    group_by: str

class HumanStatsGroupedResponse(BaseModel):
    query_summary: QuerySummary
    data: List[HumanStatsData] 

class ModuleStats(BaseModel):
    total_count: int
    unique_visitors: int
    tag_distribution: Dict[str, int]
    gender_distribution: Dict[str, int]
    age_distribution: Dict[Any, int]
    top_visitors: Dict[str, int]
    raw_records: List[Dict[str, Any]]

class PersonStats(BaseModel):
    gender: str
    age: Any
    first_seen: str
    last_seen: str
    total_visits: int
    module_usage: Dict[str, int]
    hourly_activity: Dict[str, int]
    tags: List[str]
    raw_records: List[Dict[str, Any]]

class MultiDimHumanStatsData(BaseModel):
    time_bucket: str
    module_stats: Dict[str, ModuleStats]
    person_stats: Dict[str, PersonStats]

class MultiDimHumanStatsGroupedResponse(BaseModel):
    query_summary: QuerySummary
    data: List[MultiDimHumanStatsData] 