from sqlalchemy import Column, Integer, String, DateTime, Float, ForeignKey, Enum
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from ..database import Base

class DeviceStatus(enum.Enum):
    ACTIVE_OK = "active_ok"
    USAGE_WARN = "usage_warn"
    USAGE_CRIT = "usage_crit"

class Device(Base):
    __tablename__ = "devices"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String(50), unique=True, index=True)
    name = Column(String(100))
    location = Column(String(200))
    latitude = Column(Float)
    longitude = Column(Float)
    status = Column(Enum(DeviceStatus), default=DeviceStatus.ACTIVE_OK)
    last_query_time = Column(DateTime, default=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联
    enterprise_id = Column(Integer, ForeignKey("enterprises.id"))
    enterprise = relationship("Enterprise", back_populates="devices")
    
    queries = relationship("DeviceQuery", back_populates="device")

class DeviceQuery(Base):
    __tablename__ = "device_queries"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(Integer, ForeignKey("devices.id"))
    query_time = Column(DateTime, default=datetime.utcnow)
    query_type = Column(String(50))
    query_result = Column(String(50))
    response_time = Column(Float)  # 响应时间（毫秒）
    
    device = relationship("Device", back_populates="queries") 