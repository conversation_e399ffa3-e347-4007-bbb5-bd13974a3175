from sqlalchemy import Column, BigInteger, String, Integer, DateTime, JSON, Date, Float, Text, UniqueConstraint
from ..database import Base_speech_ai_robot

class AosStatSessionBehaviorHourly(Base_speech_ai_robot):
    """会话行为统计表"""
    __tablename__ = 'aos_stat_session_behavior_hourly'
    __table_args__ = {'comment': '小时级别的会话行为统计'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    hour_bucket = Column(DateTime, nullable=False, comment='当前小时（精确到小时）')
    enterprise_id = Column(String(64), nullable=False, comment='企业ID')
    device_id = Column(String(128), nullable=False, comment='设备ID')

    session_total = Column(Integer, default=0, comment='总会话数')
    session_ids_session_total = Column(JSON, comment='该指标对应的会话ID列表')

    valid_sessions = Column(Integer, default=0, comment='有效会话数（有user+assistant消息）')
    session_ids_valid_sessions = Column(JSON, comment='该指标对应的会话ID列表')

    no_response_sessions = Column(Integer, default=0, comment='用户说话但assistant无回应的会话数')
    session_ids_no_response_sessions = Column(JSON, comment='该指标对应的会话ID列表')

    first_assistant_sessions = Column(Integer, default=0, comment='首条消息为assistant的会话数')
    session_ids_first_assistant_sessions = Column(JSON, comment='该指标对应的会话ID列表')

    first_assistant_with_user_reply = Column(Integer, default=0, comment='首assistant后有user回复的会话数')
    session_ids_first_assistant_with_user_reply = Column(JSON, comment='该指标对应的会话ID列表')

    first_user_sessions = Column(Integer, default=0, comment='首条消息为user的会话数')
    session_ids_first_user_sessions = Column(JSON, comment='该指标对应的会话ID列表')

    greeting_repeat_sessions = Column(Integer, default=0, comment='assistant重复打招呼的会话数')
    session_ids_greeting_repeat_sessions = Column(JSON, comment='该指标对应的会话ID列表')

    avg_conversation_turns = Column(Float, default=0, comment='平均对话轮数')
    avg_session_duration = Column(Float, default=0, comment='平均会话时长（秒）')

    assistant_repeat_content_sessions = Column(Integer, default=0, comment='assistant输出重复内容的会话数')
    session_ids_assistant_repeat_content_sessions = Column(JSON, comment='该指标对应的会话ID列表')

    __table_args__ = (
        {'comment': '小时级别的会话行为统计'},
    )

class AosStatMessageBehaviorHourly(Base_speech_ai_robot):
    """消息行为统计表"""
    __tablename__ = 'aos_stat_message_behavior_hourly'
    __table_args__ = {'comment': '小时级别的消息行为统计'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    hour_bucket = Column(DateTime, nullable=False, comment='小时')
    enterprise_id = Column(String(64), nullable=False, comment='企业ID')
    device_id = Column(String(128), nullable=False, comment='设备ID')

    user_msg_count = Column(Integer, default=0, comment='用户消息数')
    session_ids_user_msg_count = Column(JSON, comment='该指标对应的会话ID列表')

    assistant_msg_count = Column(Integer, default=0, comment='assistant消息数')
    session_ids_assistant_msg_count = Column(JSON, comment='该指标对应的会话ID列表')

    avg_user_msg_length = Column(Float, default=0, comment='平均用户消息长度')
    avg_assistant_msg_length = Column(Float, default=0, comment='平均assistant消息长度')

    action_trigger_count = Column(Integer, default=0, comment='触发action的次数')
    session_ids_action_trigger_count = Column(JSON, comment='该指标对应的会话ID列表')

    event_trigger_count = Column(Integer, default=0, comment='触发event的次数')
    session_ids_event_trigger_count = Column(JSON, comment='该指标对应的会话ID列表')

    __table_args__ = (
        {'comment': '小时级别的消息行为统计'},
    )

class AosStatActionBehaviorHourly(Base_speech_ai_robot):
    """动作行为统计表"""
    __tablename__ = 'aos_stat_action_behavior_hourly'
    __table_args__ = {'comment': '小时级别的单个动作行为统计'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    hour_bucket = Column(DateTime, nullable=False, comment='小时')
    enterprise_id = Column(String(64), nullable=False, comment='企业ID')
    device_id = Column(String(128), nullable=False, comment='设备ID')
    action_name = Column(String(128), nullable=False, comment='动作英文名，如rotate,start_visitor')
    action_display_name = Column(String(128), comment='动作中文名，如转圈, 打开访客')
    action_count = Column(Integer, default=0, comment='该动作触发次数')
    session_ids = Column(JSON, comment='该动作对应的会话ID列表')

    __table_args__ = (
        {'comment': '小时级别的单个动作行为统计'},
    )

class AosStatEventBehaviorHourly(Base_speech_ai_robot):
    """场景事件行为统计表"""
    __tablename__ = 'aos_stat_event_behavior_hourly'
    __table_args__ = {'comment': '小时级别事件行为统计（非结构化）'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    hour_bucket = Column(DateTime, nullable=False, comment='小时')
    enterprise_id = Column(String(64), nullable=False, comment='企业ID')
    device_id = Column(String(128), nullable=False, comment='设备ID')
    event_total_count = Column(Integer, default=0, comment='触发event总次数')
    session_ids_event_total_count = Column(JSON, comment='该指标对应的会话ID列表')
    event_path_freq = Column(Text, comment='场景路径频次，如: "通用 → 娱乐:4,娱乐 → 推销:3"')
    target_scene_freq = Column(Text, comment='目标场景频次，如: "娱乐:4,推销:3"')

    __table_args__ = (
        {'comment': '小时级别事件行为统计（非结构化）'},
    )

class AosStatUserQuestionTopNHourly(Base_speech_ai_robot):
    """小时级别用户问题TopN内容明细表"""
    __tablename__ = 'aos_stat_user_question_topn_hourly'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    hour_bucket = Column(DateTime, nullable=False, comment='小时维度，格式：YYYY-MM-DD HH:00:00')
    enterprise_id = Column(String(64), nullable=False, comment='企业ID')
    device_id = Column(String(128), nullable=False, comment='设备ID')
    question_content = Column(String(1024), nullable=False, comment='用户提问原始内容（去除前后空格）')
    question_count = Column(Integer, default=0, comment='该content出现次数')
    session_ids = Column(JSON, comment='所有出现该问题的session_id列表')
    wordcloud_keywords = Column(JSON, comment='将所有用户提问内容分词后，统计关键词频次，如 {"你好": 5, "几点": 3}')

    __table_args__ = (
        UniqueConstraint('hour_bucket', 'enterprise_id', 'device_id', 'question_content', name='uniq_question'),
        {'comment': '小时级别用户问题TopN内容明细表'}
    )

class AosStatUserPreferenceDetailHourly(Base_speech_ai_robot):
    """用户偏好详情统计表"""
    __tablename__ = 'aos_stat_user_preference_detail_hourly'
    __table_args__ = {'comment': '小时级别用户偏好详情记录'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    hour_bucket = Column(DateTime, nullable=False, comment='小时')
    enterprise_id = Column(String(64), nullable=False, comment='企业ID')
    device_id = Column(String(128), nullable=False, comment='设备ID')
    project_id = Column(String(64), nullable=False, comment='项目ID')
    field_name = Column(String(128), nullable=False, comment='偏好字段名，如 用户使用场景')
    field_value = Column(Text, nullable=False, comment='用户填写的内容，如 酒店大堂')
    session_id = Column(String(128), comment='填写此偏好内容的会话ID')

    __table_args__ = (
        {'comment': '小时级别用户偏好详情记录'},
    )

class AosStatSessionDurationDistributionHourly(Base_speech_ai_robot):
    """会话时长分布统计表"""
    __tablename__ = 'aos_stat_session_duration_distribution_hourly'
    __table_args__ = {'comment': '小时级别的会话时长分布统计'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    hour_bucket = Column(DateTime, nullable=False, comment='小时')
    enterprise_id = Column(String(64), nullable=False, comment='企业ID')
    device_id = Column(String(128), nullable=False, comment='设备ID')
    bucket_lt_30s = Column(Integer, default=0, comment='0~30秒的会话数')
    bucket_30_60s = Column(Integer, default=0, comment='30~60秒的会话数')
    bucket_1_3min = Column(Integer, default=0, comment='1~3分钟的会话数')
    bucket_3_5min = Column(Integer, default=0, comment='3~5分钟的会话数')
    bucket_5_10min = Column(Integer, default=0, comment='5~10分钟的会话数')
    bucket_10_20min = Column(Integer, default=0, comment='10~20分钟的会话数')
    bucket_gt_20min = Column(Integer, default=0, comment='大于20分钟的会话数')
    session_ids_duration_bucket = Column(JSON, comment='各时长区间对应的 session_id 列表，Map结构')

    __table_args__ = (
        {'comment': '小时级别的会话时长分布统计'},
    )

class AosStatSessionIntervalDistributionHourly(Base_speech_ai_robot):
    """会话间隔分布统计表"""
    __tablename__ = 'aos_stat_session_interval_distribution_hourly'
    __table_args__ = {'comment': '小时级别的发言间隔分布统计'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    hour_bucket = Column(DateTime, nullable=False, comment='小时')
    enterprise_id = Column(String(64), nullable=False, comment='企业ID')
    device_id = Column(String(128), nullable=False, comment='设备ID')
    bucket_lt_10s = Column(Integer, default=0, comment='小于10秒的会话间隔数')
    bucket_10_20s = Column(Integer, default=0, comment='10-20秒')
    bucket_1_3min = Column(Integer, default=0, comment='1-3分钟')
    bucket_3_5min = Column(Integer, default=0, comment='3-5分钟')
    bucket_5_10min = Column(Integer, default=0, comment='5-10分钟')
    bucket_10_20min = Column(Integer, default=0, comment='10-20分钟')
    bucket_gt_20min = Column(Integer, default=0, comment='大于20分钟')
    session_ids_interval_bucket = Column(JSON, comment='各间隔区间对应的 session_id 列表，Map结构')

    __table_args__ = (
        {'comment': '小时级别的发言间隔分布统计'},
    )

class AosStatActiveHoursHourly(Base_speech_ai_robot):
    """连续活跃小时数统计表"""
    __tablename__ = 'aos_stat_active_hours_hourly'
    __table_args__ = {'comment': '小时级别的设备活跃行为统计'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    hour_bucket = Column(DateTime, nullable=False, comment='当前小时')
    enterprise_id = Column(String(64), nullable=False, comment='企业ID')
    device_id = Column(String(128), nullable=False, comment='设备ID')
    consecutive_active_hours = Column(Integer, default=0, comment='连续活跃小时数')
    assistant_first_ratio = Column(Float, default=0, comment='assistant首发会话占比')
    greeting_ratio = Column(Float, default=0, comment='greeting会话占比')
    session_ids_active = Column(JSON, comment='该小时活跃的 session ID 列表')

    __table_args__ = (
        {'comment': '小时级别的设备活跃行为统计'},
    ) 