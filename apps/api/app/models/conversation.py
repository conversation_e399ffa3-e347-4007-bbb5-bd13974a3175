from sqlalchemy import Column, BigInteger, String, Integer, DateTime, JSON, Date, Enum
from ..database import Base_agentos

class AosConversationSession(Base_agentos):
    __tablename__ = 'aos_conversation_sessions'
    __table_args__ = {'comment': '对话会话主表'}

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    conversation_date = Column(Date, primary_key=True, nullable=False, comment='会话日期')
    session_id = Column(String(128), nullable=False, comment='会话ID')
    enterprise_id = Column(String(64), nullable=False, comment='企业ID')
    device_id = Column(String(128), nullable=False, comment='设备ID')
    group_id = Column(String(64), default='', comment='场景ID')
    client_id = Column(String(64), default='', comment='业务线ID')
    project_id = Column(String(64), default='', comment='项目ID')
    product_id = Column(Integer, default=0, comment='产品ID')
    product_model = Column(String(64), default='', comment='产品型号')
    conversation_turns = Column(Integer, default=0, comment='对话轮数')
    timezone = Column(String(32), default='', comment='时区')
    session_start_time = Column(DateTime, nullable=False, comment='会话开始时间')
    session_end_time = Column(DateTime, nullable=False, comment='会话结束时间')
    session_duration_seconds = Column(Integer, default=0, comment='会话持续时长(秒)')
    user_preferences = Column(JSON, comment='用户信息收集')
    app_id = Column(String(64), default='', comment='应用ID')
    created_at = Column(DateTime, comment='记录创建时间')
    updated_at = Column(DateTime, comment='记录更新时间')


class AosConversationMessage(Base_agentos):
    __tablename__ = 'aos_conversation_messages'
    __table_args__ = {'comment': '对话消息详情表'}

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    message_date = Column(Date, primary_key=True, nullable=False, comment='消息日期')
    session_id = Column(String(128), nullable=False, comment='会话ID')
    message_index = Column(Integer, nullable=False, comment='消息在会话中的序号')
    role = Column(Enum('user', 'assistant'), nullable=False, comment='角色')
    content = Column(String(1024), default='', comment='消息内容(支持文本、图片混合)')
    action_data = Column(JSON, comment='动作数据完整信息')
    event_data = Column(JSON, comment='事件数据完整信息')
    app_id = Column(String(64), default='', comment='应用ID')
    page_id = Column(String(64), default='', comment='页面ID')
    agent_id = Column(String(64), default='', comment='Agent ID (待废弃)')
    face_id = Column(String(64), default='', comment='人脸ID')
    message_timestamp = Column(DateTime, nullable=False, comment='消息时间戳')
    created_at = Column(DateTime, comment='记录创建时间')
    updated_at = Column(DateTime, comment='记录更新时间') 