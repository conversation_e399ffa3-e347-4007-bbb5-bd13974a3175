from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.database import get_agentos_db
from app.services.mysql.conversation_service import conversation_service
from app.schemas import PaginatedSessionResponse
from app.services.mongodb_service import mongodb_service

# --- Pydantic Schemas ---
class Message(BaseModel):
    role: str
    content: str
    message_timestamp: datetime

    class Config:
        orm_mode = True

class Session(BaseModel):
    session_id: str
    device_id: str
    session_start_time: datetime
    session_end_time: datetime
    conversation_turns: int
    messages: List[Message] = []

    class Config:
        orm_mode = True

router = APIRouter(prefix="/device-queries", tags=["device-queries"])

class SessionByIdsRequest(BaseModel):
    session_ids: List[str]

@router.post(
    "/sessions-by-ids",
    response_model=List[Session],
    summary="通过session_ids获取会话信息"
)
async def get_sessions_by_ids(
    request: SessionByIdsRequest,
    db: Session = Depends(get_agentos_db)
):
    """
    根据 session_id 列表批量获取会话信息，包括消息列表。
    """
    try:
        sessions = conversation_service.get_sessions_by_ids(db=db, session_ids=request.session_ids)
        if not sessions:
            return []
        return sessions
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取会话信息失败: {str(e)}")

class QueryResponse(BaseModel):
    device_id: str
    query_time: datetime
    query_type: str
    query_result: str
    response_time: float
    additional_data: Optional[dict] = None

@router.get(
    "/{enterprise_id}/{device_id}/conversations",
    response_model=PaginatedSessionResponse,
    summary="获取设备对话历史记录"
)
async def get_device_conversations(
    enterprise_id: str,
    device_id: str,
    project_id: Optional[str] = Query(None, description="项目ID"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(10, description="每页数量", ge=1, le=2000),
    db: Session = Depends(get_agentos_db)
):
    """
    根据设备ID和企业ID获取设备上的对话历史记录，支持按项目ID、时间范围筛选和分页。
    """
    try:
        return conversation_service.get_conversations(
            db=db,
            enterprise_id=enterprise_id,
            project_id=project_id,
            device_id=device_id,
            start_time=start_time,
            end_time=end_time,
            page=page,
            page_size=page_size
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取设备对话历史失败: {str(e)}")

@router.get("/{device_id}", response_model=List[QueryResponse])
async def get_device_queries(
    device_id: str,
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    limit: int = Query(100, description="返回记录数量限制", ge=1, le=1000)
):
    """
    获取设备查询历史记录
    """
    try:
        queries = mongodb_service.get_device_queries(
            device_id=device_id,
            start_time=start_time,
            end_time=end_time,
            limit=limit
        )
        return queries
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取设备查询历史失败: {str(e)}")

@router.get("/{device_id}/metrics", response_model=List[dict])
async def get_device_metrics(
    device_id: str,
    metric_type: Optional[str] = Query(None, description="指标类型"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间")
):
    """
    获取设备指标数据
    """
    try:
        metrics = mongodb_service.get_device_metrics(
            device_id=device_id,
            metric_type=metric_type,
            start_time=start_time,
            end_time=end_time
        )
        return metrics
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取设备指标数据失败: {str(e)}") 