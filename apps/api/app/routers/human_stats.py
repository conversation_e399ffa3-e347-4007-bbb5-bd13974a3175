from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional, Any
from datetime import datetime

from ..services.human_stats_service import human_stats_service
from ..models.human_stats import MultiDimHumanStatsGroupedResponse

router = APIRouter(prefix="/stats/human-metrics", tags=["画像统计"])

@router.get("/", response_model=MultiDimHumanStatsGroupedResponse, summary="获取人脸识别数据的深度统计")
async def get_human_statistics(
    enterprise_id: str = Query(..., description="企业ID"),
    device_id: Optional[str] = Query(None, description="设备ID (可选，用于筛选)"),
    start_time: Optional[datetime] = Query(None, description="开始时间 (YYYY-MM-DDTHH:MM:SS)，将作为北京时间处理"),
    end_time: Optional[datetime] = Query(None, description="结束时间 (YYYY-MM-DDTHH:MM:SS)，将作为北京时间处理"),
    group_by: Optional[str] = Query("total", description="按'total', 'day'或'hour'进行分组", enum=["total", "day", "hour"]),
):
    """
    从飞书多维表格获取人脸识别数据的深度统计。

    - **重点分析访客的性别和年龄分布**
    - **统计识别成功率和高频访客**
    - **支持按天(`day`), 小时(`hour`)或总览(`total`)进行聚合统计**
    """
    try:
        # Unified call to the refactored service method
        stats = await human_stats_service.get_human_stats(
            enterprise_id=enterprise_id,
            device_id=device_id,
            start_time=start_time,
            end_time=end_time,
            group_by=group_by,
        )
        return stats
    except Exception as e:
        # For debugging, log the exception
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e)) 