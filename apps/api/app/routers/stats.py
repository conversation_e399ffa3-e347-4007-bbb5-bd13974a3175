from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging
from app.services.mysql.position_service import get_device_query_stats_by_day
from app.services.statistics_service import StatisticsService
from app.config.config import Settings, get_settings
from enum import Enum
from sqlalchemy.orm import Session
from ..database import get_db, get_speech_ai_robot_db
from app.services.stats_query_service import StatsQueryService

router = APIRouter(prefix="/stats", tags=["统计"])

logger = logging.getLogger(__name__)

# 定义请求体模型
class StatsRequest(BaseModel):
    scope: str
    device_ids: List[str]

# --- Pydantic Models for Response ---

class QuerySummary(BaseModel):
    enterprise_id: str
    device_id: str
    start_time: str
    end_time: str
    total_sessions: int
    total_messages: int

class SessionBehaviorSummary(BaseModel):
    total_sessions: int
    valid_sessions: int
    no_response_sessions: int
    avg_conversation_turns: float
    avg_session_duration: float
    session_ids_total: Optional[List[str]] = []
    session_ids_valid: Optional[List[str]] = []

class MessageBehaviorSummary(BaseModel):
    user_messages: int
    assistant_messages: int
    action_trigger_count: int
    event_trigger_count: int
    avg_user_msg_length: float
    avg_assistant_msg_length: float
    session_ids_user: Optional[List[str]] = []
    session_ids_assistant: Optional[List[str]] = []

class ActionBehaviorSummary(BaseModel):
    name: str
    display_name: Optional[str]
    count: int
    session_ids: Optional[List[str]] = []

class UserQuestionSummary(BaseModel):
    question: str
    count: int
    session_ids: Optional[List[str]] = []

class EventBehaviorSummary(BaseModel):
    total_events: int
    event_path_freq: str
    target_scene_freq: str
    session_ids: Optional[List[str]] = []

class DurationDistribution(BaseModel):
    bucket_lt_30s: int
    bucket_30_60s: int
    bucket_1_3min: int
    bucket_3_5min: int
    bucket_5_10min: int
    bucket_10_20min: int
    bucket_gt_20min: int
    session_ids: Optional[List[str]] = []

class IntervalDistribution(BaseModel):
    bucket_lt_10s: int
    bucket_10_20s: int
    bucket_1_3min: int
    bucket_3_5min: int
    bucket_5_10min: int
    bucket_10_20min: int
    bucket_gt_20min: int
    session_ids: Optional[List[str]] = []

class ActiveHoursSummary(BaseModel):
    avg_consecutive_active_hours: float
    avg_assistant_first_ratio: float
    avg_greeting_ratio: float
    session_ids: Optional[List[str]] = []

class UserPreferenceSummary(BaseModel):
    field_name: str
    field_value: str
    count: int
    session_ids: Optional[List[str]] = []

class GroupBy(str, Enum):
    TOTAL = "total"
    HOUR = "hour"
    DAY = "day"

class StatisticsSummaryResponse(BaseModel):
    data: List[Dict[str, Any]]
    query_summary: QuerySummary

@router.post("/")
async def get_device_query_stats(request: StatsRequest):
    """
    获取设备按天的query数据统计
    
    Args:
        request: 包含scope和设备ID列表的请求体
    
    Returns:
        设备query统计数据
    """
    try:
        logger.info(f"接收到POST统计请求，scope: {request.scope}, 设备数量: {len(request.device_ids)}")
        
        # 根据scope确定数据库连接
        db_key = "bigdata_cn" if request.scope == "cn" else "default"
        
        # 设置默认开始日期为30天前
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        # 调用MySQL服务查询数据
        result = get_device_query_stats_by_day(
            device_ids=request.device_ids,
            start_date=start_date,
            db_key=db_key
        )
        
        logger.info(f"查询完成，返回数据条数: {len(result)}")
        
        return {
            "success": True,
            "data": result,
            "total": len(result),
            "scope": request.scope,
            "device_count": len(request.device_ids),
            "start_date": start_date
        }
        
    except Exception as e:
        logger.error(f"查询设备统计信息失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"查询失败: {str(e)}"
        )

@router.get("/")
async def get_device_query_stats_get(
    scope: str = Query(..., description="查询范围，例如：cn"),
    device_ids: str = Query(..., description="设备ID列表，用逗号分隔")
):
    """
    获取设备按天的query数据统计 (GET方法)
    
    Args:
        scope: 查询范围
        device_ids: 设备ID列表，用逗号分隔
    
    Returns:
        设备query统计数据
    """
    try:
        # 将逗号分隔的字符串转换为列表
        device_id_list = [did.strip() for did in device_ids.split(',') if did.strip()]
        
        logger.info(f"接收到GET统计请求，scope: {scope}, 设备数量: {len(device_id_list)}")
        
        # 根据scope确定数据库连接
        db_key = "bigdata_cn" if scope == "cn" else "default"
        
        # 设置默认开始日期为30天前
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        # 调用MySQL服务查询数据
        result = get_device_query_stats_by_day(
            device_ids=device_id_list,
            start_date=start_date,
            db_key=db_key
        )
        
        logger.info(f"查询完成，返回数据条数: {len(result)}")
        
        return {
            "success": True,
            "data": result,
            "total": len(result),
            "scope": scope,
            "device_count": len(device_id_list),
            "start_date": start_date
        }
        
    except Exception as e:
        logger.error(f"查询设备统计信息失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"查询失败: {str(e)}"
        )

@router.get("/summary", response_model=StatisticsSummaryResponse)
def get_statistics_summary(
    enterprise_id: str,
    device_id: str,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    group_by: GroupBy = Query(GroupBy.TOTAL, description="聚合方式: total, hour, day"),
    scope: str = Query("cn", description="查询范围: cn - 国内设备, global - 全球设备"),
    settings: Settings = Depends(get_settings)
):
    """
    获取指定设备在时间范围内的聚合统计数据。
    - **enterprise_id**: (必须) 企业ID
    - **device_id**: (必须) 设备ID
    - **start_time**: (可选) 开始时间 (UTC, YYYY-MM-DDTHH:MM:SS)
    - **end_time**: (可选) 结束时间 (UTC, YYYY-MM-DDTHH:MM:SS)

    如果未提供时间，则默认查询最近 N 天的数据（在配置中定义）。
    """
    logger.info(f"收到统计查询请求: enterprise={enterprise_id}, device={device_id}")

    # --- 时间范围处理 ---
    if not start_time or not end_time:
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=settings.DEFAULT_STATS_QUERY_DAYS)
    
    # 确保时间范围合理
    if start_time >= end_time:
        raise HTTPException(status_code=400, detail="开始时间必须早于结束时间")

    logger.info(f"查询时间范围: {start_time.isoformat()} -> {end_time.isoformat()}")

    # --- 调用服务层 ---
    try:
        # 使用 speech_ai_robot 数据库连接
        with next(get_speech_ai_robot_db()) as db:
            query_service = StatsQueryService(db)
            result = query_service.get_summary(
                enterprise_id=enterprise_id,
                device_id=device_id,
                start_time=start_time,
                end_time=end_time,
                group_by=group_by.value
            )
            
            # 计算汇总数据用于 query_summary
            total_sessions = 0
            total_messages = 0
            
            for item in result["data"]:
                if "session_behavior_summary" in item:
                    total_sessions += item["session_behavior_summary"].get("total_sessions", 0)
                if "message_behavior_summary" in item:
                    total_messages += item["message_behavior_summary"].get("user_messages", 0)
                    total_messages += item["message_behavior_summary"].get("assistant_messages", 0)
            
            # 更新 query_summary 以符合响应模型要求
            result["query_summary"]["total_sessions"] = total_sessions
            result["query_summary"]["total_messages"] = total_messages
        
        return result
    except Exception as e:
        logger.error(f"处理统计查询时发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="服务器内部错误，无法处理您的请求")

@router.get("/stats/summary", summary="获取机器人对话聚合统计数据")
async def get_stats_summary(
    enterprise_id: str,
    device_id: str,
    start_time: Optional[datetime] = Query(None, description="查询开始时间 (UTC YYYY-MM-DDTHH:MM:SS)"),
    end_time: Optional[datetime] = Query(None, description="查询结束时间 (UTC YYYY-MM-DDTHH:MM:SS)"),
    group_by: Optional[str] = Query("hour", description="数据聚合方式: day, hour"),
    db: Session = Depends(get_db)
):
    """
    获取指定设备在特定时间范围内的聚合统计数据。

    - **enterprise_id**: 企业ID (必填)
    - **device_id**: 设备ID (必填)
    - **start_time / end_time**: 若不提供，默认查询最近30天。
    - **group_by**:
        - `day`: 按天聚合，返回时间序列数据。
        - `hour`: 按小时聚合，返回时间序列数据 (默认)。
    """
    
    # --- 参数预处理与校验 ---
    if start_time and end_time and start_time >= end_time:
        raise HTTPException(status_code=400, detail="查询开始时间必须早于结束时间")

    # 默认时间范围
    if not start_time or not end_time:
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=30)

    # --- 调用服务层 ---
    query_service = StatsQueryService(db)
    result = query_service.get_summary(
        enterprise_id=enterprise_id,
        device_id=device_id,
        start_time=start_time,
        end_time=end_time,
        group_by=group_by
    )
    
    return result
