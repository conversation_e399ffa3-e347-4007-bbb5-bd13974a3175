from fastapi import APIRouter, Depends, HTTPException
from typing import List, Optional
from ..services.feishu_client import feishu_service
from ..services.redis_service import redis_service
from ..models.device import Device, DeviceStatus
from ..database import get_db
from sqlalchemy.orm import Session
from datetime import datetime
import traceback
import logging
from ..config.feishu_config import (
    FEISHU_BITABLE_CONFIG_AOS_UPGRADE_CN,
    FEISHU_BITABLE_CONFIG_AOS_UPGRADE_OS,
    FEISHU_FIELD_MAPPING
)
from ..config.redis_config import CACHE_EXPIRE
from ..services.mysql.position_service import get_device_position, get_devices_info, get_device_position_info
from ..dataprocess.bug_cache import get_bug_records_by_device_id_with_cache
import time
import asyncio

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/devices", tags=["devices"])


def get_field(record, field, default=None):
    if hasattr(record, field):
        return getattr(record, field)
    if isinstance(record, dict):
        return record.get(field, default)
    return default

def convert_to_serializable(table_data):
    """将飞书数据转换为可序列化的格式"""
    try:
        logger.info(f"开始转换数据，数据类型: {type(table_data)}")
        #logger.info(f"数据内容: {table_data}")
        
        # 如果已经是字典类型，直接组装返回（兼容缓存/反序列化场景）
        """
        if isinstance(table_data, dict):
            logger.info("数据已经是字典类型，直接组装返回")
            serializable_data = {
                "total": table_data.get("total", 0),
                "has_more": table_data.get("has_more", False),
                "page_token": table_data.get("page_token", ""),
                "items": table_data.get("items", [])
            }
            return serializable_data
        """
        # 如果是飞书 SDK 对象，进行转换
        serializable_data = {
            "total": getattr(table_data, "total", 0),
            "has_more": getattr(table_data, "has_more", False),
            "page_token": getattr(table_data, "page_token", ""),
            "items": []
        }
        
        # 1. 收集所有设备ID
        device_ids = []
        for record in table_data["items"]:
            device_id = None
            if hasattr(record, 'fields') and "设备sn" in record.fields and record.fields["设备sn"]:
                device_id = record.fields["设备sn"][0]["text"]
            elif isinstance(record, dict) and "fields" in record and "设备sn" in record["fields"] and record["fields"]["设备sn"]:
                device_id = record["fields"]["设备sn"][0]["text"]
            if device_id:
                device_ids.append(device_id)
        
        # 2. 批量查MySQL
        #position_info_map = {row['device_id']: row for row in get_devices_info(device_ids)} if device_ids else {}
        position_info_map = {row['device_id']: row for row in get_device_position_info(device_ids)} if device_ids else {}
        #logger.info(f"device_ids: {device_ids}")
        #logger.info(f"position_info_map: {position_info_map}")
        #print("\n--------------------------------\n")
        
        # 3. 合并信息
        for record in table_data["items"]:
            device_id = None
            if hasattr(record, 'fields') and "设备sn" in record.fields and record.fields["设备sn"]:
                device_id = record.fields["设备sn"][0]["text"]
            elif isinstance(record, dict) and "fields" in record and "设备sn" in record["fields"] and record["fields"]["设备sn"]:
                device_id = record["fields"]["设备sn"][0]["text"]
            position_info = position_info_map.get(device_id)
            item = {
                "record_id": get_field(record, "record_id"),
                "fields": get_field(record, "fields", {}),
                "created_time": get_field(record, "created_time"),
                "last_modified_time": get_field(record, "last_modified_time"),
                "created_by": get_field(record, "created_by"),
                "last_modified_by": get_field(record, "last_modified_by"),
                "position_info": position_info
            }
            serializable_data["items"].append(item)
        
        logger.info(f"转换后的数据: {serializable_data}")
        return serializable_data
    except Exception as e:
        logger.error(f"数据转换失败: {str(e)}")
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        raise

@router.get("/")
async def get_devices(
    scope: str = "cn",
    force_refresh: bool = False
):
    """
    获取设备列表
    scope: cn - 国内设备, global - 全球设备
    force_refresh: 是否强制刷新缓存
    """
    try:
        # 生成缓存键
        cache_key = f"feishu_bitable_raw_data:{scope}"
        logger.info(f"开始处理请求，scope: {scope}, force_refresh: {force_refresh}")
        
        # 如果不是强制刷新，尝试从缓存获取数据
        if not force_refresh:
            cached_data = redis_service.get_cache(cache_key)
            if cached_data is not None:
                logger.info("从缓存获取到数据")
                table_data = cached_data
            else:
                logger.info("缓存未命中")
                table_data = None
        else:
            logger.info("强制刷新，忽略缓存")
            table_data = None

        # 如果没有缓存数据，从飞书获取
        if table_data is None:
            logger.info("开始从飞书获取数据")
            # 检查飞书配置
            if scope == "cn":
                bitable_config = FEISHU_BITABLE_CONFIG_AOS_UPGRADE_CN
            elif scope == "global":
                bitable_config = FEISHU_BITABLE_CONFIG_AOS_UPGRADE_OS
            else:
                raise HTTPException(status_code=400, detail=f"不支持的scope: {scope}")

            if not all([
                bitable_config["app_token"],
                bitable_config["table_id"],
                bitable_config["view_id"]
            ]):
                return {
                    "total": 0,
                    "devices": [],
                    "message": f"飞书多维表格配置不完整 (scope: {scope})"
                }
            
            try:
                # 判断是否为 wiki 下的多维表格
                start_time = time.time()
                if bitable_config.get("is_wiki"):
                    logger.info("检测到 wiki 配置，获取真实 token")
                    # 通过 wiki_node_token 获取真实表格 token
                    real_token = feishu_service.get_real_bitable_token_from_wiki_node(
                        bitable_config["wiki_node_token"]
                    )
                    bitable_config["app_token"] = real_token
                
                logger.info("开始调用飞书 API 获取数据")
                
                table_data_from_api = await feishu_service.get_table_records(scope=scope)
                api_time = time.time() - start_time
                logger.info(f"飞书 API 调用完成，耗时: {api_time:.2f}秒")
                
                # 将数据转换为可序列化的格式并缓存
                serializable_data = convert_to_serializable(table_data_from_api)
                logger.info("数据转换完成，开始缓存")
                redis_service.set_cache(cache_key, serializable_data, CACHE_EXPIRE["feishu_bitable_raw_data"])
                table_data = serializable_data
                
            except ValueError as e:
                logger.error(f"飞书 API 调用失败: {str(e)}")
                return {
                    "total": 0,
                    "devices": [],
                    "message": str(e)
                }

        # --- 开始整合Bug信息 ---
        logger.info("开始处理数据并关联Bug记录")
        
        # 1. 准备并发查询bug记录的任务
        tasks = []
        records_with_device_id = []
        for record in table_data["items"]:
            fields = record.get("fields", {})
            device_id = None
            if "设备sn" in fields and fields.get("设备sn"):
                field_sn = fields["设备sn"]
                if isinstance(field_sn, list) and len(field_sn) > 0:
                    device_id = field_sn[0].get("text")

            if device_id:
                records_with_device_id.append(record)
                tasks.append(get_bug_records_by_device_id_with_cache(device_id, scope))

        # 2. 并发执行所有查询
        all_bug_records = await asyncio.gather(*tasks) if tasks else []
        
        # 3. 合并数据
        devices = []
        for i, record in enumerate(records_with_device_id):
            try:
                fields = record.get("fields", {})
                
                device_id = None
                if "设备sn" in fields and fields.get("设备sn"):
                    device_id = fields["设备sn"][0].get("text")
                
                name = None
                if "终端企业名称" in fields and fields.get("终端企业名称"):
                    name = fields["终端企业名称"][0].get("text")

                if not device_id:
                    continue
                
                devices.append({
                    "device_id": device_id,
                    "name": name,
                    "device_type": fields.get("设备类型"),
                    "version_type": fields.get("版本类型"),
                    "current_status": fields.get("当前状态"),
                    "asr_status": fields.get("ASR是否已开通"),
                    "position_info": record.get("position_info"),
                    "agentos_status": fields.get("是否开通agentos企业菜单栏？"),
                    "environment": fields.get("企业环境", []),
                    "customer_type": fields.get("内外部客户"),
                    "upgrade_time": fields.get("版本升级时间"),
                    "rollback_time": fields.get("版本回退时间"),
                    "apply_time": fields.get("申请升级日期"),
                    "operator1": fields.get("操作者1", []),
                    "operator2": fields.get("操作者2", []),
                    "applicant": fields.get("申请人员", {}),
                    "created_time": record.get("created_time"),
                    "last_modified_time": record.get("last_modified_time"),
                    "created_by": record.get("created_by"),
                    "last_modified_by": record.get("last_modified_by"),
                    "bug_records": all_bug_records[i]
                })
            except Exception as e:
                logger.error(f"处理记录时出错: {record.get('record_id', 'N/A')}, 错误: {e}", exc_info=True)

        logger.info("数据处理完成")
        return {
            "total": table_data.get("total"),
            "has_more": table_data.get("has_more"),
            "page_token": table_data.get("page_token"),
            "devices": devices
        }
        
    except Exception as e:
        logger.error(f"获取设备列表失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="获取设备列表时发生内部错误")

@router.get("/basic_info")
def get_device_basic_info(
    enterprise_id: str,
    device_id: str,
    scope: str = "cn"
):
    """
    查询设备基础信息（企业ID+设备ID），返回名称、类型、经纬度等。
    优先从位置数据库查询，如果找不到则从飞书缓存中获取基本信息。
    """
    try:
        # 首先尝试从位置数据库获取完整信息
        from app.services.mysql.position_service import get_device_position_info_by_enterprise
        
        result = get_device_position_info_by_enterprise(enterprise_id, device_id)
        if result:
            logger.info(f"从位置数据库找到设备信息: {device_id}")
            return result
        
        # 如果位置数据库中没有，尝试从飞书缓存获取基本信息
        logger.info(f"位置数据库中未找到设备 {device_id}，尝试从飞书缓存获取")
        
        # 从缓存获取飞书数据
        cache_key = f"feishu_bitable_raw_data:{scope}"
        cached_data = redis_service.get_cache(cache_key)
        
        if not cached_data:
            logger.warning("飞书缓存数据不存在")
            raise HTTPException(status_code=404, detail="未找到该企业和设备的基础信息")
        
        # 在缓存数据中查找指定设备
        target_device = None
        for item in cached_data.get("items", []):
            fields = item.get("fields", {})
            device_sn = fields.get("设备sn", [])
            
            if device_sn and len(device_sn) > 0:
                current_device_id = device_sn[0].get("text", "")
                if current_device_id == device_id:
                    target_device = item
                    break
        
        if not target_device:
            logger.warning(f"在飞书缓存中未找到设备: {device_id}")
            raise HTTPException(status_code=404, detail="未找到该企业和设备的基础信息")
        
        # 构建基本设备信息
        fields = target_device.get("fields", {})
        enterprise_name = fields.get("终端企业名称", [])
        enterprise_name_text = enterprise_name[0].get("text", "") if enterprise_name else ""
        
        # 构建返回的基本信息
        basic_info = {
            "enterprise_id": enterprise_id,
            "device_id": device_id,
            "enterprise_name": enterprise_name_text,
            "device_name": fields.get("设备类型", ""),
            "version_type": fields.get("版本类型", ""),
            "current_status": fields.get("当前状态", ""),
            "asr_status": fields.get("ASR是否已开通", ""),
            "agentos_status": fields.get("是否开通agentos企业菜单栏？", ""),
            "environment": fields.get("企业环境", []),
            "customer_type": fields.get("内外部客户", ""),
            "upgrade_time": fields.get("版本升级时间", ""),
            "rollback_time": fields.get("版本回退时间", ""),
            "apply_time": fields.get("申请升级日期", ""),
            "operator1": fields.get("操作者1", []),
            "operator2": fields.get("操作者2", []),
            "applicant": fields.get("申请人员", {}),
            "created_time": target_device.get("created_time"),
            "last_modified_time": target_device.get("last_modified_time"),
            "created_by": target_device.get("created_by"),
            "last_modified_by": target_device.get("last_modified_by"),
            "data_source": "feishu_cache",  # 标记数据来源
            "note": "该设备在位置数据库中暂无数据，信息来自飞书缓存"
        }
        
        logger.info(f"从飞书缓存获取到设备基本信息: {device_id}")
        return basic_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询设备基础信息失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="查询设备基础信息时发生内部错误") 