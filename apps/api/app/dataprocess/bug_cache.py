from ..services.feishu.repository import get_bug_records_by_device_id as fetch_bug_records_from_feishu
from ..services.redis_service import redis_service
from ..config.redis_config import CACHE_EXPIRE
import logging

logger = logging.getLogger(__name__)

async def get_bug_records_by_device_id_with_cache(device_id: str, scope: str = "cn") -> list:
    cache_key = f"feishu_bug_records:{scope}:{device_id}"
    try:
        cached = redis_service.get_cache(cache_key)
        if cached is not None:
            logger.info(f"命中缓存: {cache_key}")
            return cached
    except Exception as e:
        logger.error(f"读取 bug 工单缓存出错: {e}")

    # 未命中缓存，查飞书
    bug_records = await fetch_bug_records_from_feishu(device_id, scope)
    try:
        redis_service.set_cache(cache_key, bug_records, CACHE_EXPIRE["feishu_bug_records"])
    except Exception as e:
        logger.error(f"写入 bug 工单缓存出错: {e}")
    return bug_records 