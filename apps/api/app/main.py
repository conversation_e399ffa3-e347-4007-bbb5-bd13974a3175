from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.websockets import WebSocket
import uvicorn
from app.routers import devices, device_queries, stats, human_stats, device_reports

app = FastAPI(
    title="CEMonitor API",
    description="客户体验监控系统 API",
    version="1.0.0"
)

# CORS 配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(devices.router, prefix="/api/v1")
app.include_router(device_queries.router, prefix="/api/v1")
app.include_router(stats.router, prefix="/api/v1")
app.include_router(human_stats.router, prefix="/api/v1")
app.include_router(device_reports.router, prefix="/api/v1")

@app.get("/")
async def root():
    return {"message": "Welcome to CEMonitor API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True) 