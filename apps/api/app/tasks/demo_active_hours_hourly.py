#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示活跃小时统计功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from datetime import datetime, timedelta
from apps.api.app.models.statistics import AosStatActiveHoursHourly
from apps.api.app.database import get_speech_ai_robot_db
from apps.api.app.services.statistics_service import StatisticsService
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def demo_create_active_hours_data():
    """演示创建活跃小时统计数据"""
    logger.info("=== 演示创建活跃小时统计数据 ===")
    
    # 创建示例数据
    test_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
    
    active_hours = AosStatActiveHoursHourly(
        hour_bucket=test_hour,
        enterprise_id="demo_enterprise",
        device_id="demo_device_001",
        consecutive_active_hours=12,
        assistant_first_ratio=0.75,
        greeting_ratio=0.2,
        session_ids_active=["session_001", "session_002", "session_003", "session_004", "session_005"]
    )
    
    logger.info("创建活跃小时统计数据:")
    logger.info(f"时间: {active_hours.hour_bucket}")
    logger.info(f"企业: {active_hours.enterprise_id}")
    logger.info(f"设备: {active_hours.device_id}")
    logger.info(f"连续活跃小时数: {active_hours.consecutive_active_hours}")
    logger.info(f"assistant首发会话占比: {active_hours.assistant_first_ratio:.2%}")
    logger.info(f"greeting会话占比: {active_hours.greeting_ratio:.2%}")
    logger.info(f"活跃会话ID数量: {len(active_hours.session_ids_active)}")
    
    return active_hours

def demo_save_active_hours_data():
    """演示保存活跃小时统计数据"""
    logger.info("=== 演示保存活跃小时统计数据 ===")
    
    try:
        with next(get_speech_ai_robot_db()) as db:
            # 创建多个设备的示例数据
            test_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
            
            devices = [
                {
                    "enterprise_id": "demo_enterprise_1",
                    "device_id": "demo_device_001",
                    "consecutive_active_hours": 8,
                    "assistant_first_ratio": 0.7,
                    "greeting_ratio": 0.25,
                    "session_ids_active": ["session_001", "session_002", "session_003", "session_004"]
                },
                {
                    "enterprise_id": "demo_enterprise_1",
                    "device_id": "demo_device_002",
                    "consecutive_active_hours": 5,
                    "assistant_first_ratio": 0.6,
                    "greeting_ratio": 0.3,
                    "session_ids_active": ["session_005", "session_006", "session_007"]
                },
                {
                    "enterprise_id": "demo_enterprise_2",
                    "device_id": "demo_device_003",
                    "consecutive_active_hours": 15,
                    "assistant_first_ratio": 0.8,
                    "greeting_ratio": 0.15,
                    "session_ids_active": ["session_008", "session_009", "session_010", "session_011", "session_012"]
                },
                {
                    "enterprise_id": "demo_enterprise_2",
                    "device_id": "demo_device_004",
                    "consecutive_active_hours": 3,
                    "assistant_first_ratio": 0.4,
                    "greeting_ratio": 0.4,
                    "session_ids_active": ["session_013", "session_014"]
                }
            ]
            
            for device_data in devices:
                active_hours = AosStatActiveHoursHourly(
                    hour_bucket=test_hour,
                    enterprise_id=device_data["enterprise_id"],
                    device_id=device_data["device_id"],
                    consecutive_active_hours=device_data["consecutive_active_hours"],
                    assistant_first_ratio=device_data["assistant_first_ratio"],
                    greeting_ratio=device_data["greeting_ratio"],
                    session_ids_active=device_data["session_ids_active"]
                )
                
                db.merge(active_hours)
                logger.info(f"保存设备 {device_data['enterprise_id']}-{device_data['device_id']} 的活跃小时统计数据")
            
            db.commit()
            logger.info("所有设备活跃小时统计数据保存成功")
            
            return True
            
    except Exception as e:
        logger.error(f"保存活跃小时统计数据失败: {e}", exc_info=True)
        return False

def demo_query_active_hours_data():
    """演示查询活跃小时统计数据"""
    logger.info("=== 演示查询活跃小时统计数据 ===")
    
    try:
        with next(get_speech_ai_robot_db()) as db:
            # 查询所有活跃小时统计数据
            all_data = db.query(AosStatActiveHoursHourly).all()
            
            logger.info(f"查询到 {len(all_data)} 条活跃小时统计记录")
            
            for data in all_data:
                logger.info(f"设备: {data.enterprise_id}-{data.device_id}")
                logger.info(f"时间: {data.hour_bucket}")
                logger.info(f"连续活跃小时: {data.consecutive_active_hours}")
                logger.info(f"assistant首发占比: {data.assistant_first_ratio:.2%}")
                logger.info(f"greeting占比: {data.greeting_ratio:.2%}")
                logger.info(f"活跃会话数: {len(data.session_ids_active) if data.session_ids_active else 0}")
                logger.info("---")
            
            # 按企业统计
            enterprise_stats = db.query(
                AosStatActiveHoursHourly.enterprise_id,
                AosStatActiveHoursHourly.consecutive_active_hours,
                AosStatActiveHoursHourly.assistant_first_ratio,
                AosStatActiveHoursHourly.greeting_ratio
            ).group_by(AosStatActiveHoursHourly.enterprise_id).all()
            
            logger.info("按企业汇总的活跃小时统计:")
            for stat in enterprise_stats:
                logger.info(f"企业 {stat.enterprise_id}: 平均连续活跃小时 {stat.consecutive_active_hours}")
                logger.info(f"企业 {stat.enterprise_id}: 平均assistant首发占比 {stat.assistant_first_ratio:.2%}")
                logger.info(f"企业 {stat.enterprise_id}: 平均greeting占比 {stat.greeting_ratio:.2%}")
            
            # 查询高活跃设备
            high_active_devices = db.query(AosStatActiveHoursHourly).filter(
                AosStatActiveHoursHourly.consecutive_active_hours >= 10
            ).all()
            
            logger.info(f"高活跃设备数量: {len(high_active_devices)}")
            for device in high_active_devices:
                logger.info(f"高活跃设备: {device.enterprise_id}-{device.device_id}, 连续活跃 {device.consecutive_active_hours} 小时")
            
            return True
            
    except Exception as e:
        logger.error(f"查询活跃小时统计数据失败: {e}", exc_info=True)
        return False

def demo_statistics_service_active_hours():
    """演示统计服务中的活跃小时统计计算"""
    logger.info("=== 演示统计服务中的活跃小时统计计算 ===")
    
    try:
        service = StatisticsService()
        
        # 计算最近3小时的统计数据
        current_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
        hours_to_calculate = [current_hour - timedelta(hours=i) for i in range(1, 4)]
        
        for hour in hours_to_calculate:
            logger.info(f"计算 {hour} 的活跃小时统计")
            service.calculate_statistics_for_hour(hour)
        
        logger.info("统计服务活跃小时统计计算完成")
        
        return True
        
    except Exception as e:
        logger.error(f"演示统计服务活跃小时统计计算失败: {e}", exc_info=True)
        return False

def demo_active_hours_analysis():
    """演示活跃小时数据分析"""
    logger.info("=== 演示活跃小时数据分析 ===")
    
    try:
        with next(get_speech_ai_robot_db()) as db:
            # 分析不同活跃程度的设备分布
            active_levels = [
                (0, 2, "低活跃"),
                (3, 5, "中活跃"),
                (6, 10, "高活跃"),
                (11, 999, "超高活跃")
            ]
            
            for min_hours, max_hours, level_name in active_levels:
                count = db.query(AosStatActiveHoursHourly).filter(
                    AosStatActiveHoursHourly.consecutive_active_hours >= min_hours,
                    AosStatActiveHoursHourly.consecutive_active_hours <= max_hours
                ).count()
                
                logger.info(f"{level_name}设备数量: {count}")
            
            # 分析assistant首发占比分布
            high_assistant_first = db.query(AosStatActiveHoursHourly).filter(
                AosStatActiveHoursHourly.assistant_first_ratio >= 0.7
            ).count()
            
            medium_assistant_first = db.query(AosStatActiveHoursHourly).filter(
                AosStatActiveHoursHourly.assistant_first_ratio >= 0.4,
                AosStatActiveHoursHourly.assistant_first_ratio < 0.7
            ).count()
            
            low_assistant_first = db.query(AosStatActiveHoursHourly).filter(
                AosStatActiveHoursHourly.assistant_first_ratio < 0.4
            ).count()
            
            logger.info("assistant首发占比分布:")
            logger.info(f"高占比(≥70%): {high_assistant_first} 个设备")
            logger.info(f"中占比(40%-70%): {medium_assistant_first} 个设备")
            logger.info(f"低占比(<40%): {low_assistant_first} 个设备")
            
            return True
            
    except Exception as e:
        logger.error(f"演示活跃小时数据分析失败: {e}", exc_info=True)
        return False

def main():
    """主演示函数"""
    logger.info("开始演示活跃小时统计功能")
    
    # 演示1: 创建数据
    demo_create_active_hours_data()
    
    # 演示2: 保存数据
    demo_save_active_hours_data()
    
    # 演示3: 查询数据
    demo_query_active_hours_data()
    
    # 演示4: 统计服务
    demo_statistics_service_active_hours()
    
    # 演示5: 数据分析
    demo_active_hours_analysis()
    
    logger.info("活跃小时统计功能演示完成")

if __name__ == "__main__":
    main() 