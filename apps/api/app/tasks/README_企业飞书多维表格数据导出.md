# 企业飞书多维表格数据导出工具说明

## 1. 功能简介
本工具用于批量导出指定企业、设备在飞书多维表格中的原始数据，并按天分组，自动保存为图片文件，便于后续大模型分析和数据归档。

## 2. 参数说明

| 参数名         | 必填 | 说明                         | 示例值                                  |
| -------------- | ---- | ---------------------------- | --------------------------------------- |
| --enterprise   | 是   | 企业ID                       | orion.ovs.entprise.4498860269           |
| --device       | 是   | 设备ID                       | MC1BCNC016021348B693                    |
| --start_date   | 是   | 导出起始日期（含）           | 2025-07-08                              |
| --end_date     | 是   | 导出结束日期（含）           | 2025-07-09                              |
| --output_dir   | 是   | 导出文件保存目录             | /data/app/CEMonitor_api/apps/api/data   |

## 3. 使用示例

```bash
python app/tasks/enterprise_data_export.py \
  --enterprise orion.ovs.entprise.4498860269 \
  --device MC1BCNC016021348B693 \
  --start_date 2025-07-08 \
  --end_date 2025-07-09 \
  --output_dir /data/app/CEMonitor_api/apps/api/data
```

## 4. 输出目录与文件结构

导出后，数据将按如下目录结构保存：

```
{output_dir}/
  └── {企业ID}/
      └── {设备ID}/
          ├── 2025-07-08.png
          └── 2025-07-09.png
```
- 每个文件为一天的数据，格式为图片（如有需要可扩展为txt等）。
- 图片内容为飞书多维表格的原始数据，按层级结构展示。

## 5. 依赖与环境

- Python 3.7+
- 依赖库：pandas、matplotlib、lark-oapi（飞书SDK）等
- 安装依赖：
  ```bash
  pip install -r requirements.txt
  ```

## 6. 注意事项

- 需提前在 `app/config/feishu_config.py` 配置好飞书多维表格的 app_token、table_id、view_id。
- 脚本为异步实现，需在支持 async 的环境下运行。
- 输出目录需有写入权限。

## 7. 常见问题FAQ

- Q: 导出图片为空或报错？
  - A: 检查飞书API配置、网络连通性、参数是否正确。

- Q: 如何扩展导出字段或格式？
  - A: 可在脚本内调整 pandas DataFrame 的处理逻辑和图片渲染部分。 