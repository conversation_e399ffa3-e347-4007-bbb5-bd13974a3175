# 机器人对话统计系统使用说明

## 概述

本系统实现了机器人对话的小时级行为统计功能，按照PRD文档要求，统计各项指标并保存到`speech_ai_robot`数据库中。

## 功能特性

### 1. 统计维度
- **主维度**: `enterprise_id` + `device_id` + `hour_bucket`
- **项目维度**: `project_id`（区分业务场景与通用场景）

### 2. 统计指标

#### 会话行为统计 (`aos_stat_session_behavior_hourly`)
- 总会话数、有效会话数、无响应会话数
- 首条消息类型统计（assistant/user）
- 平均对话轮数、平均会话时长
- 重复打招呼、重复内容会话统计

#### 消息行为统计 (`aos_stat_message_behavior_hourly`)
- 用户消息数、机器人消息数
- 平均消息长度
- Action触发次数、Event触发次数

#### 动作行为统计 (`aos_stat_action_behavior_hourly`)
- Action总次数
- Action类型频次统计

#### 事件行为统计 (`aos_stat_event_behavior_hourly`)
- Event总次数
- 场景路径频次、目标场景频次

#### 用户问题统计 (`aos_stat_user_question_summary_hourly`)
- 唯一问题数量
- 关键词词云

#### 用户偏好详情统计 (`aos_stat_user_preference_detail_hourly`)
- 详细偏好记录
- 项目关联信息
- 会话关联信息

#### 会话时长分布 (`aos_stat_session_duration_distribution_hourly`)
- 6个时长区间的会话数量分布

#### 会话间隔分布 (`aos_stat_session_interval_distribution_hourly`)
- 7个间隔区间的会话数量分布

#### 活跃小时统计 (`aos_stat_active_hours_hourly`)
- 连续活跃小时数
- Assistant首发比率、Greeting会话占比

## 运行机制

### 1. 定时任务
- **执行时间**: 每小时的第45分钟
- **计算范围**: 前2个小时的数据
- **示例**: 12:45执行，计算10点和11点的数据

### 2. 任务配置
```python
@shared_task(name="tasks.calculate_conversation_statistics")
def task_calculate_conversation_statistics():
    # 获取前2个小时
    now = datetime.now()
    target_hours = []
    for i in range(2):
        target_hour = now.replace(minute=0, second=0, microsecond=0) - timedelta(hours=i+1)
        target_hours.append(target_hour)
    
    # 执行统计
    stats_service = StatisticsService()
    stats_service.calculate_statistics_for_hours(target_hours)
```

### 3. 重复运行支持
- 同一小时的数据可以重复计算
- 使用`db.merge()`确保数据完整性
- 支持幂等性操作

## 数据验证

### 1. 验证脚本
```bash
# 基本验证
python apps/api/app/tasks/test_statistics.py

# 特定设备验证
python apps/api/app/tasks/test_statistics.py <enterprise_id> <device_id>
```

### 2. 验证内容
- 原始数据与统计数据的对比
- 各项指标的计算准确性
- 数据完整性检查

### 3. 验证结果
```
==================================================
数据验证结果
==================================================
✅ 验证通过

设备 enterprise1-device1 详细对比:
  ✅ session_total: 10 vs 10
  ✅ valid_sessions: 8 vs 8
  ✅ user_msg_count: 25 vs 25
  ...
```

## 数据库表结构

### 源数据表
- `aos_conversation_sessions` (agentos_online库)
- `aos_conversation_messages` (agentos_online库)

### 统计表 (speech_ai_robot库)
- `aos_stat_session_behavior_hourly`
- `aos_stat_message_behavior_hourly`
- `aos_stat_action_behavior_hourly`
- `aos_stat_event_behavior_hourly`
- `aos_stat_user_question_summary_hourly`
- `aos_stat_user_preference_detail_hourly`
- `aos_stat_session_duration_distribution_hourly`
- `aos_stat_session_interval_distribution_hourly`
- `aos_stat_active_hours_hourly`

### 表结构更新说明

#### 用户问题统计表
- **表名更新**: `aos_stat_user_question_detail_hourly` → `aos_stat_user_question_summary_hourly`
- **字段调整**: 移除 `top_user_questions` 字段，保留 `unique_question_count` 和 `wordcloud_keywords`

#### 用户偏好统计表
- **结构调整**: 从汇总表 `aos_stat_project_preference_hourly` 改为详情表 `aos_stat_user_preference_detail_hourly`
- **字段细化**: 支持 `field_name`、`field_value`、`session_id` 等详细记录
- **全文搜索**: 支持对 `field_value` 的全文搜索

#### 会话间隔分布统计表
- **字段名更新**: `interval_10s` → `bucket_lt_10s`、`interval_10_20s` → `bucket_10_20s` 等
- **主键调整**: 新增自增主键 `id`，保持唯一约束

#### 活跃小时统计表
- **主键调整**: 新增自增主键 `id`，优化数据管理
- **注释优化**: 更新字段注释，提高可读性

## 使用方法

### 1. 指定时间运行（推荐）

```bash
# 单个时间点
python run_specific_time.py "2025-01-15 10:00:00"

# 时间范围
python run_specific_time.py "2025-01-15 10:00:00" "2025-01-15 12:00:00"

# 指定企业ID和设备ID
python app/tasks/run_specific_time.py "2025-07-04 20:00:00" --enterprise orion.ovs.entprise.4498860269 --device MC1BCNC016021348B693

# 时间范围+设备过滤
python app/tasks/run_specific_time.py "2025-07-04 20:00:00" "2025-01-15 12:00:00" --enterprise orion.ovs.entprise.4498860269 --device MC1BCNC016021348B693
```

### 2. 手动执行统计
```python
from app.services.statistics_service import StatisticsService
from datetime import datetime

# 创建服务实例
stats_service = StatisticsService()

# 计算指定小时的统计
target_hour = datetime(2025, 1, 15, 10, 0, 0)  # 10:00
stats_service.calculate_statistics_for_hour(target_hour)

# 计算多个小时的统计
hours = [datetime(2025, 1, 15, 10, 0, 0), datetime(2025, 1, 15, 11, 0, 0)]
stats_service.calculate_statistics_for_hours(hours)

# 指定企业ID和设备ID
stats_service.calculate_statistics_for_hour(target_hour, "enterprise1", "device1")
```

### 2. 数据验证
```python
from app.tasks.validation import validate_statistics_for_hour
from datetime import datetime

# 验证指定小时的统计数据
target_hour = datetime(2025, 1, 15, 10, 0, 0)
results = validate_statistics_for_hour(target_hour)

# 验证特定设备
results = validate_statistics_for_hour(target_hour, "enterprise1", "device1")
```

### 3. 查看统计结果
```sql
-- 查看会话行为统计
SELECT * FROM aos_stat_session_behavior_hourly 
WHERE hour_bucket = '2025-01-15 10:00:00';

-- 查看消息行为统计
SELECT * FROM aos_stat_message_behavior_hourly 
WHERE hour_bucket = '2025-01-15 10:00:00';

-- 查看特定设备的统计
SELECT * FROM aos_stat_session_behavior_hourly 
WHERE hour_bucket = '2025-01-15 10:00:00' 
  AND enterprise_id = 'enterprise1' 
  AND device_id = 'device1';
```

## 监控和日志

### 1. 日志级别
- INFO: 正常执行信息
- WARNING: 数据异常警告
- ERROR: 执行错误

### 2. 关键日志
```
开始执行机器人对话统计Celery任务...
开始计算 2025-01-15 10:00:00 的统计数据
处理设备: enterprise1-device1, 会话数: 15
成功保存统计数据: enterprise1-device1 2025-01-15 10:00:00
完成 2025-01-15 10:00:00 的统计数据计算
机器人对话统计Celery任务执行成功。
```

### 3. 错误处理
- 数据库连接异常
- 数据计算异常
- 保存失败异常

## 扩展功能

### 1. 分钟级统计
- 修改时间粒度
- 调整统计逻辑

### 2. 天级汇总
- 基于小时数据汇总
- 跨小时连续活跃计算

### 3. 实时统计
- 基于消息流处理
- 增量统计更新

## 注意事项

1. **数据源**: 确保`agentos_online`库中有足够的测试数据
2. **数据库权限**: 确保对`speech_ai_robot`库有写入权限
3. **时区处理**: 统计基于UTC时间，注意时区转换
4. **性能优化**: 大量数据时考虑分批处理
5. **数据一致性**: 定期运行验证脚本确保数据正确性

## 故障排除

### 1. 常见问题
- **无数据**: 检查时间范围内是否有会话数据
- **计算错误**: 运行验证脚本检查数据准确性
- **连接失败**: 检查数据库连接配置

### 2. 调试方法
```python
# 启用详细日志
logging.getLogger('app.services.statistics_service').setLevel(logging.DEBUG)

# 检查原始数据
from app.database import get_agentos_db
with next(get_agentos_db()) as db:
    sessions = db.query(AosConversationSession).filter(
        AosConversationSession.session_start_time >= start_time,
        AosConversationSession.session_start_time < end_time
    ).all()
    print(f"找到 {len(sessions)} 个会话")
```

## 联系支持

如有问题，请联系开发团队或查看相关日志文件。 