#!/usr/bin/env python3
"""
测试指定时间运行统计脚本
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.services.statistics_service import StatisticsService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_single_time():
    """测试单个时间"""
    logger.info("=" * 50)
    logger.info("测试单个时间运行")
    logger.info("=" * 50)
    
    # 测试昨天的一个小时
    yesterday = datetime.now() - timedelta(days=1)
    test_time = yesterday.replace(hour=10, minute=0, second=0, microsecond=0)
    
    stats_service = StatisticsService()
    stats_service.calculate_statistics_for_hour(test_time)
    
    logger.info("✅ 单个时间测试完成")

def test_time_range():
    """测试时间范围"""
    logger.info("=" * 50)
    logger.info("测试时间范围运行")
    logger.info("=" * 50)
    
    # 测试昨天的一个时间范围
    yesterday = datetime.now() - timedelta(days=1)
    start_time = yesterday.replace(hour=9, minute=0, second=0, microsecond=0)
    end_time = yesterday.replace(hour=11, minute=0, second=0, microsecond=0)
    
    hours = []
    current = start_time
    while current <= end_time:
        hours.append(current)
        current += timedelta(hours=1)
    
    stats_service = StatisticsService()
    stats_service.calculate_statistics_for_hours(hours)
    
    logger.info("✅ 时间范围测试完成")

def test_device_filter():
    """测试设备过滤"""
    logger.info("=" * 50)
    logger.info("测试设备过滤")
    logger.info("=" * 50)
    
    # 测试昨天的一个小时，指定设备
    yesterday = datetime.now() - timedelta(days=1)
    test_time = yesterday.replace(hour=10, minute=0, second=0, microsecond=0)
    
    # 这里使用示例的企业ID和设备ID，实际使用时需要替换为真实值
    enterprise_id = "test_enterprise"
    device_id = "test_device"
    
    stats_service = StatisticsService()
    stats_service.calculate_statistics_for_hour(test_time, enterprise_id, device_id)
    
    logger.info("✅ 设备过滤测试完成")

def test_time_range_with_device():
    """测试时间范围+设备过滤"""
    logger.info("=" * 50)
    logger.info("测试时间范围+设备过滤")
    logger.info("=" * 50)
    
    # 测试昨天的一个时间范围，指定设备
    yesterday = datetime.now() - timedelta(days=1)
    start_time = yesterday.replace(hour=9, minute=0, second=0, microsecond=0)
    end_time = yesterday.replace(hour=11, minute=0, second=0, microsecond=0)
    
    hours = []
    current = start_time
    while current <= end_time:
        hours.append(current)
        current += timedelta(hours=1)
    
    # 这里使用示例的企业ID和设备ID，实际使用时需要替换为真实值
    enterprise_id = "test_enterprise"
    device_id = "test_device"
    
    stats_service = StatisticsService()
    stats_service.calculate_statistics_for_hours(hours, enterprise_id, device_id)
    
    logger.info("✅ 时间范围+设备过滤测试完成")

def main():
    """主函数"""
    logger.info("开始测试指定时间运行功能")
    
    try:
        # 测试单个时间
        test_single_time()
        
        # 测试时间范围
        test_time_range()
        
        # 测试设备过滤
        test_device_filter()
        
        # 测试时间范围+设备过滤
        test_time_range_with_device()
        
        logger.info("🎉 所有测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 