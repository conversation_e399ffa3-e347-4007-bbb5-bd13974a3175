# 📊 用户偏好详情统计表更新说明

## 🔄 表结构变更

### 原表结构（汇总统计）
```sql
CREATE TABLE `aos_stat_project_preference_hourly` (
  `hour_bucket` DATETIME NOT NULL COMMENT '小时',
  `enterprise_id` VARCHAR(64) NOT NULL COMMENT '企业ID',
  `device_id` VARCHAR(128) NOT NULL COMMENT '设备ID',
  `project_id` VARCHAR(64) NOT NULL COMMENT '项目ID（非空）',

  `scene_session_count` INT DEFAULT 0 COMMENT '该场景下的会话数',
  `session_ids_scene_session_count` JSON COMMENT '该指标对应的会话ID列表',

  `scene_no_response_count` INT DEFAULT 0 COMMENT '该场景下无assistant回应的会话数',
  `session_ids_scene_no_response_count` JSON COMMENT '该指标对应的会话ID列表',

  `preference_filled_sessions` INT DEFAULT 0 COMMENT '填写偏好的会话数',
  `session_ids_preference_filled_sessions` JSON COMMENT '该指标对应的会话ID列表',

  `preference_field_freq` JSON COMMENT '用户偏好字段频次，如{"预算":8}',
  `preference_keywords` JSON COMMENT '偏好字段值关键词，如{"高端":3,"1000元以下":2}',

  PRIMARY KEY (`hour_bucket`, `enterprise_id`, `device_id`, `project_id`)
);
```

### 新表结构（详细记录）
```sql
CREATE TABLE `aos_stat_user_preference_detail_hourly` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',

  `hour_bucket` DATETIME NOT NULL COMMENT '小时',
  `enterprise_id` VARCHAR(64) NOT NULL COMMENT '企业ID',
  `device_id` VARCHAR(128) NOT NULL COMMENT '设备ID',
  `project_id` VARCHAR(64) NOT NULL COMMENT '项目ID',

  `field_name` VARCHAR(128) NOT NULL COMMENT '偏好字段名，如 用户使用场景',
  `field_value` TEXT NOT NULL COMMENT '用户填写的内容，如 酒店大堂',
  `session_id` VARCHAR(128) DEFAULT NULL COMMENT '填写此偏好内容的会话ID',

  PRIMARY KEY (`id`),
  KEY `idx_hour_field` (`hour_bucket`, `field_name`),
  KEY `idx_enterprise` (`enterprise_id`),
  FULLTEXT KEY `ft_field_value` (`field_value`)  -- 若使用 MySQL 5.7+ 支持全文搜索
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小时级别用户偏好详情记录';
```

## 📋 主要变更

| 变更项 | 说明 |
|--------|------|
| 表名 | `aos_stat_project_preference_hourly` → `aos_stat_user_preference_detail_hourly` |
| 结构 | 汇总统计 → 详细记录 |
| 主键 | 复合主键 → 自增ID |
| 记录粒度 | 项目级别汇总 → 字段级别详情 |
| 新增功能 | 全文搜索支持 |
| 索引优化 | 添加小时+字段索引、企业索引 |

## 🔧 代码更新

### 1. 模型更新 (`app/models/statistics.py`)

```python
class AosStatUserPreferenceDetailHourly(Base_speech_ai_robot):
    """用户偏好详情统计表"""
    __tablename__ = 'aos_stat_user_preference_detail_hourly'
    __table_args__ = {'comment': '用户偏好详情统计表（按小时汇总）'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    hour_bucket = Column(DateTime, nullable=False, comment='小时')
    enterprise_id = Column(String(64), nullable=False, comment='企业ID')
    device_id = Column(String(128), nullable=False, comment='设备ID')
    project_id = Column(String(64), nullable=False, comment='项目ID')

    field_name = Column(String(128), nullable=False, comment='偏好字段名，如 用户使用场景')
    field_value = Column(Text, nullable=False, comment='用户填写的内容，如 酒店大堂')
    session_id = Column(String(128), comment='填写此偏好内容的会话ID')
```

### 2. 统计服务更新 (`app/services/statistics_service.py`)

#### 导入更新
```python
from app.models.statistics import (
    # ... 其他导入
    AosStatUserPreferenceDetailHourly,  # 更新类名
    # ... 其他导入
)
```

#### 新增用户偏好详情统计方法
```python
def _calculate_user_preference_details(self, sessions: List[Dict]) -> List[Dict]:
    """计算用户偏好详情统计"""
    preference_details = []
    
    for session in sessions:
        user_preferences = session.get('user_preferences')
        if not user_preferences:
            continue
        
        # 处理JSON格式的user_preferences
        if isinstance(user_preferences, str):
            try:
                import json
                user_preferences = json.loads(user_preferences)
            except:
                continue
        
        if not isinstance(user_preferences, dict):
            continue
        
        # 提取每个偏好字段
        for field_name, field_value in user_preferences.items():
            if field_value:  # 确保字段值不为空
                preference_detail = {
                    'field_name': str(field_name),
                    'field_value': str(field_value),
                    'session_id': session['session_id'],
                    'project_id': session.get('project_id', '')
                }
                preference_details.append(preference_detail)
    
    return preference_details
```

#### 保存逻辑更新
```python
# 保存用户偏好详情
for preference_detail in stats_data['preference_details']:
    user_preference = AosStatUserPreferenceDetailHourly(
        hour_bucket=hour_bucket,
        enterprise_id=enterprise_id,
        device_id=device_id,
        project_id=preference_detail.get('project_id', ''),
        field_name=preference_detail['field_name'],
        field_value=preference_detail['field_value'],
        session_id=preference_detail['session_id']
    )
    db.add(user_preference)
```

## 📊 统计指标说明

### 1. 详细记录结构
每条记录包含以下信息：
- **时间维度**: `hour_bucket` - 统计小时
- **企业维度**: `enterprise_id` - 企业ID
- **设备维度**: `device_id` - 设备ID
- **项目维度**: `project_id` - 项目ID
- **字段维度**: `field_name` - 偏好字段名
- **内容维度**: `field_value` - 用户填写的内容
- **会话维度**: `session_id` - 对应的会话ID

### 2. 数据格式支持
- **字典格式**: `{"用户使用场景": "酒店大堂", "预算范围": "1000-2000元"}`
- **JSON字符串**: `'{"用户使用场景": "酒店大堂", "预算范围": "1000-2000元"}'`
- **空值处理**: 自动过滤空值和无效数据

### 3. 索引优化
- **主键索引**: `id` - 自增主键，提高插入性能
- **复合索引**: `idx_hour_field` - 支持按时间和字段查询
- **企业索引**: `idx_enterprise` - 支持按企业查询
- **全文索引**: `ft_field_value` - 支持偏好内容全文搜索

## 🧪 测试验证

### 测试脚本
```bash
# 运行用户偏好详情统计测试
python apps/api/app/tasks/test_user_preference_details.py

# 运行演示脚本
python apps/api/app/tasks/demo_user_preference_details.py
```

### 测试用例
```python
# 测试数据
test_sessions = [
    {
        'session_id': 'session_001',
        'enterprise_id': 'enterprise1',
        'device_id': 'device1',
        'project_id': 'project1',
        'user_preferences': {
            '用户使用场景': '酒店大堂',
            '预算范围': '1000-2000元',
            '使用频率': '每天使用'
        }
    },
    {
        'session_id': 'session_002',
        'enterprise_id': 'enterprise1',
        'device_id': 'device1',
        'project_id': 'project1',
        'user_preferences': {
            '用户使用场景': '商场导购',
            '预算范围': '500-1000元'
        }
    }
]

# 预期结果
expected_count = 5  # 2个会话，共5个偏好字段
expected_fields = ['用户使用场景', '预算范围', '使用频率']
```

## 🚀 使用方式

### 1. 命令行运行
```bash
# 计算指定时间的用户偏好详情统计
python run_specific_time.py "2025-01-15 10:00:00"

# 计算特定设备的用户偏好详情统计
python run_specific_time.py "2025-01-15 10:00:00" --enterprise enterprise1 --device device1
```

### 2. 编程调用
```python
from app.services.statistics_service import StatisticsService
from datetime import datetime

stats_service = StatisticsService()
target_hour = datetime(2025, 1, 15, 10, 0, 0)

# 计算所有设备的用户偏好详情统计
stats_service.calculate_statistics_for_hour(target_hour)

# 计算特定设备的用户偏好详情统计
stats_service.calculate_statistics_for_hour(target_hour, "enterprise1", "device1")
```

### 3. 查询结果
```sql
-- 查询用户偏好详情
SELECT * FROM aos_stat_user_preference_detail_hourly 
WHERE hour_bucket = '2025-01-15 10:00:00';

-- 查询特定项目的偏好详情
SELECT * FROM aos_stat_user_preference_detail_hourly 
WHERE hour_bucket = '2025-01-15 10:00:00' 
  AND project_id = 'project1';

-- 全文搜索偏好内容
SELECT * FROM aos_stat_user_preference_detail_hourly 
WHERE MATCH(field_value) AGAINST('酒店' IN NATURAL LANGUAGE MODE);

-- 按字段名统计
SELECT field_name, COUNT(*) as count 
FROM aos_stat_user_preference_detail_hourly 
WHERE hour_bucket = '2025-01-15 10:00:00'
GROUP BY field_name;
```

## ⚠️ 注意事项

1. **数据迁移**: 如果原表有数据，需要手动迁移到新表
2. **全文搜索**: 需要MySQL 5.7+版本支持全文搜索功能
3. **数据量**: 详细记录会产生更多数据，注意存储空间
4. **性能优化**: 合理使用索引，避免全表扫描
5. **向后兼容**: 确保所有相关代码都已更新为新表名和字段

## 📈 性能优化

1. **索引优化**: 
   - 主键索引提高插入性能
   - 复合索引支持多维度查询
   - 全文索引支持内容搜索

2. **数据压缩**: 
   - 使用TEXT类型存储长文本
   - 合理设置字段长度

3. **查询优化**: 
   - 使用索引字段进行过滤
   - 避免SELECT *
   - 合理使用LIMIT

4. **存储优化**: 
   - 定期清理历史数据
   - 使用分区表（可选）

## 🔮 后续扩展

1. **数据分析**: 
   - 偏好趋势分析
   - 用户画像构建
   - 推荐系统支持

2. **实时统计**: 
   - 基于消息流的实时偏好统计
   - 实时推荐和个性化服务

3. **机器学习**: 
   - 偏好预测模型
   - 用户行为分析
   - 智能推荐算法

4. **可视化**: 
   - 偏好分布图表
   - 趋势分析报表
   - 实时监控面板 