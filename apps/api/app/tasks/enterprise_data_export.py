#!/usr/bin/env python3
"""
企业数据导出功能

功能说明:
1. 按企业ID/设备ID/企业名称_设备名称_设备ID_chats数据_YYYY-MM-DD.txt格式保存数据
2. 实现chat概念：合并间隔小于30秒的session
3. 标注机器人和人的交互数据以及交互轮数
4. 支持配置session合并的时间间隔
5. 支持多种配置选项和输出格式
6. 自动获取企业名称和设备名称，优先从位置数据库获取，备选从飞书缓存获取
7. 支持对话格式输出（默认），更适合LLM理解和机器人对话分析
8. 支持历史天数导出：使用--days_back参数指定要导出最近几天的数据（从今天开始往前推）

输出格式:
- 对话格式（默认）：以对话形式展示，包含时间戳、角色标识、事件和action数据
- JSON格式：结构化数据，便于程序处理

用法示例:
# 指定日期范围导出
python app/tasks/enterprise_data_export.py --enterprise_id <企业ID> --device_id <设备ID> --start_date 2025-01-01 --end_date 2025-01-31 --output_dir /path/to/output --merge_interval 30

# 导出最近几天的数据（推荐使用）
python app/tasks/enterprise_data_export.py --enterprise_id <企业ID> --device_id <设备ID> --days_back 3 --output_dir /path/to/output --merge_interval 30
python app/tasks/enterprise_data_export.py --enterprise_id orion.ovs.entprise.4498860269 --device_id M03SCN2A170252017BB1 --days_back 7 --output_dir /data/app/CEMonitor_api/apps/api/app/data --merge_interval 30 --user_content_format merge

# 原有示例（指定日期范围）
python app/tasks/enterprise_data_export.py --enterprise_id orion.ovs.entprise.4498860269 --device_id M03SCN2A170252017BB1 --start_date 2025-07-17 --end_date 2025-07-18 --output_dir /data/app/CEMonitor_api/apps/api/app/data --merge_interval 30 --user_content_format merge
python app/tasks/enterprise_data_export.py --enterprise_id orion.ovs.entprise.4498860269 --device_id MC1BCNC016021348B693 --start_date 2025-07-08 --end_date 2025-07-18 --output_dir /data/app/CEMonitor_api/apps/api/app/data --merge_interval 30 --user_content_format merge
python app/tasks/enterprise_data_export.py --enterprise_id orion.ovs.entprise.2808534718 --device_id M03SCN2A23025122K86C --start_date 2025-07-17 --end_date 2025-07-18 --output_dir /data/app/CEMonitor_api/apps/api/app/data --merge_interval 30 --user_content_format merge
python app/tasks/enterprise_data_export.py --enterprise_id orion.ovs.entprise.2808534718 --device_id M03SCN2A23025122HB6A --start_date 2025-07-17 --end_date 2025-07-18 --output_dir /data/app/CEMonitor_api/apps/api/app/data --merge_interval 30 --user_content_format merge

python app/tasks/enterprise_data_export.py --enterprise_id orion.ovs.entprise.4498860269 --device_id M03SCN2A170252017BB1 --days_back 7 --output_dir /data/app/CEMonitor_api/apps/api/app/data --merge_interval 30 --user_content_format merge
python app/tasks/enterprise_data_export.py --enterprise_id orion.ovs.entprise.4498860269 --device_id MC1BCNC016021348B693 --days_back 7 --output_dir /data/app/CEMonitor_api/apps/api/app/data --merge_interval 30 --user_content_format merge
python app/tasks/enterprise_data_export.py --enterprise_id orion.ovs.entprise.2808534718 --device_id M03SCN2A23025122K86C --days_back 7 --output_dir /data/app/CEMonitor_api/apps/api/app/data --merge_interval 30 --user_content_format merge
python app/tasks/enterprise_data_export.py --enterprise_id orion.ovs.entprise.2808534718 --device_id M03SCN2A23025122HB6A --days_back 7 --output_dir /data/app/CEMonitor_api/apps/api/app/data --merge_interval 30 --user_content_format merge


格式选择:
--dialogue_format  # 使用对话格式（默认）
--json_format      # 使用JSON格式

日期范围指定:
方式1 - 指定具体日期范围:
--start_date 2025-01-01 --end_date 2025-01-31

方式2 - 指定历史天数（推荐）:
--days_back 3  # 导出最近3天的数据（从今天开始往前推）
--days_back 7  # 导出最近7天的数据

用户内容处理:
--user_content_format segment  # 分段显示（默认），将"..."分割的内容显示为多个片段 👤 [14:30:25] 用户: 💭 [片段1] 这个地干不干净？ 💭 [片段2] 喝干净没有？
--user_content_format merge    # 合并显示，将"..."分割的内容合并为一个完整句子 👤 [14:30:25] 用户: 这个地干不干净？ 喝干净没有？
--user_content_format original # 原始显示，保持原始格式不变 👤 14:30:25] 用户: 这个地干不干净？...喝干净没有？
"""

import sys
import os
import logging
import argparse
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from collections import defaultdict
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database import get_agentos_db
from app.models.conversation import AosConversationSession, AosConversationMessage
from sqlalchemy import and_, or_, func
from app.tasks.export_config import ExportConfig, ExportConfigTemplates

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnterpriseDataExporter:
    """企业数据导出器"""
    
    def __init__(self, config: ExportConfig = None):
        """
        初始化导出器
        
        Args:
            config: 导出配置对象，如果为None则使用默认配置
        """
        self.config = config or ExportConfig()
        self.merge_interval = self.config.get_merge_interval()
        self.logger = logging.getLogger(__name__)
        # 默认使用对话格式，更适合LLM理解
        self.use_dialogue_format = self.config.get('use_dialogue_format', True)
        # 用户内容处理方式：segment（分段显示）、merge（合并显示）、original（原始显示）
        self.user_content_format = self.config.get('user_content_format', 'segment')
    
    def _get_sessions_by_date_range(self, enterprise_id: str, device_id: str, 
                                   start_date: datetime, end_date: datetime) -> List[Dict]:
        """
        获取指定日期范围内的会话数据
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            会话数据列表
        """
        with next(get_agentos_db()) as db:
            sessions = db.query(AosConversationSession).filter(
                and_(
                    AosConversationSession.enterprise_id == enterprise_id,
                    AosConversationSession.device_id == device_id,
                    AosConversationSession.session_start_time >= start_date,
                    AosConversationSession.session_start_time < end_date
                )
            ).order_by(AosConversationSession.session_start_time).all()
            
            return [
                {
                    'session_id': session.session_id,
                    'enterprise_id': session.enterprise_id,
                    'device_id': session.device_id,
                    'project_id': session.project_id,
                    'conversation_turns': session.conversation_turns,
                    'session_duration_seconds': session.session_duration_seconds,
                    'user_preferences': session.user_preferences,
                    'session_start_time': session.session_start_time,
                    'session_end_time': session.session_end_time,
                    'app_id': session.app_id,
                    'group_id': session.group_id,
                    'client_id': session.client_id,
                    'product_id': session.product_id,
                    'product_model': session.product_model,
                    'timezone': session.timezone
                }
                for session in sessions
            ]
    
    def _get_messages_by_session_ids(self, session_ids: List[str]) -> Dict[str, List[Dict]]:
        """
        获取指定会话ID的消息数据
        
        Args:
            session_ids: 会话ID列表
            
        Returns:
            按会话ID分组的消息数据
        """
        if not session_ids:
            return {}
            
        with next(get_agentos_db()) as db:
            messages = db.query(AosConversationMessage).filter(
                AosConversationMessage.session_id.in_(session_ids)
            ).order_by(
                AosConversationMessage.session_id, 
                AosConversationMessage.message_index
            ).all()
            
            messages_by_session = defaultdict(list)
            for msg in messages:
                messages_by_session[msg.session_id].append({
                    'session_id': msg.session_id,
                    'message_index': msg.message_index,
                    'role': msg.role,
                    'content': msg.content,
                    'action_data': msg.action_data,
                    'event_data': msg.event_data,
                    'message_timestamp': msg.message_timestamp,
                    'app_id': msg.app_id,
                    'page_id': msg.page_id,
                    'agent_id': msg.agent_id,
                    'face_id': msg.face_id
                })
            
            return dict(messages_by_session)
    
    def _merge_sessions_into_chats(self, sessions: List[Dict], 
                                  messages_by_session: Dict[str, List[Dict]]) -> List[Dict]:
        """
        将sessions合并为chats
        
        Args:
            sessions: 会话数据列表
            messages_by_session: 按会话ID分组的消息数据
            
        Returns:
            合并后的chat数据列表
        """
        if not sessions:
            return []
        
        chats = []
        current_chat = {
            'chat_id': f"chat_{sessions[0]['session_id']}",
            'enterprise_id': sessions[0]['enterprise_id'],
            'device_id': sessions[0]['device_id'],
            'chat_start_time': sessions[0]['session_start_time'],
            'chat_end_time': sessions[0]['session_end_time'],
            'total_turns': sessions[0]['conversation_turns'],
            'total_duration': sessions[0]['session_duration_seconds'],
            'sessions': [sessions[0]],
            'messages': messages_by_session.get(sessions[0]['session_id'], []),
            'user_message_count': 0,
            'assistant_message_count': 0,
            'action_count': 0,
            'event_count': 0
        }
        
        # 统计第一个session的消息
        self._update_chat_stats(current_chat, messages_by_session.get(sessions[0]['session_id'], []))
        
        for i in range(1, len(sessions)):
            current_session = sessions[i]
            previous_session = sessions[i-1]
            
            # 获取上一个session的最后一个消息时间
            last_message_time = self._get_last_message_time(
                previous_session['session_id'], 
                messages_by_session, 
                previous_session['session_end_time']
            )
            
            # 计算时间间隔：当前session开始时间 - 上一个session最后一个消息时间
            time_gap = (current_session['session_start_time'] - last_message_time).total_seconds()
            
            # 如果间隔小于配置的时间，则合并到当前chat
            if time_gap <= self.merge_interval:
                current_chat['sessions'].append(current_session)
                current_chat['chat_end_time'] = current_session['session_end_time']
                current_chat['total_turns'] += current_session['conversation_turns']
                current_chat['total_duration'] += current_session['session_duration_seconds']
                
                # 添加消息并更新统计
                session_messages = messages_by_session.get(current_session['session_id'], [])
                current_chat['messages'].extend(session_messages)
                self._update_chat_stats(current_chat, session_messages)
                
            else:
                # 间隔大于配置时间，开始新的chat
                chats.append(current_chat)
                
                current_chat = {
                    'chat_id': f"chat_{current_session['session_id']}",
                    'enterprise_id': current_session['enterprise_id'],
                    'device_id': current_session['device_id'],
                    'chat_start_time': current_session['session_start_time'],
                    'chat_end_time': current_session['session_end_time'],
                    'total_turns': current_session['conversation_turns'],
                    'total_duration': current_session['session_duration_seconds'],
                    'sessions': [current_session],
                    'messages': messages_by_session.get(current_session['session_id'], []),
                    'user_message_count': 0,
                    'assistant_message_count': 0,
                    'action_count': 0,
                    'event_count': 0
                }
                
                # 统计当前session的消息
                self._update_chat_stats(current_chat, messages_by_session.get(current_session['session_id'], []))
        
        # 添加最后一个chat
        chats.append(current_chat)
        
        return chats
    
    def _get_device_info(self, enterprise_id: str, device_id: str) -> Dict[str, str]:
        """
        获取设备信息（企业名称和设备名称）
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            
        Returns:
            包含企业名称和设备名称的字典
        """
        try:
            # 直接调用内部方法获取设备信息
            from app.services.mysql.position_service import get_device_position_info_by_enterprise
            
            result = get_device_position_info_by_enterprise(enterprise_id, device_id)
            if result:
                return {
                    'enterprise_name': result.get('enterprise_name', '未知企业'),
                    'device_name': result.get('device_name', '未知设备')
                }
            else:
                # 如果位置数据库中没有找到，尝试从飞书缓存获取
                self.logger.info(f"位置数据库中未找到设备 {device_id}，尝试从飞书缓存获取")
                return self._get_device_info_from_feishu_cache(enterprise_id, device_id)
            
        except Exception as e:
            self.logger.warning(f"获取设备信息失败: {e}，使用默认名称")
            return {
                'enterprise_name': '未知企业',
                'device_name': '未知设备'
            }
    
    def _get_device_info_from_feishu_cache(self, enterprise_id: str, device_id: str) -> Dict[str, str]:
        """
        从飞书缓存获取设备信息
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            
        Returns:
            包含企业名称和设备名称的字典
        """
        try:
            from app.services.redis_service import redis_service
            
            # 从缓存获取飞书数据
            cache_key = f"feishu_bitable_raw_data:cn"
            cached_data = redis_service.get_cache(cache_key)
            
            if not cached_data:
                self.logger.warning("飞书缓存数据不存在")
                return {
                    'enterprise_name': '未知企业',
                    'device_name': '未知设备'
                }
            
            # 在缓存数据中查找指定设备
            for item in cached_data.get("items", []):
                fields = item.get("fields", {})
                device_sn = fields.get("设备sn", [])
                
                if device_sn and len(device_sn) > 0:
                    current_device_id = device_sn[0].get("text", "")
                    if current_device_id == device_id:
                        enterprise_name = fields.get("终端企业名称", [])
                        enterprise_name_text = enterprise_name[0].get("text", "") if enterprise_name else "未知企业"
                        
                        return {
                            'enterprise_name': enterprise_name_text,
                            'device_name': fields.get("设备类型", "未知设备")
                        }
            
            self.logger.warning(f"在飞书缓存中未找到设备: {device_id}")
            return {
                'enterprise_name': '未知企业',
                'device_name': '未知设备'
            }
            
        except Exception as e:
            self.logger.warning(f"从飞书缓存获取设备信息失败: {e}")
            return {
                'enterprise_name': '未知企业',
                'device_name': '未知设备'
            }
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        清理文件名中的特殊字符，确保文件名安全
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的安全文件名
        """
        import re
        # 移除或替换不允许的字符
        # 替换特殊字符为下划线
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 移除前后空格
        filename = filename.strip()
        # 如果文件名为空，使用默认名称
        if not filename:
            filename = '未知'
        # 限制长度，避免文件名过长
        if len(filename) > 50:
            filename = filename[:50]
        return filename
    
    def _get_last_message_time(self, session_id: str, messages_by_session: Dict[str, List[Dict]], 
                              fallback_time: datetime) -> datetime:
        """
        获取指定session的最后一个消息时间
        
        Args:
            session_id: 会话ID
            messages_by_session: 按会话ID分组的消息数据
            fallback_time: 如果没有消息时的备选时间
            
        Returns:
            最后一个消息的时间，如果没有消息则返回备选时间
        """
        session_messages = messages_by_session.get(session_id, [])
        if session_messages:
            # 按时间戳排序，取最后一个消息的时间
            session_messages.sort(key=lambda x: x['message_timestamp'])
            return session_messages[-1]['message_timestamp']
        else:
            return fallback_time
    
    def _update_chat_stats(self, chat: Dict, messages: List[Dict]):
        """更新chat的统计信息"""
        for message in messages:
            if message['role'] == 'user':
                chat['user_message_count'] += 1
            elif message['role'] == 'assistant':
                chat['assistant_message_count'] += 1
            
            if message['action_data']:
                chat['action_count'] += 1
            
            if message['event_data']:
                chat['event_count'] += 1
    
    def _format_chat_data(self, chat: Dict) -> Dict:
        """
        精简chat数据，仅保留核心展示内容
        """
        # 取起止时间
        chat_start_time = chat['chat_start_time']
        chat_end_time = chat['chat_end_time']
        
        # 只保留messages，按时间排序
        messages = []
        for msg in chat['messages']:
            m = {
                'role': msg['role'],
                'content': msg['content'],
                'session_id': msg['session_id'],  # 保留session_id信息
                'timestamp': msg['message_timestamp'].isoformat() if hasattr(msg['message_timestamp'], 'isoformat') else str(msg['message_timestamp'])
            }
            if msg.get('event_data'):
                m['event_data'] = msg['event_data']
            if msg.get('action_data'):
                m['action_data'] = msg['action_data']
            messages.append(m)
        # 按时间排序
        messages.sort(key=lambda x: x['timestamp'])
        
        return {
            'chat_id': chat['chat_id'],
            'chat_start_time': chat_start_time.isoformat() if hasattr(chat_start_time, 'isoformat') else str(chat_start_time),
            'chat_end_time': chat_end_time.isoformat() if hasattr(chat_end_time, 'isoformat') else str(chat_end_time),
            'messages': messages
        }
    
    def _create_message_session_mapping(self, chat: Dict) -> Dict[str, str]:
        """
        创建消息到session的映射关系
        
        Args:
            chat: chat数据字典
            
        Returns:
            消息session_id到session_id的映射字典
        """
        message_to_session = {}
        
        # 遍历所有session，为每个session中的消息建立映射
        for session in chat.get('sessions', []):
            session_id = session['session_id']
            # 查找属于这个session的所有消息
            for msg in chat.get('messages', []):
                if msg.get('session_id') == session_id:
                    # 使用消息的唯一标识作为key
                    msg_key = f"{msg.get('session_id')}_{msg.get('message_index', 0)}"
                    message_to_session[msg_key] = session_id
        
        return message_to_session
    
    def _format_chat_for_dialogue(self, chat: Dict) -> str:
        """
        将chat数据格式化为对话形式，便于LLM理解
        
        Args:
            chat: chat数据字典
            
        Returns:
            格式化的对话文本
        """
        # 格式化时间
        start_time = chat['chat_start_time']
        end_time = chat['chat_end_time']
        
        # 格式化开始时间
        if isinstance(start_time, datetime):
            start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
        elif hasattr(start_time, 'strftime'):
            start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(start_time, str):
            try:
                # 尝试解析ISO格式时间字符串
                parsed_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                start_time_str = parsed_time.strftime('%Y-%m-%d %H:%M:%S')
            except:
                start_time_str = str(start_time)
        else:
            start_time_str = str(start_time)
        
        # 格式化结束时间
        if isinstance(end_time, datetime):
            end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
        elif hasattr(end_time, 'strftime'):
            end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(end_time, str):
            try:
                # 尝试解析ISO格式时间字符串
                parsed_time = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                end_time_str = parsed_time.strftime('%Y-%m-%d %H:%M:%S')
            except:
                end_time_str = str(end_time)
        else:
            end_time_str = str(end_time)
        
        # 计算会话时长
        try:
            if isinstance(start_time, datetime) and isinstance(end_time, datetime):
                duration_seconds = int((end_time - start_time).total_seconds())
                if duration_seconds >= 3600:
                    hours = duration_seconds // 3600
                    minutes = (duration_seconds % 3600) // 60
                    seconds = duration_seconds % 60
                    duration_str = f"{hours}小时{minutes}分{seconds}秒"
                elif duration_seconds >= 60:
                    minutes = duration_seconds // 60
                    seconds = duration_seconds % 60
                    duration_str = f"{minutes}分{seconds}秒"
                else:
                    duration_str = f"{duration_seconds}秒"
            elif hasattr(start_time, 'timestamp') and hasattr(end_time, 'timestamp'):
                duration_seconds = int((end_time.timestamp() - start_time.timestamp()))
                if duration_seconds >= 3600:
                    hours = duration_seconds // 3600
                    minutes = (duration_seconds % 3600) // 60
                    seconds = duration_seconds % 60
                    duration_str = f"{hours}小时{minutes}分{seconds}秒"
                elif duration_seconds >= 60:
                    minutes = duration_seconds // 60
                    seconds = duration_seconds % 60
                    duration_str = f"{minutes}分{seconds}秒"
                else:
                    duration_str = f"{duration_seconds}秒"
            else:
                # 如果是字符串，尝试解析
                if isinstance(start_time, str) and isinstance(end_time, str):
                    try:
                        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                        end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                        duration_seconds = int((end_dt - start_dt).total_seconds())
                        if duration_seconds >= 3600:
                            hours = duration_seconds // 3600
                            minutes = (duration_seconds % 3600) // 60
                            seconds = duration_seconds % 60
                            duration_str = f"{hours}小时{minutes}分{seconds}秒"
                        elif duration_seconds >= 60:
                            minutes = duration_seconds // 60
                            seconds = duration_seconds % 60
                            duration_str = f"{minutes}分{seconds}秒"
                        else:
                            duration_str = f"{duration_seconds}秒"
                    except:
                        duration_str = "未知"
                else:
                    duration_str = "未知"
        except Exception as e:
            duration_str = f"计算错误: {str(e)}"
        
        # 获取所有session_id
        session_ids = [session['session_id'] for session in chat.get('sessions', [])]
        session_count = len(session_ids)
        
        # 构建对话头部信息
        dialogue_text = f"""┌─────────────────────────────────────────────────────────────────────────────┐
│ 🗨️  对话会话开始 - {chat['chat_id']}
├─────────────────────────────────────────────────────────────────────────────┤
│ 📅 开始时间: {start_time_str}
│ 📅 结束时间: {end_time_str}
│ ⏱️  会话时长: {duration_str}
│ 🔢 合并会话数: {session_count}
│ 💬 会话轮数: {len(chat['messages'])}
│ 📊 用户消息: {sum(1 for msg in chat['messages'] if msg['role'] == 'user')}
│ 🤖 机器人消息: {sum(1 for msg in chat['messages'] if msg['role'] == 'assistant')}
└─────────────────────────────────────────────────────────────────────────────┘
"""
        
        # 按session分组显示消息
        current_session_id = None
        session_message_count = 0
        
        for i, msg in enumerate(chat['messages'], 1):
            msg_session_id = msg.get('session_id')
            role = msg['role']
            content = msg['content']
            timestamp = msg.get('timestamp') or msg.get('message_timestamp')
            
            # 如果session发生变化，添加session分隔符
            if msg_session_id != current_session_id:
                if current_session_id is not None:
                    # 结束上一个session
                    dialogue_text += f"╰─ session_id 结束 ({session_message_count} 条消息)\n\n"
                
                # 开始新的session
                current_session_id = msg_session_id
                session_message_count = 0
                
                # 查找session索引和显示信息
                if msg_session_id and msg_session_id in session_ids:
                    session_index = session_ids.index(msg_session_id) + 1
                    dialogue_text += f"╭─ 📋 session_id [{session_index}]: {msg_session_id}\n"
                elif msg_session_id:
                    dialogue_text += f"╭─ 📋 session_id [0]: {msg_session_id}\n"
                else:
                    dialogue_text += f"╭─ 📋 session_id [未知]: 无session_id\n"
            
            session_message_count += 1
            
            # 格式化时间戳
            if hasattr(timestamp, 'strftime'):
                time_str = timestamp.strftime('%H:%M:%S')
            else:
                time_str = str(timestamp)
            
            # 根据角色格式化消息
            if role == 'user':
                if content and content.strip():
                    formatted_content = self._format_user_content(content)
                    dialogue_text += f"│ 👤 [{time_str}] 用户: {formatted_content}\n"
            elif role == 'assistant':
                if content and content.strip():
                    dialogue_text += f"│ 🤖 [{time_str}] 机器人: {content}\n"
            else:
                if content and content.strip():
                    dialogue_text += f"│ ❓ [{time_str}] {role}: {content}\n"
            
            # 如果有event数据，添加event信息
            if msg.get('event_data'):
                event_data = msg['event_data']
                if isinstance(event_data, dict):
                    # 简化event数据显示
                    simplified_event = self._simplify_json_data(event_data)
                    dialogue_text += f"│ 🤖 📡 [event数据] {simplified_event}\n"
                else:
                    dialogue_text += f"│ 🤖 📡 [event数据] {event_data}\n"
            
            # 如果有action数据，添加动作信息
            if msg.get('action_data'):
                action_data = msg['action_data']
                if isinstance(action_data, dict):
                    # 简化action数据显示
                    simplified_action = self._simplify_json_data(action_data)
                    dialogue_text += f"│ 🤖 ⚡ [action数据] {simplified_action}\n"
                else:
                    dialogue_text += f"│ 🤖 ⚡ [action数据] {action_data}\n"
            
            dialogue_text += "│\n"
        
        # 结束最后一个session
        if current_session_id is not None:
            dialogue_text += f"╰─ session_id 结束 ({session_message_count} 条消息)\n\n"
        
        dialogue_text += f"""┌─────────────────────────────────────────────────────────────────────────────┐
│ 🏁 对话会话结束 - {chat['chat_id']}
└─────────────────────────────────────────────────────────────────────────────┘
"""
        return dialogue_text
    
    def _simplify_json_data(self, data: Dict) -> str:
        """
        简化JSON数据显示，提取关键信息
        
        Args:
            data: JSON数据字典
            
        Returns:
            简化的字符串表示
        """
        if not isinstance(data, dict):
            return str(data)
        
        # 提取关键字段
        key_fields = ['name', 'display_name', 'action', 'event_type', 'result', 'status']
        simplified = {}
        
        for field in key_fields:
            if field in data:
                value = data[field]
                if isinstance(value, list) and len(value) > 0:
                    simplified[field] = f"[{len(value)}项]"
                elif isinstance(value, dict):
                    simplified[field] = "{...}"
                else:
                    simplified[field] = str(value)
        
        # 如果没有关键字段，显示前几个字段
        if not simplified and data:
            items = list(data.items())[:3]
            simplified = {k: str(v)[:50] + "..." if len(str(v)) > 50 else str(v) for k, v in items}
            if len(data) > 3:
                simplified['...'] = f"等{len(data)}个字段"
        
        return str(simplified)
    
    def _format_user_content(self, content: str) -> str:
        """
        格式化用户内容，处理连续说话的情况
        
        Args:
            content: 原始用户内容
            
        Returns:
            格式化后的内容
        """
        if not content:
            return content
        
        # 根据配置选择处理方式
        if self.user_content_format == 'original':
            # 原始显示，不做处理
            return content
        
        # 检查是否包含多个"..."分割的片段
        if '...' in content:
            import re
            # 使用正则表达式分割，处理连续的省略号
            segments = re.split(r'\.{3,}', content)
            # 过滤空片段并清理
            segments = [seg.strip() for seg in segments if seg.strip()]
            
            if len(segments) > 1:
                if self.user_content_format == 'merge':
                    # 合并显示：将所有片段合并成一个完整的句子，使用空格分割
                    merged_content = ' '.join(segments)
                    # 清理标点符号
                    merged_content = re.sub(r'[，。！？；：\s]+$', '', merged_content)
                    if merged_content and merged_content[-1] not in '。！？，；：':
                        merged_content += '。'
                    return merged_content
                else:
                    # segment模式：分段显示
                    formatted_segments = []
                    for i, segment in enumerate(segments, 1):
                        clean_segment = segment.strip()
                        if clean_segment:
                            # 清理开头和结尾的标点符号
                            clean_segment = re.sub(r'^[，。！？；：\s]+', '', clean_segment)
                            clean_segment = re.sub(r'[，。！？；：\s]+$', '', clean_segment)
                            
                            if clean_segment:
                                # 如果片段以标点结尾，保留；否则添加适当的标点
                                if clean_segment and clean_segment[-1] not in '。！？，；：':
                                    clean_segment += '。'
                                formatted_segments.append(f"💭 [片段{i}] {clean_segment}")
                    
                    if formatted_segments:
                        return '\n    '.join(formatted_segments)
                    else:
                        return "[语音识别结果为空]"
            else:
                # 只有一个有效片段，直接返回清理后的内容
                clean_segment = segments[0].strip()
                clean_segment = re.sub(r'^[，。！？；：\s]+', '', clean_segment)
                clean_segment = re.sub(r'[，。！？；：\s]+$', '', clean_segment)
                return clean_segment if clean_segment else "[语音识别结果为空]"
        else:
            # 没有"..."分割，直接返回原内容
            return content.strip()
    
    def _save_chat_data_to_file(self, chat_data: List[Dict], enterprise_id: str, 
                                device_id: str, date: datetime, output_dir: str):
        """
        将chat数据保存到文件
        
        Args:
            chat_data: chat数据列表
            enterprise_id: 企业ID
            device_id: 设备ID
            date: 日期
            output_dir: 输出目录
        """
        # 获取企业名称和设备名称
        device_info = self._get_device_info(enterprise_id, device_id)
        enterprise_name = device_info['enterprise_name']
        device_name = device_info['device_name']
        
        # 创建目录结构：企业ID/设备ID/
        device_dir = os.path.join(output_dir, enterprise_id, device_id)
        os.makedirs(device_dir, exist_ok=True)
        
        # 生成文件名：企业名称_设备名称_YYYY-MM-DD.txt
        # 清理名称中的特殊字符，避免文件名问题
        safe_enterprise_name = self._sanitize_filename(enterprise_name)
        safe_device_name = self._sanitize_filename(device_name)
        filename = f"{safe_enterprise_name}_{safe_device_name}_{device_id}_chats数据_{date.strftime('%Y-%m-%d')}.txt"
        filepath = os.path.join(device_dir, filename)
        
        # 写入数据
        with open(filepath, 'w', encoding=self.config.get_output_encoding()) as f:
            
            # 添加对话格式的统计信息
            if self.use_dialogue_format:
                total_user_messages = sum(sum(1 for msg in chat.get('messages', []) if msg.get('role') == 'user') for chat in chat_data)
                total_assistant_messages = sum(sum(1 for msg in chat.get('messages', []) if msg.get('role') == 'assistant') for chat in chat_data)
                total_events = sum(sum(1 for msg in chat.get('messages', []) if msg.get('event_data')) for chat in chat_data)
                total_actions = sum(sum(1 for msg in chat.get('messages', []) if msg.get('action_data')) for chat in chat_data)
                
                # 计算工作时长：从第一个chat到最后一个chat的时长
                work_duration_str = "未知"
                if chat_data and len(chat_data) > 0:
                    try:
                        first_chat_start = chat_data[0]['chat_start_time']
                        last_chat_end = chat_data[-1]['chat_end_time']
                        
                        if isinstance(first_chat_start, datetime) and isinstance(last_chat_end, datetime):
                            work_duration_seconds = int((last_chat_end - first_chat_start).total_seconds())
                            if work_duration_seconds >= 3600:
                                hours = work_duration_seconds // 3600
                                minutes = (work_duration_seconds % 3600) // 60
                                work_duration_str = f"{hours}小时{minutes}分"
                            elif work_duration_seconds >= 60:
                                minutes = work_duration_seconds // 60
                                work_duration_str = f"{minutes}分"
                            else:
                                work_duration_str = f"{work_duration_seconds}秒"
                        elif hasattr(first_chat_start, 'timestamp') and hasattr(last_chat_end, 'timestamp'):
                            work_duration_seconds = int((last_chat_end.timestamp() - first_chat_start.timestamp()))
                            if work_duration_seconds >= 3600:
                                hours = work_duration_seconds // 3600
                                minutes = (work_duration_seconds % 3600) // 60
                                work_duration_str = f"{hours}小时{minutes}分"
                            elif work_duration_seconds >= 60:
                                minutes = work_duration_seconds // 60
                                work_duration_str = f"{minutes}分"
                            else:
                                work_duration_str = f"{work_duration_seconds}秒"
                        elif isinstance(first_chat_start, str) and isinstance(last_chat_end, str):
                            try:
                                start_dt = datetime.fromisoformat(first_chat_start.replace('Z', '+00:00'))
                                end_dt = datetime.fromisoformat(last_chat_end.replace('Z', '+00:00'))
                                work_duration_seconds = int((end_dt - start_dt).total_seconds())
                                if work_duration_seconds >= 3600:
                                    hours = work_duration_seconds // 3600
                                    minutes = (work_duration_seconds % 3600) // 60
                                    work_duration_str = f"{hours}小时{minutes}分"
                                elif work_duration_seconds >= 60:
                                    minutes = work_duration_seconds // 60
                                    work_duration_str = f"{minutes}分"
                                else:
                                    work_duration_str = f"{work_duration_seconds}秒"
                            except:
                                work_duration_str = "解析错误"
                    except Exception as e:
                        work_duration_str = f"计算错误: {str(e)}"
                
                # 计算session总数
                total_sessions = sum(len(chat.get('sessions', [])) for chat in chat_data)
                
                f.write(f"""# 📊 数据统计概览
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🏢 企业: {enterprise_name}
│ 🤖 设备: {device_name} ({device_id})
│ 📅 日期: {date.strftime('%Y-%m-%d')}
│ 💬 Chat数: {len(chat_data)}
│ 🗨️  对话会话数: {total_sessions}
│ 📝 总消息数: {sum(len(chat.get('messages', [])) for chat in chat_data)}
│ 👤 用户消息: {total_user_messages}
│ 🤖 机器人消息: {total_assistant_messages}
│ 📡 event数据: {total_events}
│ ⚡ action数据: {total_actions}
│ 🕐 工作时长: {work_duration_str}
└─────────────────────────────────────────────────────────────────────────────┘

# 📋 对话数据展示
""")
            
            # 写入chat数据
            for i, chat in enumerate(chat_data):
                if self.use_dialogue_format:
                    # 使用对话格式，更适合LLM理解
                    dialogue_text = self._format_chat_for_dialogue(chat)
                    f.write(dialogue_text)
                    if i < len(chat_data) - 1:  # 不是最后一个chat
                        f.write(f'\n{"─"*80}\n\n')
                else:
                    # 使用JSON格式
                    separator = self.config.get_file_separator()
                    f.write(json.dumps(chat, ensure_ascii=False, indent=2, default=str))
                    if i < len(chat_data) - 1:  # 不是最后一个chat
                        f.write(f'\n\n{separator}\n\n')
        
        # 设置文件权限
        os.chmod(filepath, self.config.get_file_permissions())
        
        self.logger.info(f"数据已保存到: {filepath}")
        
        # 创建索引文件
        if self.config.should_create_index_file():
            self._create_index_file(enterprise_id, device_id, date, chat_data, output_dir)
    
    def _create_index_file(self, enterprise_id: str, device_id: str, date: datetime, 
                          chat_data: List[Dict], output_dir: str):
        """创建索引文件"""
        # 获取企业名称和设备名称
        device_info = self._get_device_info(enterprise_id, device_id)
        enterprise_name = device_info['enterprise_name']
        device_name = device_info['device_name']
        
        index_dir = os.path.join(output_dir, enterprise_id, device_id)
        index_filename = self.config.get_index_filename()
        index_filepath = os.path.join(index_dir, index_filename)
        
        # 读取现有索引或创建新索引
        index_data = {}
        if os.path.exists(index_filepath):
            try:
                with open(index_filepath, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
            except:
                index_data = {}
        
        # 生成文件名：企业名称_设备名称_YYYY-MM-DD.txt
        safe_enterprise_name = self._sanitize_filename(enterprise_name)
        safe_device_name = self._sanitize_filename(device_name)
        filename = f"{safe_enterprise_name}_{safe_device_name}_{date.strftime('%Y-%m-%d')}.txt"
        
        # 添加当前日期的索引信息
        date_str = date.strftime('%Y-%m-%d')
        index_data[date_str] = {
            'file_path': filename,
            'enterprise_name': enterprise_name,
            'device_name': device_name,
            'chat_count': len(chat_data),
            'session_count': sum(len(chat.get('sessions', [])) for chat in chat_data),
            'message_count': sum(len(chat.get('messages', [])) for chat in chat_data),
            'export_timestamp': datetime.now().isoformat(),
            'merge_interval': self.merge_interval
        }
        
        # 保存索引文件
        with open(index_filepath, 'w', encoding='utf-8') as f:
            json.dump(index_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"索引文件已更新: {index_filepath}")
    
    def export_data_by_date_range(self, enterprise_id: str, device_id: str, 
                                 start_date: datetime, end_date: datetime, 
                                 output_dir: str) -> Dict[str, Any]:
        """
        按日期范围导出数据
        
        Args:
            enterprise_id: 企业ID
            device_id: 设备ID
            start_date: 开始日期
            end_date: 结束日期
            output_dir: 输出目录
            
        Returns:
            导出统计信息
        """
        self.logger.info(f"开始导出数据: 企业={enterprise_id}, 设备={device_id}, "
                        f"时间范围={start_date.date()} 到 {end_date.date()}")
        
        # 按天处理数据
        current_date = start_date.date()
        end_date_only = end_date.date()
        
        total_chats = 0
        total_messages = 0
        total_sessions = 0
        
        while current_date <= end_date_only:
            # 获取当天的数据
            day_start = datetime.combine(current_date, datetime.min.time())
            day_end = datetime.combine(current_date, datetime.max.time())
            
            self.logger.info(f"处理日期: {current_date}")
            
            # 获取会话数据
            sessions = self._get_sessions_by_date_range(enterprise_id, device_id, day_start, day_end)
            
            if not sessions:
                self.logger.info(f"日期 {current_date} 没有数据")
                current_date += timedelta(days=1)
                continue
            
            # 获取消息数据
            session_ids = [s['session_id'] for s in sessions]
            messages_by_session = self._get_messages_by_session_ids(session_ids)
            
            # 合并sessions为chats
            chats = self._merge_sessions_into_chats(sessions, messages_by_session)
            
            if chats:
                # 保存到文件（直接使用原始chat数据）
                self._save_chat_data_to_file(chats, enterprise_id, device_id, 
                                           day_start, output_dir)
                
                # 更新统计
                total_chats += len(chats)
                total_sessions += sum(len(chat['sessions']) for chat in chats)
                total_messages += sum(len(chat['messages']) for chat in chats)
                
                self.logger.info(f"日期 {current_date}: {len(chats)} 个chats, "
                               f"{sum(len(chat['sessions']) for chat in chats)} 个sessions, "
                               f"{sum(len(chat['messages']) for chat in chats)} 条消息")
            
            current_date += timedelta(days=1)
        
        stats = {
            'enterprise_id': enterprise_id,
            'device_id': device_id,
            'start_date': start_date.date().isoformat(),
            'end_date': end_date.date().isoformat(),
            'merge_interval_seconds': self.merge_interval,
            'total_chats': total_chats,
            'total_sessions': total_sessions,
            'total_messages': total_messages,
            'output_directory': output_dir
        }
        
        self.logger.info(f"导出完成: {stats}")
        return stats


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='企业数据导出工具')
    parser.add_argument('--enterprise_id', required=True, help='企业ID')
    parser.add_argument('--device_id', required=True, help='设备ID')
    parser.add_argument('--start_date', help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end_date', help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--days_back', type=int, help='导出最近几天的数据（从今天开始往前推）')
    parser.add_argument('--output_dir', required=True, help='输出目录')
    parser.add_argument('--merge_interval', type=int, default=30, 
                       help='session合并时间间隔（秒），默认30秒')
    parser.add_argument('--config_file', help='配置文件路径')
    parser.add_argument('--config_template', choices=['basic', 'detailed', 'performance', 'minimal'],
                       default='basic', help='配置模板')
    parser.add_argument('--include_sensitive', action='store_true', 
                       help='包含敏感数据')
    parser.add_argument('--include_statistics', action='store_true', default=True,
                       help='包含统计信息')
    parser.add_argument('--include_user_preferences', action='store_true', default=True,
                       help='包含用户偏好信息')
    parser.add_argument('--include_action_event', action='store_true', default=True,
                       help='包含action和event数据')
    parser.add_argument('--include_detailed_messages', action='store_true', default=True,
                       help='包含详细消息内容')
    parser.add_argument('--include_session_metadata', action='store_true', default=True,
                       help='包含会话元数据')
    parser.add_argument('--include_message_metadata', action='store_true', default=True,
                       help='包含消息元数据')
    parser.add_argument('--max_message_length', type=int, default=1000,
                       help='消息内容最大长度')
    parser.add_argument('--show_progress', action='store_true', default=True,
                       help='显示进度信息')
    parser.add_argument('--create_index', action='store_true', default=True,
                       help='创建索引文件')
    parser.add_argument('--validate_output', action='store_true', default=True,
                       help='验证输出文件')
    parser.add_argument('--dialogue_format', action='store_true', default=True,
                       help='使用对话格式输出（默认），更适合LLM理解')
    parser.add_argument('--json_format', action='store_true', default=False,
                       help='使用JSON格式输出')
    parser.add_argument('--user_content_format', choices=['segment', 'merge', 'original'],
                       default='segment', help='用户内容处理方式：segment(分段显示)、merge(合并显示)、original(原始显示)')
    
    args = parser.parse_args()
    
    # 解析日期参数
    if args.days_back:
        # 使用历史天数模式
        if args.days_back <= 0:
            logger.error("days_back 参数必须大于0")
            return
        
        # 计算日期范围：从今天开始往前推指定天数
        end_date = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999)
        start_date = (end_date - timedelta(days=args.days_back - 1)).replace(hour=0, minute=0, second=0, microsecond=0)
        
        logger.info(f"导出最近 {args.days_back} 天数据: {start_date.date()} 到 {end_date.date()}")
        
    elif args.start_date and args.end_date:
        # 使用指定日期范围模式
        try:
            start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
            end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
        except ValueError as e:
            logger.error(f"日期格式错误: {e}")
            return
    else:
        logger.error("必须指定 --days_back 或同时指定 --start_date 和 --end_date")
        return
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 创建配置
    if args.config_file:
        config = ExportConfig.from_file(args.config_file)
    else:
        # 根据模板创建配置
        if args.config_template == 'basic':
            config = ExportConfigTemplates.get_basic_config()
        elif args.config_template == 'detailed':
            config = ExportConfigTemplates.get_detailed_config()
        elif args.config_template == 'performance':
            config = ExportConfigTemplates.get_performance_config()
        elif args.config_template == 'minimal':
            config = ExportConfigTemplates.get_minimal_config()
        else:
            config = ExportConfig()
    
    # 更新配置参数
    config.set('merge_interval_seconds', args.merge_interval)
    config.set('include_sensitive_data', args.include_sensitive)
    config.set('include_statistics', args.include_statistics)
    config.set('include_user_preferences', args.include_user_preferences)
    config.set('include_action_event_data', args.include_action_event)
    config.set('include_detailed_messages', args.include_detailed_messages)
    config.set('include_session_metadata', args.include_session_metadata)
    config.set('include_message_metadata', args.include_message_metadata)
    config.set('max_message_length', args.max_message_length)
    config.set('show_progress', args.show_progress)
    config.set('create_index_file', args.create_index)
    config.set('validate_output', args.validate_output)
    
    # 设置输出格式
    if args.json_format:
        config.set('use_dialogue_format', False)
    else:
        config.set('use_dialogue_format', True)
    
    # 设置用户内容处理方式
    config.set('user_content_format', args.user_content_format)
    
    # 创建导出器并执行导出
    exporter = EnterpriseDataExporter(config=config)
    
    try:
        stats = exporter.export_data_by_date_range(
            enterprise_id=args.enterprise_id,
            device_id=args.device_id,
            start_date=start_date,
            end_date=end_date,
            output_dir=args.output_dir
        )
        
        # 保存统计信息
        stats_file = os.path.join(args.output_dir, 'export_stats.json')
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        logger.info(f"导出统计信息已保存到: {stats_file}")
        
        # 保存配置信息（追加方式）
        config_file = os.path.join(args.output_dir, 'export_config.json')
        
        # 读取现有配置（如果存在）
        existing_configs = []
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    existing_configs = json.load(f)
                if not isinstance(existing_configs, list):
                    existing_configs = [existing_configs]
            except:
                existing_configs = []
        
        # 添加当前配置
        current_config = {
            'export_timestamp': datetime.now().isoformat(),
            'enterprise_id': args.enterprise_id,
            'device_id': args.device_id,
            'start_date': start_date.date().isoformat(),
            'end_date': end_date.date().isoformat(),
            'days_back': args.days_back,
            'merge_interval': args.merge_interval,
            'user_content_format': args.user_content_format,
            'config': config.to_dict()
        }
        existing_configs.append(current_config)
        
        # 保存所有配置
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(existing_configs, f, ensure_ascii=False, indent=2)
        
        logger.info(f"导出配置已追加保存到: {config_file}")
        
    except Exception as e:
        logger.error(f"导出过程中发生错误: {e}")
        raise


if __name__ == '__main__':
    main() 