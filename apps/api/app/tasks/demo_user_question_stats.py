#!/usr/bin/env python3
"""
演示用户问题统计功能
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.services.statistics_service import StatisticsService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def demo_user_question_calculation():
    """演示用户问题统计计算"""
    logger.info("=" * 60)
    logger.info("演示用户问题统计计算")
    logger.info("=" * 60)
    
    # 创建统计服务
    stats_service = StatisticsService()
    
    # 模拟用户消息数据
    demo_messages = [
        # 会话1
        {'session_id': 'session_001', 'role': 'user', 'content': '你好，请问你是谁？'},
        {'session_id': 'session_001', 'role': 'assistant', 'content': '我是智能机器人助手，很高兴为您服务！'},
        {'session_id': 'session_001', 'role': 'user', 'content': '现在几点了？'},
        {'session_id': 'session_001', 'role': 'assistant', 'content': '现在是下午3点30分'},
        
        # 会话2
        {'session_id': 'session_002', 'role': 'user', 'content': '你好，请问你是谁？'},
        {'session_id': 'session_002', 'role': 'assistant', 'content': '我是智能机器人助手'},
        {'session_id': 'session_002', 'role': 'user', 'content': '今天天气怎么样？'},
        {'session_id': 'session_002', 'role': 'assistant', 'content': '今天天气晴朗，温度适宜'},
        
        # 会话3
        {'session_id': 'session_003', 'role': 'user', 'content': '帮我开灯'},
        {'session_id': 'session_003', 'role': 'assistant', 'content': '好的，已经为您打开灯光'},
        {'session_id': 'session_003', 'role': 'user', 'content': '谢谢'},
        {'session_id': 'session_003', 'role': 'assistant', 'content': '不客气，还有其他需要帮助的吗？'},
        
        # 会话4
        {'session_id': 'session_004', 'role': 'user', 'content': '现在几点了？'},
        {'session_id': 'session_004', 'role': 'assistant', 'content': '现在是下午3点35分'},
        
        # 会话5
        {'session_id': 'session_005', 'role': 'user', 'content': '今天天气怎么样？'},
        {'session_id': 'session_005', 'role': 'assistant', 'content': '今天天气多云，有轻微降雨'},
    ]
    
    logger.info("模拟用户消息数据:")
    for i, msg in enumerate(demo_messages, 1):
        if msg['role'] == 'user':
            logger.info(f"  {i}. 用户: {msg['content']}")
    
    # 计算用户问题统计
    user_question_stats = stats_service._calculate_user_question_stats(demo_messages)
    
    logger.info("\n用户问题统计结果:")
    logger.info(f"📊 唯一问题数量: {user_question_stats['unique_question_count']}")
    logger.info(f"📋 包含用户问题的会话ID: {user_question_stats['session_ids_unique_question_count']}")
    logger.info(f"☁️ 关键词词云: {user_question_stats['wordcloud_keywords']}")
    
    # 详细分析
    logger.info("\n详细分析:")
    logger.info("1. 唯一问题列表:")
    unique_questions = set(msg['content'] for msg in demo_messages if msg['role'] == 'user')
    for i, question in enumerate(unique_questions, 1):
        logger.info(f"   {i}. {question}")
    
    logger.info("\n2. 关键词分析:")
    for keyword, count in user_question_stats['wordcloud_keywords'].items():
        logger.info(f"   '{keyword}': {count}次")

def demo_real_data_statistics():
    """演示真实数据统计"""
    logger.info("=" * 60)
    logger.info("演示真实数据统计")
    logger.info("=" * 60)
    
    # 计算昨天10点的数据
    yesterday = datetime.now() - timedelta(days=1)
    target_hour = yesterday.replace(hour=10, minute=0, second=0, microsecond=0)
    
    logger.info(f"计算时间: {target_hour}")
    
    stats_service = StatisticsService()
    
    try:
        # 执行统计
        stats_service.calculate_statistics_for_hour(target_hour)
        logger.info("✅ 真实数据统计完成")
        
        # 查询统计结果
        from app.database import get_speech_ai_robot_db
        from app.models.statistics import AosStatUserQuestionSummaryHourly
        
        with next(get_speech_ai_robot_db()) as db:
            results = db.query(AosStatUserQuestionSummaryHourly).filter(
                AosStatUserQuestionSummaryHourly.hour_bucket == target_hour
            ).all()
            
            logger.info(f"\n📊 查询到 {len(results)} 条用户问题统计记录:")
            for result in results:
                logger.info(f"  企业: {result.enterprise_id}, 设备: {result.device_id}")
                logger.info(f"  唯一问题数: {result.unique_question_count}")
                logger.info(f"  关键词: {result.wordcloud_keywords}")
                logger.info("  " + "-" * 40)
        
    except Exception as e:
        logger.error(f"❌ 真实数据统计失败: {e}", exc_info=True)

def demo_device_specific_statistics():
    """演示特定设备统计"""
    logger.info("=" * 60)
    logger.info("演示特定设备统计")
    logger.info("=" * 60)
    
    # 计算昨天10点特定设备的数据
    yesterday = datetime.now() - timedelta(days=1)
    target_hour = yesterday.replace(hour=10, minute=0, second=0, microsecond=0)
    
    # 使用示例的企业ID和设备ID
    enterprise_id = "demo_enterprise"
    device_id = "demo_device"
    
    logger.info(f"计算时间: {target_hour}")
    logger.info(f"企业ID: {enterprise_id}")
    logger.info(f"设备ID: {device_id}")
    
    stats_service = StatisticsService()
    
    try:
        # 执行统计
        stats_service.calculate_statistics_for_hour(target_hour, enterprise_id, device_id)
        logger.info("✅ 特定设备统计完成")
        
    except Exception as e:
        logger.error(f"❌ 特定设备统计失败: {e}", exc_info=True)

def show_usage_help():
    """显示使用帮助"""
    logger.info("=" * 60)
    logger.info("用户问题统计功能演示")
    logger.info("=" * 60)
    logger.info("")
    logger.info("功能说明:")
    logger.info("1. 唯一问题统计: 统计去重后的用户问题数量")
    logger.info("2. 关键词词云: 提取用户消息中的高频关键词")
    logger.info("3. 会话ID关联: 记录包含用户问题的会话ID")
    logger.info("")
    logger.info("表结构变更:")
    logger.info("- 表名: aos_stat_user_question_detail_hourly → aos_stat_user_question_summary_hourly")
    logger.info("- 移除: top_user_questions 字段")
    logger.info("- 保留: unique_question_count, session_ids_unique_question_count, wordcloud_keywords")
    logger.info("")
    logger.info("使用方法:")
    logger.info("python demo_user_question_stats.py")
    logger.info("")

def main():
    """主函数"""
    logger.info("开始演示用户问题统计功能")
    
    try:
        # 显示使用帮助
        show_usage_help()
        
        # 演示用户问题计算
        demo_user_question_calculation()
        
        # 演示真实数据统计
        demo_real_data_statistics()
        
        # 演示特定设备统计
        demo_device_specific_statistics()
        
        logger.info("🎉 用户问题统计功能演示完成！")
        
    except Exception as e:
        logger.error(f"❌ 演示失败: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 