
# 📊 中间统计表DDL（按小时汇总）设计文档

以下为机器人小时级行为统计系统各指标的中间统计表定义，每张表基于不同的业务模块拆分，字段详细注释如下：

---

### 1. `aos_stat_session_behavior_hourly` - 小时级别的会话行为统计

```sql
CREATE TABLE `aos_stat_session_behavior_hourly` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  
  `hour_bucket` DATETIME NOT NULL COMMENT '当前小时（精确到小时）',
  `enterprise_id` VARCHAR(64) NOT NULL COMMENT '企业ID',
  `device_id` VARCHAR(128) NOT NULL COMMENT '设备ID',

  `session_total` INT DEFAULT 0 COMMENT '总会话数',
  `session_ids_session_total` JSON COMMENT '该指标对应的会话ID列表',

  `valid_sessions` INT DEFAULT 0 COMMENT '有效会话数（有user+assistant消息）',
  `session_ids_valid_sessions` JSON COMMENT '该指标对应的会话ID列表',

  `no_response_sessions` INT DEFAULT 0 COMMENT '用户说话但assistant无回应的会话数',
  `session_ids_no_response_sessions` JSON COMMENT '该指标对应的会话ID列表',

  `first_assistant_sessions` INT DEFAULT 0 COMMENT '首条消息为assistant的会话数',
  `session_ids_first_assistant_sessions` JSON COMMENT '该指标对应的会话ID列表',

  `first_assistant_with_user_reply` INT DEFAULT 0 COMMENT '首assistant后有user回复的会话数',
  `session_ids_first_assistant_with_user_reply` JSON COMMENT '该指标对应的会话ID列表',

  `first_user_sessions` INT DEFAULT 0 COMMENT '首条消息为user的会话数',
  `session_ids_first_user_sessions` JSON COMMENT '该指标对应的会话ID列表',

  `greeting_repeat_sessions` INT DEFAULT 0 COMMENT 'assistant重复打招呼的会话数',
  `session_ids_greeting_repeat_sessions` JSON COMMENT '该指标对应的会话ID列表',

  `avg_conversation_turns` FLOAT DEFAULT 0 COMMENT '平均对话轮数',
  `avg_session_duration` FLOAT DEFAULT 0 COMMENT '平均会话时长（秒）',

  `assistant_repeat_content_sessions` INT DEFAULT 0 COMMENT 'assistant输出重复内容的会话数',
  `session_ids_assistant_repeat_content_sessions` JSON COMMENT '该指标对应的会话ID列表',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_hourly_stat` (`hour_bucket`, `enterprise_id`, `device_id`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小时级别的会话行为统计';


```

---

## 2. `aos_stat_message_behavior_hourly` - 小时级别的消息行为统计
```sql
CREATE TABLE `aos_stat_message_behavior_hourly` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',

  `hour_bucket` DATETIME NOT NULL COMMENT '小时',
  `enterprise_id` VARCHAR(64) NOT NULL COMMENT '企业ID',
  `device_id` VARCHAR(128) NOT NULL COMMENT '设备ID',

  `user_msg_count` INT DEFAULT 0 COMMENT '用户消息数',
  `session_ids_user_msg_count` JSON COMMENT '该指标对应的会话ID列表',

  `assistant_msg_count` INT DEFAULT 0 COMMENT 'assistant消息数',
  `session_ids_assistant_msg_count` JSON COMMENT '该指标对应的会话ID列表',

  `avg_user_msg_length` FLOAT DEFAULT 0 COMMENT '平均用户消息长度',
  `avg_assistant_msg_length` FLOAT DEFAULT 0 COMMENT '平均assistant消息长度',

  `action_trigger_count` INT DEFAULT 0 COMMENT '触发action的次数',
  `session_ids_action_trigger_count` JSON COMMENT '该指标对应的会话ID列表',

  `event_trigger_count` INT DEFAULT 0 COMMENT '触发event的次数',
  `session_ids_event_trigger_count` JSON COMMENT '该指标对应的会话ID列表',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_hourly_msg_stat` (`hour_bucket`, `enterprise_id`, `device_id`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小时级别的消息行为统计';

```

---

## 3. `aos_stat_action_behavior_hourly` - 小时级别的单个动作行为统计

```sql
CREATE TABLE `aos_stat_action_behavior_hourly` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',

  `hour_bucket` DATETIME NOT NULL COMMENT '小时',
  `enterprise_id` VARCHAR(64) NOT NULL COMMENT '企业ID',
  `device_id` VARCHAR(128) NOT NULL COMMENT '设备ID',

  `action_name` VARCHAR(128) NOT NULL COMMENT '动作英文名，如rotate,start_visitor',
  `action_display_name` VARCHAR(128) DEFAULT NULL COMMENT '动作中文名，如转圈, 打开访客',

  `action_count` INT DEFAULT 0 COMMENT '该动作触发次数',
  `session_ids` JSON COMMENT '该动作对应的会话ID列表',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_hourly_action` (`hour_bucket`, `enterprise_id`, `device_id`, `action_name`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  KEY `idx_hour_bucket` (`hour_bucket`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小时级别的单个动作行为统计';

```


---

## 4. `aos_stat_event_behavior_hourly` - 小时级别事件行为统计（非结构化）

```sql
CREATE TABLE `aos_stat_event_behavior_hourly` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',

  `hour_bucket` DATETIME NOT NULL COMMENT '小时',
  `enterprise_id` VARCHAR(64) NOT NULL COMMENT '企业ID',
  `device_id` VARCHAR(128) NOT NULL COMMENT '设备ID',

  `event_total_count` INT DEFAULT 0 COMMENT '触发event总次数',
  `session_ids_event_total_count` JSON COMMENT '该指标对应的会话ID列表',

  `event_path_freq` TEXT COMMENT '场景路径频次，如: "通用 → 娱乐:4,娱乐 → 推销:3"',
  `target_scene_freq` TEXT COMMENT '目标场景频次，如: "娱乐:4,推销:3"',

  PRIMARY KEY (`id`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  KEY `idx_device_id` (`device_id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小时级别事件行为统计（非结构化）';


```

---

## 5. `aos_stat_user_question_summary_hourly` - 小时级别的用户问题统计总览

```sql
CREATE TABLE `aos_stat_user_question_topn_hourly` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',

  `hour_bucket` DATETIME NOT NULL COMMENT '小时维度，格式：YYYY-MM-DD HH:00:00',
  `enterprise_id` VARCHAR(64) NOT NULL COMMENT '企业ID',
  `device_id` VARCHAR(128) NOT NULL COMMENT '设备ID',

  `question_content` VARCHAR(1024) NOT NULL COMMENT '用户提问原始内容（去除前后空格）',
  `question_count` INT DEFAULT 0 COMMENT '该content出现次数',
  `session_ids` JSON COMMENT '所有出现该问题的session_id列表',
  `wordcloud_keywords` JSON COMMENT '将所有用户提问内容分词后，统计关键词频次，如 {"你好": 5, "几点": 3}',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_question` (`hour_bucket`, `enterprise_id`, `device_id`, `question_content`),
  KEY `idx_hour_enterprise` (`hour_bucket`, `enterprise_id`),
  KEY `idx_device` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小时级别用户问题TopN内容明细表';


```
---

## 6. `aos_stat_user_preference_detail_hourly` - 项目偏好信息统计表

```sql
CREATE TABLE `aos_stat_user_preference_detail_hourly` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',

  `hour_bucket` DATETIME NOT NULL COMMENT '小时',
  `enterprise_id` VARCHAR(64) NOT NULL COMMENT '企业ID',
  `device_id` VARCHAR(128) NOT NULL COMMENT '设备ID',
  `project_id` VARCHAR(64) NOT NULL COMMENT '项目ID',

  `field_name` VARCHAR(128) NOT NULL COMMENT '偏好字段名，如 用户使用场景',
  `field_value` TEXT NOT NULL COMMENT '用户填写的内容，如 酒店大堂',
  `session_id` VARCHAR(128) DEFAULT NULL COMMENT '填写此偏好内容的会话ID',

  PRIMARY KEY (`id`),
  KEY `idx_hour_field` (`hour_bucket`, `field_name`),
  KEY `idx_enterprise` (`enterprise_id`),
  FULLTEXT KEY `ft_field_value` (`field_value`)  -- 若使用 MySQL 5.7+ 支持全文搜索
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小时级别用户偏好详情记录';


```
---

## 7. `aos_stat_session_duration_distribution_hourly` - 会话时长分布

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `<30s`、`30-60s` 等 | INT | 各时间段下的会话数量 |
| session_ids_duration_bucket | JSON | 不同区间的 session_id，形如 {"<30s": [...]} |

```sql
CREATE TABLE `aos_stat_session_duration_distribution_hourly` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',

  `hour_bucket` DATETIME NOT NULL,
  `enterprise_id` VARCHAR(64) NOT NULL,
  `device_id` VARCHAR(128) NOT NULL,

  `bucket_lt_30s` INT DEFAULT 0 COMMENT '0~30秒的会话数',
  `bucket_30_60s` INT DEFAULT 0 COMMENT '30~60秒的会话数',
  `bucket_1_3min` INT DEFAULT 0 COMMENT '1~3分钟的会话数',
  `bucket_3_5min` INT DEFAULT 0 COMMENT '3~5分钟的会话数',
  `bucket_5_10min` INT DEFAULT 0 COMMENT '5~10分钟的会话数',
  `bucket_10_20min` INT DEFAULT 0 COMMENT '10~20分钟的会话数',
  `bucket_gt_20min` INT DEFAULT 0 COMMENT '大于20分钟的会话数',

  `session_ids_duration_bucket` JSON COMMENT '各时长区间对应的 session_id 列表，Map结构',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_duration_bucket` (`hour_bucket`, `enterprise_id`, `device_id`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小时级别的会话时长分布统计';

```
---

## 8. `aos_stat_session_interval_distribution_hourly` - 会话间隔分布

```sql
CREATE TABLE `aos_stat_session_interval_distribution_hourly` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',

  `hour_bucket` DATETIME NOT NULL COMMENT '小时',
  `enterprise_id` VARCHAR(64) NOT NULL COMMENT '企业ID',
  `device_id` VARCHAR(128) NOT NULL COMMENT '设备ID',

  `bucket_lt_10s` INT DEFAULT 0 COMMENT '小于10秒的会话间隔数',
  `bucket_10_20s` INT DEFAULT 0 COMMENT '10-20秒',
  `bucket_1_3min` INT DEFAULT 0 COMMENT '1-3分钟',
  `bucket_3_5min` INT DEFAULT 0 COMMENT '3-5分钟',
  `bucket_5_10min` INT DEFAULT 0 COMMENT '5-10分钟',
  `bucket_10_20min` INT DEFAULT 0 COMMENT '10-20分钟',
  `bucket_gt_20min` INT DEFAULT 0 COMMENT '大于20分钟',

  `session_ids_interval_bucket` JSON COMMENT '各间隔区间对应的 session_id 列表，Map结构',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_interval_dist` (`hour_bucket`, `enterprise_id`, `device_id`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小时级别的发言间隔分布统计';


```
---

## 9. `aos_stat_active_hours_hourly` - 小时级别的设备活跃行为统计

```sql
CREATE TABLE `aos_stat_active_hours_hourly` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',

  `hour_bucket` DATETIME NOT NULL COMMENT '当前小时',
  `enterprise_id` VARCHAR(64) NOT NULL COMMENT '企业ID',
  `device_id` VARCHAR(128) NOT NULL COMMENT '设备ID',

  `consecutive_active_hours` INT DEFAULT 0 COMMENT '连续活跃小时数',
  `assistant_first_ratio` FLOAT DEFAULT 0 COMMENT 'assistant首发会话占比',
  `greeting_ratio` FLOAT DEFAULT 0 COMMENT 'greeting会话占比',

  `session_ids_active` JSON COMMENT '该小时活跃的 session ID 列表',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_active_hour` (`hour_bucket`, `enterprise_id`, `device_id`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小时级别的设备活跃行为统计';


```
---
