#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示会话间隔分布统计功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from datetime import datetime, timedelta
from apps.api.app.models.statistics import AosStatSessionIntervalDistributionHourly
from apps.api.app.database import get_speech_ai_robot_db
from apps.api.app.services.statistics_service import StatisticsService
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def demo_create_interval_distribution_data():
    """演示创建会话间隔分布数据"""
    logger.info("=== 演示创建会话间隔分布数据 ===")
    
    # 创建示例数据
    test_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
    
    interval_dist = AosStatSessionIntervalDistributionHourly(
        hour_bucket=test_hour,
        enterprise_id="demo_enterprise",
        device_id="demo_device_001",
        bucket_lt_10s=15,
        bucket_10_20s=25,
        bucket_1_3min=30,
        bucket_3_5min=20,
        bucket_5_10min=15,
        bucket_10_20min=10,
        bucket_gt_20min=5,
        session_ids_interval_bucket={
            "bucket_lt_10s": [f"session_{i:03d}" for i in range(1, 16)],
            "bucket_10_20s": [f"session_{i:03d}" for i in range(16, 41)],
            "bucket_1_3min": [f"session_{i:03d}" for i in range(41, 71)],
            "bucket_3_5min": [f"session_{i:03d}" for i in range(71, 91)],
            "bucket_5_10min": [f"session_{i:03d}" for i in range(91, 106)],
            "bucket_10_20min": [f"session_{i:03d}" for i in range(106, 116)],
            "bucket_gt_20min": [f"session_{i:03d}" for i in range(116, 121)]
        }
    )
    
    logger.info("创建会话间隔分布数据:")
    logger.info(f"时间: {interval_dist.hour_bucket}")
    logger.info(f"企业: {interval_dist.enterprise_id}")
    logger.info(f"设备: {interval_dist.device_id}")
    logger.info(f"<10秒: {interval_dist.bucket_lt_10s} 个会话")
    logger.info(f"10-20秒: {interval_dist.bucket_10_20s} 个会话")
    logger.info(f"1-3分钟: {interval_dist.bucket_1_3min} 个会话")
    logger.info(f"3-5分钟: {interval_dist.bucket_3_5min} 个会话")
    logger.info(f"5-10分钟: {interval_dist.bucket_5_10min} 个会话")
    logger.info(f"10-20分钟: {interval_dist.bucket_10_20min} 个会话")
    logger.info(f">20分钟: {interval_dist.bucket_gt_20min} 个会话")
    
    return interval_dist

def demo_save_interval_distribution_data():
    """演示保存会话间隔分布数据"""
    logger.info("=== 演示保存会话间隔分布数据 ===")
    
    try:
        with next(get_speech_ai_robot_db()) as db:
            # 创建多个设备的示例数据
            test_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
            
            devices = [
                {
                    "enterprise_id": "demo_enterprise_1",
                    "device_id": "demo_device_001",
                    "bucket_lt_10s": 12,
                    "bucket_10_20s": 18,
                    "bucket_1_3min": 25,
                    "bucket_3_5min": 15,
                    "bucket_5_10min": 10,
                    "bucket_10_20min": 8,
                    "bucket_gt_20min": 3
                },
                {
                    "enterprise_id": "demo_enterprise_1",
                    "device_id": "demo_device_002",
                    "bucket_lt_10s": 8,
                    "bucket_10_20s": 15,
                    "bucket_1_3min": 22,
                    "bucket_3_5min": 12,
                    "bucket_5_10min": 8,
                    "bucket_10_20min": 5,
                    "bucket_gt_20min": 2
                },
                {
                    "enterprise_id": "demo_enterprise_2",
                    "device_id": "demo_device_003",
                    "bucket_lt_10s": 20,
                    "bucket_10_20s": 30,
                    "bucket_1_3min": 35,
                    "bucket_3_5min": 20,
                    "bucket_5_10min": 15,
                    "bucket_10_20min": 12,
                    "bucket_gt_20min": 8
                }
            ]
            
            for device_data in devices:
                interval_dist = AosStatSessionIntervalDistributionHourly(
                    hour_bucket=test_hour,
                    enterprise_id=device_data["enterprise_id"],
                    device_id=device_data["device_id"],
                    bucket_lt_10s=device_data["bucket_lt_10s"],
                    bucket_10_20s=device_data["bucket_10_20s"],
                    bucket_1_3min=device_data["bucket_1_3min"],
                    bucket_3_5min=device_data["bucket_3_5min"],
                    bucket_5_10min=device_data["bucket_5_10min"],
                    bucket_10_20min=device_data["bucket_10_20min"],
                    bucket_gt_20min=device_data["bucket_gt_20min"],
                    session_ids_interval_bucket={
                        "bucket_lt_10s": [f"{device_data['device_id']}_session_{i:03d}" for i in range(1, device_data["bucket_lt_10s"] + 1)],
                        "bucket_10_20s": [f"{device_data['device_id']}_session_{i:03d}" for i in range(device_data["bucket_lt_10s"] + 1, device_data["bucket_lt_10s"] + device_data["bucket_10_20s"] + 1)],
                        "bucket_1_3min": [f"{device_data['device_id']}_session_{i:03d}" for i in range(device_data["bucket_lt_10s"] + device_data["bucket_10_20s"] + 1, device_data["bucket_lt_10s"] + device_data["bucket_10_20s"] + device_data["bucket_1_3min"] + 1)]
                    }
                )
                
                db.merge(interval_dist)
                logger.info(f"保存设备 {device_data['enterprise_id']}-{device_data['device_id']} 的会话间隔分布数据")
            
            db.commit()
            logger.info("所有设备数据保存成功")
            
            return True
            
    except Exception as e:
        logger.error(f"保存会话间隔分布数据失败: {e}", exc_info=True)
        return False

def demo_query_interval_distribution_data():
    """演示查询会话间隔分布数据"""
    logger.info("=== 演示查询会话间隔分布数据 ===")
    
    try:
        with next(get_speech_ai_robot_db()) as db:
            # 查询所有会话间隔分布数据
            all_data = db.query(AosStatSessionIntervalDistributionHourly).all()
            
            logger.info(f"查询到 {len(all_data)} 条会话间隔分布记录")
            
            for data in all_data:
                logger.info(f"设备: {data.enterprise_id}-{data.device_id}")
                logger.info(f"时间: {data.hour_bucket}")
                logger.info(f"间隔分布: <10s({data.bucket_lt_10s}) | 10-20s({data.bucket_10_20s}) | 1-3min({data.bucket_1_3min}) | 3-5min({data.bucket_3_5min}) | 5-10min({data.bucket_5_10min}) | 10-20min({data.bucket_10_20min}) | >20min({data.bucket_gt_20min})")
                logger.info("---")
            
            # 按企业统计
            enterprise_stats = db.query(
                AosStatSessionIntervalDistributionHourly.enterprise_id,
                AosStatSessionIntervalDistributionHourly.bucket_lt_10s,
                AosStatSessionIntervalDistributionHourly.bucket_10_20s,
                AosStatSessionIntervalDistributionHourly.bucket_1_3min,
                AosStatSessionIntervalDistributionHourly.bucket_3_5min,
                AosStatSessionIntervalDistributionHourly.bucket_5_10min,
                AosStatSessionIntervalDistributionHourly.bucket_10_20min,
                AosStatSessionIntervalDistributionHourly.bucket_gt_20min
            ).group_by(AosStatSessionIntervalDistributionHourly.enterprise_id).all()
            
            logger.info("按企业汇总的会话间隔分布:")
            for stat in enterprise_stats:
                total_sessions = (stat.bucket_lt_10s + stat.bucket_10_20s + stat.bucket_1_3min + 
                               stat.bucket_3_5min + stat.bucket_5_10min + stat.bucket_10_20min + stat.bucket_gt_20min)
                logger.info(f"企业 {stat.enterprise_id}: 总会话数 {total_sessions}")
            
            return True
            
    except Exception as e:
        logger.error(f"查询会话间隔分布数据失败: {e}", exc_info=True)
        return False

def demo_statistics_service_interval():
    """演示统计服务中的会话间隔分布计算"""
    logger.info("=== 演示统计服务中的会话间隔分布计算 ===")
    
    try:
        service = StatisticsService()
        
        # 计算最近2小时的统计数据
        current_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
        hours_to_calculate = [current_hour - timedelta(hours=i) for i in range(1, 3)]
        
        for hour in hours_to_calculate:
            logger.info(f"计算 {hour} 的会话间隔分布统计")
            service.calculate_statistics_for_hour(hour)
        
        logger.info("统计服务会话间隔分布计算完成")
        
        return True
        
    except Exception as e:
        logger.error(f"演示统计服务会话间隔分布计算失败: {e}", exc_info=True)
        return False

def main():
    """主演示函数"""
    logger.info("开始演示会话间隔分布统计功能")
    
    # 演示1: 创建数据
    demo_create_interval_distribution_data()
    
    # 演示2: 保存数据
    demo_save_interval_distribution_data()
    
    # 演示3: 查询数据
    demo_query_interval_distribution_data()
    
    # 演示4: 统计服务
    demo_statistics_service_interval()
    
    logger.info("会话间隔分布统计功能演示完成")

if __name__ == "__main__":
    main() 