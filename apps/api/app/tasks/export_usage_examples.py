#!/usr/bin/env python3
"""
企业数据导出功能使用示例

本文件提供了各种使用场景的示例代码
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from .enterprise_data_export import EnterpriseDataExporter
from .export_config import ExportConfig, ExportConfigTemplates


def example_basic_export():
    """基础导出示例"""
    print("=== 基础导出示例 ===")
    
    # 使用默认配置
    exporter = EnterpriseDataExporter()
    
    # 导出数据
    stats = exporter.export_data_by_date_range(
        enterprise_id="orion.ovs.entprise.4498860269",
        device_id="MC1BCNC016021348B693",
        start_date=datetime(2025, 1, 1),
        end_date=datetime(2025, 1, 31),
        output_dir="/tmp/export_basic"
    )
    
    print(f"导出完成: {stats}")


def example_detailed_export():
    """详细导出示例"""
    print("=== 详细导出示例 ===")
    
    # 使用详细配置
    config = ExportConfigTemplates.get_detailed_config()
    exporter = EnterpriseDataExporter(config=config)
    
    # 导出数据
    stats = exporter.export_data_by_date_range(
        enterprise_id="orion.ovs.entprise.4498860269",
        device_id="MC1BCNC016021348B693",
        start_date=datetime(2025, 1, 1),
        end_date=datetime(2025, 1, 31),
        output_dir="/tmp/export_detailed"
    )
    
    print(f"详细导出完成: {stats}")


def example_performance_export():
    """性能优化导出示例"""
    print("=== 性能优化导出示例 ===")
    
    # 使用性能优化配置
    config = ExportConfigTemplates.get_performance_config()
    exporter = EnterpriseDataExporter(config=config)
    
    # 导出数据
    stats = exporter.export_data_by_date_range(
        enterprise_id="orion.ovs.entprise.4498860269",
        device_id="MC1BCNC016021348B693",
        start_date=datetime(2025, 1, 1),
        end_date=datetime(2025, 1, 31),
        output_dir="/tmp/export_performance"
    )
    
    print(f"性能优化导出完成: {stats}")


def example_custom_config_export():
    """自定义配置导出示例"""
    print("=== 自定义配置导出示例 ===")
    
    # 创建自定义配置
    custom_config = ExportConfig({
        'merge_interval_seconds': 60,  # 60秒合并间隔
        'include_sensitive_data': False,
        'include_statistics': True,
        'include_user_preferences': False,
        'include_action_event_data': True,
        'include_detailed_messages': True,
        'include_session_metadata': True,
        'include_message_metadata': False,
        'max_message_length': 500,
        'show_progress': True,
        'create_index_file': True,
        'validate_output': True
    })
    
    exporter = EnterpriseDataExporter(config=custom_config)
    
    # 导出数据
    stats = exporter.export_data_by_date_range(
        enterprise_id="orion.ovs.entprise.4498860269",
        device_id="MC1BCNC016021348B693",
        start_date=datetime(2025, 1, 1),
        end_date=datetime(2025, 1, 31),
        output_dir="/tmp/export_custom"
    )
    
    print(f"自定义配置导出完成: {stats}")


def example_minimal_export():
    """最小化导出示例"""
    print("=== 最小化导出示例 ===")
    
    # 使用最小化配置
    config = ExportConfigTemplates.get_minimal_config()
    exporter = EnterpriseDataExporter(config=config)
    
    # 导出数据
    stats = exporter.export_data_by_date_range(
        enterprise_id="orion.ovs.entprise.4498860269",
        device_id="MC1BCNC016021348B693",
        start_date=datetime(2025, 1, 1),
        end_date=datetime(2025, 1, 31),
        output_dir="/tmp/export_minimal"
    )
    
    print(f"最小化导出完成: {stats}")


def example_batch_export():
    """批量导出示例"""
    print("=== 批量导出示例 ===")
    
    # 定义多个设备和时间范围
    export_tasks = [
        {
            'enterprise_id': 'orion.ovs.entprise.4498860269',
            'device_id': 'MC1BCNC016021348B693',
            'start_date': datetime(2025, 1, 1),
            'end_date': datetime(2025, 1, 31),
            'output_dir': '/tmp/export_batch/device1'
        },
        {
            'enterprise_id': 'orion.ovs.entprise.4498860269',
            'device_id': 'MC1BCNC016021348B694',
            'start_date': datetime(2025, 1, 1),
            'end_date': datetime(2025, 1, 31),
            'output_dir': '/tmp/export_batch/device2'
        },
        {
            'enterprise_id': 'orion.ovs.entprise.4498860270',
            'device_id': 'MC1BCNC016021348B695',
            'start_date': datetime(2025, 1, 1),
            'end_date': datetime(2025, 1, 31),
            'output_dir': '/tmp/export_batch/device3'
        }
    ]
    
    # 使用性能优化配置
    config = ExportConfigTemplates.get_performance_config()
    exporter = EnterpriseDataExporter(config=config)
    
    all_stats = []
    
    for i, task in enumerate(export_tasks, 1):
        print(f"处理任务 {i}/{len(export_tasks)}: {task['device_id']}")
        
        try:
            stats = exporter.export_data_by_date_range(
                enterprise_id=task['enterprise_id'],
                device_id=task['device_id'],
                start_date=task['start_date'],
                end_date=task['end_date'],
                output_dir=task['output_dir']
            )
            all_stats.append(stats)
            print(f"任务 {i} 完成: {stats}")
        except Exception as e:
            print(f"任务 {i} 失败: {e}")
    
    print(f"批量导出完成，成功 {len(all_stats)}/{len(export_tasks)} 个任务")


def example_config_file_export():
    """使用配置文件导出示例"""
    print("=== 配置文件导出示例 ===")
    
    # 创建配置文件
    config_file = "/tmp/export_config.json"
    custom_config = ExportConfig({
        'merge_interval_seconds': 45,
        'include_sensitive_data': False,
        'include_statistics': True,
        'include_user_preferences': True,
        'include_action_event_data': True,
        'include_detailed_messages': True,
        'include_session_metadata': True,
        'include_message_metadata': True,
        'max_message_length': 2000,
        'show_progress': True,
        'create_index_file': True,
        'validate_output': True
    })
    custom_config.save_to_file(config_file)
    
    # 从配置文件加载
    loaded_config = ExportConfig.from_file(config_file)
    exporter = EnterpriseDataExporter(config=loaded_config)
    
    # 导出数据
    stats = exporter.export_data_by_date_range(
        enterprise_id="orion.ovs.entprise.4498860269",
        device_id="MC1BCNC016021348B693",
        start_date=datetime(2025, 1, 1),
        end_date=datetime(2025, 1, 31),
        output_dir="/tmp/export_config_file"
    )
    
    print(f"配置文件导出完成: {stats}")
    
    # 清理配置文件
    if os.path.exists(config_file):
        os.remove(config_file)


def example_recent_data_export():
    """最近数据导出示例"""
    print("=== 最近数据导出示例 ===")
    
    # 导出最近7天的数据
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    
    config = ExportConfigTemplates.get_basic_config()
    exporter = EnterpriseDataExporter(config=config)
    
    stats = exporter.export_data_by_date_range(
        enterprise_id="orion.ovs.entprise.4498860269",
        device_id="MC1BCNC016021348B693",
        start_date=start_date,
        end_date=end_date,
        output_dir="/tmp/export_recent"
    )
    
    print(f"最近数据导出完成: {stats}")


def main():
    """运行所有示例"""
    print("企业数据导出功能使用示例")
    print("=" * 50)
    
    try:
        # 基础导出
        example_basic_export()
        print()
        
        # 详细导出
        example_detailed_export()
        print()
        
        # 性能优化导出
        example_performance_export()
        print()
        
        # 自定义配置导出
        example_custom_config_export()
        print()
        
        # 最小化导出
        example_minimal_export()
        print()
        
        # 批量导出
        example_batch_export()
        print()
        
        # 配置文件导出
        example_config_file_export()
        print()
        
        # 最近数据导出
        example_recent_data_export()
        print()
        
        print("所有示例执行完成！")
        
    except Exception as e:
        print(f"示例执行过程中发生错误: {e}")


if __name__ == '__main__':
    main() 