#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接和保存功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from datetime import datetime
from app.database import get_speech_ai_robot_db
from app.models.statistics import AosStatSessionBehaviorHourly
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_db_connection():
    """测试数据库连接"""
    try:
        logger.info("测试数据库连接...")
        with next(get_speech_ai_robot_db()) as db:
            logger.info("✅ 数据库连接成功")
            
            # 测试查询
            result = db.query(AosStatSessionBehaviorHourly).limit(1).all()
            logger.info(f"查询结果数量: {len(result)}")
            
            return True
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}", exc_info=True)
        return False

def test_save_data():
    """测试保存数据"""
    try:
        logger.info("测试保存数据...")
        with next(get_speech_ai_robot_db()) as db:
            # 创建测试数据
            test_data = AosStatSessionBehaviorHourly(
                hour_bucket=datetime.now().replace(minute=0, second=0, microsecond=0),
                enterprise_id="test_enterprise",
                device_id="test_device",
                session_total=1,
                session_ids_session_total=["test_session"],
                valid_sessions=1,
                session_ids_valid_sessions=["test_session"],
                no_response_sessions=0,
                session_ids_no_response_sessions=[],
                first_assistant_sessions=0,
                session_ids_first_assistant_sessions=[],
                first_user_sessions=1,
                session_ids_first_user_sessions=["test_session"],
                avg_conversation_turns=1.0,
                avg_session_duration=30.0,
                assistant_repeat_content_sessions=0,
                session_ids_assistant_repeat_content_sessions=[]
            )
            
            # 尝试保存
            db.add(test_data)
            db.commit()
            logger.info("✅ 数据保存成功")
            
            # 清理测试数据
            db.delete(test_data)
            db.commit()
            logger.info("✅ 测试数据清理完成")
            
            return True
    except Exception as e:
        logger.error(f"❌ 数据保存失败: {e}", exc_info=True)
        return False

def main():
    """主函数"""
    logger.info("开始数据库连接测试...")
    
    # 测试连接
    if not test_db_connection():
        logger.error("数据库连接测试失败")
        return
    
    # 测试保存
    if not test_save_data():
        logger.error("数据保存测试失败")
        return
    
    logger.info("✅ 所有测试通过")

if __name__ == "__main__":
    main() 