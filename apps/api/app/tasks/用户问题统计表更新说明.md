# 📊 用户问题统计表更新说明

## 🔄 表结构变更

### 原表结构
```sql
CREATE TABLE `aos_stat_user_question_detail_hourly` (
  `hour_bucket` DATETIME NOT NULL COMMENT '小时',
  `enterprise_id` VARCHAR(64) NOT NULL COMMENT '企业ID',
  `device_id` VARCHAR(128) NOT NULL COMMENT '设备ID',

  `unique_question_count` INT DEFAULT 0 COMMENT '去重后用户问题总类数',
  `session_ids_unique_question_count` JSON COMMENT '该指标对应的会话ID列表',

  `top_user_questions` JSON COMMENT '高频用户问题及次数，如{"你是谁": 10}',
  `wordcloud_keywords` JSON COMMENT '词云关键词和频次，如{"你好":5,"几点了":3}',

  PRIMARY KEY (`hour_bucket`, `enterprise_id`, `device_id`)
);
```

### 新表结构
```sql
CREATE TABLE `aos_stat_user_question_summary_hourly` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',

  `hour_bucket` DATETIME NOT NULL COMMENT '小时',
  `enterprise_id` VARCHAR(64) NOT NULL COMMENT '企业ID',
  `device_id` VARCHAR(128) NOT NULL COMMENT '设备ID',

  `unique_question_count` INT DEFAULT 0 COMMENT '去重后用户问题总类数',
  `session_ids_unique_question_count` JSON COMMENT '该指标对应的会话ID列表',

  `wordcloud_keywords` JSON COMMENT '词云关键词和频次，如{"你好":5,"几点了":3}',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_hourly_question_summary` (`hour_bucket`, `enterprise_id`, `device_id`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  KEY `idx_hour_bucket` (`hour_bucket`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小时级别的用户问题统计总览';
```

## 📋 主要变更

| 变更项 | 说明 |
|--------|------|
| 表名 | `aos_stat_user_question_detail_hourly` → `aos_stat_user_question_summary_hourly` |
| 主键 | 复合主键 → 自增ID + 唯一索引 |
| 移除字段 | `top_user_questions` (高频用户问题及次数) |
| 保留字段 | `unique_question_count`, `session_ids_unique_question_count`, `wordcloud_keywords` |
| 索引优化 | 添加了企业ID和小时索引 |

## 🔧 代码更新

### 1. 模型更新 (`app/models/statistics.py`)

```python
class AosStatUserQuestionSummaryHourly(Base_speech_ai_robot):
    """用户问题统计表"""
    __tablename__ = 'aos_stat_user_question_summary_hourly'
    __table_args__ = {'comment': '用户问题统计表（按小时汇总）'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    hour_bucket = Column(DateTime, nullable=False, comment='小时')
    enterprise_id = Column(String(64), nullable=False, comment='企业ID')
    device_id = Column(String(128), nullable=False, comment='设备ID')

    unique_question_count = Column(Integer, default=0, comment='去重后用户问题总类数')
    session_ids_unique_question_count = Column(JSON, comment='该指标对应的会话ID列表')

    wordcloud_keywords = Column(JSON, comment='词云关键词和频次，如{"你好":5,"几点了":3}')
```

### 2. 统计服务更新 (`app/services/statistics_service.py`)

#### 导入更新
```python
from app.models.statistics import (
    # ... 其他导入
    AosStatUserQuestionSummaryHourly,  # 更新类名
    # ... 其他导入
)
```

#### 新增用户问题统计方法
```python
def _calculate_user_question_stats(self, messages: List[Dict]) -> Dict:
    """计算用户问题统计"""
    stats = {
        'unique_question_count': 0,
        'session_ids_unique_question_count': set(),
        'wordcloud_keywords': {}
    }
    
    # 收集所有用户消息
    user_messages = [m for m in messages if m['role'] == 'user']
    user_contents = [m['content'] for m in user_messages]
    
    if not user_contents:
        return {
            'unique_question_count': 0,
            'session_ids_unique_question_count': [],
            'wordcloud_keywords': {}
        }
    
    # 计算唯一问题数量
    unique_questions = set(user_contents)
    stats['unique_question_count'] = len(unique_questions)
    stats['session_ids_unique_question_count'] = list(set(m['session_id'] for m in user_messages))
    
    # 简单的关键词统计
    keyword_count = {}
    for content in user_contents:
        import re
        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', content)
        for word in words:
            if len(word) > 1:  # 过滤单字符
                keyword_count[word] = keyword_count.get(word, 0) + 1
    
    # 取前10个高频关键词
    sorted_keywords = sorted(keyword_count.items(), key=lambda x: x[1], reverse=True)
    stats['wordcloud_keywords'] = dict(sorted_keywords[:10])
    
    return stats
```

#### 保存逻辑更新
```python
# 保存用户问题统计
user_question = AosStatUserQuestionSummaryHourly(
    hour_bucket=hour_bucket,
    enterprise_id=enterprise_id,
    device_id=device_id,
    unique_question_count=stats_data['user_question_stats']['unique_question_count'],
    session_ids_unique_question_count=stats_data['user_question_stats']['session_ids_unique_question_count'],
    wordcloud_keywords=stats_data['user_question_stats']['wordcloud_keywords']
)
```

## 📊 统计指标说明

### 1. 唯一问题数量 (`unique_question_count`)
- **定义**: 去重后的用户问题总类数
- **计算方式**: `len(set(user_contents))`
- **示例**: 如果用户问了"你好"、"现在几点了"、"你好"，则唯一问题数为2

### 2. 会话ID列表 (`session_ids_unique_question_count`)
- **定义**: 包含用户问题的会话ID列表
- **计算方式**: 收集所有包含用户消息的会话ID
- **用途**: 用于数据联动和详细分析

### 3. 关键词词云 (`wordcloud_keywords`)
- **定义**: 用户消息中的高频关键词及频次
- **计算方式**: 
  1. 对用户消息进行分词（支持中文和英文）
  2. 过滤单字符词汇
  3. 统计词频
  4. 取前10个高频词
- **格式**: `{"你好": 5, "几点了": 3, "天气": 2}`

## 🧪 测试验证

### 测试脚本
```bash
# 运行用户问题统计测试
python apps/api/app/tasks/test_user_question_stats.py

# 运行演示脚本
python apps/api/app/tasks/demo_user_question_stats.py
```

### 测试用例
```python
# 测试数据
test_messages = [
    {'session_id': 'session1', 'role': 'user', 'content': '你好，请问你是谁？'},
    {'session_id': 'session1', 'role': 'assistant', 'content': '我是智能机器人助手'},
    {'session_id': 'session1', 'role': 'user', 'content': '现在几点了？'},
    {'session_id': 'session2', 'role': 'user', 'content': '你好，请问你是谁？'},
    {'session_id': 'session3', 'role': 'user', 'content': '今天天气怎么样？'},
]

# 预期结果
expected_unique_count = 3  # "你好，请问你是谁？", "现在几点了？", "今天天气怎么样？"
expected_session_ids = ['session1', 'session2', 'session3']
expected_keywords = {"你好": 2, "现在": 1, "几点了": 1, "今天": 1, "天气": 1, "怎么样": 1}
```

## 🚀 使用方式

### 1. 命令行运行
```bash
# 计算指定时间的用户问题统计
python run_specific_time.py "2025-01-15 10:00:00"

# 计算特定设备的用户问题统计
python run_specific_time.py "2025-01-15 10:00:00" --enterprise enterprise1 --device device1
```

### 2. 编程调用
```python
from app.services.statistics_service import StatisticsService
from datetime import datetime

stats_service = StatisticsService()
target_hour = datetime(2025, 1, 15, 10, 0, 0)

# 计算所有设备的用户问题统计
stats_service.calculate_statistics_for_hour(target_hour)

# 计算特定设备的用户问题统计
stats_service.calculate_statistics_for_hour(target_hour, "enterprise1", "device1")
```

### 3. 查询结果
```sql
-- 查询用户问题统计
SELECT * FROM aos_stat_user_question_summary_hourly 
WHERE hour_bucket = '2025-01-15 10:00:00';

-- 查询特定设备的用户问题统计
SELECT * FROM aos_stat_user_question_summary_hourly 
WHERE hour_bucket = '2025-01-15 10:00:00' 
  AND enterprise_id = 'enterprise1' 
  AND device_id = 'device1';
```

## ⚠️ 注意事项

1. **数据迁移**: 如果原表有数据，需要手动迁移到新表
2. **索引优化**: 新表添加了企业ID和小时索引，查询性能更好
3. **关键词算法**: 当前使用简单的正则分词，后续可扩展为更复杂的NLP分词
4. **向后兼容**: 确保所有相关代码都已更新为新表名和字段

## 📈 性能优化

1. **索引优化**: 添加了 `idx_enterprise_id` 和 `idx_hour_bucket` 索引
2. **主键优化**: 使用自增ID作为主键，提高插入性能
3. **唯一约束**: 使用唯一索引确保数据一致性
4. **关键词限制**: 限制词云关键词数量为前10个，避免数据过大

## 🔮 后续扩展

1. **NLP分词**: 集成更专业的中文分词库（如jieba）
2. **情感分析**: 对用户问题进行情感分析
3. **问题分类**: 对用户问题进行自动分类
4. **实时统计**: 支持实时用户问题统计 