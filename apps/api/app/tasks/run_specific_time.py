#!/usr/bin/env python3
"""
指定时间运行统计脚本

用法: python run_specific_time.py [--enterprise <ID>] [--device <ID>] <时间1> [<时间2>]

示例:
# 统计所有设备在单个小时的数据
python app/tasks/run_specific_time.py "2025-07-04 19:00:00"

# 统计单个设备在单个小时的数据 (推荐格式)
python app/tasks/run_specific_time.py --enterprise orion.ovs.entprise.4498860269 --device MC1BCNC016021348B693 "2025-07-04 19:00:00"

# 统计单个设备在时间范围内的数据
python app/tasks/run_specific_time.py --enterprise orion.ovs.entprise.4498860269 --device MC1BCNC016021348B693 "2025-07-04 19:00:00" "2025-07-04 21:00:00"
"""

import sys
import os
import logging
import argparse
import json
from datetime import datetime, timedelta
try:
    from rich.console import Console
    from rich.table import Table
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False


# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.services.statistics_service import StatisticsService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def print_stats_summary(stats_by_device: dict, target_hour: datetime):
    """以表格形式打印统计结果摘要"""
    if not stats_by_device:
        return

    if not RICH_AVAILABLE:
        logger.warning("`rich` 库未安装，将以普通JSON格式打印结果。建议 `pip install rich` 以获得更好的可视化效果。")
        print(f"--- 统计时间: {target_hour.strftime('%Y-%m-%d %H:%M:%S')} ---")
        print(json.dumps(stats_by_device, indent=2, default=str))
        return

    console = Console()
    for device_key, stats in stats_by_device.items():
        console.rule(f"[bold cyan]设备 {device_key} 统计结果[/bold cyan]", style="cyan")

        # --- 会话与消息 ---
        table = Table(title="📊 会话与消息核心指标", show_header=True, header_style="bold magenta")
        table.add_column("指标", style="dim", width=35)
        table.add_column("统计值", justify="right")
        
        s_stats = stats.get('session_behavior', {})
        m_stats = stats.get('message_behavior', {})
        
        table.add_row("总会话数", str(s_stats.get('session_total', 0)))
        table.add_row("有效会话数 (有问有答)", str(s_stats.get('valid_sessions', 0)))
        table.add_row("无应答会话数", str(s_stats.get('no_response_sessions', 0)))
        table.add_row("助手首条消息会话数", str(s_stats.get('first_assistant_sessions', 0)))
        table.add_row("平均对话轮数", f"{s_stats.get('avg_conversation_turns', 0):.2f}")
        table.add_row("平均会话时长(s)", f"{s_stats.get('avg_session_duration', 0):.2f}")
        table.add_row("---", "---", end_section=True)
        table.add_row("用户消息总数", str(m_stats.get('user_msg_count', 0)))
        table.add_row("助手消息总数", str(m_stats.get('assistant_msg_count', 0)))
        table.add_row("触发Action的消息会话数", str(len(m_stats.get('session_ids_action_trigger_count', []))))
        table.add_row("触发Event的消息会话数", str(len(m_stats.get('session_ids_event_trigger_count', []))))
        console.print(table)

        # --- 动作统计 ---
        a_stats = stats.get('action_stats', [])
        table = Table(title="🕹️ 动作使用详情", show_header=True, header_style="bold yellow")
        if a_stats:
            table.add_column("动作名称", style="dim")
            table.add_column("动作中文名", style="dim")
            table.add_column("触发次数", justify="right")
            table.add_column("影响会话数", justify="right")
            for action in a_stats:
                table.add_row(
                    action.get('action_name', 'N/A'), 
                    action.get('action_display_name', ''),
                    str(action.get('action_count', 0)),
                    str(len(action.get('session_ids', [])))
                )
        else:
            table.add_column("信息")
            table.add_row("无动作使用数据")
        console.print(table)

        # --- Top N Questions ---
        q_stats = stats.get('user_question_stats', [])
        table = Table(title="💬 Top 用户问题", show_header=True, header_style="bold blue", show_edge=False)
        if q_stats:
            table.add_column("问题内容", style="dim", max_width=60, no_wrap=True)
            table.add_column("提问次数", justify="right")
            table.add_column("影响会话数", justify="right")
            
            sorted_questions = sorted(q_stats, key=lambda x: x.get('question_count', 0), reverse=True)
            for q in sorted_questions[:10]:
                table.add_row(
                    q.get('question_content', 'N/A'),
                    str(q.get('question_count', 0)),
                    str(len(q.get('session_ids', [])))
                )
        else:
            table.add_column("信息")
            table.add_row("无用户问题数据")
        console.print(table)

        # --- 时长分布 ---
        d_stats = stats.get('duration_dist', {})
        table = Table(title="⏳ 会话时长分布", show_header=True, header_style="bold green")
        if d_stats and any(isinstance(v, (int, float)) and v > 0 for k, v in d_stats.items() if 'bucket' in k):
            table.add_column("区间", style="dim")
            table.add_column("会话数", justify="right")
            table.add_row("< 30s", str(d_stats.get('bucket_lt_30s', 0)))
            table.add_row("30-60s", str(d_stats.get('bucket_30_60s', 0)))
            table.add_row("1-3min", str(d_stats.get('bucket_1_3min', 0)))
            table.add_row("3-5min", str(d_stats.get('bucket_3_5min', 0)))
            table.add_row("5-10min", str(d_stats.get('bucket_5_10min', 0)))
            table.add_row("> 10min", str(d_stats.get('bucket_gt_20min',0) + d_stats.get('bucket_10_20min',0))) # Combine two buckets for summary
        else:
            table.add_column("信息")
            table.add_row("无时长分布数据")
        console.print(table)

        # --- 事件行为统计 ---
        e_stats = stats.get('event_behavior', {})
        table = Table(title="🎉 事件行为统计", show_header=True, header_style="bold red")
        if e_stats and e_stats.get('event_total_count', 0) > 0:
            table.add_column("指标", style="dim", width=35)
            table.add_column("统计值", justify="left", max_width=60, no_wrap=False)
            table.add_row("事件总次数", str(e_stats.get('event_total_count', 0)))
            table.add_row("影响会话数", str(len(e_stats.get('session_ids_event_total_count', []))))
            table.add_row("起始场景频次 (最后一次)", e_stats.get('event_path_freq', 'N/A'))
            table.add_row("目标场景频次 (最后一次)", e_stats.get('target_scene_freq', 'N/A'))
        else:
            table.add_column("信息")
            table.add_row("无事件行为数据")
        console.print(table)

        # --- 会话间隔分布 ---
        i_stats = stats.get('interval_dist', {})
        table = Table(title="⏱️ 会话间隔分布", show_header=True, header_style="bold magenta")
        if i_stats and any(isinstance(v, (int, float)) and v > 0 for k, v in i_stats.items() if 'bucket' in k):
            table.add_column("区间", style="dim")
            table.add_column("会话数", justify="right")
            table.add_row("< 10s", str(i_stats.get('bucket_lt_10s', 0)))
            table.add_row("10-20s", str(i_stats.get('bucket_10_20s', 0)))
            table.add_row("1-3min", str(i_stats.get('bucket_1_3min', 0)))
            table.add_row("3-5min", str(i_stats.get('bucket_3_5min', 0)))
            table.add_row("5-10min", str(i_stats.get('bucket_5_10min', 0)))
            table.add_row("10-20min", str(i_stats.get('bucket_10_20min', 0)))
            table.add_row("> 20min", str(i_stats.get('bucket_gt_20min', 0)))
        else:
            table.add_column("信息")
            table.add_row("无会话间隔数据")
        console.print(table)
            
        # --- 用户偏好详情 ---
        p_stats = stats.get('preference_details', [])
        table = Table(title="❤️ 用户偏好详情 (Top 5)", show_header=True, header_style="bold blue")
        if p_stats:
            table.add_column("偏好字段", style="dim")
            table.add_column("偏好内容", style="dim", max_width=50)
            table.add_column("会话ID", style="dim", no_wrap=True)
            for pref in p_stats[:5]:
                table.add_row(
                    pref.get('field_name', 'N/A'),
                    str(pref.get('field_value', 'N/A')),
                    pref.get('session_id', 'N/A')
                )
            if len(p_stats) > 5:
                table.add_row("...", f"({len(p_stats) - 5} more)", "...")
        else:
            table.add_column("信息")
            table.add_row("无用户偏好数据")
        console.print(table)

        # --- 设备活跃统计 ---
        ac_stats = stats.get('active_hours_stats', {})
        table = Table(title="🔥 设备活跃统计", show_header=True, header_style="bold yellow")
        if ac_stats:
            table.add_column("指标", style="dim", width=35)
            table.add_column("统计值", justify="right")
            table.add_row("连续活跃小时数", str(ac_stats.get('consecutive_active_hours', 1))) # 默认为1
            table.add_row("助手首发会话占比", f"{ac_stats.get('assistant_first_ratio', 0):.2%}")
            table.add_row("Greeting会话占比", f"{ac_stats.get('greeting_ratio', 0):.2%}")
            table.add_row("活跃会话数", str(len(ac_stats.get('session_ids_active', []))))
        else:
            table.add_column("信息")
            table.add_row("无设备活跃数据")
        console.print(table)

        # 在末尾右对齐打印时间
        console.print(f"[dim]统计时间: {target_hour.strftime('%Y-%m-%d %H:%M:%S')}[/dim]", justify="right")
        console.print()

def run_statistics_for_time(target_time_str: str, enterprise_id: str = None, device_id: str = None):
    """运行指定时间的统计"""
    try:
        target_hour = datetime.strptime(target_time_str, "%Y-%m-%d %H:%M:%S").replace(minute=0, second=0, microsecond=0)
        stats_service = StatisticsService()
        calculated_data = stats_service.calculate_statistics_for_hour(target_hour, enterprise_id, device_id)
        print_stats_summary(calculated_data, target_hour)
        return True
    except Exception as e:
        logger.error(f"❌ 统计计算失败: {e}", exc_info=True)
        return False

def run_statistics_for_time_range(start_time_str: str, end_time_str: str, enterprise_id: str = None, device_id: str = None):
    """运行指定时间范围的统计"""
    try:
        start_hour = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S").replace(minute=0, second=0, microsecond=0)
        end_hour = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S").replace(minute=0, second=0, microsecond=0)
        
        current_hour = start_hour
        stats_service = StatisticsService()
        while current_hour <= end_hour:
            logger.info(f"--- 正在计算 {current_hour} 的数据 ---")
            calculated_data = stats_service.calculate_statistics_for_hour(current_hour, enterprise_id, device_id)
            print_stats_summary(calculated_data, current_hour)
            current_hour += timedelta(hours=1)
        return True
    except Exception as e:
        logger.error(f"❌ 统计计算失败: {e}", exc_info=True)
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='指定时间运行统计脚本', formatter_class=argparse.RawTextHelpFormatter)
    parser.add_argument('time1', help='开始时间 (YYYY-MM-DD HH:MM:SS)')
    parser.add_argument('time2', nargs='?', help='结束时间 (YYYY-MM-DD HH:MM:SS)')
    parser.add_argument('--enterprise', help='企业ID')
    parser.add_argument('--device', help='设备ID')
    
    args = parser.parse_args()
    
    has_device_filter = args.enterprise and args.device
    
    if args.time2:
        logger.info(f"运行时间范围统计: {args.time1} -> {args.time2}")
        success = run_statistics_for_time_range(args.time1, args.time2, args.enterprise, args.device)
    else:
        logger.info(f"运行单个时间点统计: {args.time1}")
        success = run_statistics_for_time(args.time1, args.enterprise, args.device)
    
    if success:
        logger.info("✅ 所有统计计算已完成。")
    else:
        logger.error("❌ 统计计算中发生错误。")
        
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())