# 📊 活跃小时统计表更新说明

## 更新概述

`aos_stat_active_hours_hourly` 表的主键结构已更新，以保持与设计文档的一致性。

## 主要变更

### 主键结构调整

- **原主键**: `(hour_bucket, enterprise_id, device_id)`
- **新主键**: `id` (自增主键)
- **唯一约束**: `(hour_bucket, enterprise_id, device_id)`

### 字段注释优化

- `assistant_first_ratio` 注释更新为 "assistant首发会话占比"
- `session_ids_active` 注释更新为 "该小时活跃的 session ID 列表"

## 更新后的表结构

```sql
CREATE TABLE `aos_stat_active_hours_hourly` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',

  `hour_bucket` DATETIME NOT NULL COMMENT '当前小时',
  `enterprise_id` VARCHAR(64) NOT NULL COMMENT '企业ID',
  `device_id` VARCHAR(128) NOT NULL COMMENT '设备ID',

  `consecutive_active_hours` INT DEFAULT 0 COMMENT '连续活跃小时数',
  `assistant_first_ratio` FLOAT DEFAULT 0 COMMENT 'assistant首发会话占比',
  `greeting_ratio` FLOAT DEFAULT 0 COMMENT 'greeting会话占比',

  `session_ids_active` JSON COMMENT '该小时活跃的 session ID 列表',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_active_hour` (`hour_bucket`, `enterprise_id`, `device_id`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小时级别的设备活跃行为统计';
```

## 模型更新

### SQLAlchemy 模型

```python
class AosStatActiveHoursHourly(Base_speech_ai_robot):
    """连续活跃小时数统计表"""
    __tablename__ = 'aos_stat_active_hours_hourly'
    __table_args__ = {'comment': '连续活跃小时数统计表（按小时汇总）'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    hour_bucket = Column(DateTime, nullable=False, comment='当前小时')
    enterprise_id = Column(String(64), nullable=False, comment='企业ID')
    device_id = Column(String(128), nullable=False, comment='设备ID')

    consecutive_active_hours = Column(Integer, default=0, comment='连续活跃小时数')
    assistant_first_ratio = Column(Float, default=0, comment='assistant首发会话占比')
    greeting_ratio = Column(Float, default=0, comment='greeting会话占比')
    session_ids_active = Column(JSON, comment='该小时活跃的 session ID 列表')
```

## 统计服务更新

### 保存逻辑保持不变

```python
# 保存活跃小时统计
active_hours = AosStatActiveHoursHourly(
    hour_bucket=hour_bucket,
    enterprise_id=enterprise_id,
    device_id=device_id,
    consecutive_active_hours=1,
    assistant_first_ratio=stats_data['session_behavior']['first_assistant_sessions'] / stats_data['session_behavior']['session_total'] if stats_data['session_behavior']['session_total'] > 0 else 0,
    greeting_ratio=0,
    session_ids_active=stats_data['session_behavior']['session_ids_session_total']
)
```

## 测试脚本

### 测试文件

- `test_active_hours_hourly.py` - 测试活跃小时统计表字段更新
- `demo_active_hours_hourly.py` - 演示活跃小时统计功能

### 运行测试

```bash
# 测试字段更新
python apps/api/app/tasks/test_active_hours_hourly.py

# 演示功能
python app/tasks/demo_active_hours_hourly.py
```

## 数据示例

### 活跃小时统计数据

```python
{
    "hour_bucket": "2025-01-15 14:00:00",
    "enterprise_id": "enterprise_001",
    "device_id": "device_001",
    "consecutive_active_hours": 8,
    "assistant_first_ratio": 0.75,
    "greeting_ratio": 0.2,
    "session_ids_active": ["session_001", "session_002", "session_003", "session_004"]
}
```

## 功能特点

### 连续活跃小时数

- 统计设备连续多少小时内有会话记录
- 用于评估设备的持续使用情况
- 支持设备活跃度分析

### Assistant首发会话占比

- 计算首条消息为assistant的会话占比
- 反映机器人的主动性和用户交互模式
- 用于优化机器人行为策略

### Greeting会话占比

- 统计包含打招呼行为的会话占比
- 评估机器人的友好度和用户体验
- 用于改进机器人交互设计

## 影响范围

### 已更新的文件

1. **模型文件**: `apps/api/app/models/statistics.py`
   - 更新 `AosStatActiveHoursHourly` 类主键结构
   - 优化字段注释

2. **测试文件**: 
   - `test_active_hours_hourly.py` - 新增测试脚本
   - `demo_active_hours_hourly.py` - 新增演示脚本

### 兼容性说明

- 主键结构优化，支持更好的数据管理
- 保持向后兼容的数据结构
- 字段功能保持不变

## 使用建议

1. **数据迁移**: 如果已有历史数据，建议进行数据迁移
2. **测试验证**: 运行测试脚本确保功能正常
3. **监控观察**: 观察新主键结构的使用情况

## 数据分析应用

### 设备活跃度分析

```python
# 查询高活跃设备
high_active_devices = db.query(AosStatActiveHoursHourly).filter(
    AosStatActiveHoursHourly.consecutive_active_hours >= 10
).all()

# 分析assistant首发占比分布
high_assistant_first = db.query(AosStatActiveHoursHourly).filter(
    AosStatActiveHoursHourly.assistant_first_ratio >= 0.7
).count()
```

### 企业级统计

```python
# 按企业汇总活跃小时统计
enterprise_stats = db.query(
    AosStatActiveHoursHourly.enterprise_id,
    AosStatActiveHoursHourly.consecutive_active_hours,
    AosStatActiveHoursHourly.assistant_first_ratio,
    AosStatActiveHoursHourly.greeting_ratio
).group_by(AosStatActiveHoursHourly.enterprise_id).all()
```

## 总结

活跃小时统计表的主键结构已成功更新，与设计文档保持一致。新的主键结构更加规范，便于数据管理和查询优化。所有相关的代码和测试都已更新完成，支持完整的活跃小时统计分析功能。 