#!/usr/bin/env python3
"""
机器人对话统计系统演示脚本
展示如何使用统计功能和验证数据
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.services.statistics_service import StatisticsService
from app.tasks.validation import validate_statistics_for_hour

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def demo_basic_statistics():
    """演示基础统计功能"""
    logger.info("=" * 60)
    logger.info("演示：基础统计功能")
    logger.info("=" * 60)
    
    # 创建统计服务
    stats_service = StatisticsService()
    
    # 计算前1个小时的统计
    now = datetime.now()
    target_hour = now.replace(minute=0, second=0, microsecond=0) - timedelta(hours=1)
    
    logger.info(f"计算时间: {target_hour}")
    
    try:
        # 执行统计
        stats_service.calculate_statistics_for_hour(target_hour)
        logger.info("✅ 统计计算完成")
        return True
    except Exception as e:
        logger.error(f"❌ 统计计算失败: {e}")
        return False

def demo_data_validation():
    """演示数据验证功能"""
    logger.info("\n" + "=" * 60)
    logger.info("演示：数据验证功能")
    logger.info("=" * 60)
    
    # 验证前1个小时的数据
    now = datetime.now()
    target_hour = now.replace(minute=0, second=0, microsecond=0) - timedelta(hours=1)
    
    logger.info(f"验证时间: {target_hour}")
    
    try:
        # 执行验证
        validation_results = validate_statistics_for_hour(target_hour)
        
        if validation_results['passed']:
            logger.info("✅ 数据验证通过")
            return True
        else:
            logger.error("❌ 数据验证失败")
            return False
    except Exception as e:
        logger.error(f"❌ 数据验证出错: {e}")
        return False

def demo_multiple_hours():
    """演示多小时统计"""
    logger.info("\n" + "=" * 60)
    logger.info("演示：多小时统计功能")
    logger.info("=" * 60)
    
    # 计算前2个小时的统计
    now = datetime.now()
    target_hours = []
    for i in range(2):
        target_hour = now.replace(minute=0, second=0, microsecond=0) - timedelta(hours=i+1)
        target_hours.append(target_hour)
    
    logger.info(f"计算时间范围: {target_hours[0]} 到 {target_hours[-1]}")
    
    try:
        # 创建统计服务
        stats_service = StatisticsService()
        
        # 执行统计
        stats_service.calculate_statistics_for_hours(target_hours)
        logger.info("✅ 多小时统计完成")
        return True
    except Exception as e:
        logger.error(f"❌ 多小时统计失败: {e}")
        return False

def demo_specific_device(enterprise_id: str, device_id: str):
    """演示特定设备统计"""
    logger.info("\n" + "=" * 60)
    logger.info(f"演示：特定设备统计 ({enterprise_id}-{device_id})")
    logger.info("=" * 60)
    
    # 计算前1个小时的统计
    now = datetime.now()
    target_hour = now.replace(minute=0, second=0, microsecond=0) - timedelta(hours=1)
    
    logger.info(f"计算时间: {target_hour}")
    logger.info(f"目标设备: {enterprise_id}-{device_id}")
    
    try:
        # 执行统计
        stats_service = StatisticsService()
        stats_service.calculate_statistics_for_hour(target_hour)
        
        # 执行验证
        validation_results = validate_statistics_for_hour(target_hour, enterprise_id, device_id)
        
        if validation_results['passed']:
            logger.info("✅ 特定设备统计验证通过")
            return True
        else:
            logger.error("❌ 特定设备统计验证失败")
            return False
    except Exception as e:
        logger.error(f"❌ 特定设备统计出错: {e}")
        return False

def show_usage():
    """显示使用说明"""
    logger.info("\n" + "=" * 60)
    logger.info("使用说明")
    logger.info("=" * 60)
    logger.info("1. 基础演示: python demo.py")
    logger.info("2. 特定设备演示: python demo.py <enterprise_id> <device_id>")
    logger.info("3. 查看帮助: python demo.py --help")
    logger.info("\n示例:")
    logger.info("  python demo.py")
    logger.info("  python demo.py enterprise1 device1")

def main():
    """主演示函数"""
    logger.info("🤖 机器人对话统计系统演示")
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] in ['--help', '-h']:
            show_usage()
            return 0
        
        if len(sys.argv) == 3:
            # 特定设备演示
            enterprise_id = sys.argv[1]
            device_id = sys.argv[2]
            
            # 执行所有演示
            demo1 = demo_basic_statistics()
            demo2 = demo_data_validation()
            demo3 = demo_multiple_hours()
            demo4 = demo_specific_device(enterprise_id, device_id)
            
            # 总结
            logger.info("\n" + "=" * 60)
            logger.info("演示结果总结")
            logger.info("=" * 60)
            
            demos = [
                ("基础统计功能", demo1),
                ("数据验证功能", demo2),
                ("多小时统计功能", demo3),
                (f"特定设备统计 ({enterprise_id}-{device_id})", demo4)
            ]
            
            for name, result in demos:
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"{name}: {status}")
            
            overall_success = all([demo1, demo2, demo3, demo4])
            
            if overall_success:
                logger.info("\n🎉 所有演示通过！")
                return 0
            else:
                logger.error("\n💥 部分演示失败！")
                return 1
        else:
            logger.error("❌ 参数错误！请使用: python demo.py <enterprise_id> <device_id>")
            show_usage()
            return 1
    else:
        # 基础演示
        demo1 = demo_basic_statistics()
        demo2 = demo_data_validation()
        demo3 = demo_multiple_hours()
        
        # 总结
        logger.info("\n" + "=" * 60)
        logger.info("演示结果总结")
        logger.info("=" * 60)
        
        demos = [
            ("基础统计功能", demo1),
            ("数据验证功能", demo2),
            ("多小时统计功能", demo3)
        ]
        
        for name, result in demos:
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{name}: {status}")
        
        overall_success = all([demo1, demo2, demo3])
        
        if overall_success:
            logger.info("\n🎉 所有演示通过！")
            return 0
        else:
            logger.error("\n💥 部分演示失败！")
            return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 