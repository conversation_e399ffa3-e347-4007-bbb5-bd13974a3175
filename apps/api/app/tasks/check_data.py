#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中是否有指定时间范围的数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from datetime import datetime, timedelta
from app.database import get_agentos_db
from app.models.conversation import AosConversationSession, AosConversationMessage
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_session_data(target_hour: datetime, enterprise_id: str = None, device_id: str = None):
    """检查指定时间范围内的会话数据"""
    logger.info(f"=== 检查 {target_hour} 的会话数据 ===")
    
    start_time = target_hour
    end_time = target_hour + timedelta(hours=1)
    
    try:
        with next(get_agentos_db()) as db:
            # 构建查询
            query = db.query(AosConversationSession).filter(
                AosConversationSession.session_start_time >= start_time,
                AosConversationSession.session_start_time < end_time
            )
            
            if enterprise_id:
                query = query.filter(AosConversationSession.enterprise_id == enterprise_id)
                logger.info(f"企业ID过滤: {enterprise_id}")
            
            if device_id:
                query = query.filter(AosConversationSession.device_id == device_id)
                logger.info(f"设备ID过滤: {device_id}")
            
            # 执行查询
            sessions = query.all()
            
            logger.info(f"找到 {len(sessions)} 个会话")
            
            if sessions:
                # 显示前5个会话的详细信息
                logger.info("前5个会话详情:")
                for i, session in enumerate(sessions[:5]):
                    logger.info(f"  {i+1}. 会话ID: {session.session_id}")
                    logger.info(f"     企业ID: {session.enterprise_id}")
                    logger.info(f"     设备ID: {session.device_id}")
                    logger.info(f"     开始时间: {session.session_start_time}")
                    logger.info(f"     结束时间: {session.session_end_time}")
                    logger.info(f"     对话轮数: {session.conversation_turns}")
                    logger.info(f"     会话时长: {session.session_duration_seconds}秒")
                    logger.info(f"     项目ID: {session.project_id}")
                    logger.info("     ---")
                
                # 统计企业ID和设备ID分布
                enterprise_counts = {}
                device_counts = {}
                
                for session in sessions:
                    enterprise_counts[session.enterprise_id] = enterprise_counts.get(session.enterprise_id, 0) + 1
                    device_counts[session.device_id] = device_counts.get(session.device_id, 0) + 1
                
                logger.info("企业ID分布:")
                for enterprise_id, count in sorted(enterprise_counts.items()):
                    logger.info(f"  {enterprise_id}: {count} 个会话")
                
                logger.info("设备ID分布:")
                for device_id, count in sorted(device_counts.items()):
                    logger.info(f"  {device_id}: {count} 个会话")
                
                # 检查消息数据
                session_ids = [s.session_id for s in sessions]
                messages = db.query(AosConversationMessage).filter(
                    AosConversationMessage.session_id.in_(session_ids)
                ).all()
                
                logger.info(f"找到 {len(messages)} 条消息")
                
                if messages:
                    # 统计消息类型
                    user_messages = [m for m in messages if m.role == 'user']
                    assistant_messages = [m for m in messages if m.role == 'assistant']
                    
                    logger.info(f"用户消息: {len(user_messages)} 条")
                    logger.info(f"Assistant消息: {len(assistant_messages)} 条")
                    
                    # 检查action和event数据
                    action_messages = [m for m in messages if m.action_data]
                    event_messages = [m for m in messages if m.event_data]
                    
                    logger.info(f"包含action的消息: {len(action_messages)} 条")
                    logger.info(f"包含event的消息: {len(event_messages)} 条")
                
            else:
                logger.warning("没有找到会话数据")
                
                # 检查是否有其他时间的数据
                logger.info("检查其他时间的数据...")
                
                # 检查前后各2小时的数据
                for i in range(-2, 3):
                    check_time = target_hour + timedelta(hours=i)
                    check_start = check_time
                    check_end = check_time + timedelta(hours=1)
                    
                    check_query = db.query(AosConversationSession).filter(
                        AosConversationSession.session_start_time >= check_start,
                        AosConversationSession.session_start_time < check_end
                    )
                    
                    if enterprise_id:
                        check_query = check_query.filter(AosConversationSession.enterprise_id == enterprise_id)
                    if device_id:
                        check_query = check_query.filter(AosConversationSession.device_id == device_id)
                    
                    check_sessions = check_query.all()
                    
                    if check_sessions:
                        logger.info(f"  {check_time}: 找到 {len(check_sessions)} 个会话")
                    else:
                        logger.info(f"  {check_time}: 无数据")
                
                # 检查是否有该企业或设备的数据
                if enterprise_id or device_id:
                    logger.info("检查是否有该企业或设备的数据...")
                    
                    if enterprise_id:
                        enterprise_sessions = db.query(AosConversationSession).filter(
                            AosConversationSession.enterprise_id == enterprise_id
                        ).limit(5).all()
                        
                        if enterprise_sessions:
                            logger.info(f"企业 {enterprise_id} 有数据，最近的会话:")
                            for session in enterprise_sessions:
                                logger.info(f"  {session.session_start_time}: {session.session_id}")
                        else:
                            logger.warning(f"企业 {enterprise_id} 没有数据")
                    
                    if device_id:
                        device_sessions = db.query(AosConversationSession).filter(
                            AosConversationSession.device_id == device_id
                        ).limit(5).all()
                        
                        if device_sessions:
                            logger.info(f"设备 {device_id} 有数据，最近的会话:")
                            for session in device_sessions:
                                logger.info(f"  {session.session_start_time}: {session.session_id}")
                        else:
                            logger.warning(f"设备 {device_id} 没有数据")
            
            return len(sessions) > 0
            
    except Exception as e:
        logger.error(f"检查数据时出错: {e}", exc_info=True)
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        logger.error("请提供时间参数")
        logger.info("用法: python check_data.py <YYYY-MM-DD HH:MM:SS> [enterprise_id] [device_id]")
        return
    
    # 解析参数
    time_str = sys.argv[1]
    enterprise_id = sys.argv[2] if len(sys.argv) > 2 else None
    device_id = sys.argv[3] if len(sys.argv) > 3 else None
    
    try:
        target_hour = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
        target_hour = target_hour.replace(minute=0, second=0, microsecond=0)
        
        logger.info(f"检查时间: {target_hour}")
        if enterprise_id:
            logger.info(f"企业ID: {enterprise_id}")
        if device_id:
            logger.info(f"设备ID: {device_id}")
        
        has_data = check_session_data(target_hour, enterprise_id, device_id)
        
        if has_data:
            logger.info("✅ 找到数据")
        else:
            logger.warning("❌ 没有找到数据")
            
    except ValueError as e:
        logger.error(f"时间格式错误: {e}")
        logger.info("正确格式: YYYY-MM-DD HH:MM:SS")
    except Exception as e:
        logger.error(f"检查数据失败: {e}", exc_info=True)

if __name__ == "__main__":
    main() 