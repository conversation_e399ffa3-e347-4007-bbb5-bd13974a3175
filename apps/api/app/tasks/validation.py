#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证脚本
功能: 
- 对比统计结果与原始数据，确保计算准确性
- 支持验证单个设备、单个小时的数据
- 输出清晰的验证报告

用法:
python validation.py <YYYY-MM-DD HH:MM:SS> --enterprise <enterprise_id> --device <device_id>

示例:
python app/tasks/validation.py --enterprise orion.ovs.entprise.4498860269 --device MC1BCNC016021348B693 "2025-07-04 19:00:00"
"""
import sys
import os
import logging
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Any
from collections import defaultdict
import math

try:
    from rich.logging import RichHandler
    from rich.console import Console
    from rich.theme import Theme
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database import get_agentos_db, get_speech_ai_robot_db
from app.models.conversation import AosConversationSession, AosConversationMessage
from app.models.statistics import *

# 配置日志
if RICH_AVAILABLE:
    logging.basicConfig(
        level=logging.INFO,
        format="%(message)s",
        datefmt="[%X]",
        handlers=[RichHandler(rich_tracebacks=True, console=Console(theme=Theme({
            "logging.level.warning": "bold yellow",
            "logging.level.error": "bold red",
        })))]
    )
else:
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

logger = logging.getLogger("rich" if RICH_AVAILABLE else __name__)

class DataValidator:
    """数据验证器"""

    def __init__(self, target_hour: datetime, enterprise_id: str, device_id: str):
        self.target_hour = target_hour
        self.start_time = target_hour
        self.end_time = target_hour + timedelta(hours=1)
        self.enterprise_id = enterprise_id
        self.device_id = device_id
        self.mismatches = []

    def _get_source_data(self) -> (List[Dict], List[Dict]):
        """从 agentos_cn 获取原始数据"""
        logger.info("正在从 agentos_cn 获取原始数据...")
        with next(get_agentos_db()) as db:
            sessions = db.query(AosConversationSession).filter(
                AosConversationSession.session_start_time >= self.start_time,
                AosConversationSession.session_start_time < self.end_time,
                AosConversationSession.enterprise_id == self.enterprise_id,
                AosConversationSession.device_id == self.device_id
            ).all()

            if not sessions:
                return [], []

            session_ids = [s.session_id for s in sessions]
            messages = db.query(AosConversationMessage).filter(
                AosConversationMessage.session_id.in_(session_ids)
            ).all()
            logger.info(f"获取到 {len(sessions)} 个会话, {len(messages)} 条消息")
            return sessions, messages

    def _get_stats_data(self) -> Dict[str, Any]:
        """从 speech_ai_robot 获取统计数据"""
        logger.info("正在从 speech_ai_robot 获取统计数据...")
        stats_data = {}
        with next(get_speech_ai_robot_db()) as db:
            query_params = {
                'hour_bucket': self.target_hour,
                'enterprise_id': self.enterprise_id,
                'device_id': self.device_id
            }
            stats_data['session_behavior'] = db.query(AosStatSessionBehaviorHourly).filter_by(**query_params).first()
            stats_data['message_behavior'] = db.query(AosStatMessageBehaviorHourly).filter_by(**query_params).first()
            stats_data['action_behavior'] = db.query(AosStatActionBehaviorHourly).filter_by(**query_params).all()
            stats_data['event_behavior'] = db.query(AosStatEventBehaviorHourly).filter_by(**query_params).first()
            stats_data['user_question'] = db.query(AosStatUserQuestionTopNHourly).filter_by(**query_params).all()
            stats_data['user_preference'] = db.query(AosStatUserPreferenceDetailHourly).filter_by(**query_params).all()
            stats_data['duration_dist'] = db.query(AosStatSessionDurationDistributionHourly).filter_by(**query_params).first()
            stats_data['interval_dist'] = db.query(AosStatSessionIntervalDistributionHourly).filter_by(**query_params).first()
            stats_data['active_hours'] = db.query(AosStatActiveHoursHourly).filter_by(**query_params).first()
        return stats_data

    def _compare(self, metric_name: str, expected: Any, actual: Any, precision=4):
        """比较期望值和实际值，对浮点数使用四舍五入"""
        is_match = False
        if isinstance(expected, float) or isinstance(actual, float):
            is_match = round(expected, precision) == round(actual, precision)
        else:
            is_match = (expected == actual)

        if is_match:
            logger.info(f"  ✅ {metric_name}: 匹配 (值: {actual})")
        else:
            logger.warning(f"  ❌ {metric_name}: 不匹配 (期望: {expected}, 实际: {actual})")
            self.mismatches.append(metric_name)
    
    def run_validation(self):
        """执行验证"""
        source_sessions, source_messages = self._get_source_data()
        stats_data = self._get_stats_data()

        if not source_sessions:
            logger.warning("没有找到原始数据，无法验证")
            # 检查统计表是否也不应有数据
            if any(stats for stats in stats_data.values()):
                 logger.error("❌ 原始数据为空，但统计表不为空！")
                 self.mismatches.append("数据不一致：原始数据为空但统计表不为空")
            else:
                logger.info("✅ 原始数据和统计数据均为空，一致。")
            return

        # --- 验证会话行为 ---
        logger.info("\n--- 1. 验证会话行为 (aos_stat_session_behavior_hourly) ---")
        actual_session_stats = stats_data['session_behavior']
        if not actual_session_stats:
            logger.error("未找到会话行为统计数据")
            self.mismatches.append("会话行为统计数据缺失")
        else:
            # 期望值计算
            expected_session_total = len(source_sessions)
            expected_avg_duration = sum(s.session_duration_seconds for s in source_sessions) / expected_session_total if expected_session_total else 0
            expected_avg_turns = sum(s.conversation_turns for s in source_sessions) / expected_session_total if expected_session_total else 0

            self._compare("会话总数", expected_session_total, actual_session_stats.session_total)
            self._compare("平均会话时长", expected_avg_duration, actual_session_stats.avg_session_duration)
            self._compare("平均对话轮次", expected_avg_turns, actual_session_stats.avg_conversation_turns)

        # --- 验证消息行为 ---
        logger.info("\n--- 2. 验证消息行为 (aos_stat_message_behavior_hourly) ---")
        actual_message_stats = stats_data['message_behavior']
        if not actual_message_stats:
            logger.error("未找到消息行为统计数据")
            self.mismatches.append("消息行为统计数据缺失")
        else:
            # 期望值计算
            expected_user_msg_count = len([m for m in source_messages if m.role == 'user'])
            expected_assistant_msg_count = len([m for m in source_messages if m.role == 'assistant'])
            expected_action_count = len([m for m in source_messages if m.action_data])
            expected_event_count = len([m for m in source_messages if m.event_data])

            self._compare("用户消息数", expected_user_msg_count, actual_message_stats.user_msg_count)
            self._compare("助手消息数", expected_assistant_msg_count, actual_message_stats.assistant_msg_count)
            self._compare("Action触发数", expected_action_count, actual_message_stats.action_trigger_count)
            self._compare("Event触发数", expected_event_count, actual_message_stats.event_trigger_count)

        # --- 验证动作行为 ---
        logger.info("\n--- 3. 验证动作行为 (aos_stat_action_behavior_hourly) ---")
        actual_action_stats = stats_data['action_behavior']
        # 期望值计算
        expected_actions = defaultdict(int)
        for msg in source_messages:
             if msg.action_data:
                action_data = msg.action_data
                if isinstance(action_data, str):
                    import json
                    try: action_data = json.loads(action_data)
                    except: continue
                if isinstance(action_data, dict):
                    name = action_data.get('name') or action_data.get('type')
                    if name: expected_actions[name] += 1
        
        self._compare("动作类型总数", len(expected_actions), len(actual_action_stats))
        for action_stat in actual_action_stats:
            self._compare(f"动作[{action_stat.action_name}]计数", expected_actions.get(action_stat.action_name, 0), action_stat.action_count)
            
        # --- 验证用户问题 ---
        logger.info("\n--- 4. 验证用户问题 (aos_stat_user_question_topn_hourly) ---")
        actual_question_stats = stats_data['user_question']
        
        expected_questions = defaultdict(int)
        for msg in source_messages:
            if msg.role == 'user' and msg.content:
                expected_questions[msg.content.strip()] += 1

        self._compare("独立问题种类数", len(expected_questions), len(actual_question_stats))

        for q_stat in actual_question_stats:
            self._compare(f"问题[{q_stat.question_content[:20]}...]计数", expected_questions.get(q_stat.question_content, 0), q_stat.question_count)
            
        # --- 总结 ---
        logger.info("\n" + "="*50)
        if not self.mismatches:
            success_message = "🎉🎉🎉 恭喜！所有指标验证通过！🎉🎉🎉"
            if RICH_AVAILABLE:
                console = Console()
                console.print(success_message, style="bold green")
            else:
                logger.info(success_message)
        else:
            logger.error(f"发现 {len(self.mismatches)} 个不匹配的指标: {self.mismatches}")
        logger.info("="*50 + "\n")

    def generate_validation_sql(self):
        """生成用于手动验证的SQL查询语句"""
        logger.info("正在生成验证 SQL 查询...")
        
        # --- 头部信息 ---
        header = f"""-- =====================================================================
-- Validation Queries For:
--   Enterprise: {self.enterprise_id}
--   Device:     {self.device_id}
--   Hour:       {self.target_hour.strftime('%Y-%m-%d %H:%M:%S')}
-- =====================================================================

"""
        
        sql_queries = [header]
        
        base_where_clause = f"""
WHERE hour_bucket = '{self.target_hour.strftime('%Y-%m-%d %H:%M:%S')}'
  AND enterprise_id = '{self.enterprise_id}'
  AND device_id = '{self.device_id}'
"""
        source_where_clause = f"""
WHERE session_start_time >= '{self.start_time.strftime('%Y-%m-%d %H:%M:%S')}'
  AND session_start_time < '{self.end_time.strftime('%Y-%m-%d %H:%M:%S')}'
  AND enterprise_id = '{self.enterprise_id}'
  AND device_id = '{self.device_id}'
"""
        
        # --- 会话行为 ---
        sql_queries.append("-- 1. 会话行为 (aos_stat_session_behavior_hourly)")
        sql_queries.append(f"SELECT session_total, valid_sessions, no_response_sessions, avg_conversation_turns, avg_session_duration FROM speech_ai_robot.aos_stat_session_behavior_hourly{base_where_clause};")
        sql_queries.append(f"SELECT COUNT(*), AVG(conversation_turns), AVG(session_duration_seconds) FROM agentos_online.aos_conversation_sessions{source_where_clause};")
        sql_queries.append("\n")

        # --- 消息行为 ---
        sql_queries.append("-- 2. 消息行为 (aos_stat_message_behavior_hourly)")
        sql_queries.append(f"SELECT user_msg_count, assistant_msg_count, action_trigger_count, event_trigger_count FROM speech_ai_robot.aos_stat_message_behavior_hourly{base_where_clause};")
        sql_queries.append(f"""
SELECT 
    (SELECT COUNT(*) FROM agentos_online.aos_conversation_messages WHERE session_id IN (SELECT session_id FROM agentos_online.aos_conversation_sessions{source_where_clause}) AND role = 'user') AS user_msg,
    (SELECT COUNT(*) FROM agentos_online.aos_conversation_messages WHERE session_id IN (SELECT session_id FROM agentos_online.aos_conversation_sessions{source_where_clause}) AND role = 'assistant') AS assistant_msg;
""")
        sql_queries.append("\n")

        # --- 动作行为 ---
        sql_queries.append("-- 3. 动作行为 (aos_stat_action_behavior_hourly)")
        sql_queries.append(f"SELECT action_name, action_count FROM speech_ai_robot.aos_stat_action_behavior_hourly{base_where_clause} ORDER BY action_count DESC;")
        sql_queries.append(f"""
SELECT 
    JSON_UNQUOTE(JSON_EXTRACT(action_data, '$.name')) as action_name, 
    COUNT(*) 
FROM agentos_online.aos_conversation_messages 
WHERE session_id IN (SELECT session_id FROM agentos_online.aos_conversation_sessions{source_where_clause}) 
  AND action_data IS NOT NULL
GROUP BY action_name 
ORDER BY COUNT(*) DESC;
""")

        # --- 文件名生成 ---
        time_str = self.target_hour.strftime('%Y-%m-%d_%H-%M-%S')
        safe_enterprise_id = self.enterprise_id.replace('.', '_')
        safe_device_id = self.device_id.replace('.', '_')
        
        # 为了避免文件名过长，可以只取部分ID
        short_device_id = safe_device_id.split('_')[0]
        
        file_path = f"./app/tests/tasks/validation_{safe_enterprise_id}_{short_device_id}_{time_str}.sql"

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("\n".join(sql_queries))
        
        logger.info(f"✅ 验证 SQL 已保存到当前目录下的 `{file_path}` 文件中。")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='机器人对话统计数据验证脚本',
        formatter_class=argparse.RawTextHelpFormatter,
        epilog="""
示例:
  python app/tasks/validation.py --enterprise orion.ovs.entprise.4498860269 --device MC1BCNC016021348B693 "2025-07-04 19:00:00"
"""
    )
    parser.add_argument('--enterprise', required=True, help='企业ID')
    parser.add_argument('--device', required=True, help='设备ID')
    parser.add_argument('time', help='要验证的小时 (YYYY-MM-DD HH:MM:SS)')

    args = parser.parse_args()
    
    try:
        target_hour = datetime.strptime(args.time, "%Y-%m-%d %H:%M:%S").replace(minute=0, second=0, microsecond=0)
    except ValueError:
        logger.error("时间格式错误，请使用 'YYYY-MM-DD HH:MM:SS'")
        sys.exit(1)

    logger.info(f"开始验证数据 - 时间: {target_hour}, 企业: {args.enterprise}, 设备: {args.device}")
    
    validator = DataValidator(target_hour, args.enterprise, args.device)
    validator.run_validation()
    validator.generate_validation_sql()

if __name__ == "__main__":
    main() 