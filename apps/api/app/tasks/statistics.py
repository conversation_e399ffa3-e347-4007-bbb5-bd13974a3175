from celery import shared_task
from datetime import datetime, timedelta
from app.services.statistics_service import StatisticsService
import logging

logger = logging.getLogger(__name__)

@shared_task(name="tasks.calculate_conversation_statistics")
def task_calculate_conversation_statistics():
    """
    Celery task to calculate conversation statistics for the previous 2 hours.
    Runs at minute 45 of every hour, calculating data for the previous 2 hours.
    """
    logger.info("开始执行机器人对话统计Celery任务...")
    
    try:
        # 获取当前时间
        now = datetime.now()
        
        # 计算目标小时（前2个小时）
        # 例如：当前是12:45，计算10点和11点的数据
        target_hours = []
        for i in range(2):
            target_hour = now.replace(minute=0, second=0, microsecond=0) - timedelta(hours=i+1)
            target_hours.append(target_hour)
        
        logger.info(f"准备计算以下小时的统计数据: {[h.strftime('%Y-%m-%d %H:00') for h in target_hours]}")
        
        # 创建统计服务实例
        stats_service = StatisticsService()
        
        # 计算统计数据
        stats_service.calculate_statistics_for_hours(target_hours)
        
        logger.info("机器人对话统计Celery任务执行成功。")
        
    except Exception as e:
        logger.error(f"机器人对话统计Celery任务执行失败: {e}", exc_info=True)
        raise 