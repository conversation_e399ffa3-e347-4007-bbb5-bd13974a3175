# 企业数据导出功能

## 功能概述

企业数据导出功能是一个强大的工具，用于将指定企业的对话数据按天导出到本地文件系统，支持大模型进行基础数据分析。

## 主要特性

### 1. Chat概念实现
- **Session合并**: 将间隔小于30秒（可配置）的session合并为一个chat
- **智能分组**: 基于时间间隔自动识别连续的对话会话
- **交互轮数统计**: 准确计算用户和机器人的对话轮次

### 2. 数据格式规范
- **文件路径**: `企业ID/设备ID/YYYY-MM-DD.txt`
- **数据内容**: 包含完整的chat信息、会话元数据、消息详情
- **索引文件**: 自动生成导出索引，便于数据管理

### 3. 配置灵活性
- **多种配置模板**: 基础、详细、性能优化、最小化
- **自定义配置**: 支持通过配置文件或命令行参数自定义
- **数据过滤**: 可控制包含的数据类型和详细程度

### 4. 输出质量
- **数据完整性**: 保留所有关键信息
- **格式标准化**: JSON格式，便于程序处理
- **文件头信息**: 包含导出元数据，便于数据追踪

## 文件结构

```
apps/api/app/tasks/
├── enterprise_data_export.py      # 主要导出功能
├── export_config.py               # 配置管理
├── export_usage_examples.py       # 使用示例
├── test_enterprise_export.py      # 测试文件
└── README_企业数据导出.md         # 本文档
```

## 核心组件

### EnterpriseDataExporter
主要的导出器类，负责：
- 从数据库查询会话和消息数据
- 将sessions合并为chats
- 格式化数据并保存到文件
- 生成统计信息和索引文件

### ExportConfig
配置管理类，支持：
- 默认配置和自定义配置
- 配置模板（基础、详细、性能、最小化）
- 配置文件保存和加载
- 运行时配置修改

## 使用方法

### 1. 命令行使用

#### 基础导出
```bash
python enterprise_data_export.py \
  --enterprise orion.ovs.entprise.4498860269 \
  --device MC1BCNC016021348B693 \
  --start_date 2025-01-01 \
  --end_date 2025-01-31 \
  --output_dir /path/to/output
```

#### 使用配置模板
```bash
python enterprise_data_export.py \
  --enterprise orion.ovs.entprise.4498860269 \
  --device MC1BCNC016021348B693 \
  --start_date 2025-01-01 \
  --end_date 2025-01-31 \
  --output_dir /path/to/output \
  --config_template detailed \
  --merge_interval 60
```

#### 使用配置文件
```bash
python enterprise_data_export.py \
  --enterprise orion.ovs.entprise.4498860269 \
  --device MC1BCNC016021348B693 \
  --start_date 2025-01-01 \
  --end_date 2025-01-31 \
  --output_dir /path/to/output \
  --config_file /path/to/config.json
```

### 2. 编程使用

#### 基础使用
```python
from enterprise_data_export import EnterpriseDataExporter
from datetime import datetime

exporter = EnterpriseDataExporter()
stats = exporter.export_data_by_date_range(
    enterprise_id="orion.ovs.entprise.4498860269",
    device_id="MC1BCNC016021348B693",
    start_date=datetime(2025, 1, 1),
    end_date=datetime(2025, 1, 31),
    output_dir="/path/to/output"
)
```

#### 自定义配置
```python
from enterprise_data_export import EnterpriseDataExporter
from export_config import ExportConfig

# 创建自定义配置
config = ExportConfig({
    'merge_interval_seconds': 60,
    'include_sensitive_data': False,
    'include_statistics': True,
    'include_user_preferences': True,
    'include_action_event_data': True,
    'include_detailed_messages': True,
    'max_message_length': 2000
})

exporter = EnterpriseDataExporter(config=config)
stats = exporter.export_data_by_date_range(
    enterprise_id="orion.ovs.entprise.4498860269",
    device_id="MC1BCNC016021348B693",
    start_date=datetime(2025, 1, 1),
    end_date=datetime(2025, 1, 31),
    output_dir="/path/to/output"
)
```

#### 使用配置模板
```python
from export_config import ExportConfigTemplates

# 使用详细配置模板
config = ExportConfigTemplates.get_detailed_config()
exporter = EnterpriseDataExporter(config=config)

# 使用性能优化模板
config = ExportConfigTemplates.get_performance_config()
exporter = EnterpriseDataExporter(config=config)
```

## 配置选项

### 核心配置
- `merge_interval_seconds`: session合并时间间隔（秒）
- `include_sensitive_data`: 是否包含敏感数据
- `include_statistics`: 是否包含统计信息
- `include_user_preferences`: 是否包含用户偏好信息

### 数据内容配置
- `include_action_event_data`: 是否包含动作和事件数据
- `include_detailed_messages`: 是否包含详细消息内容
- `include_session_metadata`: 是否包含会话元数据
- `include_message_metadata`: 是否包含消息元数据
- `max_message_length`: 消息内容最大长度

### 输出配置
- `output_format`: 输出格式（txt, json, csv）
- `output_encoding`: 输出文件编码
- `compress_output`: 是否压缩输出文件
- `create_index_file`: 是否创建索引文件
- `add_file_header`: 是否添加文件头信息

### 性能配置
- `batch_size`: 数据库查询批次大小
- `max_concurrent_processes`: 最大并发处理数
- `use_bulk_queries`: 是否使用批量查询
- `cache_session_data`: 是否缓存会话数据

## 配置模板

### 基础配置 (basic)
- 包含基本统计信息
- 不包含敏感数据
- 适合一般使用场景

### 详细配置 (detailed)
- 包含所有数据类型
- 包含敏感数据
- 适合深度分析

### 性能配置 (performance)
- 优化查询性能
- 批量处理
- 适合大数据量导出

### 最小配置 (minimal)
- 只包含核心数据
- 最小化输出
- 适合快速预览

## 输出文件结构

### 文件路径
```
output_dir/
├── enterprise_id/
│   └── device_id/
│       ├── 2025-01-01.txt
│       ├── 2025-01-02.txt
│       ├── ...
│       └── export_index.json
└── export_stats.json
```

### 数据格式
每个chat包含以下信息：
```json
{
  "chat_id": "chat_session_id",
  "enterprise_id": "enterprise_id",
  "device_id": "device_id",
  "chat_start_time": "2025-01-01T10:00:00",
  "chat_end_time": "2025-01-01T10:05:00",
  "chat_duration_seconds": 300,
  "session_count": 2,
  "total_turns": 5,
  "interaction_rounds": 3,
  "user_message_count": 3,
  "assistant_message_count": 3,
  "action_count": 1,
  "event_count": 0,
  "user_preferences": {...},
  "sessions": [...],
  "messages": [...]
}
```

## 数据质量保证

### 数据完整性检查
- 时间戳顺序验证
- 消息序列检查
- 会话完整性验证
- 数据完整性检查

### 错误处理
- 数据库连接异常处理
- 文件写入错误处理
- 数据格式错误处理
- 配置错误处理

### 性能优化
- 批量数据库查询
- 内存使用优化
- 并发处理支持
- 缓存机制

## 测试

### 运行测试
```bash
python test_enterprise_export.py
```

### 测试覆盖
- 配置管理测试
- 数据导出测试
- 集成测试
- 错误处理测试

## 使用示例

### 示例1: 基础导出
```python
from enterprise_data_export import EnterpriseDataExporter
from datetime import datetime

exporter = EnterpriseDataExporter()
stats = exporter.export_data_by_date_range(
    enterprise_id="orion.ovs.entprise.4498860269",
    device_id="MC1BCNC016021348B693",
    start_date=datetime(2025, 1, 1),
    end_date=datetime(2025, 1, 31),
    output_dir="/tmp/export_basic"
)
print(f"导出完成: {stats}")
```

### 示例2: 批量导出
```python
from enterprise_data_export import EnterpriseDataExporter
from export_config import ExportConfigTemplates
from datetime import datetime

# 定义导出任务
export_tasks = [
    {
        'enterprise_id': 'orion.ovs.entprise.4498860269',
        'device_id': 'MC1BCNC016021348B693',
        'start_date': datetime(2025, 1, 1),
        'end_date': datetime(2025, 1, 31),
        'output_dir': '/tmp/export_batch/device1'
    },
    {
        'enterprise_id': 'orion.ovs.entprise.4498860269',
        'device_id': 'MC1BCNC016021348B694',
        'start_date': datetime(2025, 1, 1),
        'end_date': datetime(2025, 1, 31),
        'output_dir': '/tmp/export_batch/device2'
    }
]

# 使用性能优化配置
config = ExportConfigTemplates.get_performance_config()
exporter = EnterpriseDataExporter(config=config)

# 执行批量导出
for task in export_tasks:
    stats = exporter.export_data_by_date_range(**task)
    print(f"设备 {task['device_id']} 导出完成: {stats}")
```

### 示例3: 自定义配置导出
```python
from enterprise_data_export import EnterpriseDataExporter
from export_config import ExportConfig
from datetime import datetime

# 创建自定义配置
custom_config = ExportConfig({
    'merge_interval_seconds': 60,  # 60秒合并间隔
    'include_sensitive_data': False,
    'include_statistics': True,
    'include_user_preferences': False,
    'include_action_event_data': True,
    'include_detailed_messages': True,
    'include_session_metadata': True,
    'include_message_metadata': False,
    'max_message_length': 500,
    'show_progress': True,
    'create_index_file': True,
    'validate_output': True
})

exporter = EnterpriseDataExporter(config=custom_config)
stats = exporter.export_data_by_date_range(
    enterprise_id="orion.ovs.entprise.4498860269",
    device_id="MC1BCNC016021348B693",
    start_date=datetime(2025, 1, 1),
    end_date=datetime(2025, 1, 31),
    output_dir="/tmp/export_custom"
)
print(f"自定义配置导出完成: {stats}")
```

## 注意事项

### 性能考虑
- 大数据量导出时建议使用性能配置模板
- 合理设置批次大小和并发数
- 监控内存使用情况

### 数据安全
- 敏感数据默认不包含，需要时可通过配置启用
- 输出文件权限设置为644
- 建议在安全环境中使用

### 错误处理
- 程序会自动处理常见的数据库和文件错误
- 错误信息会记录到日志中
- 建议在生产环境中添加监控和告警

### 维护建议
- 定期清理临时文件
- 监控磁盘空间使用
- 备份重要的导出配置

## 更新日志

### v1.0.0 (2025-01-01)
- 初始版本发布
- 实现基础的session合并功能
- 支持多种配置选项
- 提供完整的测试覆盖

## 技术支持

如有问题或建议，请联系开发团队。 