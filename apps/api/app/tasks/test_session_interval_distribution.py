#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试会话间隔分布统计表字段更新
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from datetime import datetime, timedelta
from apps.api.app.models.statistics import AosStatSessionIntervalDistributionHourly
from apps.api.app.database import get_speech_ai_robot_db
from apps.api.app.services.statistics_service import StatisticsService
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_session_interval_distribution_model():
    """测试会话间隔分布模型字段"""
    logger.info("=== 测试会话间隔分布模型字段 ===")
    
    # 创建模型实例
    interval_dist = AosStatSessionIntervalDistributionHourly(
        hour_bucket=datetime.now().replace(minute=0, second=0, microsecond=0),
        enterprise_id="test_enterprise",
        device_id="test_device",
        bucket_lt_10s=5,
        bucket_10_20s=10,
        bucket_1_3min=15,
        bucket_3_5min=8,
        bucket_5_10min=12,
        bucket_10_20min=6,
        bucket_gt_20min=3,
        session_ids_interval_bucket={
            "bucket_lt_10s": ["session_1", "session_2"],
            "bucket_10_20s": ["session_3", "session_4"],
            "bucket_1_3min": ["session_5", "session_6"]
        }
    )
    
    logger.info("模型实例创建成功")
    logger.info(f"字段 bucket_lt_10s: {interval_dist.bucket_lt_10s}")
    logger.info(f"字段 bucket_10_20s: {interval_dist.bucket_10_20s}")
    logger.info(f"字段 bucket_1_3min: {interval_dist.bucket_1_3min}")
    logger.info(f"字段 bucket_3_5min: {interval_dist.bucket_3_5min}")
    logger.info(f"字段 bucket_5_10min: {interval_dist.bucket_5_10min}")
    logger.info(f"字段 bucket_10_20min: {interval_dist.bucket_10_20min}")
    logger.info(f"字段 bucket_gt_20min: {interval_dist.bucket_gt_20min}")
    
    return True

def test_session_interval_distribution_save():
    """测试会话间隔分布数据保存"""
    logger.info("=== 测试会话间隔分布数据保存 ===")
    
    try:
        with next(get_speech_ai_robot_db()) as db:
            # 创建测试数据
            test_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
            interval_dist = AosStatSessionIntervalDistributionHourly(
                hour_bucket=test_hour,
                enterprise_id="test_enterprise_interval",
                device_id="test_device_interval",
                bucket_lt_10s=3,
                bucket_10_20s=7,
                bucket_1_3min=12,
                bucket_3_5min=5,
                bucket_5_10min=9,
                bucket_10_20min=4,
                bucket_gt_20min=2,
                session_ids_interval_bucket={
                    "bucket_lt_10s": ["session_001", "session_002", "session_003"],
                    "bucket_10_20s": ["session_004", "session_005", "session_006", "session_007"],
                    "bucket_1_3min": ["session_008", "session_009", "session_010", "session_011", "session_012"]
                }
            )
            
            # 保存到数据库
            db.add(interval_dist)
            db.commit()
            logger.info("会话间隔分布数据保存成功")
            
            # 查询验证
            saved_data = db.query(AosStatSessionIntervalDistributionHourly).filter(
                AosStatSessionIntervalDistributionHourly.hour_bucket == test_hour,
                AosStatSessionIntervalDistributionHourly.enterprise_id == "test_enterprise_interval",
                AosStatSessionIntervalDistributionHourly.device_id == "test_device_interval"
            ).first()
            
            if saved_data:
                logger.info("查询验证成功")
                logger.info(f"保存的数据: bucket_lt_10s={saved_data.bucket_lt_10s}")
                logger.info(f"保存的数据: bucket_10_20s={saved_data.bucket_10_20s}")
                logger.info(f"保存的数据: bucket_1_3min={saved_data.bucket_1_3min}")
                logger.info(f"保存的数据: bucket_3_5min={saved_data.bucket_3_5min}")
                logger.info(f"保存的数据: bucket_5_10min={saved_data.bucket_5_10min}")
                logger.info(f"保存的数据: bucket_10_20min={saved_data.bucket_10_20min}")
                logger.info(f"保存的数据: bucket_gt_20min={saved_data.bucket_gt_20min}")
                
                # 清理测试数据
                db.delete(saved_data)
                db.commit()
                logger.info("测试数据清理完成")
                
                return True
            else:
                logger.error("查询验证失败")
                return False
                
    except Exception as e:
        logger.error(f"测试会话间隔分布数据保存失败: {e}", exc_info=True)
        return False

def test_statistics_service_interval():
    """测试统计服务中的会话间隔分布计算"""
    logger.info("=== 测试统计服务中的会话间隔分布计算 ===")
    
    try:
        service = StatisticsService()
        
        # 测试特定时间
        test_hour = datetime.now().replace(minute=0, second=0, microsecond=0) - timedelta(hours=1)
        
        # 计算统计数据
        service.calculate_statistics_for_hour(test_hour)
        logger.info("统计服务会话间隔分布计算完成")
        
        return True
        
    except Exception as e:
        logger.error(f"测试统计服务会话间隔分布计算失败: {e}", exc_info=True)
        return False

def main():
    """主测试函数"""
    logger.info("开始测试会话间隔分布统计表字段更新")
    
    # 测试1: 模型字段
    test1_result = test_session_interval_distribution_model()
    
    # 测试2: 数据保存
    test2_result = test_session_interval_distribution_save()
    
    # 测试3: 统计服务
    test3_result = test_statistics_service_interval()
    
    # 输出测试结果
    logger.info("=== 测试结果汇总 ===")
    logger.info(f"模型字段测试: {'通过' if test1_result else '失败'}")
    logger.info(f"数据保存测试: {'通过' if test2_result else '失败'}")
    logger.info(f"统计服务测试: {'通过' if test3_result else '失败'}")
    
    if all([test1_result, test2_result, test3_result]):
        logger.info("所有测试通过！会话间隔分布统计表字段更新成功")
    else:
        logger.error("部分测试失败，请检查代码")

if __name__ == "__main__":
    main() 