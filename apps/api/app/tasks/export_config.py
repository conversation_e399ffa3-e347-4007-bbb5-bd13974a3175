#!/usr/bin/env python3
"""
企业数据导出配置文件
"""

import os
from typing import Dict, Any

class ExportConfig:
    """导出功能配置类"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        # session合并时间间隔（秒）
        'merge_interval_seconds': 30,
        
        # 输出文件格式
        'output_format': 'txt',  # txt, json, csv
        
        # 是否包含敏感信息
        'include_sensitive_data': False,
        
        # 消息内容最大长度（字符）
        'max_message_length': 1000,
        
        # 是否压缩输出文件
        'compress_output': False,
        
        # 输出文件编码
        'output_encoding': 'utf-8',
        
        # 是否包含统计信息
        'include_statistics': True,
        
        # 是否包含用户偏好信息
        'include_user_preferences': True,
        
        # 是否包含动作和事件数据
        'include_action_event_data': True,
        
        # 输出文件分隔符
        'file_separator': '=' * 80,
        
        # 日志级别
        'log_level': 'INFO',
        
        # 数据库查询批次大小
        'batch_size': 1000,
        
        # 是否启用进度显示
        'show_progress': True,
        
        # 输出文件命名规则
        'filename_pattern': '{enterprise_id}/{device_id}/{date}.txt',
        
        # 是否创建索引文件
        'create_index_file': True,
        
        # 索引文件名称
        'index_filename': 'export_index.json',
        
        # 是否验证输出文件
        'validate_output': True,
        
        # 最大并发处理数
        'max_concurrent_processes': 1,
        
        # 临时文件目录
        'temp_dir': '/tmp/enterprise_export',
        
        # 是否保留临时文件
        'keep_temp_files': False,
        
        # 输出文件权限
        'file_permissions': 0o644,
        
        # 是否添加文件头信息
        'add_file_header': True,
        
        # 文件头信息模板
        'file_header_template': """# 企业数据导出文件
# 企业ID: {enterprise_id}
# 设备ID: {device_id}
# 导出日期: {export_date}
# 数据日期: {data_date}
# 合并间隔: {merge_interval}秒
# 导出时间: {export_timestamp}
# 数据条数: {chat_count}
# 会话数: {session_count}
# 消息数: {message_count}
# 分隔符: {separator}
""",
        
        # 是否包含数据摘要
        'include_data_summary': True,
        
        # 数据摘要字段
        'summary_fields': [
            'chat_id',
            'chat_start_time',
            'chat_end_time',
            'interaction_rounds',
            'user_message_count',
            'assistant_message_count',
            'action_count',
            'event_count'
        ],
        
        # 是否包含详细消息内容
        'include_detailed_messages': True,
        
        # 消息内容过滤规则
        'message_content_filters': {
            'min_length': 1,
            'max_length': 10000,
            'exclude_patterns': [],
            'include_patterns': []
        },
        
        # 是否包含会话元数据
        'include_session_metadata': True,
        
        # 会话元数据字段
        'session_metadata_fields': [
            'session_id',
            'project_id',
            'app_id',
            'group_id',
            'client_id',
            'product_id',
            'product_model',
            'timezone'
        ],
        
        # 是否包含消息元数据
        'include_message_metadata': True,
        
        # 消息元数据字段
        'message_metadata_fields': [
            'message_index',
            'role',
            'message_timestamp',
            'app_id',
            'page_id',
            'agent_id',
            'face_id'
        ],
        
        # 数据质量检查
        'data_quality_checks': {
            'check_timestamp_order': True,
            'check_message_sequence': True,
            'check_session_integrity': True,
            'check_data_completeness': True
        },
        
        # 错误处理策略
        'error_handling': {
            'continue_on_error': True,
            'log_errors': True,
            'save_error_log': True,
            'max_retries': 3
        },
        
        # 性能优化选项
        'performance_options': {
            'use_bulk_queries': True,
            'cache_session_data': True,
            'cache_message_data': True,
            'optimize_memory_usage': True
        }
    }
    
    def __init__(self, config_overrides: Dict[str, Any] = None):
        """
        初始化配置
        
        Args:
            config_overrides: 配置覆盖项
        """
        self.config = self.DEFAULT_CONFIG.copy()
        if config_overrides:
            self.config.update(config_overrides)
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        self.config[key] = value
    
    def update(self, config_dict: Dict[str, Any]):
        """更新配置"""
        self.config.update(config_dict)
    
    def get_merge_interval(self) -> int:
        """获取session合并时间间隔"""
        return self.config['merge_interval_seconds']
    
    def get_output_format(self) -> str:
        """获取输出格式"""
        return self.config['output_format']
    
    def should_include_sensitive_data(self) -> bool:
        """是否包含敏感数据"""
        return self.config['include_sensitive_data']
    
    def get_max_message_length(self) -> int:
        """获取消息最大长度"""
        return self.config['max_message_length']
    
    def should_compress_output(self) -> bool:
        """是否压缩输出"""
        return self.config['compress_output']
    
    def get_output_encoding(self) -> str:
        """获取输出编码"""
        return self.config['output_encoding']
    
    def should_include_statistics(self) -> bool:
        """是否包含统计信息"""
        return self.config['include_statistics']
    
    def should_include_user_preferences(self) -> bool:
        """是否包含用户偏好信息"""
        return self.config['include_user_preferences']
    
    def should_include_action_event_data(self) -> bool:
        """是否包含动作和事件数据"""
        return self.config['include_action_event_data']
    
    def get_file_separator(self) -> str:
        """获取文件分隔符"""
        return self.config['file_separator']
    
    def get_log_level(self) -> str:
        """获取日志级别"""
        return self.config['log_level']
    
    def get_batch_size(self) -> int:
        """获取批次大小"""
        return self.config['batch_size']
    
    def should_show_progress(self) -> bool:
        """是否显示进度"""
        return self.config['show_progress']
    
    def get_filename_pattern(self) -> str:
        """获取文件名模式"""
        return self.config['filename_pattern']
    
    def should_create_index_file(self) -> bool:
        """是否创建索引文件"""
        return self.config['create_index_file']
    
    def get_index_filename(self) -> str:
        """获取索引文件名"""
        return self.config['index_filename']
    
    def should_validate_output(self) -> bool:
        """是否验证输出"""
        return self.config['validate_output']
    
    def get_max_concurrent_processes(self) -> int:
        """获取最大并发处理数"""
        return self.config['max_concurrent_processes']
    
    def get_temp_dir(self) -> str:
        """获取临时文件目录"""
        return self.config['temp_dir']
    
    def should_keep_temp_files(self) -> bool:
        """是否保留临时文件"""
        return self.config['keep_temp_files']
    
    def get_file_permissions(self) -> int:
        """获取文件权限"""
        return self.config['file_permissions']
    
    def should_add_file_header(self) -> bool:
        """是否添加文件头信息"""
        return self.config['add_file_header']
    
    def get_file_header_template(self) -> str:
        """获取文件头模板"""
        return self.config['file_header_template']
    
    def should_include_data_summary(self) -> bool:
        """是否包含数据摘要"""
        return self.config['include_data_summary']
    
    def get_summary_fields(self) -> list:
        """获取摘要字段"""
        return self.config['summary_fields']
    
    def should_include_detailed_messages(self) -> bool:
        """是否包含详细消息内容"""
        return self.config['include_detailed_messages']
    
    def get_message_content_filters(self) -> dict:
        """获取消息内容过滤规则"""
        return self.config['message_content_filters']
    
    def should_include_session_metadata(self) -> bool:
        """是否包含会话元数据"""
        return self.config['include_session_metadata']
    
    def get_session_metadata_fields(self) -> list:
        """获取会话元数据字段"""
        return self.config['session_metadata_fields']
    
    def should_include_message_metadata(self) -> bool:
        """是否包含消息元数据"""
        return self.config['include_message_metadata']
    
    def get_message_metadata_fields(self) -> list:
        """获取消息元数据字段"""
        return self.config['message_metadata_fields']
    
    def get_data_quality_checks(self) -> dict:
        """获取数据质量检查配置"""
        return self.config['data_quality_checks']
    
    def get_error_handling(self) -> dict:
        """获取错误处理配置"""
        return self.config['error_handling']
    
    def get_performance_options(self) -> dict:
        """获取性能优化配置"""
        return self.config['performance_options']
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.config.copy()
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'ExportConfig':
        """从字典创建配置对象"""
        return cls(config_dict)
    
    @classmethod
    def from_file(cls, config_file: str) -> 'ExportConfig':
        """从文件加载配置"""
        import json
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            return cls(config_dict)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return cls()
    
    def save_to_file(self, config_file: str):
        """保存配置到文件"""
        import json
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")


# 预定义配置模板
class ExportConfigTemplates:
    """导出配置模板"""
    
    @staticmethod
    def get_basic_config() -> ExportConfig:
        """基础配置"""
        return ExportConfig({
            'merge_interval_seconds': 30,
            'include_sensitive_data': False,
            'include_statistics': True,
            'show_progress': True
        })
    
    @staticmethod
    def get_detailed_config() -> ExportConfig:
        """详细配置"""
        return ExportConfig({
            'merge_interval_seconds': 30,
            'include_sensitive_data': True,
            'include_statistics': True,
            'include_user_preferences': True,
            'include_action_event_data': True,
            'include_detailed_messages': True,
            'include_session_metadata': True,
            'include_message_metadata': True,
            'show_progress': True,
            'validate_output': True
        })
    
    @staticmethod
    def get_performance_config() -> ExportConfig:
        """性能优化配置"""
        return ExportConfig({
            'merge_interval_seconds': 30,
            'batch_size': 2000,
            'max_concurrent_processes': 4,
            'use_bulk_queries': True,
            'cache_session_data': True,
            'cache_message_data': True,
            'optimize_memory_usage': True,
            'show_progress': False
        })
    
    @staticmethod
    def get_minimal_config() -> ExportConfig:
        """最小配置"""
        return ExportConfig({
            'merge_interval_seconds': 30,
            'include_sensitive_data': False,
            'include_statistics': False,
            'include_user_preferences': False,
            'include_action_event_data': False,
            'include_detailed_messages': False,
            'include_session_metadata': False,
            'include_message_metadata': False,
            'show_progress': False,
            'validate_output': False
        }) 