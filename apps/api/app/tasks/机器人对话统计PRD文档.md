
# 📄 产品需求文档（PRD）
## 项目名称：机器人对话小时级行为统计系统  
**版本**：V1.0  
**编写人**：产品经理  
**发布日期**：2025-07-03  

---

## 一、背景与目标

随着各业务线部署机器人逐渐增多，需要构建一套**小时级别的对话行为统计系统**，用于持续监控：

- 每台机器人的使用活跃情况；
- 会话的质量和交互效果；
- 用户提出的主要问题与偏好；
- 场景交互中存在的异常和重复行为；
- 各业务项目的使用频率和执行质量。

该系统以企业（`enterprise_id`）+设备（`device_id`）为主维度，按小时统计所有会话数据，并通过关键指标输出给可视化平台展示与联动分析。

---

## 二、统计维度说明

### 主统计维度：

| 维度 | 描述 |
|------|------|
| `enterprise_id` | 企业标识 |
| `device_id` | 设备标识 |
| `hour_bucket` | 当前统计小时（整点） |
| `project_id` | 项目标识，区分业务场景与通用场景（为空为通用） |

> 所有维度数据按每小时独立汇总。每条记录需包含所有该小时的 `session_id` 列表，用于图表联动。

---

## 三、统计指标汇总

> 下述所有指标均为每小时、每设备维度聚合。

### 1️⃣ 会话行为统计

| 指标名称 | 数据来源 | 汇总逻辑 |
|----------|----------|----------|
| 会话总数 | `aos_conversation_sessions` | 当前小时内所有 `session_start_time` 落在此小时内的 session 数量 |
| 有效会话数 | sessions + messages | 用户和机器人都至少说了一句话的 session 数量 |
| 无响应会话数 | messages | 用户说话但无任何 assistant 回复的 session 数量 |
| 首消息为 assistant 的会话数 | messages | 每个 session 中第一条消息 role 为 assistant 的数量 |
| 多次打招呼会话数 | messages | assistant 在同一个 session 中多次输出“你好”“欢迎”等固定打招呼词的会话数（>1 次） |
| assistant 重复输出内容会话数 | messages | assistant 在 session 中输出完全相同内容 ≥2 次的会话数量 |
| 平均对话轮数 | sessions | `AVG(conversation_turns)` |
| 平均会话时长 | sessions | `AVG(session_duration_seconds)` |

### 2️⃣ 消息行为统计

| 指标名称 | 数据来源 | 汇总逻辑 |
|----------|----------|----------|
| 用户消息总数 | messages | 当前小时所有 role='user' 的消息条数 |
| 机器人消息总数 | messages | 当前小时所有 role='assistant' 的消息条数 |
| 平均用户消息长度 | messages | `AVG(LENGTH(content))` where role='user' |
| 平均 assistant 消息长度 | messages | 同上，role='assistant' |
| 触发 action 次数 | messages | 当前小时内 action_data 字段非空的消息总数 |
| 触发事件次数 | messages | 当前小时内 event_data 字段非空的消息总数 |

### 3️⃣ 用户问题详情统计

| 指标名称 | 数据来源 | 汇总逻辑 |
|----------|----------|----------|
| 高频用户问题 | messages | 将用户消息归类并统计出现频次，按 Top10 输出（例：“怎么用”、“开灯”、“你好”） |
| 用户提问总类数 | messages | 当前小时 role='user' 且 message 去重后的条数 |
| 用户提问关键词（词云） | messages | 用户消息中抽取高频关键词，用于词云图展示（可选 NLP 分词） |

### 4️⃣ Action 使用统计

| 指标名称 | 数据来源 | 汇总逻辑 |
|----------|----------|----------|
| 当前小时触发 action 总数 | messages | 所有消息中 `action_data IS NOT NULL` 的计数 |
| 不同 action 类型及频次 | messages | 从 action_data 中提取 `type` 或 `name` 字段进行聚合计数 |
| 含有 action 的 session 数 | messages | 当前小时内至少出现过一次 action 的会话数量 |

### 5️⃣ 会话时长分布统计

| 指标名称 | 数据来源 | 汇总逻辑 |
|----------|----------|----------|
| 时长分布（6 个区间） | sessions | 按 session_duration_seconds 分为以下 6 个区间并计数：<30s、30-60s、1-3min、3-5min、5-10min、>10min |

### 6️⃣ 用户偏好信息统计（来自 user_preferences）

| 指标名称 | 数据来源 | 汇总逻辑 |
|----------|----------|----------|
| user_preferences 填写的会话数 | sessions | 当前小时 user_preferences 非空的 session 数量 |
| 偏好字段名及频次 | sessions | 提取 JSON 中字段名并按字段名聚合计数（例：“购买预算”：4，“使用场景”：3） |
| 常见偏好关键词 | sessions | 提取偏好值中的关键词，构建词云原料，如“养老”、“工厂”、“省一块钱” |

### 7️⃣ 场景（项目）维度指标

| 指标名称 | 数据来源 | 汇总逻辑 |
|----------|----------|----------|
| 当前是否为项目场景 | sessions | 判断 project_id 是否为空 |
| 当前活跃场景名 | sessions | 取当前小时该设备下 project_id 的值，用于展示 |
| 场景会话数 | sessions | 当前小时内有 project_id 的 session 数量 |
| 场景下无回应会话数 | messages | 该场景下用户说话但无 assistant 回复的 session 数 |

### 8️⃣ 连续活跃分析（跨小时缓存）

| 指标名称 | 数据来源 | 汇总逻辑 |
|----------|----------|----------|
| 连续活跃小时数 | 内存缓存 / Redis | 当前设备连续多少小时内有会话记录 |
| greeting 会话占比 | messages | 首条消息为 assistant 的会话数 ÷ session 总数 |

---

## 四、数据源结构

### 会话主表：aos_conversation_sessions

```sql
CREATE TABLE `aos_conversation_sessions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `session_id` varchar(128) NOT NULL,
  `enterprise_id` varchar(64) NOT NULL,
  `device_id` varchar(128) NOT NULL,
  `group_id` varchar(64) DEFAULT '',
  `client_id` varchar(64) DEFAULT '',
  `project_id` varchar(64) DEFAULT '',
  `product_id` int DEFAULT '0',
  `product_model` varchar(64) DEFAULT '',
  `conversation_turns` int DEFAULT '0',
  `timezone` varchar(32) DEFAULT '',
  `session_start_time` datetime NOT NULL,
  `session_end_time` datetime NOT NULL,
  `session_duration_seconds` int DEFAULT '0',
  `user_preferences` json DEFAULT NULL,
  `app_id` varchar(64) DEFAULT '',
  `conversation_date` date NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`,`conversation_date`)
);
```

### 消息明细表：aos_conversation_messages

```sql
CREATE TABLE `aos_conversation_messages` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `session_id` varchar(128) NOT NULL,
  `message_index` int NOT NULL,
  `role` enum('user','assistant') NOT NULL,
  `content` varchar(1024) DEFAULT '',
  `action_data` json DEFAULT NULL,
  `event_data` json DEFAULT NULL,
  `app_id` varchar(64) DEFAULT '',
  `page_id` varchar(64) DEFAULT '',
  `agent_id` varchar(64) DEFAULT '',
  `face_id` varchar(64) DEFAULT '',
  `message_timestamp` datetime NOT NULL,
  `message_date` date NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`,`message_date`)
);
```

---

## 五、交付要求

- Python 脚本每小时运行一次，拉取并汇总上一小时数据
- 所有指标按维度输出，支持前端 drill-down（带 session_id）
- 后续扩展支持分钟级、天级汇总
