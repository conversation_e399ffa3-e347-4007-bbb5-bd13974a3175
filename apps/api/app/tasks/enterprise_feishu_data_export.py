# -*- coding: utf-8 -*-
"""
企业飞书多维表格数据导出工具

使用方式1 - 指定日期范围：
PYTHONPATH=. python app/tasks/enterprise_feishu_data_export.py \
  --enterprise_id orion.ovs.entprise.4498860269 \
  --device_id MC1BCNC016021348B693 \
  --start_date 2025-07-14 \
  --end_date 2025-07-14 \
  --output_dir /data/app/CEMonitor_api/apps/api/app/data/human_stats

使用方式2 - 导出最近几天的数据：
PYTHONPATH=. python app/tasks/enterprise_feishu_data_export.py \
  --enterprise_id orion.ovs.entprise.4498860269 \
  --device_id MC1BCNC016021348B693 \
  --days_back 7 \
  --output_dir /data/app/CEMonitor_api/apps/api/app/data/human_stats

参数说明：
- --days_back: 导出最近几天的数据（从今天往前推，包含今天）
- --start_date/--end_date: 指定具体的日期范围
- 两种模式不能同时使用

字体安装（如果需要生成图片）：
sudo apt-get install fonts-wqy-microhei
sudo apt-get install fonts-noto-cjk
"""
import argparse
import asyncio
import os
from datetime import datetime, timedelta
import pandas as pd
import matplotlib.pyplot as plt
from app.services.feishu_client import FeishuService
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'STSong']
matplotlib.rcParams['axes.unicode_minus'] = False
import matplotlib.pyplot as plt
import pytz

# ---------------------- 参数解析 ----------------------
def parse_args():
    parser = argparse.ArgumentParser(description="企业飞书多维表格数据导出工具")
    parser.add_argument('--enterprise_id', required=True, help='企业ID')
    parser.add_argument('--device_id', required=True, help='设备ID')
    parser.add_argument('--start_date', help='导出起始日期（YYYY-MM-DD）')
    parser.add_argument('--end_date', help='导出结束日期（YYYY-MM-DD）')
    parser.add_argument('--days_back', type=int, help='导出最近几天的数据（从今天往前推）')
    parser.add_argument('--output_dir', required=True, help='导出文件保存目录')
    
    # 验证参数组合
    args = parser.parse_args()
    
    # 检查参数组合
    if args.days_back is not None:
        # 使用 days_back 模式
        if args.start_date or args.end_date:
            parser.error("使用 --days_back 时不能同时使用 --start_date 或 --end_date")
    else:
        # 使用日期范围模式
        if not args.start_date or not args.end_date:
            parser.error("必须提供 --start_date 和 --end_date，或者使用 --days_back")
    
    return args

# ---------------------- 数据导出主逻辑 ----------------------
async def export_data(args):
    feishu_service = FeishuService()
    print("正在拉取飞书多维表格数据...")
    records = await feishu_service.get_all_records_by_scope(
        scope="human_stats",
        filter=f"CurrentValue.[设备ID]=\"{args.device_id}\""
    )
    if not records:
        print("未获取到任何数据！")
        return
    # 解析fields
    df = pd.DataFrame([r.fields for r in records if hasattr(r, 'fields')])
    print("==== 原始DataFrame前5行 ====")
    print(df.head(5))
    print("==== DataFrame所有字段 ====")
    print(df.columns)
    print("==== 企业ID唯一值 ====")
    print(df['企业ID'] if '企业ID' in df.columns else df['enterprise_id'])
    print("==== 设备ID唯一值 ====")
    print(df['设备ID'] if '设备ID' in df.columns else df['device_id'])
    print("==== 触发时间前5行 ====")
    print(df['触发时间'] if '触发时间' in df.columns else df['trigger_time'])
    if df.empty:
        print("表格数据为空！")
        return
    # 字段标准化
    if '企业ID' not in df.columns and 'enterprise_id' in df.columns:
        df['企业ID'] = df['enterprise_id']
    if '设备ID' not in df.columns and 'device_id' in df.columns:
        df['设备ID'] = df['device_id']
    if '触发时间' not in df.columns and 'trigger_time' in df.columns:
        df['触发时间'] = df['trigger_time']

    print("==== 企业ID唯一值 ====")
    print(df['企业ID'].unique())
    print("==== 设备ID唯一值 ====")
    print(df['设备ID'].unique())
    print("==== 触发时间前5行 ====")
    print(df['触发时间'].head(5))

    # 过滤企业、设备
    df = df[(df['设备ID'] == args.device_id)]
    print(f"筛选企业和设备后数据量: {len(df)}")
    if df.empty:
        print("筛选后无数据！")
        return
    
    # 处理日期（修正：毫秒级时间戳）
    df['日期'] = pd.to_datetime(df['触发时间'], unit='ms', errors='coerce').dt.strftime('%Y-%m-%d')
    print("==== 修正后日期列唯一值 ====")
    print(df['日期'].unique())
    # 确定日期范围
    if args.days_back is not None:
        # 使用 days_back 模式：从今天往前推指定天数
        end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        start_date = end_date - timedelta(days=args.days_back - 1)  # -1 是因为包含今天
        print(f"导出最近 {args.days_back} 天的数据：{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
    else:
        # 使用指定的日期范围
        start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
        end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
        print(f"导出指定日期范围的数据：{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
    
    date_list = [(start_date + timedelta(days=i)).strftime('%Y-%m-%d')
                 for i in range((end_date - start_date).days + 1)]
    print(f"将导出的日期列表：{date_list}")
    # 按天分组导出
    export_stats = {
        'total_days': len(date_list),
        'exported_days': 0,
        'skipped_days': 0,
        'total_records': 0,
        'exported_files': []
    }
    
    for day in date_list:
        day_df = df[df['日期'] == day]
        if day_df.empty:
            print(f"{day} 无数据，跳过...")
            export_stats['skipped_days'] += 1
            continue
        save_dir = os.path.join(args.output_dir, args.enterprise_id, args.device_id)
        os.makedirs(save_dir, exist_ok=True)
        """
        file_path = os.path.join(save_dir, f"{day}.png")
        # 渲染为图片
        fig, ax = plt.subplots(figsize=(min(20, 2+len(day_df.columns)*2), min(0.5*len(day_df), 20)))
        ax.axis('off')
        pd.plotting.table(ax, day_df, loc='center', cellLoc='center', colWidths=[0.2]*len(day_df.columns))
        plt.tight_layout()
        plt.savefig(file_path, bbox_inches='tight')
        plt.close(fig)
        """
        # 只保留需要的字段（去掉session_id）
        fields_to_keep = ['功能模块', '人脸信息', '标签信息', '触发时间', '设备ID', '扩展字段']
        missing_fields = [f for f in fields_to_keep if f not in day_df.columns]
        if missing_fields:
            print(f"警告：以下字段在数据中不存在，将被跳过: {missing_fields}")
        export_fields = [f for f in fields_to_keep if f in day_df.columns]
        export_df = day_df[export_fields].copy()
        # 提取扩展字段的text内容
        if '扩展字段' in export_df.columns:
            def extract_text(val):
                if isinstance(val, dict) and 'text' in val:
                    return val['text']
                elif isinstance(val, list) and val and isinstance(val[0], dict) and 'text' in val[0]:
                    return val[0]['text']
                elif isinstance(val, str):
                    return val
                return ''
            export_df['扩展字段'] = export_df['扩展字段'].apply(extract_text)
        # 格式化触发时间为"YYYY-MM-DD HH:MM:SS"
        if '触发时间' in export_df.columns:
            export_df['触发时间'] = (
                pd.to_datetime(export_df['触发时间'], unit='ms', errors='coerce')
                .dt.tz_localize('UTC')
                .dt.tz_convert('Asia/Shanghai')
                .dt.strftime('%Y-%m-%d %H:%M:%S')
            )
        # 增加"数据来源"字段
        #export_df['数据来源'] = '飞书多维表格'
        # 导出为txt（tab分隔，utf-8编码），文件名带设备ID和数据来源
        file_path = os.path.join(save_dir, f"{args.device_id}_飞书多维表格_{day}.txt")
        export_df.to_csv(file_path, sep='\t', index=False, encoding='utf-8')
        
        # 更新统计信息
        export_stats['exported_days'] += 1
        export_stats['total_records'] += len(export_df)
        export_stats['exported_files'].append(file_path)
        
        print(f"已导出: {file_path} (记录数: {len(export_df)})")
    
    # 打印导出统计信息
    print("\n" + "="*50)
    print("导出统计信息")
    print("="*50)
    print(f"总天数: {export_stats['total_days']}")
    print(f"成功导出天数: {export_stats['exported_days']}")
    print(f"跳过天数: {export_stats['skipped_days']}")
    print(f"总记录数: {export_stats['total_records']}")
    print(f"导出文件数: {len(export_stats['exported_files'])}")
    
    if export_stats['exported_files']:
        print(f"\n导出文件列表:")
        for file_path in export_stats['exported_files']:
            print(f"  - {file_path}")
    
    print("\n全部导出完成！")

# ---------------------- 主入口 ----------------------
if __name__ == "__main__":
    args = parse_args()
    asyncio.run(export_data(args)) 