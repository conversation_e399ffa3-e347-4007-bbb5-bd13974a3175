#!/usr/bin/env python3
"""
测试用户偏好详情统计功能
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.services.statistics_service import StatisticsService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_user_preference_details():
    """测试用户偏好详情统计功能"""
    logger.info("=" * 60)
    logger.info("测试用户偏好详情统计功能")
    logger.info("=" * 60)
    
    # 创建统计服务
    stats_service = StatisticsService()
    
    # 测试数据
    test_sessions = [
        {
            'session_id': 'session_001',
            'enterprise_id': 'enterprise1',
            'device_id': 'device1',
            'project_id': 'project1',
            'user_preferences': {
                '用户使用场景': '酒店大堂',
                '预算范围': '1000-2000元',
                '使用频率': '每天使用'
            }
        },
        {
            'session_id': 'session_002',
            'enterprise_id': 'enterprise1',
            'device_id': 'device1',
            'project_id': 'project1',
            'user_preferences': {
                '用户使用场景': '商场导购',
                '预算范围': '500-1000元'
            }
        },
        {
            'session_id': 'session_003',
            'enterprise_id': 'enterprise1',
            'device_id': 'device1',
            'project_id': 'project2',
            'user_preferences': {
                '用户使用场景': '医院导诊',
                '使用频率': '每周使用'
            }
        },
        {
            'session_id': 'session_004',
            'enterprise_id': 'enterprise1',
            'device_id': 'device1',
            'project_id': '',
            'user_preferences': None  # 没有偏好信息
        }
    ]
    
    # 计算用户偏好详情
    preference_details = stats_service._calculate_user_preference_details(test_sessions)
    
    logger.info("用户偏好详情统计结果:")
    logger.info(f"📊 偏好详情记录数: {len(preference_details)}")
    
    for i, detail in enumerate(preference_details, 1):
        logger.info(f"  {i}. 字段: {detail['field_name']}, 值: {detail['field_value']}, 会话: {detail['session_id']}, 项目: {detail['project_id']}")
    
    # 验证结果
    expected_count = 6  # 3个会话，共6个偏好字段
    expected_fields = ['用户使用场景', '预算范围', '使用频率']
    
    assert len(preference_details) == expected_count, f"偏好详情数量不匹配: {len(preference_details)} != {expected_count}"
    
    # 检查字段类型
    field_names = set(detail['field_name'] for detail in preference_details)
    assert field_names == set(expected_fields), f"字段名不匹配: {field_names} != {set(expected_fields)}"
    
    logger.info("✅ 用户偏好详情统计测试通过")

def test_json_string_preferences():
    """测试JSON字符串格式的偏好数据"""
    logger.info("=" * 60)
    logger.info("测试JSON字符串格式的偏好数据")
    logger.info("=" * 60)
    
    # 创建统计服务
    stats_service = StatisticsService()
    
    # 测试JSON字符串格式的数据
    test_sessions = [
        {
            'session_id': 'session_001',
            'enterprise_id': 'enterprise1',
            'device_id': 'device1',
            'project_id': 'project1',
            'user_preferences': '{"用户使用场景": "酒店大堂", "预算范围": "1000-2000元"}'
        },
        {
            'session_id': 'session_002',
            'enterprise_id': 'enterprise1',
            'device_id': 'device1',
            'project_id': 'project1',
            'user_preferences': '{"用户使用场景": "商场导购"}'
        }
    ]
    
    # 计算用户偏好详情
    preference_details = stats_service._calculate_user_preference_details(test_sessions)
    
    logger.info("JSON字符串格式偏好数据统计结果:")
    logger.info(f"📊 偏好详情记录数: {len(preference_details)}")
    
    for i, detail in enumerate(preference_details, 1):
        logger.info(f"  {i}. 字段: {detail['field_name']}, 值: {detail['field_value']}, 会话: {detail['session_id']}")
    
    # 验证结果
    expected_count = 3  # 2个会话，共3个偏好字段
    
    assert len(preference_details) == expected_count, f"偏好详情数量不匹配: {len(preference_details)} != {expected_count}"
    
    logger.info("✅ JSON字符串格式偏好数据测试通过")

def test_full_statistics_with_preferences():
    """测试完整的统计功能（包含用户偏好详情统计）"""
    logger.info("=" * 60)
    logger.info("测试完整的统计功能（包含用户偏好详情统计）")
    logger.info("=" * 60)
    
    # 计算昨天10点的数据
    yesterday = datetime.now() - timedelta(days=1)
    target_hour = yesterday.replace(hour=10, minute=0, second=0, microsecond=0)
    
    stats_service = StatisticsService()
    
    try:
        # 执行统计
        stats_service.calculate_statistics_for_hour(target_hour)
        logger.info("✅ 完整统计测试通过")
    except Exception as e:
        logger.error(f"❌ 完整统计测试失败: {e}", exc_info=True)
        raise

def test_device_specific_preferences():
    """测试特定设备的用户偏好详情统计"""
    logger.info("=" * 60)
    logger.info("测试特定设备的用户偏好详情统计")
    logger.info("=" * 60)
    
    # 计算昨天10点特定设备的数据
    yesterday = datetime.now() - timedelta(days=1)
    target_hour = yesterday.replace(hour=10, minute=0, second=0, microsecond=0)
    
    # 使用示例的企业ID和设备ID
    enterprise_id = "test_enterprise"
    device_id = "test_device"
    
    stats_service = StatisticsService()
    
    try:
        # 执行统计
        stats_service.calculate_statistics_for_hour(target_hour, enterprise_id, device_id)
        logger.info("✅ 特定设备用户偏好详情统计测试通过")
    except Exception as e:
        logger.error(f"❌ 特定设备用户偏好详情统计测试失败: {e}", exc_info=True)
        raise

def main():
    """主函数"""
    logger.info("开始测试用户偏好详情统计功能")
    
    try:
        # 测试用户偏好详情计算
        test_user_preference_details()
        
        # 测试JSON字符串格式
        test_json_string_preferences()
        
        # 测试完整统计功能
        test_full_statistics_with_preferences()
        
        # 测试特定设备统计
        test_device_specific_preferences()
        
        logger.info("🎉 所有用户偏好详情统计测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 