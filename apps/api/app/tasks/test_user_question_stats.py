#!/usr/bin/env python3
"""
测试用户问题统计功能
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.services.statistics_service import StatisticsService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_user_question_stats():
    """测试用户问题统计功能"""
    logger.info("=" * 60)
    logger.info("测试用户问题统计功能")
    logger.info("=" * 60)
    
    # 创建统计服务
    stats_service = StatisticsService()
    
    # 测试数据
    test_messages = [
        {
            'session_id': 'session1',
            'role': 'user',
            'content': '你好，请问你是谁？'
        },
        {
            'session_id': 'session1',
            'role': 'assistant',
            'content': '我是智能机器人助手'
        },
        {
            'session_id': 'session1',
            'role': 'user',
            'content': '现在几点了？'
        },
        {
            'session_id': 'session2',
            'role': 'user',
            'content': '你好，请问你是谁？'
        },
        {
            'session_id': 'session2',
            'role': 'assistant',
            'content': '我是智能机器人助手'
        },
        {
            'session_id': 'session3',
            'role': 'user',
            'content': '今天天气怎么样？'
        }
    ]
    
    # 计算用户问题统计
    user_question_stats = stats_service._calculate_user_question_stats(test_messages)
    
    logger.info("用户问题统计结果:")
    logger.info(f"唯一问题数量: {user_question_stats['unique_question_count']}")
    logger.info(f"包含用户问题的会话ID: {user_question_stats['session_ids_unique_question_count']}")
    logger.info(f"关键词词云: {user_question_stats['wordcloud_keywords']}")
    
    # 验证结果
    expected_unique_count = 3  # "你好，请问你是谁？", "现在几点了？", "今天天气怎么样？"
    expected_session_ids = ['session1', 'session2', 'session3']
    
    assert user_question_stats['unique_question_count'] == expected_unique_count, f"唯一问题数量不匹配: {user_question_stats['unique_question_count']} != {expected_unique_count}"
    assert set(user_question_stats['session_ids_unique_question_count']) == set(expected_session_ids), f"会话ID不匹配"
    
    logger.info("✅ 用户问题统计测试通过")

def test_full_statistics_with_user_questions():
    """测试完整的统计功能（包含用户问题统计）"""
    logger.info("=" * 60)
    logger.info("测试完整的统计功能（包含用户问题统计）")
    logger.info("=" * 60)
    
    # 计算昨天10点的数据
    yesterday = datetime.now() - timedelta(days=1)
    target_hour = yesterday.replace(hour=10, minute=0, second=0, microsecond=0)
    
    stats_service = StatisticsService()
    
    try:
        # 执行统计
        stats_service.calculate_statistics_for_hour(target_hour)
        logger.info("✅ 完整统计测试通过")
    except Exception as e:
        logger.error(f"❌ 完整统计测试失败: {e}", exc_info=True)
        raise

def test_device_specific_user_questions():
    """测试特定设备的用户问题统计"""
    logger.info("=" * 60)
    logger.info("测试特定设备的用户问题统计")
    logger.info("=" * 60)
    
    # 计算昨天10点特定设备的数据
    yesterday = datetime.now() - timedelta(days=1)
    target_hour = yesterday.replace(hour=10, minute=0, second=0, microsecond=0)
    
    # 使用示例的企业ID和设备ID
    enterprise_id = "test_enterprise"
    device_id = "test_device"
    
    stats_service = StatisticsService()
    
    try:
        # 执行统计
        stats_service.calculate_statistics_for_hour(target_hour, enterprise_id, device_id)
        logger.info("✅ 特定设备用户问题统计测试通过")
    except Exception as e:
        logger.error(f"❌ 特定设备用户问题统计测试失败: {e}", exc_info=True)
        raise

def main():
    """主函数"""
    logger.info("开始测试用户问题统计功能")
    
    try:
        # 测试用户问题统计计算
        test_user_question_stats()
        
        # 测试完整统计功能
        test_full_statistics_with_user_questions()
        
        # 测试特定设备统计
        test_device_specific_user_questions()
        
        logger.info("🎉 所有用户问题统计测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 