#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试活跃小时统计表字段更新
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from datetime import datetime, timedelta
from apps.api.app.models.statistics import AosStatActiveHoursHourly
from apps.api.app.database import get_speech_ai_robot_db
from apps.api.app.services.statistics_service import StatisticsService
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_active_hours_hourly_model():
    """测试活跃小时统计模型字段"""
    logger.info("=== 测试活跃小时统计模型字段 ===")
    
    # 创建模型实例
    active_hours = AosStatActiveHoursHourly(
        hour_bucket=datetime.now().replace(minute=0, second=0, microsecond=0),
        enterprise_id="test_enterprise",
        device_id="test_device",
        consecutive_active_hours=5,
        assistant_first_ratio=0.6,
        greeting_ratio=0.3,
        session_ids_active=["session_1", "session_2", "session_3"]
    )
    
    logger.info("模型实例创建成功")
    logger.info(f"连续活跃小时数: {active_hours.consecutive_active_hours}")
    logger.info(f"assistant首发会话占比: {active_hours.assistant_first_ratio}")
    logger.info(f"greeting会话占比: {active_hours.greeting_ratio}")
    logger.info(f"活跃会话ID列表: {active_hours.session_ids_active}")
    
    return True

def test_active_hours_hourly_save():
    """测试活跃小时统计数据保存"""
    logger.info("=== 测试活跃小时统计数据保存 ===")
    
    try:
        with next(get_speech_ai_robot_db()) as db:
            # 创建测试数据
            test_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
            active_hours = AosStatActiveHoursHourly(
                hour_bucket=test_hour,
                enterprise_id="test_enterprise_active",
                device_id="test_device_active",
                consecutive_active_hours=8,
                assistant_first_ratio=0.65,
                greeting_ratio=0.25,
                session_ids_active=["session_001", "session_002", "session_003", "session_004", "session_005"]
            )
            
            # 保存到数据库
            db.add(active_hours)
            db.commit()
            logger.info("活跃小时统计数据保存成功")
            
            # 查询验证
            saved_data = db.query(AosStatActiveHoursHourly).filter(
                AosStatActiveHoursHourly.hour_bucket == test_hour,
                AosStatActiveHoursHourly.enterprise_id == "test_enterprise_active",
                AosStatActiveHoursHourly.device_id == "test_device_active"
            ).first()
            
            if saved_data:
                logger.info("查询验证成功")
                logger.info(f"保存的数据: consecutive_active_hours={saved_data.consecutive_active_hours}")
                logger.info(f"保存的数据: assistant_first_ratio={saved_data.assistant_first_ratio}")
                logger.info(f"保存的数据: greeting_ratio={saved_data.greeting_ratio}")
                logger.info(f"保存的数据: session_ids_active={saved_data.session_ids_active}")
                
                # 清理测试数据
                db.delete(saved_data)
                db.commit()
                logger.info("测试数据清理完成")
                
                return True
            else:
                logger.error("查询验证失败")
                return False
                
    except Exception as e:
        logger.error(f"测试活跃小时统计数据保存失败: {e}", exc_info=True)
        return False

def test_statistics_service_active_hours():
    """测试统计服务中的活跃小时统计计算"""
    logger.info("=== 测试统计服务中的活跃小时统计计算 ===")
    
    try:
        service = StatisticsService()
        
        # 测试特定时间
        test_hour = datetime.now().replace(minute=0, second=0, microsecond=0) - timedelta(hours=1)
        
        # 计算统计数据
        service.calculate_statistics_for_hour(test_hour)
        logger.info("统计服务活跃小时统计计算完成")
        
        return True
        
    except Exception as e:
        logger.error(f"测试统计服务活跃小时统计计算失败: {e}", exc_info=True)
        return False

def test_multiple_devices_active_hours():
    """测试多个设备的活跃小时统计"""
    logger.info("=== 测试多个设备的活跃小时统计 ===")
    
    try:
        with next(get_speech_ai_robot_db()) as db:
            # 创建多个设备的测试数据
            test_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
            
            devices = [
                {
                    "enterprise_id": "demo_enterprise_1",
                    "device_id": "demo_device_001",
                    "consecutive_active_hours": 6,
                    "assistant_first_ratio": 0.7,
                    "greeting_ratio": 0.2,
                    "session_ids_active": ["session_001", "session_002", "session_003"]
                },
                {
                    "enterprise_id": "demo_enterprise_1",
                    "device_id": "demo_device_002",
                    "consecutive_active_hours": 4,
                    "assistant_first_ratio": 0.5,
                    "greeting_ratio": 0.3,
                    "session_ids_active": ["session_004", "session_005"]
                },
                {
                    "enterprise_id": "demo_enterprise_2",
                    "device_id": "demo_device_003",
                    "consecutive_active_hours": 10,
                    "assistant_first_ratio": 0.8,
                    "greeting_ratio": 0.15,
                    "session_ids_active": ["session_006", "session_007", "session_008", "session_009"]
                }
            ]
            
            for device_data in devices:
                active_hours = AosStatActiveHoursHourly(
                    hour_bucket=test_hour,
                    enterprise_id=device_data["enterprise_id"],
                    device_id=device_data["device_id"],
                    consecutive_active_hours=device_data["consecutive_active_hours"],
                    assistant_first_ratio=device_data["assistant_first_ratio"],
                    greeting_ratio=device_data["greeting_ratio"],
                    session_ids_active=device_data["session_ids_active"]
                )
                
                db.merge(active_hours)
                logger.info(f"保存设备 {device_data['enterprise_id']}-{device_data['device_id']} 的活跃小时统计数据")
            
            db.commit()
            logger.info("所有设备活跃小时统计数据保存成功")
            
            # 查询验证
            all_data = db.query(AosStatActiveHoursHourly).filter(
                AosStatActiveHoursHourly.hour_bucket == test_hour
            ).all()
            
            logger.info(f"查询到 {len(all_data)} 条活跃小时统计记录")
            
            for data in all_data:
                logger.info(f"设备: {data.enterprise_id}-{data.device_id}")
                logger.info(f"连续活跃小时: {data.consecutive_active_hours}")
                logger.info(f"assistant首发占比: {data.assistant_first_ratio:.2%}")
                logger.info(f"greeting占比: {data.greeting_ratio:.2%}")
                logger.info("---")
            
            return True
            
    except Exception as e:
        logger.error(f"测试多个设备活跃小时统计失败: {e}", exc_info=True)
        return False

def main():
    """主测试函数"""
    logger.info("开始测试活跃小时统计表字段更新")
    
    # 测试1: 模型字段
    test1_result = test_active_hours_hourly_model()
    
    # 测试2: 数据保存
    test2_result = test_active_hours_hourly_save()
    
    # 测试3: 统计服务
    test3_result = test_statistics_service_active_hours()
    
    # 测试4: 多设备统计
    test4_result = test_multiple_devices_active_hours()
    
    # 输出测试结果
    logger.info("=== 测试结果汇总 ===")
    logger.info(f"模型字段测试: {'通过' if test1_result else '失败'}")
    logger.info(f"数据保存测试: {'通过' if test2_result else '失败'}")
    logger.info(f"统计服务测试: {'通过' if test3_result else '失败'}")
    logger.info(f"多设备统计测试: {'通过' if test4_result else '失败'}")
    
    if all([test1_result, test2_result, test3_result, test4_result]):
        logger.info("所有测试通过！活跃小时统计表字段更新成功")
    else:
        logger.error("部分测试失败，请检查代码")

if __name__ == "__main__":
    main() 