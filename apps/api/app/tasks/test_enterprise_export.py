#!/usr/bin/env python3
"""
企业数据导出功能测试

本文件用于测试企业数据导出功能的各个组件
"""

import sys
import os
import unittest
import tempfile
import shutil
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from .enterprise_data_export import EnterpriseDataExporter
from .export_config import ExportConfig, ExportConfigTemplates


class TestExportConfig(unittest.TestCase):
    """测试导出配置类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = ExportConfig()
    
    def test_default_config(self):
        """测试默认配置"""
        self.assertEqual(self.config.get_merge_interval(), 30)
        self.assertEqual(self.config.get_output_format(), 'txt')
        self.assertFalse(self.config.should_include_sensitive_data())
        self.assertTrue(self.config.should_include_statistics())
    
    def test_custom_config(self):
        """测试自定义配置"""
        custom_config = ExportConfig({
            'merge_interval_seconds': 60,
            'include_sensitive_data': True,
            'include_statistics': False
        })
        
        self.assertEqual(custom_config.get_merge_interval(), 60)
        self.assertTrue(custom_config.should_include_sensitive_data())
        self.assertFalse(custom_config.should_include_statistics())
    
    def test_config_templates(self):
        """测试配置模板"""
        # 基础配置
        basic_config = ExportConfigTemplates.get_basic_config()
        self.assertEqual(basic_config.get_merge_interval(), 30)
        self.assertFalse(basic_config.should_include_sensitive_data())
        
        # 详细配置
        detailed_config = ExportConfigTemplates.get_detailed_config()
        self.assertTrue(detailed_config.should_include_sensitive_data())
        self.assertTrue(detailed_config.should_include_detailed_messages())
        
        # 性能配置
        performance_config = ExportConfigTemplates.get_performance_config()
        self.assertEqual(performance_config.get_batch_size(), 2000)
        self.assertEqual(performance_config.get_max_concurrent_processes(), 4)
        
        # 最小配置
        minimal_config = ExportConfigTemplates.get_minimal_config()
        self.assertFalse(minimal_config.should_include_statistics())
        self.assertFalse(minimal_config.should_include_detailed_messages())
    
    def test_config_file_operations(self):
        """测试配置文件操作"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_file = f.name
        
        try:
            # 保存配置
            test_config = ExportConfig({
                'merge_interval_seconds': 45,
                'include_sensitive_data': True
            })
            test_config.save_to_file(config_file)
            
            # 加载配置
            loaded_config = ExportConfig.from_file(config_file)
            self.assertEqual(loaded_config.get_merge_interval(), 45)
            self.assertTrue(loaded_config.should_include_sensitive_data())
            
        finally:
            if os.path.exists(config_file):
                os.remove(config_file)


class TestEnterpriseDataExporter(unittest.TestCase):
    """测试企业数据导出器"""
    
    def setUp(self):
        """测试前准备"""
        self.config = ExportConfig({
            'merge_interval_seconds': 30,
            'include_sensitive_data': False,
            'include_statistics': True,
            'include_user_preferences': True,
            'include_action_event_data': True,
            'include_detailed_messages': True,
            'include_session_metadata': True,
            'include_message_metadata': True
        })
        self.exporter = EnterpriseDataExporter(config=self.config)
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_merge_sessions_into_chats(self):
        """测试session合并为chat"""
        # 模拟会话数据
        sessions = [
            {
                'session_id': 'session1',
                'enterprise_id': 'enterprise1',
                'device_id': 'device1',
                'session_start_time': datetime(2025, 1, 1, 10, 0, 0),
                'session_end_time': datetime(2025, 1, 1, 10, 5, 0),
                'conversation_turns': 3,
                'session_duration_seconds': 300
            },
            {
                'session_id': 'session2',
                'enterprise_id': 'enterprise1',
                'device_id': 'device1',
                'session_start_time': datetime(2025, 1, 1, 10, 5, 30),  # 30秒后
                'session_end_time': datetime(2025, 1, 1, 10, 8, 0),
                'conversation_turns': 2,
                'session_duration_seconds': 150
            },
            {
                'session_id': 'session3',
                'enterprise_id': 'enterprise1',
                'device_id': 'device1',
                'session_start_time': datetime(2025, 1, 1, 10, 10, 0),  # 2分钟后
                'session_end_time': datetime(2025, 1, 1, 10, 12, 0),
                'conversation_turns': 1,
                'session_duration_seconds': 120
            }
        ]
        
        messages_by_session = {
            'session1': [
                {'session_id': 'session1', 'role': 'user', 'content': 'Hello'},
                {'session_id': 'session1', 'role': 'assistant', 'content': 'Hi'}
            ],
            'session2': [
                {'session_id': 'session2', 'role': 'user', 'content': 'How are you?'},
                {'session_id': 'session2', 'role': 'assistant', 'content': 'I am fine'}
            ],
            'session3': [
                {'session_id': 'session3', 'role': 'user', 'content': 'Goodbye'},
                {'session_id': 'session3', 'role': 'assistant', 'content': 'Bye'}
            ]
        }
        
        # 执行合并
        chats = self.exporter._merge_sessions_into_chats(sessions, messages_by_session)
        
        # 验证结果
        self.assertEqual(len(chats), 2)  # 应该有两个chat
        
        # 第一个chat应该包含session1和session2
        first_chat = chats[0]
        self.assertEqual(len(first_chat['sessions']), 2)
        self.assertEqual(first_chat['sessions'][0]['session_id'], 'session1')
        self.assertEqual(first_chat['sessions'][1]['session_id'], 'session2')
        self.assertEqual(first_chat['total_turns'], 5)  # 3 + 2
        
        # 第二个chat应该包含session3
        second_chat = chats[1]
        self.assertEqual(len(second_chat['sessions']), 1)
        self.assertEqual(second_chat['sessions'][0]['session_id'], 'session3')
        self.assertEqual(second_chat['total_turns'], 1)
    
    def test_format_chat_data(self):
        """测试chat数据格式化"""
        chat = {
            'chat_id': 'chat_session1',
            'enterprise_id': 'enterprise1',
            'device_id': 'device1',
            'chat_start_time': datetime(2025, 1, 1, 10, 0, 0),
            'chat_end_time': datetime(2025, 1, 1, 10, 5, 0),
            'total_turns': 3,
            'total_duration': 300,
            'sessions': [
                {
                    'session_id': 'session1',
                    'session_start_time': datetime(2025, 1, 1, 10, 0, 0),
                    'session_end_time': datetime(2025, 1, 1, 10, 5, 0),
                    'conversation_turns': 3,
                    'session_duration_seconds': 300,
                    'project_id': 'project1',
                    'app_id': 'app1'
                }
            ],
            'messages': [
                {
                    'message_index': 1,
                    'role': 'user',
                    'content': 'Hello',
                    'message_timestamp': datetime(2025, 1, 1, 10, 0, 5),
                    'action_data': None,
                    'event_data': None,
                    'app_id': 'app1',
                    'page_id': 'page1'
                },
                {
                    'message_index': 2,
                    'role': 'assistant',
                    'content': 'Hi there!',
                    'message_timestamp': datetime(2025, 1, 1, 10, 0, 10),
                    'action_data': {'name': 'greet'},
                    'event_data': None,
                    'app_id': 'app1',
                    'page_id': 'page1'
                }
            ],
            'user_message_count': 1,
            'assistant_message_count': 1,
            'action_count': 1,
            'event_count': 0
        }
        
        # 执行格式化
        formatted_chat = self.exporter._format_chat_data(chat)
        
        # 验证基础字段
        self.assertEqual(formatted_chat['chat_id'], 'chat_session1')
        self.assertEqual(formatted_chat['enterprise_id'], 'enterprise1')
        self.assertEqual(formatted_chat['device_id'], 'device1')
        self.assertEqual(formatted_chat['interaction_rounds'], 1)
        self.assertEqual(formatted_chat['user_message_count'], 1)
        self.assertEqual(formatted_chat['assistant_message_count'], 1)
        self.assertEqual(formatted_chat['action_count'], 1)
        self.assertEqual(formatted_chat['event_count'], 0)
        
        # 验证时间格式
        self.assertIsInstance(formatted_chat['chat_start_time'], str)
        self.assertIsInstance(formatted_chat['chat_end_time'], str)
        
        # 验证会话数据
        self.assertIn('sessions', formatted_chat)
        self.assertEqual(len(formatted_chat['sessions']), 1)
        
        # 验证消息数据
        self.assertIn('messages', formatted_chat)
        self.assertEqual(len(formatted_chat['messages']), 2)
        
        # 验证消息内容
        user_message = formatted_chat['messages'][0]
        self.assertEqual(user_message['role'], 'user')
        self.assertEqual(user_message['content'], 'Hello')
        
        assistant_message = formatted_chat['messages'][1]
        self.assertEqual(assistant_message['role'], 'assistant')
        self.assertEqual(assistant_message['content'], 'Hi there!')
        self.assertTrue(assistant_message['has_action'])
    
    @patch('app.database.get_agentos_db')
    def test_get_sessions_by_date_range(self, mock_get_db):
        """测试获取会话数据"""
        # 模拟数据库会话
        mock_session = Mock()
        mock_db = Mock()
        mock_get_db.return_value = iter([mock_db])
        
        # 模拟查询结果
        mock_session_obj = Mock()
        mock_session_obj.session_id = 'session1'
        mock_session_obj.enterprise_id = 'enterprise1'
        mock_session_obj.device_id = 'device1'
        mock_session_obj.session_start_time = datetime(2025, 1, 1, 10, 0, 0)
        mock_session_obj.session_end_time = datetime(2025, 1, 1, 10, 5, 0)
        mock_session_obj.conversation_turns = 3
        mock_session_obj.session_duration_seconds = 300
        mock_session_obj.project_id = 'project1'
        mock_session_obj.app_id = 'app1'
        mock_session_obj.group_id = 'group1'
        mock_session_obj.client_id = 'client1'
        mock_session_obj.product_id = 1
        mock_session_obj.product_model = 'model1'
        mock_session_obj.timezone = 'UTC'
        mock_session_obj.user_preferences = None
        
        mock_db.query.return_value.filter.return_value.order_by.return_value.all.return_value = [mock_session_obj]
        
        # 执行查询
        sessions = self.exporter._get_sessions_by_date_range(
            'enterprise1', 'device1',
            datetime(2025, 1, 1), datetime(2025, 1, 2)
        )
        
        # 验证结果
        self.assertEqual(len(sessions), 1)
        self.assertEqual(sessions[0]['session_id'], 'session1')
        self.assertEqual(sessions[0]['enterprise_id'], 'enterprise1')
        self.assertEqual(sessions[0]['device_id'], 'device1')
    
    @patch('app.database.get_agentos_db')
    def test_get_messages_by_session_ids(self, mock_get_db):
        """测试获取消息数据"""
        # 模拟数据库会话
        mock_db = Mock()
        mock_get_db.return_value = iter([mock_db])
        
        # 模拟查询结果
        mock_message = Mock()
        mock_message.session_id = 'session1'
        mock_message.message_index = 1
        mock_message.role = 'user'
        mock_message.content = 'Hello'
        mock_message.action_data = None
        mock_message.event_data = None
        mock_message.message_timestamp = datetime(2025, 1, 1, 10, 0, 5)
        mock_message.app_id = 'app1'
        mock_message.page_id = 'page1'
        mock_message.agent_id = 'agent1'
        mock_message.face_id = 'face1'
        
        mock_db.query.return_value.filter.return_value.order_by.return_value.all.return_value = [mock_message]
        
        # 执行查询
        messages_by_session = self.exporter._get_messages_by_session_ids(['session1'])
        
        # 验证结果
        self.assertIn('session1', messages_by_session)
        self.assertEqual(len(messages_by_session['session1']), 1)
        self.assertEqual(messages_by_session['session1'][0]['role'], 'user')
        self.assertEqual(messages_by_session['session1'][0]['content'], 'Hello')


class TestExportIntegration(unittest.TestCase):
    """测试导出功能集成"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = ExportConfigTemplates.get_basic_config()
        self.exporter = EnterpriseDataExporter(config=self.config)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    @patch.object(EnterpriseDataExporter, '_get_sessions_by_date_range')
    @patch.object(EnterpriseDataExporter, '_get_messages_by_session_ids')
    def test_export_data_by_date_range(self, mock_get_messages, mock_get_sessions):
        """测试完整导出流程"""
        # 模拟会话数据
        mock_sessions = [
            {
                'session_id': 'session1',
                'enterprise_id': 'enterprise1',
                'device_id': 'device1',
                'session_start_time': datetime(2025, 1, 1, 10, 0, 0),
                'session_end_time': datetime(2025, 1, 1, 10, 5, 0),
                'conversation_turns': 3,
                'session_duration_seconds': 300,
                'project_id': 'project1',
                'app_id': 'app1',
                'group_id': 'group1',
                'client_id': 'client1',
                'product_id': 1,
                'product_model': 'model1',
                'timezone': 'UTC',
                'user_preferences': None
            }
        ]
        
        # 模拟消息数据
        mock_messages_by_session = {
            'session1': [
                {
                    'session_id': 'session1',
                    'message_index': 1,
                    'role': 'user',
                    'content': 'Hello',
                    'message_timestamp': datetime(2025, 1, 1, 10, 0, 5),
                    'action_data': None,
                    'event_data': None,
                    'app_id': 'app1',
                    'page_id': 'page1',
                    'agent_id': 'agent1',
                    'face_id': 'face1'
                },
                {
                    'session_id': 'session1',
                    'message_index': 2,
                    'role': 'assistant',
                    'content': 'Hi there!',
                    'message_timestamp': datetime(2025, 1, 1, 10, 0, 10),
                    'action_data': {'name': 'greet'},
                    'event_data': None,
                    'app_id': 'app1',
                    'page_id': 'page1',
                    'agent_id': 'agent1',
                    'face_id': 'face1'
                }
            ]
        }
        
        mock_get_sessions.return_value = mock_sessions
        mock_get_messages.return_value = mock_messages_by_session
        
        # 执行导出
        stats = self.exporter.export_data_by_date_range(
            'enterprise1', 'device1',
            datetime(2025, 1, 1), datetime(2025, 1, 2),
            self.temp_dir
        )
        
        # 验证统计信息
        self.assertEqual(stats['enterprise_id'], 'enterprise1')
        self.assertEqual(stats['device_id'], 'device1')
        self.assertEqual(stats['total_chats'], 1)
        self.assertEqual(stats['total_sessions'], 1)
        self.assertEqual(stats['total_messages'], 2)
        
        # 验证输出文件
        expected_file = os.path.join(self.temp_dir, 'enterprise1', 'device1', '2025-01-01.txt')
        self.assertTrue(os.path.exists(expected_file))
        
        # 验证索引文件
        expected_index = os.path.join(self.temp_dir, 'enterprise1', 'device1', 'export_index.json')
        self.assertTrue(os.path.exists(expected_index))


def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_suite.addTest(unittest.makeSuite(TestExportConfig))
    test_suite.addTest(unittest.makeSuite(TestEnterpriseDataExporter))
    test_suite.addTest(unittest.makeSuite(TestExportIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1) 