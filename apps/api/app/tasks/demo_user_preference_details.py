#!/usr/bin/env python3
"""
演示用户偏好详情统计功能
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.services.statistics_service import StatisticsService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def demo_user_preference_calculation():
    """演示用户偏好详情统计计算"""
    logger.info("=" * 60)
    logger.info("演示用户偏好详情统计计算")
    logger.info("=" * 60)
    
    # 创建统计服务
    stats_service = StatisticsService()
    
    # 模拟会话数据
    demo_sessions = [
        # 会话1 - 酒店场景
        {
            'session_id': 'session_001',
            'enterprise_id': 'enterprise1',
            'device_id': 'device1',
            'project_id': 'hotel_project',
            'user_preferences': {
                '用户使用场景': '酒店大堂',
                '预算范围': '1000-2000元',
                '使用频率': '每天使用',
                '机器人功能': '迎宾接待'
            }
        },
        # 会话2 - 商场场景
        {
            'session_id': 'session_002',
            'enterprise_id': 'enterprise1',
            'device_id': 'device1',
            'project_id': 'mall_project',
            'user_preferences': {
                '用户使用场景': '商场导购',
                '预算范围': '500-1000元',
                '机器人功能': '商品推荐'
            }
        },
        # 会话3 - 医院场景
        {
            'session_id': 'session_003',
            'enterprise_id': 'enterprise1',
            'device_id': 'device1',
            'project_id': 'hospital_project',
            'user_preferences': {
                '用户使用场景': '医院导诊',
                '使用频率': '每周使用',
                '机器人功能': '科室导航'
            }
        },
        # 会话4 - 无偏好信息
        {
            'session_id': 'session_004',
            'enterprise_id': 'enterprise1',
            'device_id': 'device1',
            'project_id': '',
            'user_preferences': None
        },
        # 会话5 - JSON字符串格式
        {
            'session_id': 'session_005',
            'enterprise_id': 'enterprise1',
            'device_id': 'device1',
            'project_id': 'school_project',
            'user_preferences': '{"用户使用场景": "学校图书馆", "预算范围": "2000-3000元", "机器人功能": "图书查询"}'
        }
    ]
    
    logger.info("模拟会话数据:")
    for i, session in enumerate(demo_sessions, 1):
        logger.info(f"  {i}. 会话ID: {session['session_id']}, 项目: {session['project_id']}")
        if session['user_preferences']:
            logger.info(f"     偏好: {session['user_preferences']}")
        else:
            logger.info(f"     偏好: 无")
    
    # 计算用户偏好详情
    preference_details = stats_service._calculate_user_preference_details(demo_sessions)
    
    logger.info("\n用户偏好详情统计结果:")
    logger.info(f"📊 偏好详情记录数: {len(preference_details)}")
    
    # 按项目分组显示
    project_groups = {}
    for detail in preference_details:
        project_id = detail['project_id']
        if project_id not in project_groups:
            project_groups[project_id] = []
        project_groups[project_id].append(detail)
    
    for project_id, details in project_groups.items():
        logger.info(f"\n📋 项目: {project_id}")
        for detail in details:
            logger.info(f"  字段: {detail['field_name']}, 值: {detail['field_value']}, 会话: {detail['session_id']}")
    
    # 统计字段频次
    field_freq = {}
    for detail in preference_details:
        field_name = detail['field_name']
        field_freq[field_name] = field_freq.get(field_name, 0) + 1
    
    logger.info("\n📈 字段频次统计:")
    for field_name, count in sorted(field_freq.items()):
        logger.info(f"  {field_name}: {count}次")

def demo_real_data_statistics():
    """演示真实数据统计"""
    logger.info("=" * 60)
    logger.info("演示真实数据统计")
    logger.info("=" * 60)
    
    # 计算昨天10点的数据
    yesterday = datetime.now() - timedelta(days=1)
    target_hour = yesterday.replace(hour=10, minute=0, second=0, microsecond=0)
    
    logger.info(f"计算时间: {target_hour}")
    
    stats_service = StatisticsService()
    
    try:
        # 执行统计
        stats_service.calculate_statistics_for_hour(target_hour)
        logger.info("✅ 真实数据统计完成")
        
        # 查询统计结果
        from app.database import get_speech_ai_robot_db
        from app.models.statistics import AosStatUserPreferenceDetailHourly
        
        with next(get_speech_ai_robot_db()) as db:
            results = db.query(AosStatUserPreferenceDetailHourly).filter(
                AosStatUserPreferenceDetailHourly.hour_bucket == target_hour
            ).all()
            
            logger.info(f"\n📊 查询到 {len(results)} 条用户偏好详情记录:")
            for result in results:
                logger.info(f"  企业: {result.enterprise_id}, 设备: {result.device_id}, 项目: {result.project_id}")
                logger.info(f"  字段: {result.field_name}, 值: {result.field_value}, 会话: {result.session_id}")
                logger.info("  " + "-" * 40)
        
    except Exception as e:
        logger.error(f"❌ 真实数据统计失败: {e}", exc_info=True)

def demo_device_specific_statistics():
    """演示特定设备统计"""
    logger.info("=" * 60)
    logger.info("演示特定设备统计")
    logger.info("=" * 60)
    
    # 计算昨天10点特定设备的数据
    yesterday = datetime.now() - timedelta(days=1)
    target_hour = yesterday.replace(hour=10, minute=0, second=0, microsecond=0)
    
    # 使用示例的企业ID和设备ID
    enterprise_id = "demo_enterprise"
    device_id = "demo_device"
    
    logger.info(f"计算时间: {target_hour}")
    logger.info(f"企业ID: {enterprise_id}")
    logger.info(f"设备ID: {device_id}")
    
    stats_service = StatisticsService()
    
    try:
        # 执行统计
        stats_service.calculate_statistics_for_hour(target_hour, enterprise_id, device_id)
        logger.info("✅ 特定设备统计完成")
        
    except Exception as e:
        logger.error(f"❌ 特定设备统计失败: {e}", exc_info=True)

def show_usage_help():
    """显示使用帮助"""
    logger.info("=" * 60)
    logger.info("用户偏好详情统计功能演示")
    logger.info("=" * 60)
    logger.info("")
    logger.info("功能说明:")
    logger.info("1. 详细记录: 每条记录对应一个具体的偏好字段填写")
    logger.info("2. 项目关联: 记录每个偏好对应的项目ID")
    logger.info("3. 会话关联: 记录每个偏好对应的会话ID")
    logger.info("4. 全文搜索: 支持对偏好内容进行全文搜索")
    logger.info("")
    logger.info("表结构变更:")
    logger.info("- 表名: aos_stat_project_preference_hourly → aos_stat_user_preference_detail_hourly")
    logger.info("- 结构: 汇总统计 → 详细记录")
    logger.info("- 字段: hour_bucket, enterprise_id, device_id, project_id, field_name, field_value, session_id")
    logger.info("")
    logger.info("使用方法:")
    logger.info("python demo_user_preference_details.py")
    logger.info("")

def main():
    """主函数"""
    logger.info("开始演示用户偏好详情统计功能")
    
    try:
        # 显示使用帮助
        show_usage_help()
        
        # 演示用户偏好计算
        demo_user_preference_calculation()
        
        # 演示真实数据统计
        demo_real_data_statistics()
        
        # 演示特定设备统计
        demo_device_specific_statistics()
        
        logger.info("🎉 用户偏好详情统计功能演示完成！")
        
    except Exception as e:
        logger.error(f"❌ 演示失败: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 