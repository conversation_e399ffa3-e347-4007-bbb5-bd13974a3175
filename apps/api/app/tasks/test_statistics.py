#!/usr/bin/env python3
"""
机器人对话统计功能测试脚本
用于测试统计功能和验证数据正确性
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.services.statistics_service import StatisticsService
from app.tasks.validation import validate_statistics_for_hour

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_statistics_calculation():
    """测试统计计算功能"""
    logger.info("开始测试统计计算功能...")
    
    # 创建统计服务
    stats_service = StatisticsService()
    
    # 测试时间：前2个小时
    now = datetime.now()
    test_hours = []
    for i in range(2):
        target_hour = now.replace(minute=0, second=0, microsecond=0) - timedelta(hours=i+1)
        test_hours.append(target_hour)
    
    logger.info(f"测试时间范围: {test_hours[0]} 到 {test_hours[-1]}")
    
    try:
        # 执行统计计算
        stats_service.calculate_statistics_for_hours(test_hours)
        logger.info("✅ 统计计算测试完成")
        return True
    except Exception as e:
        logger.error(f"❌ 统计计算测试失败: {e}", exc_info=True)
        return False

def test_data_validation():
    """测试数据验证功能"""
    logger.info("开始测试数据验证功能...")
    
    # 测试时间：前1个小时
    now = datetime.now()
    test_hour = now.replace(minute=0, second=0, microsecond=0) - timedelta(hours=1)
    
    try:
        # 执行数据验证
        validation_results = validate_statistics_for_hour(test_hour)
        
        if validation_results['passed']:
            logger.info("✅ 数据验证测试通过")
            return True
        else:
            logger.error("❌ 数据验证测试失败")
            return False
    except Exception as e:
        logger.error(f"❌ 数据验证测试出错: {e}", exc_info=True)
        return False

def test_specific_device(enterprise_id: str, device_id: str):
    """测试特定设备的统计"""
    logger.info(f"开始测试特定设备: {enterprise_id}-{device_id}")
    
    # 测试时间：前1个小时
    now = datetime.now()
    test_hour = now.replace(minute=0, second=0, microsecond=0) - timedelta(hours=1)
    
    try:
        # 执行统计计算
        stats_service = StatisticsService()
        stats_service.calculate_statistics_for_hour(test_hour)
        
        # 执行数据验证
        validation_results = validate_statistics_for_hour(test_hour, enterprise_id, device_id)
        
        if validation_results['passed']:
            logger.info(f"✅ 设备 {enterprise_id}-{device_id} 统计测试通过")
            return True
        else:
            logger.error(f"❌ 设备 {enterprise_id}-{device_id} 统计测试失败")
            return False
    except Exception as e:
        logger.error(f"❌ 设备 {enterprise_id}-{device_id} 统计测试出错: {e}", exc_info=True)
        return False

def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("机器人对话统计功能测试")
    logger.info("=" * 60)
    
    # 测试1: 统计计算功能
    logger.info("\n1. 测试统计计算功能")
    calc_success = test_statistics_calculation()
    
    # 测试2: 数据验证功能
    logger.info("\n2. 测试数据验证功能")
    validation_success = test_data_validation()
    
    # 测试3: 特定设备测试（如果有参数）
    if len(sys.argv) > 2:
        enterprise_id = sys.argv[1]
        device_id = sys.argv[2]
        logger.info(f"\n3. 测试特定设备: {enterprise_id}-{device_id}")
        device_success = test_specific_device(enterprise_id, device_id)
    else:
        device_success = True
        logger.info("\n3. 跳过特定设备测试（未提供参数）")
    
    # 总结
    logger.info("\n" + "=" * 60)
    logger.info("测试结果总结")
    logger.info("=" * 60)
    
    if calc_success:
        logger.info("✅ 统计计算功能: 通过")
    else:
        logger.error("❌ 统计计算功能: 失败")
    
    if validation_success:
        logger.info("✅ 数据验证功能: 通过")
    else:
        logger.error("❌ 数据验证功能: 失败")
    
    if device_success:
        logger.info("✅ 特定设备测试: 通过")
    else:
        logger.error("❌ 特定设备测试: 失败")
    
    overall_success = calc_success and validation_success and device_success
    
    if overall_success:
        logger.info("\n🎉 所有测试通过！")
        return 0
    else:
        logger.error("\n💥 部分测试失败！")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 