from celery import shared_task
from app.services.synchronizer import sync_robot_positions
import logging

logger = logging.getLogger(__name__)

@shared_task(name="tasks.sync_robot_positions")
def task_sync_robot_positions():
    """
    Celery task to synchronize robot positions.
    """
    logger.info("开始执行机器人位置同步Celery任务...")
    try:
        sync_robot_positions()
        logger.info("机器人位置同步Celery任务执行成功。")
    except Exception as e:
        logger.error(f"机器人位置同步Celery任务执行失败: {e}", exc_info=True) 