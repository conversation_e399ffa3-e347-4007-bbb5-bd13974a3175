#!/usr/bin/env python3
"""
机器人对话统计系统使用示例
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.services.statistics_service import StatisticsService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def example_1_single_time():
    """示例1：计算单个时间点的数据"""
    logger.info("=" * 60)
    logger.info("示例1：计算单个时间点的数据")
    logger.info("=" * 60)
    
    # 计算昨天10点的数据
    yesterday = datetime.now() - timedelta(days=1)
    target_hour = yesterday.replace(hour=10, minute=0, second=0, microsecond=0)
    
    stats_service = StatisticsService()
    stats_service.calculate_statistics_for_hour(target_hour)
    
    logger.info("✅ 示例1完成")

def example_2_time_range():
    """示例2：计算时间范围的数据"""
    logger.info("=" * 60)
    logger.info("示例2：计算时间范围的数据")
    logger.info("=" * 60)
    
    # 计算昨天9点到11点的数据
    yesterday = datetime.now() - timedelta(days=1)
    start_hour = yesterday.replace(hour=9, minute=0, second=0, microsecond=0)
    end_hour = yesterday.replace(hour=11, minute=0, second=0, microsecond=0)
    
    hours = []
    current = start_hour
    while current <= end_hour:
        hours.append(current)
        current += timedelta(hours=1)
    
    stats_service = StatisticsService()
    stats_service.calculate_statistics_for_hours(hours)
    
    logger.info("✅ 示例2完成")

def example_3_specific_device():
    """示例3：计算特定设备的数据"""
    logger.info("=" * 60)
    logger.info("示例3：计算特定设备的数据")
    logger.info("=" * 60)
    
    # 计算昨天10点特定设备的数据
    yesterday = datetime.now() - timedelta(days=1)
    target_hour = yesterday.replace(hour=10, minute=0, second=0, microsecond=0)
    
    # 替换为实际的企业ID和设备ID
    enterprise_id = "example_enterprise"
    device_id = "example_device"
    
    stats_service = StatisticsService()
    stats_service.calculate_statistics_for_hour(target_hour, enterprise_id, device_id)
    
    logger.info("✅ 示例3完成")

def example_4_time_range_with_device():
    """示例4：计算时间范围+特定设备的数据"""
    logger.info("=" * 60)
    logger.info("示例4：计算时间范围+特定设备的数据")
    logger.info("=" * 60)
    
    # 计算昨天9点到11点特定设备的数据
    yesterday = datetime.now() - timedelta(days=1)
    start_hour = yesterday.replace(hour=9, minute=0, second=0, microsecond=0)
    end_hour = yesterday.replace(hour=11, minute=0, second=0, microsecond=0)
    
    hours = []
    current = start_hour
    while current <= end_hour:
        hours.append(current)
        current += timedelta(hours=1)
    
    # 替换为实际的企业ID和设备ID
    enterprise_id = "example_enterprise"
    device_id = "example_device"
    
    stats_service = StatisticsService()
    stats_service.calculate_statistics_for_hours(hours, enterprise_id, device_id)
    
    logger.info("✅ 示例4完成")

def example_5_command_line_style():
    """示例5：命令行风格的调用"""
    logger.info("=" * 60)
    logger.info("示例5：命令行风格的调用")
    logger.info("=" * 60)
    
    # 模拟命令行参数
    import argparse
    
    # 示例：python run_specific_time.py "2025-01-15 10:00:00"
    target_time_str = "2025-01-15 10:00:00"
    target_time = datetime.strptime(target_time_str, "%Y-%m-%d %H:%M:%S")
    target_hour = target_time.replace(minute=0, second=0, microsecond=0)
    
    stats_service = StatisticsService()
    stats_service.calculate_statistics_for_hour(target_hour)
    
    logger.info("✅ 示例5完成")

def show_usage_help():
    """显示使用帮助"""
    logger.info("=" * 60)
    logger.info("机器人对话统计系统使用示例")
    logger.info("=" * 60)
    logger.info("")
    logger.info("命令行使用方式：")
    logger.info("")
    logger.info("1. 单个时间点：")
    logger.info("   python run_specific_time.py \"2025-01-15 10:00:00\"")
    logger.info("")
    logger.info("2. 时间范围：")
    logger.info("   python run_specific_time.py \"2025-01-15 10:00:00\" \"2025-01-15 12:00:00\"")
    logger.info("")
    logger.info("3. 指定企业ID和设备ID：")
    logger.info("   python run_specific_time.py \"2025-01-15 10:00:00\" --enterprise enterprise1 --device device1")
    logger.info("")
    logger.info("4. 时间范围+设备过滤：")
    logger.info("   python run_specific_time.py \"2025-01-15 10:00:00\" \"2025-01-15 12:00:00\" --enterprise enterprise1 --device device1")
    logger.info("")
    logger.info("5. 查看帮助：")
    logger.info("   python run_specific_time.py --help")
    logger.info("")
    logger.info("注意事项：")
    logger.info("- 时间会自动转换为整点小时")
    logger.info("- 例如：2025-01-15 10:30:00 会计算 2025-01-15 10:00:00 的数据")
    logger.info("- 如果不指定企业ID和设备ID，将计算所有设备的数据")
    logger.info("")

def main():
    """主函数"""
    logger.info("开始运行使用示例")
    
    try:
        # 显示使用帮助
        show_usage_help()
        
        # 运行示例
        example_1_single_time()
        example_2_time_range()
        example_3_specific_device()
        example_4_time_range_with_device()
        example_5_command_line_style()
        
        logger.info("🎉 所有示例运行完成！")
        
    except Exception as e:
        logger.error(f"❌ 示例运行失败: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 