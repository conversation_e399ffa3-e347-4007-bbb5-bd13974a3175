from pydantic import BaseModel
from typing import List, Optional, Any
from datetime import datetime

class Message(BaseModel):
    role: str
    content: str
    message_timestamp: datetime
    images_path: Optional[str] = None
    audio_path: Optional[str] = None
    action_data: Optional[Any] = None
    event_data: Optional[Any] = None

    class Config:
        from_attributes = True

class Session(BaseModel):
    session_id: str
    client_id: str
    group_id: str
    enterprise_id: str
    device_id: str
    product_id: int
    product_model: str
    user_preferences: Optional[Any] = None
    session_start_time: datetime
    session_end_time: datetime
    conversation_turns: int
    messages: List[Message] = []

    class Config:
        from_attributes = True

class PaginatedSessionResponse(BaseModel):
    total: int
    valid_query_count: int
    page: int
    page_size: int
    items: List[Session]

    class Config:
        from_attributes = True 