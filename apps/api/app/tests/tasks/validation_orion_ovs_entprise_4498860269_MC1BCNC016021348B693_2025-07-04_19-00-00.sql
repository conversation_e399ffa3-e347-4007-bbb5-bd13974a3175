-- =====================================================================
-- Validation Queries For:
--   Enterprise: orion.ovs.entprise.4498860269
--   Device:     MC1BCNC016021348B693
--   Hour:       2025-07-04 19:00:00
-- =====================================================================


-- 1. 会话行为 (aos_stat_session_behavior_hourly)
SELECT session_total, valid_sessions, no_response_sessions, avg_conversation_turns, avg_session_duration FROM speech_ai_robot.aos_stat_session_behavior_hourly
WHERE hour_bucket = '2025-07-04 19:00:00'
  AND enterprise_id = 'orion.ovs.entprise.4498860269'
  AND device_id = 'MC1BCNC016021348B693'
;
SELECT COUNT(*), AVG(conversation_turns), AVG(session_duration_seconds) FROM agentos_online.aos_conversation_sessions
WHERE session_start_time >= '2025-07-04 19:00:00'
  AND session_start_time < '2025-07-04 20:00:00'
  AND enterprise_id = 'orion.ovs.entprise.4498860269'
  AND device_id = 'MC1BCNC016021348B693'
;


-- 2. 消息行为 (aos_stat_message_behavior_hourly)
SELECT user_msg_count, assistant_msg_count, action_trigger_count, event_trigger_count FROM speech_ai_robot.aos_stat_message_behavior_hourly
WHERE hour_bucket = '2025-07-04 19:00:00'
  AND enterprise_id = 'orion.ovs.entprise.4498860269'
  AND device_id = 'MC1BCNC016021348B693'
;

SELECT 
    (SELECT COUNT(*) FROM agentos_online.aos_conversation_messages WHERE session_id IN (SELECT session_id FROM agentos_online.aos_conversation_sessions
WHERE session_start_time >= '2025-07-04 19:00:00'
  AND session_start_time < '2025-07-04 20:00:00'
  AND enterprise_id = 'orion.ovs.entprise.4498860269'
  AND device_id = 'MC1BCNC016021348B693'
) AND role = 'user') AS user_msg,
    (SELECT COUNT(*) FROM agentos_online.aos_conversation_messages WHERE session_id IN (SELECT session_id FROM agentos_online.aos_conversation_sessions
WHERE session_start_time >= '2025-07-04 19:00:00'
  AND session_start_time < '2025-07-04 20:00:00'
  AND enterprise_id = 'orion.ovs.entprise.4498860269'
  AND device_id = 'MC1BCNC016021348B693'
) AND role = 'assistant') AS assistant_msg;



-- 3. 动作行为 (aos_stat_action_behavior_hourly)
SELECT action_name, action_count FROM speech_ai_robot.aos_stat_action_behavior_hourly
WHERE hour_bucket = '2025-07-04 19:00:00'
  AND enterprise_id = 'orion.ovs.entprise.4498860269'
  AND device_id = 'MC1BCNC016021348B693'
 ORDER BY action_count DESC;

SELECT 
    JSON_UNQUOTE(JSON_EXTRACT(action_data, '$.name')) as action_name, 
    COUNT(*) 
FROM agentos_online.aos_conversation_messages 
WHERE session_id IN (SELECT session_id FROM agentos_online.aos_conversation_sessions
WHERE session_start_time >= '2025-07-04 19:00:00'
  AND session_start_time < '2025-07-04 20:00:00'
  AND enterprise_id = 'orion.ovs.entprise.4498860269'
  AND device_id = 'MC1BCNC016021348B693'
) 
  AND action_data IS NOT NULL
GROUP BY action_name 
ORDER BY COUNT(*) DESC;
