import pytest
from unittest.mock import Mock, patch
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from app.services.feishu import feishu_service
from lark_oapi.api.wiki.v2.model.get_node_space_response import GetNodeSpaceResponse, GetNodeSpaceResponseBody

def test_get_real_bitable_token_from_wiki_node():
    # 模拟响应数据
    mock_response = Mock(spec=GetNodeSpaceResponse)
    mock_response.success.return_value = True
    
    # 创建模拟的响应体
    mock_data = Mock(spec=GetNodeSpaceResponseBody)
    mock_data.obj_token = "test_real_token"
    mock_response.data = mock_data

    # 模拟 client.wiki.v2.space.get_node 方法
    with patch.object(feishu_service.client.wiki.v2.space, 'get_node', return_value=mock_response):
        # 调用被测试的方法
        result = feishu_service.get_real_bitable_token_from_wiki_node(
            node_token="test_node_token"
        )
        
        # 验证结果
        assert result == "test_real_token"

def test_get_real_bitable_token_from_wiki_node_failure():
    # 模拟失败响应
    mock_response = Mock(spec=GetNodeSpaceResponse)
    mock_response.success.return_value = False
    mock_response.code = "error_code"
    mock_response.msg = "error_message"
    mock_response.get_log_id.return_value = "test_log_id"

    # 模拟 client.wiki.v2.space.get_node 方法
    with patch.object(feishu_service.client.wiki.v2.space, 'get_node', return_value=mock_response):
        # 验证是否抛出异常
        with pytest.raises(Exception) as exc_info:
            feishu_service.get_real_bitable_token_from_wiki_node(
                node_token="test_node_token"
            )
        
        # 验证异常信息
        assert "获取wiki节点信息失败" in str(exc_info.value) 