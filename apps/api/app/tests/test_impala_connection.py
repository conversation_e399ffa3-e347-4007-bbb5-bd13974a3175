import pytest
from sqlalchemy import text
from app.services.impala.connection import get_impala_connection

@pytest.mark.integration
def test_impala_connection_basic():
    """
    基础连接测试：只测试能否成功连接到Impala数据库。
    """
    try:
        with get_impala_connection() as conn:
            # 执行一个简单的查询来验证连接
            result = conn.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            assert row is not None, "连接成功但查询返回空结果"
            assert row[0] == 1, f"期望返回1，实际返回{row[0]}"
            print(f"✅ 成功连接到Impala数据库！测试查询结果: {row[0]}")
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        raise

@pytest.mark.integration
def test_impala_database_exists():
    """
    测试数据库是否存在。
    """
    try:
        with get_impala_connection() as conn:
            # 检查bi_gb数据库是否存在
            result = conn.execute(text("SHOW DATABASES LIKE 'bi_gb'"))
            databases = result.fetchall()
            assert len(databases) > 0, "bi_gb数据库不存在"
            print(f"✅ bi_gb数据库存在！")
            
            # 检查data_gb_location表是否存在
            result = conn.execute(text("SHOW TABLES IN bi_gb LIKE 'data_gb_location'"))
            tables = result.fetchall()
            assert len(tables) > 0, "bi_gb.data_gb_location表不存在"
            print(f"✅ bi_gb.data_gb_location表存在！")
            
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        raise 