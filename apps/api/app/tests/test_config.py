#!/usr/bin/env python3
"""
测试Impala配置加载
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.config.impala_config import IMPALA_CONNECTIONS

def test_config_loading():
    """测试配置加载"""
    print("=== Impala配置测试 ===")
    
    # 检查默认配置
    if "default" in IMPALA_CONNECTIONS:
        config = IMPALA_CONNECTIONS["default"]
        print(f"默认配置:")
        print(f"  Host: {config.get('host')}")
        print(f"  Port: {config.get('port')}")
        print(f"  Database: {config.get('database')}")
        print(f"  User: {config.get('user')}")
        print(f"  Password: {'***' if config.get('password') else 'None'}")
        print(f"  Auth Mechanism: {config.get('auth_mechanism')}")
        print(f"  Timeout: {config.get('timeout')}")
        print(f"  Use SSL: {config.get('use_ssl')}")
    else:
        print("❌ 未找到默认配置")
    
    print("\n=== 所有可用配置 ===")
    for key in IMPALA_CONNECTIONS.keys():
        print(f"  - {key}")

if __name__ == "__main__":
    test_config_loading() 