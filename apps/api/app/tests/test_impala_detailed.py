#!/usr/bin/env python3
"""
详细的Impala连接测试
"""
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.config.impala_config import IMPALA_CONNECTIONS
from sqlalchemy import create_engine, text

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def test_impala_connection_detailed():
    """详细的Impala连接测试"""
    print("=== 详细Impala连接测试 ===")
    
    config = IMPALA_CONNECTIONS["default"]
    
    # 尝试不同的连接参数组合
    connection_configs = [
        {
            "name": "基础连接（NOSASL）",
            "connect_args": {
                'auth_mechanism': 'NOSASL',
                'timeout': 30,
                'use_ssl': False
            }
        },
        {
            "name": "带用户认证（PLAIN）",
            "connect_args": {
                'auth_mechanism': 'PLAIN',
                'user': config.get('user'),
                'password': config.get('password'),
                'timeout': 30,
                'use_ssl': False
            }
        },
        {
            "name": "带用户认证（NOSASL）",
            "connect_args": {
                'auth_mechanism': 'NOSASL',
                'user': config.get('user'),
                'password': config.get('password'),
                'timeout': 30,
                'use_ssl': False
            }
        },
        {
            "name": "SSL连接",
            "connect_args": {
                'auth_mechanism': 'NOSASL',
                'timeout': 30,
                'use_ssl': True
            }
        }
    ]
    
    for config_test in connection_configs:
        print(f"\n--- 测试: {config_test['name']} ---")
        
        try:
            # 过滤掉None值
            filtered_args = {k: v for k, v in config_test['connect_args'].items() if v is not None}
            
            print(f"连接参数: {filtered_args}")
            
            # 创建连接字符串
            connection_string = f"impala://{config['host']}:{config['port']}/{config.get('database', '')}"
            print(f"连接字符串: {connection_string}")
            
            # 创建引擎
            engine = create_engine(connection_string, connect_args=filtered_args)
            
            # 尝试连接
            with engine.connect() as conn:
                print("✅ 连接成功！")
                
                # 执行简单查询
                result = conn.execute(text("SELECT 1 as test"))
                row = result.fetchone()
                print(f"✅ 查询成功！结果: {row[0]}")
                
                return True  # 如果成功，直接返回
                
        except Exception as e:
            print(f"❌ 连接失败: {type(e).__name__}: {e}")
            logger.debug(f"详细错误信息:", exc_info=True)
    
    return False

def test_impala_direct():
    """直接使用impala-shell测试连接"""
    print("\n=== 直接Impala连接测试 ===")
    
    config = IMPALA_CONNECTIONS["default"]
    
    try:
        import impala.dbapi
        
        print(f"尝试直接连接到: {config['host']}:{config['port']}")
        
        # 尝试不同的连接方式
        connection_configs = [
            {
                "name": "无认证连接",
                "kwargs": {
                    'host': config['host'],
                    'port': config['port'],
                    'database': config.get('database', 'default'),
                    'auth_mechanism': 'NOSASL'
                }
            },
            {
                "name": "带用户认证连接",
                "kwargs": {
                    'host': config['host'],
                    'port': config['port'],
                    'database': config.get('database', 'default'),
                    'auth_mechanism': 'PLAIN',
                    'user': config.get('user'),
                    'password': config.get('password')
                }
            }
        ]
        
        for conn_config in connection_configs:
            print(f"\n--- 测试: {conn_config['name']} ---")
            
            try:
                # 过滤None值
                filtered_kwargs = {k: v for k, v in conn_config['kwargs'].items() if v is not None}
                
                print(f"连接参数: {filtered_kwargs}")
                
                conn = impala.dbapi.connect(**filtered_kwargs)
                cursor = conn.cursor()
                
                print("✅ 直接连接成功！")
                
                # 执行查询
                cursor.execute("SELECT 1 as test")
                result = cursor.fetchone()
                print(f"✅ 查询成功！结果: {result[0]}")
                
                cursor.close()
                conn.close()
                
                return True
                
            except Exception as e:
                print(f"❌ 直接连接失败: {type(e).__name__}: {e}")
                logger.debug(f"详细错误信息:", exc_info=True)
        
    except ImportError:
        print("❌ impala-shell未安装")
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
    
    return False

if __name__ == "__main__":
    print("开始Impala连接测试...")
    
    # 测试SQLAlchemy连接
    success1 = test_impala_connection_detailed()
    
    # 测试直接连接
    success2 = test_impala_direct()
    
    print(f"\n=== 测试结果 ===")
    print(f"SQLAlchemy连接: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"直接连接: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if not success1 and not success2:
        print("\n💡 建议:")
        print("1. 检查网络连接是否正常")
        print("2. 确认Impala服务是否运行")
        print("3. 检查防火墙设置")
        print("4. 尝试使用不同的认证机制")
        print("5. 检查用户名密码是否正确") 