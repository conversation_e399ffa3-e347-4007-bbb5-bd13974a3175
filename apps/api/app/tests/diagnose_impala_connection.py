#!/usr/bin/env python3
"""
Impala连接诊断脚本
用于诊断Impala数据库连接问题
"""

import socket
import subprocess
import sys
import time
from sqlalchemy import text
from app.config.impala_config import IMPALA_CONNECTIONS

def test_network_connectivity():
    """测试网络连接"""
    print("=== 网络连接测试 ===")
    config = IMPALA_CONNECTIONS["default"]
    host = config['host']
    port = config['port']
    
    print(f"目标服务器: {host}:{port}")
    
    # 1. DNS解析测试
    try:
        ip = socket.gethostbyname(host)
        print(f"✓ DNS解析成功: {host} -> {ip}")
    except socket.gaierror as e:
        print(f"✗ DNS解析失败: {e}")
        return False
    
    # 2. 端口连接测试
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✓ 端口 {port} 连接成功")
            return True
        else:
            print(f"✗ 端口 {port} 连接失败 (错误代码: {result})")
            return False
    except Exception as e:
        print(f"✗ 连接测试异常: {e}")
        return False

def test_ping():
    """测试ping连接"""
    print("\n=== Ping测试 ===")
    config = IMPALA_CONNECTIONS["default"]
    host = config['host']
    
    try:
        result = subprocess.run(['ping', '-c', '3', host], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(f"✓ Ping {host} 成功")
            print(result.stdout)
        else:
            print(f"✗ Ping {host} 失败")
            print(result.stderr)
    except subprocess.TimeoutExpired:
        print(f"✗ Ping {host} 超时")
    except FileNotFoundError:
        print("⚠ ping命令不可用")

def test_telnet():
    """测试telnet连接"""
    print("\n=== Telnet测试 ===")
    config = IMPALA_CONNECTIONS["default"]
    host = config['host']
    port = config['port']
    
    try:
        result = subprocess.run(['telnet', host, str(port)], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✓ Telnet {host}:{port} 成功")
        else:
            print(f"✗ Telnet {host}:{port} 失败")
    except subprocess.TimeoutExpired:
        print(f"✓ Telnet {host}:{port} 连接成功（超时退出是正常的）")
    except FileNotFoundError:
        print("⚠ telnet命令不可用")

def test_impala_connection():
    """测试Impala连接"""
    print("\n=== Impala连接测试 ===")
    try:
        from app.services.impala.connection import get_impala_connection
        
        with get_impala_connection() as conn:
            # 使用text()函数包装SQL字符串
            result = conn.execute(text("SELECT 1 as test"))
            data = result.fetchone()
            if data:
                print("✓ Impala连接和查询测试成功")
                return True
            else:
                print("✗ Impala查询返回空结果")
                return False
    except Exception as e:
        print(f"✗ Impala连接测试失败: {e}")
        return False

def main():
    """主函数"""
    print("Impala连接诊断工具")
    print("=" * 50)
    
    # 显示配置信息
    config = IMPALA_CONNECTIONS["default"]
    print(f"配置信息:")
    print(f"  主机: {config['host']}")
    print(f"  端口: {config['port']}")
    print(f"  数据库: {config.get('database', 'N/A')}")
    print(f"  用户: {config.get('user', 'N/A')}")
    print(f"  认证机制: {config.get('auth_mechanism', 'N/A')}")
    print()
    
    # 执行各种测试
    network_ok = test_network_connectivity()
    test_ping()
    test_telnet()
    
    if network_ok:
        print("\n网络连接正常，尝试Impala连接...")
        impala_ok = test_impala_connection()
        
        if impala_ok:
            print("\n🎉 所有测试通过！Impala连接正常。")
        else:
            print("\n❌ Impala连接失败，但网络连接正常。")
            print("可能的问题:")
            print("1. Impala服务未启动")
            print("2. 认证配置错误")
            print("3. 数据库权限问题")
    else:
        print("\n❌ 网络连接失败。")
        print("可能的问题:")
        print("1. 网络不通")
        print("2. 防火墙阻止")
        print("3. 服务器不可达")
        print("4. DNS解析问题")

if __name__ == "__main__":
    main() 