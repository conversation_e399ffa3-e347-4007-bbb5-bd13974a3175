#!/usr/bin/env python3
"""
简单的Impala连接测试
"""
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.impala.connection import get_impala_connection
from sqlalchemy import text

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_simple_connection():
    """简单的连接测试"""
    print("=== 简单Impala连接测试 ===")
    
    try:
        with get_impala_connection() as conn:
            print("✅ 连接成功！")
            
            # 执行简单查询
            result = conn.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            print(f"✅ 查询成功！结果: {row[0]}")
            
            # 测试数据库查询
            result = conn.execute(text("SHOW DATABASES"))
            databases = result.fetchall()
            print(f"✅ 数据库列表查询成功！找到 {len(databases)} 个数据库")
            
            # 测试表查询
            result = conn.execute(text("SHOW TABLES IN bi_gb"))
            tables = result.fetchall()
            print(f"✅ 表列表查询成功！在bi_gb数据库中找到 {len(tables)} 个表")
            
            if tables:
                print(f"前5个表: {[table[0] for table in tables[:5]]}")
            
            return True
            
    except Exception as e:
        print(f"❌ 连接失败: {type(e).__name__}: {e}")
        logger.error("详细错误信息:", exc_info=True)
        return False

def test_data_query():
    """测试数据查询"""
    print("\n=== 数据查询测试 ===")
    
    try:
        with get_impala_connection() as conn:
            # 测试查询data_gb_location表
            query = text("""
                SELECT COUNT(*) as count 
                FROM bi_gb.data_gb_location 
                LIMIT 1
            """)
            
            result = conn.execute(query)
            row = result.fetchone()
            print(f"✅ data_gb_location表查询成功！总记录数: {row[0]}")
            
            # 测试查询特定设备
            test_rdid = "M03SCN2A170253075474"
            query = text("""
                SELECT COUNT(*) as count 
                FROM bi_gb.data_gb_location 
                WHERE p_rdid = :rdid
            """)
            
            result = conn.execute(query, {"rdid": test_rdid})
            row = result.fetchone()
            print(f"✅ 设备 {test_rdid} 查询成功！记录数: {row[0]}")
            
            return True
            
    except Exception as e:
        print(f"❌ 数据查询失败: {type(e).__name__}: {e}")
        logger.error("详细错误信息:", exc_info=True)
        return False

if __name__ == "__main__":
    print("开始Impala连接测试...")
    
    # 测试基本连接
    success1 = test_simple_connection()
    
    # 测试数据查询
    success2 = test_data_query()
    
    print(f"\n=== 测试结果 ===")
    print(f"基本连接: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"数据查询: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！Impala连接正常工作。")
    else:
        print("\n💡 建议:")
        print("1. 检查网络连接")
        print("2. 确认Impala服务状态")
        print("3. 验证用户名密码")
        print("4. 检查防火墙设置") 