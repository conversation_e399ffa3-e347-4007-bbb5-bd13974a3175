import pytest
import socket
from datetime import datetime
from app.services.impala.repository import get_latest_location
from app.services.impala.connection import get_impala_connection
from app.config.impala_config import IMPALA_CONNECTIONS

def can_connect_to_impala():
    """
    测试是否可以连接到Impala服务器
    """
    try:
        config = IMPALA_CONNECTIONS["default"]
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)  # 5秒超时
        result = sock.connect_ex((config['host'], config['port']))
        sock.close()
        return result == 0
    except Exception:
        return False

@pytest.mark.integration
@pytest.mark.skipif(not can_connect_to_impala(), reason="无法连接到Impala服务器，跳过集成测试")
def test_get_latest_location_from_impala():
    """
    集成测试：测试从Impala获取最新位置数据的功能。
    注意：此测试需要真实的Impala数据库连接，并假设数据库中存在指定的测试数据。
    """
    # 准备测试数据
    # 请将此RDID替换为您数据库中确定存在的设备ID
    test_rdid = "M03SCN2A170253075474" 
    today_str = datetime.now().strftime('%Y-%m-%d')

    try:
        # 使用上下文管理器获取数据库连接
        with get_impala_connection() as impala_conn:
            # 调用被测试的函数
            location_data = get_latest_location(impala_conn, test_rdid, today_str)

            # 断言验证结果
            # 1. 确保返回了数据
            assert location_data is not None, f"未能为 rdid '{test_rdid}' 找到任何位置数据"
            
            # 2. 确保返回的是一个字典
            assert isinstance(location_data, dict), "返回的数据类型不是字典"
            
            # 3. 确保字典中包含了关键字段
            assert "p_rdid" in location_data, "返回的数据中缺少 'p_rdid' 字段"
            
            # 4. 确保返回的rdid与我们查询的rdid一致
            assert location_data["p_rdid"] == test_rdid, "返回的 rdid 与查询的 rdid 不匹配"
            
            print(f"\n成功获取 rdid '{test_rdid}' 的位置数据: {location_data}")
    
    except Exception as e:
        pytest.fail(f"测试失败，无法连接到Impala或执行查询: {str(e)}")

@pytest.mark.integration
def test_impala_connection():
    """
    测试Impala连接是否可用
    """
    if not can_connect_to_impala():
        pytest.skip("无法连接到Impala服务器")
    
    try:
        with get_impala_connection() as conn:
            # 执行一个简单的查询来测试连接
            result = conn.execute("SELECT 1 as test")
            data = result.fetchone()
            assert data is not None, "无法执行简单查询"
            print("Impala连接测试成功")
    except Exception as e:
        pytest.fail(f"Impala连接测试失败: {str(e)}")

# 如何运行此测试:
# 1. 确保您的虚拟环境已激活。
# 2. 确保 apps/api/app/config/impala_config.py 中的连接信息正确。
# 3. 在终端中，进入 apps/api/ 目录。
# 4. 运行 pytest:
#    pytest app/tests/test_impala_service.py 
# 5. 如果只想运行连接测试:
#    pytest app/tests/test_impala_service.py::test_impala_connection -v -s 