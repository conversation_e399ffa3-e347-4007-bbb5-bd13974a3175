"""
飞书配置
"""

# 飞书应用配置
FEISHU_CONFIG = {
    "app_id": "cli_a7ce94ec90b09013",  # 替换为您的应用 ID
    "app_secret": "yZPqbhrS4oCUe2nN6eHElbPXD1Xlqcs3",    # 替换为您的应用密钥
    "verification_token": "0Dzax2gIFeBdSCyY8oLyHb4HRm7kze5h",  # 替换为您的验证令牌
    "encrypt_key": "nQ9k985sMYYulh43SqSqpgkHEMSAunbd",  # 替换为您的加密密钥
    "open_id": "ou_b66b39228fd067807b684ca60f9ef37f",  # 替换为您的 open_id
}

# 飞书多维表格配置 - 国内升级表单
FEISHU_BITABLE_CONFIG_AOS_UPGRADE_CN = {
    "app_token": "UaUywWlMviGrkWkwIQHcmQq7nxe",      # 国内升级表单 token
    "table_id": "tblstnHFhQY5WdmR",        # 国内升级表单 ID
    "view_id": "vewkopsQbm",          # 国内升级表单视图 ID
    "is_wiki": True,  # 是否为wiki下的多维表格
    "wiki_node_token": "UaUywWlMviGrkWkwIQHcmQq7nxe",  # wiki节点token（如有）
}

# 飞书多维表格配置 - 海外升级表单
FEISHU_BITABLE_CONFIG_AOS_UPGRADE_OS = {
    "app_token": "UaUywWlMviGrkWkwIQHcmQq7nxe",      # 海外升级表单 token
    "table_id": "tblJev3t8ZTKkTqp",        # 海外升级表单 ID
    "view_id": "vewkopsQbm",          # 海外升级表单视图 ID
    "is_wiki": True,  # 是否为wiki下的多维表格
    "wiki_node_token": "UaUywWlMviGrkWkwIQHcmQq7nxe",  # wiki节点token（如有）
}

# 说明：
# is_wiki: True 表示该多维表格属于wiki空间，需要通过wiki节点token获取真实表格token
#         False 表示为独立表格，可直接使用 table_id
# wiki_node_token: 仅在 is_wiki 为 True 时需要，表示wiki节点的token

# 飞书多维表格字段映射
FEISHU_FIELD_MAPPING = {
    "device_id": "设备ID",
    "name": "设备名称",
    "location": "位置",
    "latitude": "纬度",
    "longitude": "经度",
    "status": "状态",
    "last_query_time": "最后查询时间"
}

# 飞书多维表格配置 - Bug 跟踪表
FEISHU_BITABLE_CONFIG_BUGS_CN = {
    "app_token": "FXvNbgLLuabsRHsexVMcOEl0neb", # Bug 跟踪表 token - 国内
    "table_id": "tbldJHRVqJ6zDf4k",           # Bug 跟踪表 ID - 国内
    "view_id": "vewodZZ9hK",             # Bug 跟踪表视图 ID - 国内
}

# 飞书多维表格配置 - Bug 跟踪表 (海外)
FEISHU_BITABLE_CONFIG_BUGS_OS = {
    "app_token": "YOUR_OVERSEAS_APP_TOKEN", # 请替换为海外 Bug 跟踪表 token
    "table_id": "YOUR_OVERSEAS_TABLE_ID",   # 请替换为海外 Bug 跟踪表 ID
    "view_id": "YOUR_OVERSEAS_VIEW_ID",     # 请替换为海外 Bug 跟踪表视图 ID
}

# 飞书多维表格配置 - 人像统计
FEISHU_BITABLE_CONFIG_HUMAN_STATS = {
    "app_token": "D8zYbblwCa7ZYbs5XzCcesN5nTd",
    "table_id": "tblOrQn6SymPDDg4",
    "view_id": "vewmN4Z8ce",
    "is_wiki": False,
} 