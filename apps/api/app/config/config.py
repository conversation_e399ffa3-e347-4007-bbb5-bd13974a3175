from pydantic_settings import BaseSettings
from functools import lru_cache

class Settings(BaseSettings):
    # App settings
    APP_NAME: str = "CEMonitor API"
    DEBUG: bool = False
    
    # 默认统计查询天数
    DEFAULT_STATS_QUERY_DAYS: int = 30
    
    # LLM API密钥统一管理
    # OpenAI API密钥
    OPENAI_API_KEY: str = "********************************************************************************************************************************************************************"
    
    # Google API密钥
    GOOGLE_API_KEY: str = "AIzaSyAP34wsm9vG1knJf-e9neiMTYQ3iIE99cY"
    
    # Anthropic API密钥
    ANTHROPIC_API_KEY: str = ""

    class Config:
        env_file = ".env"

@lru_cache()
def get_settings():
    return Settings() 