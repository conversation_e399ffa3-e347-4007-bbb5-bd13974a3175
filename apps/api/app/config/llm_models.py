# -*- coding: utf-8 -*-
"""
LLM模型配置管理
支持GPT和Google Gemini的不同版本选择
"""

import os
from typing import Dict, Any, Optional
from enum import Enum
from .config import get_settings


class ModelProvider(Enum):
    """模型提供商枚举"""
    OPENAI = "openai"
    GOOGLE = "google"
    ANTHROPIC = "anthropic"


class ModelType(Enum):
    """模型类型枚举"""
    GPT_3_5_TURBO = "gpt-3.5-turbo"
    GPT_4 = "gpt-4"
    GPT_4_1 = "gpt-4.1"
    GPT_4_TURBO = "gpt-4-turbo"
    GPT_4O = "gpt-4o"
    GPT_4O_MINI = "gpt-4o-mini"
    GEMINI_PRO = "gemini-pro"
    GEMINI_PRO_VISION = "gemini-pro-vision"
    GEMINI_1_5_PRO = "gemini-1.5-pro"
    GEMINI_1_5_FLASH = "gemini-1.5-flash"
    GEMINI_1_5_PRO_LONG = "gemini-1.5-pro-long"
    GEMINI_2_5_PRO = "gemini-2.5-pro"
    GEMINI_2_5_FLASH = "gemini-2.5-flash"
    CLAUDE_3_OPUS = "claude-3-opus"
    CLAUDE_3_SONNET = "claude-3-sonnet"
    CLAUDE_3_HAIKU = "claude-3-haiku"
    QWEN2_5_32B = "qwq-2.5-32b"


class LLMModelConfig:
    """LLM模型配置类"""
    
    # 模型配置映射
    MODEL_CONFIGS = {
        # OpenAI GPT模型
        ModelType.GPT_3_5_TURBO.value: {
            "provider": ModelProvider.OPENAI.value,
            "api_key_env": "OPENAI_API_KEY",
            "base_url": "https://api.openai.com/v1",
            "model_name": "gpt-3.5-turbo",
            "max_tokens": 4000,
            "temperature": 0.7,
            "description": "GPT-3.5 Turbo - 快速且经济实惠",
            "cost_per_1k_tokens": 0.002,  # 美元
            "supports_streaming": True,
            "supports_vision": False,
            "enabled": False  # 是否启用此模型
        },
        ModelType.GPT_4.value: {
            "provider": ModelProvider.OPENAI.value,
            "api_key_env": "OPENAI_API_KEY",
            "base_url": "https://api.openai.com/v1",
            "model_name": "gpt-4",
            "max_tokens": 4000,
            "temperature": 0.7,
            "description": "GPT-4 - 强大的推理能力",
            "cost_per_1k_tokens": 0.03,
            "supports_streaming": True,
            "supports_vision": False,
            "enabled": False  # 默认禁用
        },
        ModelType.GPT_4_1.value: {
            "provider": ModelProvider.OPENAI.value,
            "api_key_env": "OPENAI_API_KEY",
            "base_url": "https://api.openai.com/v1",
            "model_name": "gpt-4.1",
            "max_tokens": 10000,
            "temperature": 0.7,
            "description": "GPT-4.1 - 最新版本，支持更长上下文和更强推理能力",
            "cost_per_1k_tokens": 0.015,
            "supports_streaming": True,
            "supports_vision": True,
            "enabled": True,  # 默认禁用
            "is_default": False  # 默认模型标签
        },
        ModelType.GPT_4_TURBO.value: {
            "provider": ModelProvider.OPENAI.value,
            "api_key_env": "OPENAI_API_KEY",
            "base_url": "https://api.openai.com/v1",
            "model_name": "gpt-4-turbo",
            "max_tokens": 51200,
            "temperature": 0.7,
            "description": "GPT-4 Turbo - 最新版本，支持更长上下文",
            "cost_per_1k_tokens": 0.01,
            "supports_streaming": True,
            "supports_vision": True,
            "enabled": False  # 默认禁用
        },
        ModelType.GPT_4O.value: {
            "provider": ModelProvider.OPENAI.value,
            "api_key_env": "OPENAI_API_KEY",
            "base_url": "https://api.openai.com/v1",
            "model_name": "gpt-4o",
            "max_tokens": 51200,
            "temperature": 0.7,
            "description": "GPT-4o - 最新多模态模型",
            "cost_per_1k_tokens": 0.005,
            "supports_streaming": True,
            "supports_vision": True,
            "enabled": False  # 默认禁用
        },
        ModelType.GPT_4O_MINI.value: {
            "provider": ModelProvider.OPENAI.value,
            "api_key_env": "OPENAI_API_KEY",
            "base_url": "https://api.openai.com/v1",
            "model_name": "gpt-4o-mini",
            "max_tokens": 51200,
            "temperature": 0.7,
            "description": "GPT-4o Mini - 经济实惠的GPT-4o版本",
            "cost_per_1k_tokens": 0.00015,
            "supports_streaming": True,
            "supports_vision": True,
            "enabled": False,  # 默认启用
            "is_default": True  # 默认模型标签
        },
        
        # Google Gemini模型
        ModelType.GEMINI_PRO.value: {
            "provider": ModelProvider.GOOGLE.value,
            "api_key_env": "GOOGLE_API_KEY",
            "base_url": "https://generativelanguage.googleapis.com/v1beta",
            "model_name": "gemini-pro",
            "max_tokens": 32768,
            "temperature": 0.7,
            "description": "Gemini Pro - Google的文本生成模型",
            "cost_per_1k_tokens": 0.0005,
            "supports_streaming": True,
            "supports_vision": False,
            "enabled": False  # 默认禁用
        },
        ModelType.GEMINI_PRO_VISION.value: {
            "provider": ModelProvider.GOOGLE.value,
            "api_key_env": "GOOGLE_API_KEY",
            "base_url": "https://generativelanguage.googleapis.com/v1beta",
            "model_name": "gemini-pro-vision",
            "max_tokens": 32768,
            "temperature": 0.7,
            "description": "Gemini Pro Vision - 支持图像理解",
            "cost_per_1k_tokens": 0.0005,
            "supports_streaming": True,
            "supports_vision": True,
            "enabled": False  # 默认禁用
        },
        ModelType.GEMINI_1_5_PRO.value: {
            "provider": ModelProvider.GOOGLE.value,
            "api_key_env": "GOOGLE_API_KEY",
            "base_url": "https://generativelanguage.googleapis.com/v1beta",
            "model_name": "gemini-1.5-pro",
            "max_tokens": 1048576,  # 1M tokens
            "temperature": 0.7,
            "description": "Gemini 1.5 Pro - 超长上下文支持",
            "cost_per_1k_tokens": 0.00375,
            "supports_streaming": True,
            "supports_vision": True,
            "enabled": False  # 默认禁用
        },
        ModelType.GEMINI_1_5_FLASH.value: {
            "provider": ModelProvider.GOOGLE.value,
            "api_key_env": "GOOGLE_API_KEY",
            "base_url": "https://generativelanguage.googleapis.com/v1beta",
            "model_name": "gemini-1.5-flash",
            "max_tokens": 1048576,  # 1M tokens
            "temperature": 0.7,
            "description": "Gemini 1.5 Flash - 快速且经济实惠",
            "cost_per_1k_tokens": 0.000075,
            "supports_streaming": True,
            "supports_vision": True,
            "enabled": False  # 默认启用
        },
        ModelType.GEMINI_1_5_PRO_LONG.value: {
            "provider": ModelProvider.GOOGLE.value,
            "api_key_env": "GOOGLE_API_KEY",
            "base_url": "https://generativelanguage.googleapis.com/v1beta",
            "model_name": "gemini-1.5-pro-long",
            "max_tokens": 2097152,  # 2M tokens - 超长上下文
            "temperature": 0.7,
            "description": "Gemini 1.5 Pro Long - 超长上下文支持，适合处理大型文档",
            "cost_per_1k_tokens": 0.0075,
            "supports_streaming": True,
            "supports_vision": True,
            "enabled": False  # 默认禁用
        },
        ModelType.GEMINI_2_5_PRO.value: {
            "provider": ModelProvider.GOOGLE.value,
            "api_key_env": "GOOGLE_API_KEY",
            "base_url": "https://generativelanguage.googleapis.com/v1beta",
            "model_name": "gemini-2.5-pro",
            "max_tokens": 2097152,  # 2M tokens - 超长上下文
            "temperature": 0.7,
            "description": "Gemini 2.5 Pro - 最新版本，强大的推理能力和超长上下文支持",
            "cost_per_1k_tokens": 0.005,
            "supports_streaming": True,
            "supports_vision": True,
            "enabled": True,  # 默认禁用
            "is_default": True  # 默认模型标签
        },
        ModelType.GEMINI_2_5_FLASH.value: {
            "provider": ModelProvider.GOOGLE.value,
            "api_key_env": "GOOGLE_API_KEY",
            "base_url": "https://generativelanguage.googleapis.com/v1beta",
            "model_name": "gemini-2.5-flash",
            "max_tokens": 2097152,  # 2M tokens - 超长上下文
            "temperature": 0.7,
            "description": "Gemini 2.5 Flash - 快速且经济实惠的2.5版本",
            "cost_per_1k_tokens": 0.0001,
            "supports_streaming": True,
            "supports_vision": True,
            "enabled": False  # 默认禁用
        },
        
        # Anthropic Claude模型
        ModelType.CLAUDE_3_OPUS.value: {
            "provider": ModelProvider.ANTHROPIC.value,
            "api_key_env": "ANTHROPIC_API_KEY",
            "base_url": "https://api.anthropic.com",
            "model_name": "claude-3-opus-20240229",
            "max_tokens": 4096,
            "temperature": 0.7,
            "description": "Claude 3 Opus - 最强大的Claude模型",
            "cost_per_1k_tokens": 0.015,
            "supports_streaming": True,
            "supports_vision": True,
            "enabled": False  # 默认禁用
        },
        ModelType.CLAUDE_3_SONNET.value: {
            "provider": ModelProvider.ANTHROPIC.value,
            "api_key_env": "ANTHROPIC_API_KEY",
            "base_url": "https://api.anthropic.com",
            "model_name": "claude-3-sonnet-20240229",
            "max_tokens": 4096,
            "temperature": 0.7,
            "description": "Claude 3 Sonnet - 平衡性能和成本",
            "cost_per_1k_tokens": 0.003,
            "supports_streaming": True,
            "supports_vision": True,
            "enabled": False  # 默认禁用
        },
        ModelType.CLAUDE_3_HAIKU.value: {
            "provider": ModelProvider.ANTHROPIC.value,
            "api_key_env": "ANTHROPIC_API_KEY",
            "base_url": "https://api.anthropic.com",
            "model_name": "claude-3-haiku-20240307",
            "max_tokens": 4096,
            "temperature": 0.7,
            "description": "Claude 3 Haiku - 快速且经济实惠",
            "cost_per_1k_tokens": 0.00025,
            "supports_streaming": True,
            "supports_vision": True,
            "enabled": False  # 默认启用
        }
    }
    
    @classmethod
    def _get_api_key_from_settings(cls, api_key_env: str) -> Optional[str]:
        """
        从统一配置中获取API密钥
        
        Args:
            api_key_env: API密钥环境变量名
            
        Returns:
            API密钥，如果不存在则返回None
        """
        settings = get_settings()
        
        # 根据环境变量名获取对应的API密钥
        if api_key_env == "OPENAI_API_KEY":
            return settings.OPENAI_API_KEY
        elif api_key_env == "GOOGLE_API_KEY":
            return settings.GOOGLE_API_KEY
        elif api_key_env == "ANTHROPIC_API_KEY":
            return settings.ANTHROPIC_API_KEY
        else:
            # 如果不在统一配置中，回退到环境变量
            return os.getenv(api_key_env)
    
    @classmethod
    def get_model_config(cls, model_name: str) -> Optional[Dict[str, Any]]:
        """
        获取模型配置
        
        Args:
            model_name: 模型名称
            
        Returns:
            模型配置字典，如果不存在则返回None
        """
        return cls.MODEL_CONFIGS.get(model_name)
    
    @classmethod
    def get_available_models(cls) -> Dict[str, Dict[str, Any]]:
        """
        获取所有可用模型（只返回启用的模型）
        
        Returns:
            所有启用的模型配置字典
        """
        return {
            model_name: config 
            for model_name, config in cls.MODEL_CONFIGS.items()
            if config.get("enabled", False)
        }
    
    @classmethod
    def get_all_models(cls) -> Dict[str, Dict[str, Any]]:
        """
        获取所有模型（包括未启用的）
        
        Returns:
            所有模型配置字典
        """
        return cls.MODEL_CONFIGS.copy()
    
    @classmethod
    def get_models_by_provider(cls, provider: str, enabled_only: bool = True) -> Dict[str, Dict[str, Any]]:
        """
        根据提供商获取模型列表
        
        Args:
            provider: 提供商名称
            enabled_only: 是否只返回启用的模型
            
        Returns:
            该提供商的所有模型配置
        """
        models = {
            model_name: config 
            for model_name, config in cls.MODEL_CONFIGS.items()
            if config["provider"] == provider
        }
        
        if enabled_only:
            return {
                model_name: config 
                for model_name, config in models.items()
                if config.get("enabled", False)
            }
        
        return models
    
    @classmethod
    def validate_model(cls, model_name: str) -> bool:
        """
        验证模型是否可用（需要同时满足启用状态和API密钥可用）
        
        Args:
            model_name: 模型名称
            
        Returns:
            模型是否可用
        """
        if model_name not in cls.MODEL_CONFIGS:
            return False
        
        config = cls.MODEL_CONFIGS[model_name]
        
        # 检查模型是否启用
        if not config.get("enabled", False):
            return False
        
        # 检查API密钥是否可用
        api_key = cls._get_api_key_from_settings(config["api_key_env"])
        return api_key is not None and len(api_key.strip()) > 0
    
    @classmethod
    def is_model_enabled(cls, model_name: str) -> bool:
        """
        检查模型是否启用（不考虑API密钥）
        
        Args:
            model_name: 模型名称
            
        Returns:
            模型是否启用
        """
        config = cls.get_model_config(model_name)
        if not config:
            return False
        
        return config.get("enabled", False)
    
    @classmethod
    def enable_model(cls, model_name: str) -> bool:
        """
        启用模型
        
        Args:
            model_name: 模型名称
            
        Returns:
            是否成功启用
        """
        if model_name not in cls.MODEL_CONFIGS:
            return False
        
        cls.MODEL_CONFIGS[model_name]["enabled"] = True
        return True
    
    @classmethod
    def disable_model(cls, model_name: str) -> bool:
        """
        禁用模型
        
        Args:
            model_name: 模型名称
            
        Returns:
            是否成功禁用
        """
        if model_name not in cls.MODEL_CONFIGS:
            return False
        
        cls.MODEL_CONFIGS[model_name]["enabled"] = False
        return True
    
    @classmethod
    def get_model_api_key(cls, model_name: str) -> Optional[str]:
        """
        获取模型的API密钥
        
        Args:
            model_name: 模型名称
            
        Returns:
            API密钥，如果不存在则返回None
        """
        config = cls.get_model_config(model_name)
        if not config:
            return None
        
        return cls._get_api_key_from_settings(config["api_key_env"])
    
    @classmethod
    def get_model_base_url(cls, model_name: str) -> Optional[str]:
        """
        获取模型的API基础URL
        
        Args:
            model_name: 模型名称
            
        Returns:
            API基础URL，如果不存在则返回None
        """
        config = cls.get_model_config(model_name)
        if not config:
            return None
        
        return config["base_url"]
    
    @classmethod
    def get_model_info(cls, model_name: str) -> Optional[Dict[str, Any]]:
        """
        获取模型信息（用于前端显示）
        
        Args:
            model_name: 模型名称
            
        Returns:
            模型信息字典
        """
        config = cls.get_model_config(model_name)
        if not config:
            return None
        
        return {
            "name": model_name,
            "provider": config["provider"],
            "description": config["description"],
            "max_tokens": config["max_tokens"],
            "temperature": config["temperature"],
            "cost_per_1k_tokens": config["cost_per_1k_tokens"],
            "supports_streaming": config["supports_streaming"],
            "supports_vision": config["supports_vision"],
            "enabled": config.get("enabled", False),
            "is_default": config.get("is_default", False),
            "is_available": cls.validate_model(model_name)
        }
    
    @classmethod
    def get_all_model_info(cls, enabled_only: bool = True) -> Dict[str, Dict[str, Any]]:
        """
        获取所有模型信息（用于前端显示）
        
        Args:
            enabled_only: 是否只返回启用的模型
            
        Returns:
            所有模型信息字典
        """
        models = cls.MODEL_CONFIGS.keys()
        
        if enabled_only:
            models = [
                model_name for model_name in models
                if cls.MODEL_CONFIGS[model_name].get("enabled", False)
            ]
        
        return {
            model_name: cls.get_model_info(model_name)
            for model_name in models
        }
    
    @classmethod
    def get_default_model(cls) -> Optional[str]:
        """
        获取默认模型名称
        
        Returns:
            默认模型名称，如果没有设置则返回None
        """
        for model_name, config in cls.MODEL_CONFIGS.items():
            if config.get("is_default", False):
                return model_name
        return None
    
    @classmethod
    def set_default_model(cls, model_name: str) -> bool:
        """
        设置默认模型
        
        Args:
            model_name: 要设置为默认的模型名称
            
        Returns:
            是否成功设置
        """
        if model_name not in cls.MODEL_CONFIGS:
            return False
        
        # 先清除所有模型的默认标记
        for config in cls.MODEL_CONFIGS.values():
            config["is_default"] = False
        
        # 设置新的默认模型
        cls.MODEL_CONFIGS[model_name]["is_default"] = True
        return True
    
    @classmethod
    def is_default_model(cls, model_name: str) -> bool:
        """
        检查模型是否为默认模型
        
        Args:
            model_name: 模型名称
            
        Returns:
            是否为默认模型
        """
        config = cls.get_model_config(model_name)
        if not config:
            return False
        
        return config.get("is_default", False)


# 默认模型配置
DEFAULT_MODEL = LLMModelConfig.get_default_model() or ModelType.GPT_4O_MINI.value

# 推荐的模型列表（按性能和成本排序）
RECOMMENDED_MODELS = [
    {
        "category": "经济实惠",
        "models": [
            ModelType.GPT_4O_MINI.value,
            ModelType.GEMINI_1_5_FLASH.value,
            ModelType.GEMINI_2_5_FLASH.value,
            ModelType.CLAUDE_3_HAIKU.value
        ]
    },
    {
        "category": "平衡性能",
        "models": [
            ModelType.GPT_4_TURBO.value,
            ModelType.GPT_4_1.value,
            ModelType.GEMINI_1_5_PRO.value,
            ModelType.GEMINI_2_5_PRO.value,
            ModelType.CLAUDE_3_SONNET.value
        ]
    },
    {
        "category": "长上下文",
        "models": [
            ModelType.GEMINI_1_5_PRO_LONG.value,
            ModelType.GEMINI_2_5_PRO.value,
            ModelType.GEMINI_2_5_FLASH.value,
            ModelType.GPT_4_TURBO.value,
            ModelType.GPT_4_1.value
        ]
    },
    {
        "category": "高性能",
        "models": [
            ModelType.GPT_4O.value,
            ModelType.GEMINI_2_5_PRO.value,
            ModelType.CLAUDE_3_OPUS.value
        ]
    }
] 