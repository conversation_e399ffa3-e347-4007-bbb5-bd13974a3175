# -*- coding: utf-8 -*-
"""
设备使用报告生成的Prompt模板配置
"""

from typing import Dict, List
from datetime import datetime


class ReportPrompts:
    """报告生成Prompt模板管理"""
    
    # 统一的数据源模板
    DATA_SOURCE_TEMPLATE = """

【机器人数据说明】
{data_description}

【机器人了解的详细数据】
{analysis_data}
"""

    # 基础系统提示词
    SYSTEM_PROMPT = """
你是一个专业的前台机器人数据分析师，擅长分析机器人的使用数据并生成有价值的商业报告。

【机器人主要功能】
- 访客接待登记（邀约码验证，访客信息确认，人脸录入）
- 找人/留言发消息（给公司员工发送消息）
- 参观讲解服务（分配讲解任务给其他机器人完成）
- 智能问答（咨询公司内的一些常用知识问答）
- 带路引领（通过导航带领用户带到指定的目的地）
- 多语言服务（提供跨语种的实时翻译服务）
- 人工服务（可能机器人未解决用户问题，发起找人工服务）

【分析要求】
- 基于真实数据进行分析，不要编造数据
- 提供具体可行的业务建议
- 重点关注业务价值和用户体验
- 重点: 虽然我要求好多指标, 但整体的报告内容数据不要重复出现,这样让人感觉到重复, 要突出重点, 不要让用户感觉到重复
- 使用Markdown格式
- 包含关键数据可视化（使用Mermaid图表）
- 请严格按照 Mermaid 的语法要求，严格使用下边示例进行生成, 生成以下不同类型的图表代码，并确保每个图表的结构、节点、连接和时间等都符合 Mermaid 的标准。示例如下:
-- 示例中没有标注[```mermaid] 就是不需要这个标识
-- 示例中没有标注[```] 就是不需要这个标识
-- timeline 不需要[```mermaid] 标识

### 饼图 pie
```mermaid
pie
    title 机器人接待访客数据统计
    "访客接待登记": 45
    "找人/留言发消息": 30
    "参观讲解服务": 20
    "智能问答": 15
    "带路引领": 10
    "多语言服务": 5
    "人工服务": 2
```

### 柱状图 graph

```mermaid
graph LR
    A[08:00-09:00<br/>13人] --> B[09:00-12:00<br/>45人]
    B --> C[13:00-14:00<br/>8人]
    C --> D[14:00-17:00<br/>52人]
    D --> E[17:00-18:00<br/>38人]
```

### 序列图 sequenceDiagram
```mermaid
sequenceDiagram
    Alice ->> Bob: Hello Bob, how are you?
    Bob-->>John: How about you John?
    Bob--x Alice: I am good thanks!
    Bob-x John: I am good thanks!
    Note right of John: Bob thinks a long<br/>long time, so long<br/>that the text does<br/>not fit on a row.

    Bob-->Alice: Checking with John...
    Alice->John: Yes... John, how are you?
```


### 折线图 xychart-beta
```mermaid
xychart-beta
    title "Sales Revenue"
    x-axis [jan, feb, mar, apr, may, jun, jul, aug, sep, oct, nov, dec]
    y-axis "Revenue (in $)" 4000 --> 11000
    bar [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]
    line [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]
```

### 流程图 journey

```mermaid
journey
    title My working day
    section Go to work
      Make tea: 5: Me
      Go upstairs: 3: Me
      Do work: 1: Me, Cat
    section Go home
      Go downstairs: 5: Me
      Sit down: 5: Me
```

### 甘特图 gantt

```mermaid
gantt
    title A Gantt Diagram
    dateFormat YYYY-MM-DD
    section Section
        A task          :a1, 2014-01-01, 30d
        Another task    :after a1, 20d
    section Another
        Task in Another :2014-01-12, 12d
        another task    :24d
```


### 时间线 timeline
```mermaid
timeline
    title History of Social Media Platform
    2002 : LinkedIn
    2004 : Facebook
         : Google
    2005 : YouTube
    2006 : Twitter
```

### 架构图 architecture-beta
```mermaid
architecture-beta
    group api(cloud)[API]

    service db(database)[Database] in api
    service disk1(disk)[Storage] in api
    service disk2(disk)[Storage] in api
    service server(server)[Server] in api

    db:L -- R:server
    disk1:T -- B:server
    disk2:T -- B:db
```

### gitGraph
```mermaid
gitGraph:
    commit "Ashish"
    branch newbranch
    checkout newbranch
    commit id:"1111"
    commit tag:"test"
    checkout main
    commit type: HIGHLIGHT
    commit
    merge newbranch
    commit
    branch b2
    commit
```

"""

    UNIFIED_PROMPT = """
这是我公司前台的机器人的当日使用数据报告。

【机制】
人靠近机器人时，人员会被动人脸识别（认识、不认识/男、女/年龄）。

【目标】
请你帮我生成一个每日工作报告，目标观看者为老板和运营人员.

我希望需要体现出来
- 基础使用数据、当天数据趋势、action数据统计
- 当日亮点事件和预警事件
- 当日满意服务案例和不满意的案例
- 一些新视角的观察
    - 如：最积极的用户，每日高峰，最早到达公司的员工，最晚离开的员工，大家最感兴趣的话题等等。
- 提出一些经营建议（短一点，讲具体的，比如优化哪些配置等等）




"""
    # 统一的报告生成提示词
    UNIFIED_PROMPT_V2 = """
请基于提供的机器人使用数据，生成一份完整的设备使用报告。报告应包含以下九个核心部分，请按顺序生成：

## 1. 基础使用数据概览

【分析内容】
- 总体使用情况统计
- 各功能模块使用频率
- 用户访问时间分布
- 对话轮次分析
- 与昨日数据对比

【输出要求】
- 重点突出核心指标
- 简洁明了，适合老板快速浏览
- 使用饼图展示功能使用分布

## 2. 数据趋势分析

【分析内容】
- 使用量趋势变化（与昨日、上周、上月对比）
- 用户行为模式变化
- 高峰时段分析
- 功能受欢迎度变化趋势
- 异常数据识别

【输出要求】
- 重点关注变化趋势和异常
- 使用时间线图表展示趋势
- 提供趋势解读和预测

## 3. 今日亮点事件

【分析内容】
- 成功服务案例
- 用户满意度高的交互
- 机器人表现出色的场景
- 有趣的用户互动
- 值得表扬的服务时刻

【输出要求】
- 选择3-5个最具代表性的亮点
- 详细描述事件场景
- 分析成功因素
- 正面激励的表达方式

## 4. 预警事件

【分析内容】
- 服务失败案例
- 用户不满意的交互
- 系统性能问题
- 功能异常情况
- 需要人工介入的场景

【输出要求】
- 按严重程度分类
- 提供问题根因分析
- 给出改进建议
- 标注紧急程度

## 5. 满意服务案例

【分析内容】
- 服务流程顺畅的案例
- 用户反馈积极的交互
- 问题解决效率高的场景
- 用户体验优秀的服务
- 值得复制推广的做法

【输出要求】
- 详细还原服务过程
- 分析成功关键因素
- 提炼最佳实践
- 可复制的服务标准

## 6. 服务改进案例

【分析内容】
- 服务不够理想的案例
- 用户体验待优化的场景
- 流程可以简化的环节
- 响应时间过长的情况
- 理解偏差的对话

【输出要求】
- 客观分析问题原因
- 提供具体改进方案
- 优化优先级排序
- 可操作的改进步骤

## 7. 新视角观察

【分析内容】
- 最积极的用户画像
- 每日使用高峰分析
- 最早到达公司的员工
- 最晚离开的员工
- 大家最感兴趣的话题
- 用户行为偏好分析
- 意想不到的数据发现

【输出要求】
- 提供新颖的数据视角
- 发现隐藏的使用模式
- 有趣且有价值的洞察
- 用数据讲故事

## 8. 经营改进建议

【分析内容】
- 配置优化建议
- 服务流程改进
- 功能使用优化
- 人员配置建议
- 设备部署优化
- 用户体验提升

【输出要求】
- 建议要具体可操作
- 优先级明确排序
- 预期效果评估
- 实施难度评级
- 成本效益分析

## 9. 明日重点工作

【分析内容】
- 紧急需要处理的问题
- 重点优化的功能
- 需要关注的时间段
- 用户服务重点
- 数据监控重点
- 团队协作事项

【输出要求】
- 任务优先级清晰
- 责任人建议明确
- 时间节点合理
- 可衡量的目标
- 风险预警提示

【整体要求】
- 每个部分都要有明确的标题和分隔
- 内容要连贯，避免重复
- 重点突出，数据准确
- 建议具体可操作
- 使用适当的图表增强可读性
"""

    # 多天数据分析专用提示词
    MULTI_DAY_PROMPT = """
    请基于提供的多天机器人使用数据，生成一份完整的设备使用趋势分析报告。报告应包含以下十个核心部分，请按顺序生成：

## 1. 多天数据概览

【分析内容】
- 分析期间总体使用情况
- 各action功能模块使用频率汇总
- 用户访问时间分布趋势
- 使用量变化趋势（日环比）

【输出要求】
- 突出期间整体表现
- 使用饼图展示功能使用分布
- 简洁明了，适合管理层快速浏览
- 标注数据覆盖天数

## 2. 趋势变化分析

【分析内容】
- 使用量变化趋势（日环比、周环比）
- 用户行为模式演变
- 高峰时段变化规律
- 功能受欢迎度变化趋势
- 异常数据识别和解释

【输出要求】
- 使用折线图展示趋势变化
- 重点关注拐点和异常
- 提供趋势解读和预测
- 标注关键变化节点


## 3. 期间亮点事件汇总

【分析内容】
- 期间最成功的服务案例
- 用户满意度最高的交互
- 机器人表现最出色的场景
- 有趣的用户互动记录
- 值得表扬的服务时刻

【输出要求】
- 按时间顺序排列
- 详细描述事件场景和影响
- 分析成功因素和可复制性

## 4. 期间预警事件分析

【分析内容】
- 服务失败案例汇总
- 用户不满意交互统计
- 系统性能问题记录
- 功能异常情况分析
- 需要人工介入的场景统计

【输出要求】
- 按严重程度和频率分类
- 提供问题根因分析
- 给出系统性改进建议
- 标注紧急程度和影响范围

## 5. 一些新视角的观察

【分析内容】
- 最积极的用户
- 高峰时段变化规律
- 最早到达公司的员工
- 最晚离开公司的员工 
- 大家最感兴趣的话题等等


【多天分析特殊要求】
- 重点关注数据趋势和变化规律
- 识别周期性模式和异常
- 提供基于历史数据的预测
- 分析长期运营效果
- 避免单日数据的简单累加
- 突出多天数据的独特价值
- 使用时间序列图表展示趋势
- 提供深度洞察而非表面统计
"""

    MULTI_DAY_PROMPT_V2 = """
请基于提供的多天机器人使用数据，生成一份完整的设备使用趋势分析报告。报告应包含以下十个核心部分，请按顺序生成：

## 1. 多天数据概览

【分析内容】
- 分析期间总体使用情况
- 各action功能模块使用频率汇总
- 用户访问时间分布趋势
- 使用量变化趋势（日环比）

【输出要求】
- 突出期间整体表现
- 使用饼图展示功能使用分布
- 简洁明了，适合管理层快速浏览
- 标注数据覆盖天数

## 2. 趋势变化分析

【分析内容】
- 使用量变化趋势（日环比、周环比）
- 用户行为模式演变
- 高峰时段变化规律
- 功能受欢迎度变化趋势
- 异常数据识别和解释

【输出要求】
- 使用折线图展示趋势变化
- 重点关注拐点和异常
- 提供趋势解读和预测
- 标注关键变化节点

## 3. 周期性模式识别

【分析内容】
- 工作日vs周末使用差异
- 每周使用高峰规律
- 月度使用周期特征
- 季节性使用模式
- 节假日影响分析

【输出要求】
- 使用时间线图表展示周期性
- 发现规律性使用模式
- 提供周期性洞察
- 预测未来使用趋势

## 4. 期间亮点事件汇总

【分析内容】
- 期间最成功的服务案例
- 用户满意度最高的交互
- 机器人表现最出色的场景
- 有趣的用户互动记录
- 值得表扬的服务时刻

【输出要求】
- 选择5-8个最具代表性的亮点
- 按时间顺序排列
- 详细描述事件场景和影响
- 分析成功因素和可复制性

## 5. 期间预警事件分析

【分析内容】
- 服务失败案例汇总
- 用户不满意交互统计
- 系统性能问题记录
- 功能异常情况分析
- 需要人工介入的场景统计

【输出要求】
- 按严重程度和频率分类
- 提供问题根因分析
- 给出系统性改进建议
- 标注紧急程度和影响范围

## 6. 用户行为深度分析

【分析内容】
- 最活跃用户画像分析
- 用户使用习惯变化
- 新用户vs老用户行为差异
- 用户满意度变化趋势
- 用户流失风险分析

【输出要求】
- 使用用户画像图表
- 发现用户行为规律
- 提供用户洞察
- 预测用户需求变化

## 7. 功能使用优化分析

【分析内容】
- 各功能使用效率分析
- 功能使用难度评估
- 用户反馈汇总分析
- 功能改进建议汇总
- 新功能需求识别

【输出要求】
- 使用功能热力图
- 提供功能优化优先级
- 分析功能使用瓶颈
- 建议功能改进方向

## 8. 运营效果评估

【分析内容】
- 运营目标达成情况
- 服务质量提升效果
- 用户满意度变化
- 运营成本效益分析
- 运营策略有效性评估

【输出要求】
- 使用KPI仪表板
- 提供量化评估结果
- 分析运营效果
- 建议运营优化方向

## 9. 长期经营建议

【分析内容】
- 基于趋势的长期规划建议
- 资源配置优化建议
- 服务流程改进建议
- 技术升级建议
- 团队建设建议

【输出要求】
- 建议要具体可操作
- 优先级明确排序
- 预期效果评估
- 实施时间规划
- 成本效益分析

## 10. 下阶段重点工作

【分析内容】
- 基于趋势预测的重点工作
- 需要持续关注的问题
- 新功能开发优先级
- 运营策略调整建议
- 风险预警和应对措施

【输出要求】
- 任务优先级清晰
- 时间节点合理
- 可衡量的目标
- 风险预警提示
- 成功标准明确

【多天分析特殊要求】
- 重点关注数据趋势和变化规律
- 识别周期性模式和异常
- 提供基于历史数据的预测
- 分析长期运营效果
- 避免单日数据的简单累加
- 突出多天数据的独特价值
- 使用时间序列图表展示趋势
- 提供深度洞察而非表面统计
"""

    # Mermaid格式优化专用提示词
    MERMAID_OPTIMIZE_PROMPT = """
你是一个专业的Mermaid图表格式优化专家，专门负责纠正和优化Mermaid图表代码的格式问题。

【你的任务】
分析提供的Mermaid图表代码，识别并修复格式错误，确保图表能够正确渲染。

【Mermaid语法规则】
1. **图表类型声明**：必须在第一行声明图表类型（如 pie, graph, timeline, xychart-beta 等）
2. **标题格式**：title 关键字后跟标题文本，用双引号包围
3. **节点格式**：
   - 饼图：使用 "标签": 数值 格式
   - 流程图：使用 A[文本] 或 A(文本) 格式
   - 时间线：使用 时间 : 事件 格式
4. **连接符**：
   - 流程图：使用 -->, ---, -.- 等
   - 序列图：使用 ->>, -->, -x 等
5. **特殊语法**：
   - 换行：使用 <br/> 标签
   - 注释：使用 %% 开头
   - 样式：使用 ::: 包围

【常见错误类型】
1. 缺少图表类型声明
2. 标题格式错误
3. 节点标识符格式错误
4. 连接符使用错误
5. 特殊字符未转义
6. 语法结构不完整

【输出要求】
1. 只返回修正后的Mermaid代码，不要包含任何解释文字
2. 确保代码可以直接复制使用
3. 保持原有的数据内容和逻辑
4. 修复所有语法错误
5. 优化格式以提高可读性
6. 所有日期格式都需使用中文进行标识, 比如 2025年7月19日 10点35分


【输出示例】
### 饼图 pie
```mermaid
pie
    title 机器人接待访客数据统计
    "访客接待登记": 45
    "找人/留言发消息": 30
    "参观讲解服务": 20
    "智能问答": 15
    "带路引领": 10
    "多语言服务": 5
    "人工服务": 2
```

### 柱状图 graph

```mermaid
graph LR
    A[08:00-09:00<br/>13人] --> B[09:00-12:00<br/>45人]
    B --> C[13:00-14:00<br/>8人]
    C --> D[14:00-17:00<br/>52人]
    D --> E[17:00-18:00<br/>38人]
```

### 序列图 sequenceDiagram
```mermaid
sequenceDiagram
    Alice ->> Bob: Hello Bob, how are you?
    Bob-->>John: How about you John?
    Bob--x Alice: I am good thanks!
    Bob-x John: I am good thanks!
    Note right of John: Bob thinks a long<br/>long time, so long<br/>that the text does<br/>not fit on a row.

    Bob-->Alice: Checking with John...
    Alice->John: Yes... John, how are you?
```


### 折线图 xychart-beta
```mermaid
xychart-beta
    title "Sales Revenue"
    x-axis [jan, feb, mar, apr, may, jun, jul, aug, sep, oct, nov, dec]
    y-axis "Revenue (in $)" 4000 --> 11000
    bar [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]
    line [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]
```

### 流程图 journey

```mermaid
journey
    title My working day
    section Go to work
      Make tea: 5: Me
      Go upstairs: 3: Me
      Do work: 1: Me, Cat
    section Go home
      Go downstairs: 5: Me
      Sit down: 5: Me
```

### 甘特图 gantt

```mermaid
gantt
    title A Gantt Diagram
    dateFormat YYYY-MM-DD
    section Section
        A task          :a1, 2014-01-01, 30d
        Another task    :after a1, 20d
    section Another
        Task in Another :2014-01-12, 12d
        another task    :24d
```


### 时间线 timeline
```mermaid
timeline
    title History of Social Media Platform
    2002 : LinkedIn
    2004 : Facebook
         : Google
    2005 : YouTube
    2006 : Twitter
```

### 架构图 architecture-beta
```mermaid
architecture-beta
    group api(cloud)[API]

    service db(database)[Database] in api
    service disk1(disk)[Storage] in api
    service disk2(disk)[Storage] in api
    service server(server)[Server] in api

    db:L -- R:server
    disk1:T -- B:server
    disk2:T -- B:db
```

### gitGraph
```mermaid
gitGraph:
    commit "Ashish"
    branch newbranch
    checkout newbranch
    commit id:"1111"
    commit tag:"test"
    checkout main
    commit type: HIGHLIGHT
    commit
    merge newbranch
    commit
    branch b2
    commit
```

现在请分析并优化提供的Mermaid代码：
"""

    @classmethod
    def get_unified_prompt(cls, data_info: dict, prompt_version: str = "UNIFIED_PROMPT_V2") -> str:
        """
        获取统一的完整提示词
        
        Args:
            data_info: 数据信息字典，包含：
                - target_date: 目标分析日期
                - data_sources: 数据源列表，每个包含 date 和 content
                - description: 数据描述
            prompt_version: Prompt版本 ("UNIFIED_PROMPT", "UNIFIED_PROMPT_V2" 或 "MULTI_DAY_PROMPT")
        
        Returns:
            完整的统一提示词
        """
        # 构建数据源部分
        data_description = data_info.get('description', '')
        data_sources = data_info.get('data_sources', [])
        
        # 格式化多天数据
        formatted_data = ""
        if data_sources:
            if len(data_sources) == 1:
                # 单天数据
                source = data_sources[0]
                formatted_data = f"【{source['date']} 数据】\n{source['content']}"
                if not data_description:
                    data_description = f"以下是 {source['date']} 的设备使用数据："
            else:
                # 多天数据
                formatted_data_parts = []
                dates = []
                for source in data_sources:
                    formatted_data_parts.append(f"【{source['date']} 数据】\n{source['content']}")
                    dates.append(source['date'])
                formatted_data = "\n\n".join(formatted_data_parts)
                if not data_description:
                    date_range = f"{dates[0]} 至 {dates[-1]}"
                    data_description = f"以下是 {date_range} 期间的设备使用数据（共{len(data_sources)}天）："
        
        # 组合完整提示词
        data_section = cls.DATA_SOURCE_TEMPLATE.format(
            data_description=data_description,
            analysis_data=formatted_data
        )
        
        # 根据版本选择对应的prompt
        if prompt_version == "UNIFIED_PROMPT":
            base_prompt = cls.UNIFIED_PROMPT
        elif prompt_version == "UNIFIED_PROMPT_V2":
            base_prompt = cls.UNIFIED_PROMPT_V2
        elif prompt_version == "MULTI_DAY_PROMPT":
            base_prompt = cls.MULTI_DAY_PROMPT
        elif prompt_version == "MERMAID_OPTIMIZE":
            base_prompt = cls.MERMAID_OPTIMIZE_PROMPT
        else:
            # 默认使用V2版本
            base_prompt = cls.UNIFIED_PROMPT_V2
        
        return base_prompt + data_section
    
    @classmethod
    def get_system_prompt(cls) -> str:
        """获取系统提示词"""
        return cls.SYSTEM_PROMPT
    
    @classmethod
    def create_single_day_data_info(cls, date: str, chat_data: str, description: str = None) -> dict:
        """
        创建单天数据信息字典
        
        Args:
            date: 日期
            chat_data: 聊天数据内容
            description: 数据描述
            
        Returns:
            数据信息字典
        """
        return {
            'target_date': date,
            'data_sources': [
                {
                    'date': date,
                    'content': chat_data
                }
            ],
            'description': description
        }
    
    @classmethod
    def create_multi_day_data_info(cls, data_sources: List[dict], description: str = None) -> dict:
        """
        创建多天数据信息字典
        
        Args:
            data_sources: 数据源列表，每个包含 date 和 content
            description: 数据描述
            
        Returns:
            数据信息字典
        """
        if not data_sources:
            raise ValueError("数据源列表不能为空")
        
        # 按日期排序
        sorted_sources = sorted(data_sources, key=lambda x: x['date'])
        
        return {
            'target_date': sorted_sources[-1]['date'],  # 最新日期作为目标日期
            'data_sources': sorted_sources,
            'description': description
        }
    
    @classmethod
    def get_auto_prompt(cls, data_info: dict) -> str:
        """
        根据数据自动选择合适的prompt版本
        
        Args:
            data_info: 数据信息字典
            
        Returns:
            完整的提示词
        """
        data_sources = data_info.get('data_sources', [])
        
        # 根据数据源数量自动选择prompt版本
        if len(data_sources) == 1:
            # 单天数据使用V2版本
            return cls.get_unified_prompt(data_info, "UNIFIED_PROMPT_V2")
        elif len(data_sources) >= 2:
            # 多天数据使用多天版本
            return cls.get_unified_prompt(data_info, "MULTI_DAY_PROMPT")
        else:
            raise ValueError("数据源不能为空")
    
    @classmethod
    def get_multi_day_prompt(cls, data_info: dict) -> str:
        """
        获取多天数据分析专用提示词
        
        Args:
            data_info: 数据信息字典
            
        Returns:
            多天分析专用提示词
        """
        return cls.get_unified_prompt(data_info, "MULTI_DAY_PROMPT") 