"""
Redis 配置
"""

REDIS_CONFIG = {
    "host": "localhost",  # Redis 服务器地址
    "port": 6379,        # Redis 端口
    "db": 0,            # 使用的数据库编号
    "password": None,    # Redis 密码，如果有的话
    "decode_responses": True  # 自动将响应解码为字符串
}

# 缓存过期时间配置（秒）
CACHE_EXPIRE = {
    "feishu_bitable_raw_data": 3600*24*30,  # 设备列表缓存 1 小时
    "feishu_bitable_processed_data": 300*24*30,  # 设备统计信息缓存 5 分钟
    "feishu_bug_records": 3600*24,  # bug 工单缓存 1 小时
} 