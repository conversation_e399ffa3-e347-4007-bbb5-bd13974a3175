from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from urllib.parse import quote_plus
from .config.mysql_config import MYSQL_DATABASES

# --- Connection for 'bigdata_cn' (default) ---
default_config = MYSQL_DATABASES["bigdata_cn"]
DATABASE_URL_DEFAULT = f"mysql+pymysql://{default_config['user']}:{default_config['password']}@{default_config['host']}:{default_config['port']}/{default_config['database']}"
engine_default = create_engine(
    DATABASE_URL_DEFAULT,
    echo=False,  # 设置为 True 可以看到 SQL 语句
    pool_pre_ping=True,
    pool_recycle=3600
)

# 创建会话工厂
SessionLocal_default = sessionmaker(autocommit=False, autoflush=False, bind=engine_default)

# 创建 Base 类
Base = declarative_base()

# 数据库依赖
def get_db():
    db = SessionLocal_default()
    try:
        yield db
    finally:
        db.close()

# --- Connection for 'agentos_cn' ---
agentos_config = MYSQL_DATABASES["agentos_cn"]
DATABASE_URL_AGENTOS = f"mysql+pymysql://{agentos_config['user']}:{agentos_config['password']}@{agentos_config['host']}:{agentos_config['port']}/{agentos_config['database']}"
engine_agentos = create_engine(
    DATABASE_URL_AGENTOS,
    echo=False,
    pool_pre_ping=True,
    pool_recycle=3600
)
SessionLocal_agentos = sessionmaker(autocommit=False, autoflush=False, bind=engine_agentos)
Base_agentos = declarative_base()

def get_agentos_db():
    db = SessionLocal_agentos()
    try:
        yield db
    finally:
        db.close()

# --- Connection for 'speech_ai_robot' ---
speech_ai_robot_config = MYSQL_DATABASES["speech_ai_robot"]
# URL编码密码中的特殊字符
encoded_password = quote_plus(speech_ai_robot_config['password'])
DATABASE_URL_SPEECH_AI_ROBOT = f"mysql+pymysql://{speech_ai_robot_config['user']}:{encoded_password}@{speech_ai_robot_config['host']}:{speech_ai_robot_config['port']}/{speech_ai_robot_config['database']}"
engine_speech_ai_robot = create_engine(
    DATABASE_URL_SPEECH_AI_ROBOT,
    echo=False,
    pool_pre_ping=True,
    pool_recycle=3600
)
SessionLocal_speech_ai_robot = sessionmaker(autocommit=False, autoflush=False, bind=engine_speech_ai_robot)
Base_speech_ai_robot = declarative_base()

def get_speech_ai_robot_db():
    db = SessionLocal_speech_ai_robot()
    try:
        yield db
    finally:
        db.close() 