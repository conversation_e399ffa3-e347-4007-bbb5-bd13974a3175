# 设备报告API集成指南

## 概述

本文档详细说明了设备使用报告生成API的集成方法，包括SSE协议的使用、数据格式、错误处理等内容。

## API基础信息

- **基础URL**: `/api/v1/device-reports`
- **协议**: HTTP/HTTPS + Server-Sent Events (SSE)
- **认证**: 暂无（根据实际需求配置）
- **内容类型**: `text/event-stream` (SSE接口), `application/json` (其他接口)

## 功能架构

### 报告生成流程

```mermaid
graph TD
    A[Web端发起请求] --> B[获取报告概览]
    B --> C[检查数据可用性]
    C --> D{数据是否存在?}
    D -->|是| E[选择批次生成]
    D -->|否| F[提示无数据]
    E --> G[建立SSE连接]
    G --> H[流式接收报告内容]
    H --> I[渲染Markdown+Mermaid]
    I --> J[显示完整报告]
```

### 报告分批次结构

| 批次 | 名称 | 包含阶段 | 用途 |
|------|------|----------|------|
| 1 | 基础数据与趋势 | 基础使用数据概览、数据趋势分析、今日亮点事件 | 快速了解核心指标 |
| 2 | 事件分析与案例 | 预警事件、满意服务案例、服务改进案例 | 深入分析问题和优秀案例 |
| 3 | 洞察与建议 | 新视角观察、经营改进建议、明日重点工作 | 获得行动指导 |

## API接口详情

### 1. 获取报告概览

**接口**: `GET /{enterprise_id}/{device_id}/overview`

**参数**:
- `enterprise_id` (path): 企业ID
- `device_id` (path): 设备ID  
- `date` (query): 日期，格式：YYYY-MM-DD

**响应示例**:
```json
{
  "enterprise_id": "orion.ovs.entprise.4498860269",
  "device_id": "MC1BCNC016021348B693",
  "date": "2025-07-15",
  "data_exists": true,
  "total_batches": 3,
  "batches": {
    "1": {
      "name": "基础数据与趋势",
      "stages": [
        {
          "id": "stage_1",
          "name": "基础使用数据概览",
          "description": "Basic Usage Overview"
        },
        {
          "id": "stage_2", 
          "name": "数据趋势分析",
          "description": "Data Trend Analysis"
        },
        {
          "id": "stage_3",
          "name": "今日亮点事件", 
          "description": "Today's Highlight Events"
        }
      ]
    },
    "2": {
      "name": "事件分析与案例",
      "stages": [...]
    },
    "3": {
      "name": "洞察与建议", 
      "stages": [...]
    }
  },
  "generated_at": "2025-01-15T10:30:00"
}
```

### 2. 获取可用数据日期

**接口**: `GET /{enterprise_id}/{device_id}/available-dates`

**参数**:
- `enterprise_id` (path): 企业ID
- `device_id` (path): 设备ID

**响应示例**:
```json
{
  "enterprise_id": "orion.ovs.entprise.4498860269",
  "device_id": "MC1BCNC016021348B693", 
  "available_dates": [
    "2025-07-15",
    "2025-07-14", 
    "2025-07-13"
  ],
  "total_count": 3
}
```

### 3. 生成设备报告 (SSE接口)

**接口**: `GET /{enterprise_id}/{device_id}/generate`

**参数**:
- `enterprise_id` (path): 企业ID
- `device_id` (path): 设备ID
- `date` (query): 日期，格式：YYYY-MM-DD
- `batch` (query): 批次ID，取值：1-3

**SSE事件类型**:

#### 开始事件
```
event: start
data: {
  "enterprise_id": "orion.ovs.entprise.4498860269",
  "device_id": "MC1BCNC016021348B693",
  "date": "2025-07-15",
  "batch": 1,
  "timestamp": "2025-01-15T10:30:00"
}
```

#### 内容事件  
```
event: content
data: {
  "content": "# 📊 基础使用数据概览\n\n## 总体使用情况\n今日设备共服务用户..."
}
```

#### 完成事件
```
event: complete
data: {
  "batch": 1,
  "timestamp": "2025-01-15T10:35:00", 
  "message": "批次 1 生成完成"
}
```

#### 错误事件
```
event: error
data: {
  "error": "生成报告时发生错误: 具体错误信息"
}
```

### 4. 获取阶段信息

**接口**: `GET /stages-info`

**响应示例**:
```json
{
  "total_stages": 9,
  "total_batches": 3,
  "batches": {
    "1": {
      "name": "基础数据与趋势",
      "description": "包含基础使用数据概览、数据趋势分析、今日亮点事件",
      "stages": [...]
    }
  }
}
```

### 5. 健康检查

**接口**: `GET /health`

**响应示例**:
```json
{
  "status": "healthy",
  "service": "device-reports", 
  "timestamp": "2025-01-15T10:30:00",
  "version": "1.0.0"
}
```

## Web端集成代码示例

### 1. 基础页面结构

```html
<!DOCTYPE html>
<html>
<head>
    <title>设备报告</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
    <div id="app">
        <div id="overview"></div>
        <div id="batch-selector"></div>
        <div id="report-content"></div>
        <div id="loading"></div>
    </div>
</body>
</html>
```

### 2. JavaScript集成代码

```javascript
class DeviceReportClient {
    constructor(baseURL = '/api/v1/device-reports') {
        this.baseURL = baseURL;
        this.currentEventSource = null;
    }

    // 获取报告概览
    async getReportOverview(enterpriseId, deviceId, date) {
        const response = await fetch(
            `${this.baseURL}/${enterpriseId}/${deviceId}/overview?date=${date}`
        );
        
        if (!response.ok) {
            throw new Error(`获取概览失败: ${response.statusText}`);
        }
        
        return await response.json();
    }

    // 获取可用日期
    async getAvailableDates(enterpriseId, deviceId) {
        const response = await fetch(
            `${this.baseURL}/${enterpriseId}/${deviceId}/available-dates`
        );
        
        if (!response.ok) {
            throw new Error(`获取日期失败: ${response.statusText}`);
        }
        
        return await response.json();
    }

    // 生成报告 (SSE)
    generateReport(enterpriseId, deviceId, date, batch, callbacks = {}) {
        // 关闭之前的连接
        if (this.currentEventSource) {
            this.currentEventSource.close();
        }

        const url = `${this.baseURL}/${enterpriseId}/${deviceId}/generate?date=${date}&batch=${batch}`;
        this.currentEventSource = new EventSource(url);

        // 开始事件
        this.currentEventSource.addEventListener('start', (event) => {
            const data = JSON.parse(event.data);
            if (callbacks.onStart) {
                callbacks.onStart(data);
            }
        });

        // 内容事件
        this.currentEventSource.addEventListener('content', (event) => {
            const data = JSON.parse(event.data);
            if (callbacks.onContent) {
                callbacks.onContent(data.content);
            }
        });

        // 完成事件
        this.currentEventSource.addEventListener('complete', (event) => {
            const data = JSON.parse(event.data);
            if (callbacks.onComplete) {
                callbacks.onComplete(data);
            }
            this.currentEventSource.close();
        });

        // 错误事件
        this.currentEventSource.addEventListener('error', (event) => {
            const data = JSON.parse(event.data);
            if (callbacks.onError) {
                callbacks.onError(data.error);
            }
            this.currentEventSource.close();
        });

        // 连接错误
        this.currentEventSource.onerror = (error) => {
            console.error('SSE连接错误:', error);
            if (callbacks.onConnectionError) {
                callbacks.onConnectionError(error);
            }
        };

        return this.currentEventSource;
    }

    // 关闭连接
    close() {
        if (this.currentEventSource) {
            this.currentEventSource.close();
            this.currentEventSource = null;
        }
    }
}

// 使用示例
const reportClient = new DeviceReportClient();

// 初始化页面
async function initReportPage(enterpriseId, deviceId, date) {
    try {
        // 1. 获取概览信息
        const overview = await reportClient.getReportOverview(enterpriseId, deviceId, date);
        renderOverview(overview);

        // 2. 渲染批次选择器
        renderBatchSelector(overview.batches);

        // 3. 检查数据可用性
        if (!overview.data_exists) {
            showMessage('该日期暂无数据', 'warning');
            return;
        }

    } catch (error) {
        console.error('初始化失败:', error);
        showMessage('加载失败: ' + error.message, 'error');
    }
}

// 生成报告
function generateBatchReport(enterpriseId, deviceId, date, batch) {
    const reportContent = document.getElementById('report-content');
    const loading = document.getElementById('loading');
    
    // 清空之前的内容
    reportContent.innerHTML = '';
    loading.style.display = 'block';

    reportClient.generateReport(enterpriseId, deviceId, date, batch, {
        onStart: (data) => {
            console.log('开始生成报告:', data);
            showMessage(`开始生成批次 ${data.batch} 报告`, 'info');
        },

        onContent: (content) => {
            // 追加内容到报告区域
            appendMarkdownContent(reportContent, content);
        },

        onComplete: (data) => {
            console.log('报告生成完成:', data);
            loading.style.display = 'none';
            showMessage(`批次 ${data.batch} 生成完成`, 'success');
            
            // 重新渲染Mermaid图表
            mermaid.init(undefined, reportContent.querySelectorAll('.mermaid'));
        },

        onError: (error) => {
            console.error('生成报告错误:', error);
            loading.style.display = 'none';
            showMessage('生成报告失败: ' + error, 'error');
        },

        onConnectionError: (error) => {
            console.error('连接错误:', error);
            loading.style.display = 'none';
            showMessage('连接失败，请检查网络', 'error');
        }
    });
}

// 渲染Markdown内容
function appendMarkdownContent(container, markdownText) {
    const htmlContent = marked.parse(markdownText);
    
    // 创建临时元素
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    
    // 将内容追加到容器
    while (tempDiv.firstChild) {
        container.appendChild(tempDiv.firstChild);
    }
}

// 渲染概览信息
function renderOverview(overview) {
    const overviewElement = document.getElementById('overview');
    overviewElement.innerHTML = `
        <div class="overview-card">
            <h2>设备报告概览</h2>
            <p><strong>企业ID:</strong> ${overview.enterprise_id}</p>
            <p><strong>设备ID:</strong> ${overview.device_id}</p>
            <p><strong>日期:</strong> ${overview.date}</p>
            <p><strong>数据状态:</strong> ${overview.data_exists ? '✅ 有数据' : '❌ 无数据'}</p>
            <p><strong>总批次:</strong> ${overview.total_batches}</p>
        </div>
    `;
}

// 渲染批次选择器
function renderBatchSelector(batches) {
    const selectorElement = document.getElementById('batch-selector');
    let html = '<div class="batch-selector"><h3>选择报告批次:</h3>';
    
    for (const [batchId, batch] of Object.entries(batches)) {
        html += `
            <button class="batch-btn" onclick="generateBatchReport('${enterpriseId}', '${deviceId}', '${date}', ${batchId})">
                批次 ${batchId}: ${batch.name}
                <small>${batch.description}</small>
            </button>
        `;
    }
    
    html += '</div>';
    selectorElement.innerHTML = html;
}

// 显示消息
function showMessage(message, type = 'info') {
    // 实现消息显示逻辑
    console.log(`[${type.toUpperCase()}] ${message}`);
}
```

### 3. CSS样式建议

```css
.overview-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.batch-selector {
    margin-bottom: 20px;
}

.batch-btn {
    display: block;
    width: 100%;
    padding: 15px;
    margin: 10px 0;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    text-align: left;
}

.batch-btn:hover {
    background: #0056b3;
}

.batch-btn small {
    display: block;
    opacity: 0.8;
    font-size: 0.9em;
}

#report-content {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    min-height: 400px;
}

#loading {
    text-align: center;
    padding: 20px;
    display: none;
}

/* Markdown样式 */
#report-content h1 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

#report-content h2 {
    color: #34495e;
    border-left: 4px solid #3498db;
    padding-left: 15px;
}

#report-content .mermaid {
    text-align: center;
    margin: 20px 0;
}
```

## 错误处理

### 常见错误码

| 状态码 | 错误类型 | 处理建议 |
|--------|----------|----------|
| 404 | 数据文件不存在 | 提示用户选择其他日期 |
| 400 | 参数错误 | 检查日期格式和批次范围 |
| 500 | 服务器内部错误 | 重试或联系技术支持 |

### 错误处理示例

```javascript
// 错误重试机制
async function retryRequest(requestFn, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await requestFn();
        } catch (error) {
            if (i === maxRetries - 1) throw error;
            console.log(`请求失败，第 ${i + 1} 次重试...`);
            await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        }
    }
}

// 使用示例
try {
    const overview = await retryRequest(() => 
        reportClient.getReportOverview(enterpriseId, deviceId, date)
    );
} catch (error) {
    showMessage('多次尝试后仍然失败: ' + error.message, 'error');
}
```

## 最佳实践

### 1. 性能优化
- 使用防抖处理用户快速点击
- 合理设置SSE连接超时
- 大量内容时使用虚拟滚动

### 2. 用户体验
- 显示生成进度
- 支持批次切换
- 提供内容搜索功能
- 支持报告导出

### 3. 安全考虑
- 验证用户权限
- 过滤敏感数据
- 限制请求频率

### 4. 监控和日志
- 记录API调用次数
- 监控生成时间
- 用户行为分析

## 扩展功能

### 1. 报告缓存
```javascript
class ReportCache {
    constructor(maxSize = 10) {
        this.cache = new Map();
        this.maxSize = maxSize;
    }

    getKey(enterpriseId, deviceId, date, batch) {
        return `${enterpriseId}-${deviceId}-${date}-${batch}`;
    }

    get(enterpriseId, deviceId, date, batch) {
        const key = this.getKey(enterpriseId, deviceId, date, batch);
        return this.cache.get(key);
    }

    set(enterpriseId, deviceId, date, batch, content) {
        const key = this.getKey(enterpriseId, deviceId, date, batch);
        
        if (this.cache.size >= this.maxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        
        this.cache.set(key, content);
    }
}
```

### 2. 报告分享
```javascript
function shareReport(content) {
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `设备报告-${new Date().toISOString().split('T')[0]}.md`;
    a.click();
    
    URL.revokeObjectURL(url);
}
```

### 3. 实时通知
```javascript
// 支持WebSocket通知报告生成状态
function setupReportNotifications() {
    const ws = new WebSocket('ws://localhost:8000/ws/report-notifications');
    
    ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'report_ready') {
            showNotification(`${data.device_id} 的报告已生成完成`);
        }
    };
}
```

## 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0.0 | 2025-01-15 | 初始版本，支持SSE流式生成 |

## 联系信息

- **技术支持**: [技术团队邮箱]
- **文档更新**: [文档维护人员]
- **API版本**: v1.0.0 