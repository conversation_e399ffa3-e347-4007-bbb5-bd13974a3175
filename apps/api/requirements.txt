fastapi>=0.109.0
uvicorn>=0.27.0
sqlalchemy>=2.0.0
pymysql>=1.1.0
pydantic>=2.6.0
pydantic-settings>=2.1.0
python-jose[cryptography]>=3.3.0
httpx>=0.26.0
python-multipart>=0.0.9
redis==5.0.1
celery>=5.3.0
passlib==1.7.4
alembic==1.13.1
lark-oapi==1.0.0
openai>=1.50.0
python-dotenv==1.0.1 
pytest==8.0.0
pytest-mock==3.12.0
pymongo>=4.13.2
impyla>=0.17.0
watchgod>=0.8.2
jieba
rich
pandas
any
matplotlib
seaborn
numpy
wordcloud
pyecharts
snapshot_selenium
# 新增依赖
sse-starlette==1.8.2
asyncio-throttle==1.0.2
jinja2>=3.1.0

# LLM服务依赖
google-generativeai>=0.3.0  # Google Gemini
anthropic>=0.7.0  # Anthropic Claude