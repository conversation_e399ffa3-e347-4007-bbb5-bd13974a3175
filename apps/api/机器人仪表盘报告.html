<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts-wordcloud.min.js"></script>

    
</head>
<body >
    <div id="46ed930b7e454a748e53775bf72c7814" class="chart-container" style="width:1200px; height:1800px; "></div>
    <script>
        var chart_46ed930b7e454a748e53775bf72c7814 = echarts.init(
            document.getElementById('46ed930b7e454a748e53775bf72c7814'), 'light', {renderer: 'canvas'});
        var option_46ed930b7e454a748e53775bf72c7814 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "series": [
        {
            "type": "bar",
            "name": "\u4f1a\u8bdd\u6570",
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "legendHoverLink": true,
            "data": [
                1,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                8,
                15,
                0,
                0,
                0,
                21,
                27,
                35,
                37,
                7,
                0,
                2,
                3,
                11,
                8,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            }
        },
        {
            "type": "pie",
            "colorBy": "data",
            "legendHoverLink": true,
            "selectedMode": false,
            "selectedOffset": 10,
            "clockwise": true,
            "startAngle": 90,
            "minAngle": 0,
            "minShowLabelAngle": 0,
            "avoidLabelOverlap": true,
            "stillShowZeroSum": true,
            "percentPrecision": 2,
            "showEmptyCircle": true,
            "emptyCircleStyle": {
                "color": "lightgray",
                "borderColor": "#000",
                "borderWidth": 0,
                "borderType": "solid",
                "borderDashOffset": 0,
                "borderCap": "butt",
                "borderJoin": "bevel",
                "borderMiterLimit": 10,
                "opacity": 1
            },
            "data": [
                {
                    "name": "greeting",
                    "value": 418
                },
                {
                    "name": "navigation",
                    "value": 22
                },
                {
                    "name": "search",
                    "value": 2
                },
                {
                    "name": "interview",
                    "value": 0
                },
                {
                    "name": "visit",
                    "value": 24
                },
                {
                    "name": "system",
                    "value": 7
                },
                {
                    "name": "other",
                    "value": 100
                }
            ],
            "radius": [
                "40%",
                "70%"
            ],
            "center": [
                "50%",
                "50%"
            ],
            "label": {
                "show": true,
                "margin": 8,
                "formatter": "{b}: {d}%",
                "valueAnimation": false
            },
            "labelLine": {
                "show": true,
                "showAbove": false,
                "length": 15,
                "length2": 15,
                "smooth": false,
                "minTurnAngle": 90,
                "maxSurfaceAngle": 90
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            },
            "xAxisIndex": 1,
            "yAxisIndex": 1
        },
        {
            "type": "bar",
            "name": "\u6b21\u6570",
            "xAxisIndex": 2,
            "yAxisIndex": 2,
            "legendHoverLink": true,
            "data": [
                8,
                4,
                3,
                0,
                19
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            }
        },
        {
            "type": "bar",
            "name": "\u6267\u884c\u6b21\u6570",
            "xAxisIndex": 3,
            "yAxisIndex": 3,
            "legendHoverLink": true,
            "data": [
                3,
                2,
                1,
                1,
                1,
                2,
                1,
                1,
                2
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            }
        },
        {
            "type": "bar",
            "name": "\u4f1a\u8bdd\u6570",
            "xAxisIndex": 4,
            "yAxisIndex": 4,
            "legendHoverLink": true,
            "data": [
                89,
                37,
                32,
                10,
                3,
                3,
                0
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            }
        },
        {
            "type": "wordCloud",
            "shape": "circle",
            "rotationRange": [
                -90,
                90
            ],
            "rotationStep": 45,
            "girdSize": 20,
            "sizeRange": [
                20,
                80
            ],
            "data": [
                {
                    "name": "\u6211\u6765",
                    "value": 6,
                    "textStyle": {
                        "color": "rgb(47,51,125)"
                    }
                },
                {
                    "name": "\u9762\u8bd5",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(55,134,82)"
                    }
                },
                {
                    "name": "\u3002",
                    "value": 41,
                    "textStyle": {
                        "color": "rgb(103,78,114)"
                    }
                },
                {
                    "name": "\u63a5\u4e0b\u6765",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(65,122,55)"
                    }
                },
                {
                    "name": "\u53bb",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(48,52,77)"
                    }
                },
                {
                    "name": "\u524d\u53f0",
                    "value": 2,
                    "textStyle": {
                        "color": "rgb(112,87,2)"
                    }
                },
                {
                    "name": "\u63a5\u5f85\u70b9",
                    "value": 2,
                    "textStyle": {
                        "color": "rgb(5,99,111)"
                    }
                },
                {
                    "name": "\u6ca1\u6709",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(147,120,83)"
                    }
                },
                {
                    "name": "\u662f",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(27,51,99)"
                    }
                },
                {
                    "name": "\u5427",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(108,92,54)"
                    }
                },
                {
                    "name": "\uff1f",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(116,97,50)"
                    }
                },
                {
                    "name": "\u4ece",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(130,41,59)"
                    }
                },
                {
                    "name": "\u8fd9\u91cc",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(151,34,67)"
                    }
                },
                {
                    "name": "\u4f60",
                    "value": 5,
                    "textStyle": {
                        "color": "rgb(33,1,86)"
                    }
                },
                {
                    "name": "\u8bf4",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(65,35,35)"
                    }
                },
                {
                    "name": "\u7684\u8bdd",
                    "value": 2,
                    "textStyle": {
                        "color": "rgb(115,109,64)"
                    }
                },
                {
                    "name": "\uff0c",
                    "value": 11,
                    "textStyle": {
                        "color": "rgb(81,102,26)"
                    }
                },
                {
                    "name": "\u5982\u679c",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(77,82,139)"
                    }
                },
                {
                    "name": "\u5728",
                    "value": 2,
                    "textStyle": {
                        "color": "rgb(5,115,91)"
                    }
                },
                {
                    "name": "\u6709",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(75,110,4)"
                    }
                },
                {
                    "name": "\u663e\u793a",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(77,17,109)"
                    }
                },
                {
                    "name": "\u6240\u4ee5",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(38,8,54)"
                    }
                },
                {
                    "name": "\u5c31",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(60,146,141)"
                    }
                },
                {
                    "name": "\u8fd9\u4e2a",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(153,13,138)"
                    }
                },
                {
                    "name": "...",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(36,5,126)"
                    }
                },
                {
                    "name": "\u9009\u62e9",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(93,117,145)"
                    }
                },
                {
                    "name": "\u90a3",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(65,114,64)"
                    }
                },
                {
                    "name": "\u5c31\u662f",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(142,116,148)"
                    }
                },
                {
                    "name": "\u4e0d",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(83,36,28)"
                    }
                },
                {
                    "name": "\u5c5e\u4e8e",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(157,109,65)"
                    }
                },
                {
                    "name": "\u4e3b\u8981",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(141,77,66)"
                    }
                },
                {
                    "name": "\u90a3\u79cd",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(41,73,81)"
                    }
                },
                {
                    "name": "\u5403",
                    "value": 2,
                    "textStyle": {
                        "color": "rgb(160,108,26)"
                    }
                },
                {
                    "name": "\u5e0c\u671b",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(76,29,128)"
                    }
                },
                {
                    "name": "\u4e86",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(98,86,68)"
                    }
                },
                {
                    "name": "\u518d",
                    "value": 2,
                    "textStyle": {
                        "color": "rgb(119,0,115)"
                    }
                },
                {
                    "name": "\u770b",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(150,140,141)"
                    }
                },
                {
                    "name": "\u4e0b",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(126,91,41)"
                    }
                },
                {
                    "name": "\u4e00\u6b21",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(3,38,99)"
                    }
                },
                {
                    "name": "\u4e0b\u6b21",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(96,90,80)"
                    }
                },
                {
                    "name": "\u51fa\u73b0",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(50,27,132)"
                    }
                },
                {
                    "name": "\u8fd9\u79cd",
                    "value": 2,
                    "textStyle": {
                        "color": "rgb(89,146,77)"
                    }
                },
                {
                    "name": "\u5176\u5b9e",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(130,24,12)"
                    }
                },
                {
                    "name": "\u6211",
                    "value": 7,
                    "textStyle": {
                        "color": "rgb(136,144,101)"
                    }
                },
                {
                    "name": "\u5f53\u65f6",
                    "value": 2,
                    "textStyle": {
                        "color": "rgb(5,98,125)"
                    }
                },
                {
                    "name": "\u5df2\u7ecf",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(15,60,97)"
                    }
                },
                {
                    "name": "\u770b\u5230",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(23,25,157)"
                    }
                },
                {
                    "name": "\u62bd\u70df",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(108,107,83)"
                    }
                },
                {
                    "name": "\u88c5",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(16,38,83)"
                    }
                },
                {
                    "name": "\u5bf9",
                    "value": 2,
                    "textStyle": {
                        "color": "rgb(149,60,69)"
                    }
                },
                {
                    "name": "\u628a",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(89,24,4)"
                    }
                },
                {
                    "name": "\u8fd9",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(99,9,21)"
                    }
                },
                {
                    "name": "\u73b0\u573a",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(91,82,96)"
                    }
                },
                {
                    "name": "\u4fdd\u6301",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(143,118,0)"
                    }
                },
                {
                    "name": "\u73b0\u72b6",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(66,131,19)"
                    }
                },
                {
                    "name": "\u73b0\u5728",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(2,25,120)"
                    }
                },
                {
                    "name": "\u6ca1",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(127,74,151)"
                    }
                },
                {
                    "name": "\u95ee\u9898",
                    "value": 2,
                    "textStyle": {
                        "color": "rgb(75,151,39)"
                    }
                },
                {
                    "name": "\u597d\u50cf",
                    "value": 2,
                    "textStyle": {
                        "color": "rgb(89,142,103)"
                    }
                },
                {
                    "name": "\u4ed6",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(94,107,139)"
                    }
                },
                {
                    "name": "\u53cd\u590d",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(136,25,143)"
                    }
                },
                {
                    "name": "\u51fa\u53bb",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(96,66,128)"
                    }
                },
                {
                    "name": "\u7684",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(64,63,129)"
                    }
                },
                {
                    "name": "\u6536",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(86,84,145)"
                    }
                },
                {
                    "name": "\u53ea\u8981",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(35,137,31)"
                    }
                },
                {
                    "name": "\u80fd",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(135,50,16)"
                    }
                },
                {
                    "name": "\u6536\u97f3",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(156,159,158)"
                    }
                },
                {
                    "name": "\u4f1a",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(107,47,85)"
                    }
                },
                {
                    "name": "\u7ed9",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(104,88,84)"
                    }
                },
                {
                    "name": "\u58f0\u97f3",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(71,93,44)"
                    }
                },
                {
                    "name": "\u53c8",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(48,72,129)"
                    }
                },
                {
                    "name": "\u5565",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(134,43,141)"
                    }
                },
                {
                    "name": "\u4e5f",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(14,113,153)"
                    }
                },
                {
                    "name": "\u542c",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(37,139,78)"
                    }
                },
                {
                    "name": "\u4e0d\u5230",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(44,142,115)"
                    }
                },
                {
                    "name": "\u627e",
                    "value": 4,
                    "textStyle": {
                        "color": "rgb(2,1,91)"
                    }
                },
                {
                    "name": "\u4eba",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(34,52,143)"
                    }
                },
                {
                    "name": "\u5e2e",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(41,79,119)"
                    }
                },
                {
                    "name": "\u4e00\u4e0b",
                    "value": 2,
                    "textStyle": {
                        "color": "rgb(16,142,106)"
                    }
                },
                {
                    "name": "\u66f9\u6f47",
                    "value": 2,
                    "textStyle": {
                        "color": "rgb(155,6,23)"
                    }
                },
                {
                    "name": "\u786e\u8ba4",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(58,157,87)"
                    }
                },
                {
                    "name": "\u9000\u51fa",
                    "value": 2,
                    "textStyle": {
                        "color": "rgb(57,5,73)"
                    }
                },
                {
                    "name": "\u307e",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(38,58,148)"
                    }
                },
                {
                    "name": "\u3060",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(2,122,30)"
                    }
                },
                {
                    "name": "\u5225",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(90,143,85)"
                    }
                },
                {
                    "name": "\u8358",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(77,94,154)"
                    }
                },
                {
                    "name": "\u62dc\u8bbf",
                    "value": 2,
                    "textStyle": {
                        "color": "rgb(28,99,116)"
                    }
                },
                {
                    "name": "\u5e26\u8def",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(31,47,134)"
                    }
                },
                {
                    "name": "\u5f15\u9886",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(56,50,1)"
                    }
                },
                {
                    "name": "\u63a5",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(113,15,114)"
                    }
                },
                {
                    "name": "\u554a",
                    "value": 3,
                    "textStyle": {
                        "color": "rgb(149,88,2)"
                    }
                },
                {
                    "name": "\u6210",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(10,5,80)"
                    }
                },
                {
                    "name": "\u5443",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(117,150,117)"
                    }
                },
                {
                    "name": "\u60a8",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(23,84,12)"
                    }
                },
                {
                    "name": "\u90a3\u8fb9",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(0,129,61)"
                    }
                },
                {
                    "name": "\u6536\u5230",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(49,40,77)"
                    }
                },
                {
                    "name": "\u90a3\u4e2a",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(29,105,25)"
                    }
                },
                {
                    "name": "\u77ed\u4fe1",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(8,86,48)"
                    }
                },
                {
                    "name": "\u5230",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(149,141,16)"
                    }
                },
                {
                    "name": "\u4e00\u697c",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(159,73,72)"
                    }
                },
                {
                    "name": "\u4e1c",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(77,133,118)"
                    }
                },
                {
                    "name": "\u4e00\u4f1a\u513f",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(125,22,96)"
                    }
                },
                {
                    "name": "\u665a\u4e0a",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(99,144,150)"
                    }
                },
                {
                    "name": "10",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(90,4,138)"
                    }
                },
                {
                    "name": "\u70b9\u534a\u53bb",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(66,63,112)"
                    }
                },
                {
                    "name": "\u591c\u5bb5",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(8,95,14)"
                    }
                },
                {
                    "name": "\u674e\u4e1c",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(112,100,134)"
                    }
                },
                {
                    "name": "\u8ba9",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(0,26,155)"
                    }
                },
                {
                    "name": "\u4e0b\u6765",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(17,12,157)"
                    }
                },
                {
                    "name": "\u62ff",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(105,6,104)"
                    }
                },
                {
                    "name": "\u5feb\u9012",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(134,154,124)"
                    }
                },
                {
                    "name": "\u53ef\u4ee5",
                    "value": 1,
                    "textStyle": {
                        "color": "rgb(14,91,100)"
                    }
                }
            ],
            "drawOutOfBound": false,
            "textStyle": {
                "emphasis": {}
            },
            "xAxisIndex": 5,
            "yAxisIndex": 5
        }
    ],
    "legend": [
        {
            "data": [
                "\u4f1a\u8bdd\u6570"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "greeting",
                "navigation",
                "search",
                "interview",
                "visit",
                "system",
                "other"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u6b21\u6570"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u6267\u884c\u6b21\u6570"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [
                "\u4f1a\u8bdd\u6570"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        },
        {
            "data": [],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 0,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "0\u65f6",
                "1\u65f6",
                "2\u65f6",
                "3\u65f6",
                "4\u65f6",
                "5\u65f6",
                "6\u65f6",
                "7\u65f6",
                "8\u65f6",
                "9\u65f6",
                "10\u65f6",
                "11\u65f6",
                "12\u65f6",
                "13\u65f6",
                "14\u65f6",
                "15\u65f6",
                "16\u65f6",
                "17\u65f6",
                "18\u65f6",
                "19\u65f6",
                "20\u65f6",
                "21\u65f6",
                "22\u65f6",
                "23\u65f6"
            ]
        },
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLabel": {
                "show": true,
                "rotate": 20,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "navigation_requests",
                "search_requests",
                "interview_requests",
                "visit_requests",
                "other"
            ]
        },
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 2,
            "axisLabel": {
                "show": true,
                "rotate": 30,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "\u9762\u8bd5/\u8bbf\u5ba2\u767b\u8bb0",
                "\u5e26\u8def\u5f15\u9886",
                "\u5bfc\u822a\u5230\u76ee\u7684\u5730",
                "\u627e\u4eba\u7559\u8a00",
                "\u786e\u8ba4\u53d1\u9001\u6d88\u606f",
                "\u9000\u51fa\u9762\u8bd5\u9875\u9762",
                "\u7ffb\u8bd1\u52a9\u624b",
                "\u91cd\u65b0\u8f93\u5165\u9a8c\u8bc1\u7801",
                "\u66f4\u65b0\u540d\u5b57\u548c\u7559\u8a00\u7684\u4fe1\u606f"
            ]
        },
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 3,
            "axisLabel": {
                "show": true,
                "rotate": 0,
                "margin": 8,
                "valueAnimation": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "0-30s",
                "30-60s",
                "60-120s",
                "120-300s",
                "300-600s",
                "600-1000s"
            ]
        }
    ],
    "yAxis": [
        {
            "name": "\u4f1a\u8bdd\u6570",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u6b21\u6570",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u6b21\u6570",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 2,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u4f1a\u8bdd\u6570",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 3,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "24\u5c0f\u65f6\u4f1a\u8bdd\u91cf\u5206\u5e03",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "text": "\u673a\u5668\u4eba\u6d88\u606f\u7c7b\u578b\u5206\u5e03",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "text": "\u7528\u6237\u4ea4\u4e92\u7c7b\u578b\u5206\u5e03",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "text": "\u673a\u5668\u4eba\u52a8\u4f5c\u6267\u884c\u7edf\u8ba1",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "text": "\u4f1a\u8bdd\u65f6\u957f\u5206\u5e03",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "text": "\u7528\u6237\u8f93\u5165\u70ed\u8bcd\u8bcd\u4e91",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "2%",
            "right": "55%",
            "height": "22%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "55%",
            "top": "2%",
            "right": "5%",
            "height": "22%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "26%",
            "right": "55%",
            "height": "22%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "55%",
            "top": "26%",
            "right": "5%",
            "height": "22%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "top": "50%",
            "right": "55%",
            "height": "22%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "55%",
            "top": "50%",
            "right": "5%",
            "height": "22%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ],
    "color": []
};
        chart_46ed930b7e454a748e53775bf72c7814.setOption(option_46ed930b7e454a748e53775bf72c7814);
    </script>
</body>
</html>
