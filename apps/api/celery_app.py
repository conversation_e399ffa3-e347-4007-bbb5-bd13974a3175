from celery import Celery
from celery.schedules import crontab
import os

# Redis 配置
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")

# 创建 Celery 实例
celery_app = Celery(
    "ce_monitor",
    broker=REDIS_URL,
    backend=REDIS_URL,
    #include=["tasks.sync_feishu", "tasks.aggregate_daily", "tasks.generate_llm_report", "app.tasks.synchronization"]
    include=["app.tasks.synchronization"]
)

# Celery 配置
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="Asia/Shanghai",
    enable_utc=True,
)

# 定时任务配置
celery_app.conf.beat_schedule = {
    """
    "aggregate_daily": {
        "task": "tasks.aggregate_daily",
        "schedule": crontab(hour=0, minute=5)
    },
    "sync_feishu": {
        "task": "tasks.sync_feishu",
        "schedule": crontab(hour="*/6")
    },
    "gen_llm_report": {
        "task": "tasks.generate_llm_report",
        "schedule": crontab(hour=0, minute=15)
    },
    """
    "sync_robot_positions": {
        "task": "tasks.sync_robot_positions",
        "schedule": crontab(minute=0, hour='*') # 每小时的0分执行
    }
} 