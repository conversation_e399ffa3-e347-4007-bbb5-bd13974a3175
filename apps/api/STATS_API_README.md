# Stats API 使用说明

## 概述

Stats API 提供了查询设备按天的query数据统计功能。该接口支持POST和GET两种请求方式。

## 接口地址

- **POST**: `http://10.118.9.78:8000/stats/`
- **GET**: `http://10.118.9.78:8000/stats/`

## 参数说明

### POST 方法参数 (JSON格式)

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scope | string | 是 | 查询范围，例如：cn |
| device_ids | array[string] | 是 | 设备ID列表 |

### GET 方法参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scope | string | 是 | 查询范围，例如：cn |
| device_ids | string | 是 | 设备ID列表，用逗号分隔 |

## 使用示例

### POST 方法示例 (JSON格式)

```bash
curl -X POST "http://10.118.9.78:8000/stats/" \
  -H "Content-Type: application/json" \
  -d '{
    "scope": "cn",
    "device_ids": [
      "M03SCN2A170253075474",
      "M03SCN1A120242156CAD",
      "M03SCN2A170252017BB1"
    ]
  }'
```

### GET 方法示例

```bash
curl "http://10.118.9.78:8000/stats/?scope=cn&device_ids=M03SCN2A170253075474,M03SCN1A120242156CAD,M03SCN2A170252017BB1"
```

## 请求体格式 (POST方法)

```json
{
    "scope": "cn",
    "device_ids": [
        "M03SCN2A100252161B26",
        "M03SCN1A120242156CAD",
        "M03SCN2A170252017BB1"
    ]
}
```

## 响应格式

```json
{
  "success": true,
  "data": [
    {
      "enterprise_id": "企业ID",
      "enterprise_name": "企业名称",
      "count_time": "2025-03-10",
      "device_nums": 5,
      "query_nums": 150
    }
  ],
  "total": 1,
  "scope": "cn",
  "device_count": 3,
  "start_date": "2025-02-10"
}
```

## 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| data | array | 统计数据列表 |
| total | integer | 数据总条数 |
| scope | string | 查询范围 |
| device_count | integer | 查询的设备数量 |
| start_date | string | 查询开始日期 |

### data 数组中的字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| enterprise_id | string | 企业ID |
| enterprise_name | string | 企业名称 |
| device_id | string | 设备ID |
| count_time | string | 统计日期 |
| query_nums | integer | 该设备该日期的查询次数 |

## 数据库查询逻辑

该接口基于以下SQL查询实现：

```sql
SELECT  
    `enterprise_id`,
    `enterprise_name`, 
    device_id,
    count_time,
    SUM(`d_b_interaction_times`) AS query_nums 
FROM `orion_device_profile_gb` 
WHERE count_time >= '2025-02-10' 
AND device_id IN('M03SCN2A170253075474','M03SCN1A120242156CAD',...)
AND `is_agentOs` = 1 
GROUP BY `enterprise_id`, `enterprise_name`, count_time, device_id
ORDER BY `enterprise_id`, count_time
```

## 注意事项

1. 查询时间范围默认为最近30天
2. 只查询 `is_agentOs = 1` 的设备数据
3. 按企业ID和日期进行分组统计
4. 支持多个设备ID的批量查询
5. 数据库连接根据scope参数自动选择（cn -> bigdata_cn）

## 错误处理

当发生错误时，接口会返回以下格式的错误信息：

```json
{
  "detail": "查询失败: 具体错误信息"
}
```

常见错误：
- 400: 参数错误
- 500: 服务器内部错误或数据库连接失败 