
# CEMonitor

CEMonitor 客户体验监控系统【Customer Experience Monitor 】  
**数据源**：MySQL · Redis · 飞书多维表格  
**技术栈**：React 19 / Vite 6 · FastAPI · Celery · PostGIS · OpenAI GPT‑4

> 本 README 含 Mermaid 图，GitHub / Gitee / 飞书文档均可直接渲染。如在本地查看请安装 [Markdown Preview Mermaid 支持](https://marketplace.visualstudio.com/items?itemName=shd101wyy.markdown-preview-enhanced)。

---

## 📈 顶层架构

```mermaid
flowchart LR
  %% ============ 数据源 ============
  subgraph MySQL_CN["🗄 MySQL · 国内库"]
    CN_QueryDay["设备日查询数"]
    CN_QueryDetail["Query 明细 + Plan"]
    CN_DeviceInfo["设备位置·企业·代理商"]
  end
  subgraph MySQL_OS["🗄 MySQL · 海外库"]
    OS_QueryDay["设备日查询数"]
    OS_QueryDetail["Query 明细 + Plan"]
    OS_DeviceInfo["设备位置·企业·代理商"]
  end

  subgraph Feishu["📋 飞书多维表格"]
    Sheet_Up_CN["国内升级申请"]
    Sheet_Up_OS["海外升级申请"]
    Sheet_Fbk_CN["国内用户反馈"]
    Sheet_Fbk_OS["海外用户反馈"]
  end

  %% ============ 第三方服务 ============
  subgraph ThirdParty["🔌 第三方服务"]
    FeishuSDK["飞书 SDK"]
    OpenAI["OpenAI GPT-4 API"]
  end

  %% ============ 缓存层 ============
  Redis["🧭 Redis Cache / Broker"]

  %% ============ 后端 ============
  subgraph Backend["🔗 FastAPI (REST + WS)"]
    FastAPI
  end

  %% ============ 前端 ============
  subgraph Frontend["💻 React 19 · Vite 6 SPA"]
    Tabs["Tag Tabs<br>(国内 / 全球)"]
    Map["WorldMap<br>(React-Leaflet)"]
    Stats["Stats Cards<br>(在线 / 离线 / 今日查询)"]
    DeviceList["Device Status List"]
  end

  %% ---------- 数据流 ----------
  %% MySQL → FastAPI
  CN_QueryDay --> FastAPI
  CN_QueryDetail --> FastAPI
  CN_DeviceInfo --> FastAPI
  OS_QueryDay --> FastAPI
  OS_QueryDetail --> FastAPI
  OS_DeviceInfo --> FastAPI

  %% 飞书 → FastAPI
  Sheet_Up_CN --> FastAPI
  Sheet_Up_OS --> FastAPI
  Sheet_Fbk_CN --> FastAPI
  Sheet_Fbk_OS --> FastAPI

  %% FastAPI 调用第三方
  FastAPI -.-> FeishuSDK
  FastAPI -.-> OpenAI

  %% FastAPI 与 Redis
  FastAPI -. Cache .- Redis

  %% FastAPI → 前端
  FastAPI -->|JSON / WebSocket| Map
  FastAPI -->|JSON / WebSocket| Stats
  FastAPI -->|JSON / WebSocket| DeviceList
  FastAPI -->|选项数据| Tabs

  %% 页面组件关系（虚线）
  Tabs -- 切换国内/全球 --> Map
  Tabs -. 同步过滤 .- Stats
  Tabs -. 同步过滤 .- DeviceList


```

---

## ⏱ 时序图（前端一次数据切换请求）

```mermaid
sequenceDiagram
    participant UI as "TagTabs / Map\nStatsCards / List"
    participant API as FastAPI
    participant R as Redis
    participant DB as MySQL(国内 / 海外)

    UI->>API: GET /devices?scope=global
    API->>R: 读取缓存
    alt Cache Hit
        R-->>API: 设备+统计
    else Cache Miss
        API->>DB: SQL 查询设备 & 统计
        DB-->>API: rows
        API->>R: SET key ttl=60s
    end
    API-->>UI: JSON {points, stats, list}
    Note right of UI: 组件统一刷新


```

---
## ⏱ 核心时序图（后台 10 min 同步飞书表 + GPT 分析）

```mermaid
sequenceDiagram
    participant Cron as Scheduler
    participant API as FastAPI Job
    participant FS as 飞书 SDK
    participant AI as GPT-4
    participant R as Redis

    Cron->>API: trigger sync_feishu
    API->>FS: 拉升级申请 / 用户反馈
    FS-->>API: Sheet JSON
    API->>AI: prompt(近24h汇总)
    AI-->>API: 分析文本
    API->>R: UPDATE 缓存 (sheet & 分析)
    API-->>Cron: OK
    Note over R: 前端通过 WS<br/>收到增量推送


```

---

## 🗄 ER 模型

```mermaid

```

---

---

## 设备使用活跃度状态机

```mermaid
stateDiagram-v2
    direction LR
    [*] --> ActiveOK
    ActiveOK --> UsageWarn : 连续1天无Query
    UsageWarn --> UsageCrit : 连续≥3天无Query
    UsageWarn --> ActiveOK : 当天有Query
    UsageCrit --> ActiveOK : 当天有Query

```



## 📂 目录结构

```text
CEMonitor/
├─ apps/
│  ├─ api/                # FastAPI 服务
│  │   ├─ app/            #   ├─ main.py 入口
│  │   │   ├─ models/     #   ├─ ORM & Pydantic
│  │   │   ├─ services/   #   ├─ 业务逻辑
│  │   │   ├─ deps/       #   ├─ Depends 注入
│  │   │   └─ ws/         #   └─ WebSocket
│  │   ├─ tasks/          # Celery 任务
│  │   ├─ celery_app.py   # Celery 实例
│  │   └─ alembic/        # 迁移脚本
│  └─ web/
│      ├─ src/
│      │   ├─ api/        # OpenAPI hooks
│      │   ├─ features/   # map / leaderboard / detail
│      │   ├─ components/ # 通用 UI
│      │   ├─ hooks/      # 自定义 hooks
│      │   └─ main.tsx
│      └─ tests/
├─ packages/
│  ├─ shared-types/       # TS ↔ Pydantic 公共 DTO
│  └─ ui/                 # shadcn/ui 二次封装
├─ infra/
│  ├─ docker-compose.yml
│  ├─ k8s/
│  └─ sql/
├─ scripts/               # 运维辅助
└─ README.md
```

### 目录说明

| 目录 | 作用 |
|------|------|
| `apps/api` | REST & WebSocket 网关、鉴权、OpenAPI |
| `apps/web` | 前端 SPA（React + Vite） |
| `packages/shared-types` | 自动生成的共享类型 |
| `infra` | 容器/云原生部署脚本 |
| `scripts` | 数据导入、运维脚本 |

---

## 🛠 技术栈

### 前端

| 技术 | 用途 |
|------|------|
| **React 19 + Vite 6 + TS** | 组件化、极速构建 |
| React‑Leaflet + Leaflet.ChineseTmsProviders | 全球/国内双底图、MarkerCluster |
| Plotly.js (`react-plotly`) | 交互式图表 |
| TanStack Query v6 | 数据获取 & 缓存 |
| Zustand / Redux Toolkit | 全局状态 |
| TailwindCSS + shadcn/ui | 样式与组件 |
| Vitest · Playwright | 单测 & E2E |
|  Node.js · v16.20.2 | 前端依赖管理 |
|  npm · v10.9.0 | 前端依赖管理 |
|  nvm · v0.39.7 | 前端依赖管理 |

### 后端

| 技术 | 用途 |
|------|------|
| **FastAPI (Python 3.12)** | ASGI 网关、OpenAPI |
| SQLAlchemy 2 · Pydantic v2 | ORM / 校验 |
| Celery 5 · Redis 7 | 批处理 & 定时任务 |
| PostgreSQL 15 + PostGIS 3 | 持久化 & GIS |
| ClickHouse 24 | OLAP 查询日志 |
| MySQL 8 | 设备/企业主数据 |
| 飞书多维表格 API | 维度同步 |
| OpenAI GPT‑4 | LLM 分析 |
| Docker Compose / K8s | 部署 |

---

## 🚀 快速开始

```bash
# 复制环境变量样例并填写
cp .env.example .env

# 一键启动
docker compose up -d --build

# 首次初始化数据库
docker compose exec api alembic upgrade head

# 访问
open http://localhost:5173          # 前端
open http://localhost:8000/docs     # API Swagger
```

---

## ⏲ 任务计划（Celery Beat）

```python
CELERY_BEAT_SCHEDULE = {
    "aggregate_daily":  { "task": "tasks.aggregate_daily", "schedule": crontab(hour=0, minute=5) },
    "sync_feishu":      { "task": "tasks.sync_feishu",     "schedule": crontab(hour="*/6")      },
    "gen_llm_report":   { "task": "tasks.generate_llm_report", "schedule": crontab(hour=0, minute=15) },
}
```

---

## 🔑 环境变量

| 名称 | 说明 |
|------|------|
| `MYSQL_*` | MySQL 连接信息 |
| `REDIS_URL` | Celery broker/result |
| `OPENAI_API_KEY` | GPT‑4 调用 |
| `FEISHU_APP_ID/SECRET` | 飞书 API |
| `JWT_SECRET_KEY` | 认证密钥 |

---

## 其他可补充
* 环境变量
  * MYSQL_CN_URI
  * MYSQL_OS_URI
  * REDIS_URL
  * FEISHU_APP_ID
  * FEISHU_APP_SECRET
  * OPENAI_API_KEY
  * JWT_SECRET_KEY

* 每日任务
  * sync_feishu
  * usage_watchdog
  * send_gpt_report

* 告警
  * 飞书机器人卡片
  * 邮件双通道
  

* i18n
  * 前端 react-i18next
  * 后端 Accept-Language 头

## 📜 License

MIT © 2025 CEMonitor Contributors
